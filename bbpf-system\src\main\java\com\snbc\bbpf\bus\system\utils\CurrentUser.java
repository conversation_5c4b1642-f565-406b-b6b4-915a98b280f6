/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.utils;

import com.snbc.bbpf.bus.system.constans.CommonConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;


/**
 * @ClassName: CurrentUser
 * 获取当前用户信息类
 * @module: bbpf-bus-system
 * @Author: yangweipeng
 * @date: 2021/5/18
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 */
@Component
@Slf4j
public final class CurrentUser {
    private static final ThreadLocal<String> CURRENT_USER = new ThreadLocal<>();
    private static final ThreadLocal<String> CURRENT_USER_NAME = new ThreadLocal<>();

    private CurrentUser() {
    }

    /**
     * 获取用户id
     * 如果用户ID存在直接获取，如果没有默认为1
     *
     * @return
     */
    public static String getRequestUrl() {
        HttpServletRequest request = getRequest();
        return request.getRequestURI();
    }

    /**
     * 获取用户id
     * 如果用户ID存在直接获取，如果没有默认为1
     *
     * @return
     */
    public static String getUserId() {
        String headerUserId = null;
        try {
            RequestAttributes req = RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = ((ServletRequestAttributes) req).getRequest();
            headerUserId = request.getHeader("userId");
        }catch (Exception e){
            log.warn("system get userId from header error",e);
        }
        if(StringUtils.isBlank(headerUserId)){
            return CURRENT_USER.get();
        }
        CURRENT_USER.set(headerUserId);
        return CURRENT_USER.get();
    }

    public static void setUserId(String userId) {
        CURRENT_USER.set(userId);
    }

    private static HttpServletRequest getRequest() {
        RequestAttributes req = RequestContextHolder.getRequestAttributes();
        return ((ServletRequestAttributes) req).getRequest();
    }

    /**
     * 获取用户名称
     *
     * @return
     */
    public static String getUserName() {
        HttpServletRequest request = getRequest();
        String userName = request.getHeader(CommonConstant.COM_HEAD_USERNAME);
        log.info("currentUser get user name={}", userName);
        try {
            userName= URLDecoder.decode(userName, StandardCharsets.UTF_8.name());
        } catch (UnsupportedEncodingException e) {
            log.error("CurrentUser decode user name error,userName={}",userName, e);
        }
        if (StringUtils.isBlank(userName)) {
            return CURRENT_USER_NAME.get();
        }
        CURRENT_USER_NAME.set(userName);
        return CURRENT_USER_NAME.get();
    }

    public static String getTenantId() {
        return getRequest().getHeader("tenantId");
    }
    /**
     * 获取用户登录的端
     *
     * @return
     */
    public static String getSysType() {
        HttpServletRequest request = getRequest();
        return request.getHeader(CommonConstant.CURRENT_SYS_TYPE);
    }
}
