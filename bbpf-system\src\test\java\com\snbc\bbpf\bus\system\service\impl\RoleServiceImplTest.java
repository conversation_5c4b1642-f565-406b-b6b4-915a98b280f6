package com.snbc.bbpf.bus.system.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.feign.BbpfMessageCenterService;
import com.snbc.bbpf.bus.system.utils.AllOrg;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.bus.system.utils.SendMsgUtil;
import com.snbc.bbpf.system.db.common.entity.Org;
import com.snbc.bbpf.system.db.common.entity.Role;
import com.snbc.bbpf.system.db.common.entity.User;
import com.snbc.bbpf.system.db.common.entity.UserRole;
import com.snbc.bbpf.system.db.common.mapper.OrgMapper;
import com.snbc.bbpf.system.db.common.mapper.PermissionRoleMapper;
import com.snbc.bbpf.system.db.common.mapper.RoleMapper;
import com.snbc.bbpf.system.db.common.mapper.RoleOrgMapper;
import com.snbc.bbpf.system.db.common.mapper.RolePermissionScopeMapper;
import com.snbc.bbpf.system.db.common.mapper.UserMapper;
import com.snbc.bbpf.system.db.common.mapper.UserRoleMapper;
import com.snbc.bbpf.system.db.common.model.RoleRelationUser;
import com.snbc.bbpf.system.db.common.model.SysRole;
import com.snbc.bbpf.system.db.common.model.SysScopePermission;
import com.snbc.bbpf.system.db.common.vo.RoleUserPage;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;

/**
 * @ClassName: RoleServiceImplTest
 * @Description: 角色逻辑测试
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/6/2
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
public class RoleServiceImplTest {

    @InjectMocks
    private RoleServiceImpl roleService = new RoleServiceImpl();
    @InjectMocks
    private RoleServiceExImpl roleServiceEx = new RoleServiceExImpl();
    @Mock
    private RoleMapper roleMapper;
    @Mock
    private RoleOrgMapper roleOrgMapper;
    @Mock
    private OrgMapper orgMapper;
    @Mock
    private PermissionRoleMapper permissionRoleMapper;
    @Mock
    private RolePermissionScopeMapper rolePermissionScopeMapper;
    @Mock
    private AllOrg allOrg;
    @Mock
    private UserRoleMapper userRoleMapper;
    @Mock
    private UserMapper userMapper;
    @Mock
    RedisTemplate<String, String> redisTemplate;
    @Mock
    private ValueOperations valueOperations;
    @Mock
    private HashOperations hashOperations;
    @Mock
    private SendMsgUtil sendMsgUtil;
    @Mock
    private BbpfMessageCenterService messageService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("测试新增角色")
    @Tag("@id:23612")
    @Tag("@author:wangsong")
    @Tag("@date:2021/6/2")
    void testAddRole() {
        Mockito.when(roleMapper.selectCountByRoleName(Mockito.any())).thenReturn(0);
        List<Org> orgList = new ArrayList<>();
        orgList.add(Org.builder().orgId("1").build());
        orgList.add(Org.builder().orgId("2").build());
        Mockito.when(orgMapper.getOrgsByUserId(Mockito.any())).thenReturn(orgList);
        String[] permissionIds = {"1", "2"};
        String[] dataScopeIds = {"23425", "356"};
        List<SysScopePermission> dataPermission = new ArrayList<>();
        SysScopePermission sysScopePermission = new SysScopePermission();
        sysScopePermission.setDataScopeIds(dataScopeIds);
        sysScopePermission.setPermissionId("098765432");
        dataPermission.add(sysScopePermission);
        SysRole sysRole = new SysRole();
        sysRole.setPermissionIds(permissionIds);
        sysRole.setBelongOrgIds("0");
        roleService.addRole(sysRole, "1");
        Mockito.verify(roleMapper,times(1)).insertRole(Mockito.any());
    }

//    @Test
    @DisplayName("角色关联用户")
    @Tag("@id:23616")
    @Tag("@author:wangsong")
    @Tag("@date:2021/6/2")
    void testRoleRelationUser_role_bind_user() {
        try(MockedStatic<CurrentUser> currentUserMockedStatic = Mockito.mockStatic(CurrentUser.class)) {
            String[] roleIds = {"234", "76"};
            String[] userIds = {"098", "234"};
            User user = User.builder().userName("ceshi").build();
            List<UserRole> userRoleList = new ArrayList<>();
            userRoleList.add(UserRole.builder().id("0").roleId("234").userId("098").build());
            Mockito.when(userRoleMapper.selectAlreadyUserRole(Mockito.anyList())).thenReturn(userRoleList);
            Mockito.when(userMapper.selectByPrimaryKey(Mockito.any())).thenReturn(user);
            roleService.roleRelationUser(RoleRelationUser.builder().roleIds(roleIds).userIds(userIds).status("1").build());
            Mockito.verify(userRoleMapper, times(1)).insertRelation(Mockito.any());
        }
    }

//    @Test
    @DisplayName("角色解绑用户")
    @Tag("@id:23616")
    @Tag("@author:wangsong")
    @Tag("@date:2021/6/2")
    void testRoleRelationUser_role_unBind_user() {
        try(MockedStatic<CurrentUser> currentUserMockedStatic = Mockito.mockStatic(CurrentUser.class)) {
            String[] roleIds = {"234", "76"};
            String[] userIds = {"098", "234"};
            User user = User.builder().userName("ceshi").build();
            roleService.roleRelationUser(RoleRelationUser.builder().roleIds(roleIds).userIds(userIds).status("0").build());
            Mockito.when(userMapper.selectByPrimaryKey(Mockito.any())).thenReturn(user);
            Mockito.verify(userRoleMapper, times(1)).deleteRelation(Mockito.any());
        }
    }

    @Test
    @DisplayName("删除角色")
    @Tag("@id:23614")
    @Tag("@author:wangsong")
    @Tag("@date:2021/6/2")
    void testDelRole() throws JsonProcessingException {
        String roleId = "2";
        Mockito.when(userRoleMapper.selectUserCountByRole(Mockito.anyString())).thenReturn(0);
        Mockito.when(roleMapper.selectRoleDetail(Mockito.anyString())).thenReturn(Role.builder().roleType(1).build());
        roleService.delRole(roleId);
        Mockito.verify(roleMapper,times(1)).deleteByPrimaryKey(Mockito.any());
    }

    @Test
    @DisplayName("不能删除超级管理员角色")
    @Tag("@id:23614")
    @Tag("@author:wangsong")
    @Tag("@date:2021/6/2")
    void testDel_super_role() {
        try {
            String roleId = "-1";
            Mockito.when(userRoleMapper.selectUserCountByRole(Mockito.anyString())).thenReturn(0);
            Mockito.when(roleMapper.selectRoleDetail(Mockito.anyString())).thenReturn(Role.builder().roleType(1).build());
            roleService.delRole(roleId);
        } catch (Exception e) {
            Assertions.assertEquals(e.getMessage(), ErrorMessage.SUPER_ROLE_CANNOT_UPDATE.getMessage());
        }
    }

    @Test
    @DisplayName("测试根据组织机构获取用户")
    @Tag("@id:23614")
    @Tag("@author:wangsong")
    @Tag("@date:2021/6/2")
    void testGetUserByOrg() {
        try(MockedStatic mockedStatic = mockStatic(CurrentUser.class)) {
            List<String> orgIdList = new ArrayList<>();
            orgIdList.add("1");
            Mockito.when(orgMapper.selectOrgIdsByParentId(Mockito.any())).thenReturn(orgIdList);
            List<RoleUserPage> roleUserPageList = new ArrayList<>();
            RoleUserPage roleUserPage = new RoleUserPage();
            roleUserPage.setPhone("P7d31x0R/PXtHA570jjKEQ==");
            roleUserPageList.add(roleUserPage);
            Mockito.when(userMapper.getUserByOrgPath(Mockito.any())).thenReturn(roleUserPageList);
            List<String> orgPathList = new ArrayList<>();
            orgPathList.add("");
            Mockito.when(allOrg.getOrgPathList(Mockito.any(),Mockito.any())).thenReturn(orgPathList);
            String orgId = "1";
            Mockito.when(allOrg.getOrgByOrgId(orgId)).thenReturn(new Org());
            roleServiceEx.getUserByOrg(orgId);
        }
    }

    @Test
    @DisplayName("测试根据用户获取角色id")
    @Tag("@id:23614")
    @Tag("@author:wangsong")
    @Tag("@date:2021/7/7")
    void testQueryRoleIdsByUserId(){
        List<Role> roles = new ArrayList<>();
        roles.add(Role.builder().roleName("角色").roleId("01").build());
        Mockito.when(roleMapper.queryRoleListByUserId(Mockito.anyString())).thenReturn(roles);
        Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        List<String> roleIds = roleServiceEx.queryRoleIdsByUserId("1");
        Assertions.assertEquals(roleIds.get(0),"01");
    }

    @Test
    @DisplayName("测试修改角色")
    @Tag("@id:23614")
    @Tag("@author:wangsong")
    @Tag("@date:2021/7/7")
    void testUpdateRole(){
        String[] permissionIds = {"1","2"};
        String[] dataScopeIds = {"11","22"};
        List<SysScopePermission> dataPermission = new ArrayList<>();
        SysScopePermission sysScopePermission = new SysScopePermission();
        sysScopePermission.setPermissionId("3");
        sysScopePermission.setDataScopeIds(dataScopeIds);
        dataPermission.add(sysScopePermission);
        SysRole updateRoleVo = new SysRole();
        updateRoleVo.setRoleId("1");
        updateRoleVo.setRoleName("juese001");
        updateRoleVo.setPermissionIds(permissionIds);
        updateRoleVo.setBelongOrgIds("0");
        Mockito.when(redisTemplate.opsForHash()).thenReturn(hashOperations);
        roleService.updateRole(updateRoleVo);
        Mockito.verify(roleMapper,times(1)).updateRole(Mockito.any());

    }

    @Test
    @DisplayName("删除已绑定用户的角色")
    @Tag("@id:23614")
    @Tag("@author:wangsong")
    @Tag("@date:2021/6/2")
    void testDelRole_role_bind_user_throw_execption() {
        String roleId = "2";
        Mockito.when(userRoleMapper.selectUserCountByRole(Mockito.anyString())).thenReturn(1);
        Mockito.when(roleMapper.selectRoleDetail(Mockito.anyString())).thenReturn(Role.builder().roleType(1).build());
        try {
            roleService.delRole(roleId);
        } catch (BusinessException e) {
            Assertions.assertEquals(e.getCode(),ErrorMessage.ROLE_BOUND_USER_DEL.getCode());
        }
    }
}
