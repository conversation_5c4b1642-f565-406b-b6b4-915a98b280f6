package com.snbc.bbpf.grafana.proxy.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Grafana代理服务配置类
 * 
 * 用于配置Grafana代理服务的相关参数，包括：
 * - Grafana服务器地址
 * - BBPF权限API地址
 * - JWT密钥配置
 * - 缓存配置等
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2024-12-13
 */
@Configuration
@ConfigurationProperties(prefix = "bbpf.grafana.proxy")
public class GrafanaProxyConfig {

	/**
	 * Grafana服务器基础URL
	 */
	private String grafanaBaseUrl = "http://localhost:3000";

	/**
	 * 代理服务器基础URL
	 */
	private String proxyBaseUrl = "http://localhost:8081";

	/**
	 * BBPF权限API基础URL
	 */
	private String bbpfPermissionApiUrl = "http://localhost:8080/api/datapermission";

	// BBPF API Token 已移除 - 现在从请求中动态获取

	/**
	 * BBPF API签名密钥
	 */
	private String bbpfApiSignatureKey = "";

	/**
	 * JWT密钥
	 */
	private String jwtSecret = "bbpf-grafana-proxy-secret-key";

	/**
	 * JWT过期时间（秒）
	 */
	private long jwtExpirationSeconds = 3600;

	/**
	 * 权限缓存过期时间（秒）
	 */
	private long permissionCacheExpirationSeconds = 300;

	/**
	 * 是否启用权限缓存
	 */
	private boolean enablePermissionCache = true;

	/**
	 * 代理超时时间（毫秒）
	 */
	private int proxyTimeoutMs = 30000;

	/**
	 * 最大重试次数
	 */
	private int maxRetryCount = 3;

	/**
	 * 是否启用详细日志
	 */
	private boolean enableVerboseLogging = false;

	/**
	 * Grafana管理员用户名
	 */
	private String grafanaAdminUsername = "admin";

	/**
	 * Grafana管理员密码
	 */
	private String grafanaAdminPassword = "admin";

	/**
	 * Grafana API Token（优先使用，如果配置了则不使用用户名密码）
	 */
	private String grafanaApiToken = "";

	/**
	 * 是否启用Grafana Auth Proxy模式
	 */
	private boolean enableAuthProxy = true;

	/**
	 * Grafana Auth Proxy Header名称
	 */
	private String authProxyHeaderName = "X-WEBAUTH-USER";

	/**
	 * 是否自动创建用户（Auth Proxy模式下）
	 */
	private boolean autoSignUp = true;

	/**
	 * 允许的CORS源
	 */
	private String allowedOrigins = "*";

	/**
	 * 是否启用WebSocket支持
	 */
	private boolean enableWebSocket = true;

	/**
	 * WebSocket连接超时时间（毫秒）
	 */
	private int webSocketTimeoutMs = 30000;

	/**
	 * WebSocket心跳间隔（毫秒）
	 */
	private int webSocketHeartbeatMs = 25000;

	/**
	 * 是否禁用Grafana分析功能（Explore和Inspect）
	 */
	private boolean disableAnalysisFeatures = true;

	/**
	 * 是否启用动态模板变量注入
	 */
	private boolean enableDynamicVariables = true;

	/**
	 * 动态变量缓存过期时间（秒）
	 */
	private long variableCacheTtl = 300;

	/**
	 * 是否启用变量注入详细日志
	 */
	private boolean enableVariableInjectionLogging = false;

	// Getter and Setter methods

	public String getGrafanaBaseUrl() {
		return grafanaBaseUrl;
	}

	public void setGrafanaBaseUrl(String grafanaBaseUrl) {
		this.grafanaBaseUrl = grafanaBaseUrl;
	}

	public String getProxyBaseUrl() {
		return proxyBaseUrl;
	}

	public void setProxyBaseUrl(String proxyBaseUrl) {
		this.proxyBaseUrl = proxyBaseUrl;
	}

	public String getBbpfPermissionApiUrl() {
		return bbpfPermissionApiUrl;
	}

	public void setBbpfPermissionApiUrl(String bbpfPermissionApiUrl) {
		this.bbpfPermissionApiUrl = bbpfPermissionApiUrl;
	}

	// getBbpfApiToken 和 setBbpfApiToken 方法已移除 - 现在从请求中动态获取token

	public String getBbpfApiSignatureKey() {
		return bbpfApiSignatureKey;
	}

	public void setBbpfApiSignatureKey(String bbpfApiSignatureKey) {
		this.bbpfApiSignatureKey = bbpfApiSignatureKey;
	}

	public String getJwtSecret() {
		return jwtSecret;
	}

	public void setJwtSecret(String jwtSecret) {
		this.jwtSecret = jwtSecret;
	}

	public long getJwtExpirationSeconds() {
		return jwtExpirationSeconds;
	}

	public void setJwtExpirationSeconds(long jwtExpirationSeconds) {
		this.jwtExpirationSeconds = jwtExpirationSeconds;
	}

	public long getPermissionCacheExpirationSeconds() {
		return permissionCacheExpirationSeconds;
	}

	public void setPermissionCacheExpirationSeconds(long permissionCacheExpirationSeconds) {
		this.permissionCacheExpirationSeconds = permissionCacheExpirationSeconds;
	}

	public boolean isEnablePermissionCache() {
		return enablePermissionCache;
	}

	public void setEnablePermissionCache(boolean enablePermissionCache) {
		this.enablePermissionCache = enablePermissionCache;
	}

	public int getProxyTimeoutMs() {
		return proxyTimeoutMs;
	}

	public void setProxyTimeoutMs(int proxyTimeoutMs) {
		this.proxyTimeoutMs = proxyTimeoutMs;
	}

	public int getMaxRetryCount() {
		return maxRetryCount;
	}

	public void setMaxRetryCount(int maxRetryCount) {
		this.maxRetryCount = maxRetryCount;
	}

	public boolean isEnableVerboseLogging() {
		return enableVerboseLogging;
	}

	public void setEnableVerboseLogging(boolean enableVerboseLogging) {
		this.enableVerboseLogging = enableVerboseLogging;
	}

	public String getGrafanaAdminUsername() {
		return grafanaAdminUsername;
	}

	public void setGrafanaAdminUsername(String grafanaAdminUsername) {
		this.grafanaAdminUsername = grafanaAdminUsername;
	}

	public String getGrafanaAdminPassword() {
		return grafanaAdminPassword;
	}

	public void setGrafanaAdminPassword(String grafanaAdminPassword) {
		this.grafanaAdminPassword = grafanaAdminPassword;
	}

	public String getGrafanaApiToken() {
		return grafanaApiToken;
	}

	public void setGrafanaApiToken(String grafanaApiToken) {
		this.grafanaApiToken = grafanaApiToken;
	}

	public boolean isEnableAuthProxy() {
		return enableAuthProxy;
	}

	public void setEnableAuthProxy(boolean enableAuthProxy) {
		this.enableAuthProxy = enableAuthProxy;
	}

	public String getAuthProxyHeaderName() {
		return authProxyHeaderName;
	}

	public void setAuthProxyHeaderName(String authProxyHeaderName) {
		this.authProxyHeaderName = authProxyHeaderName;
	}

	public boolean isAutoSignUp() {
		return autoSignUp;
	}

	public void setAutoSignUp(boolean autoSignUp) {
		this.autoSignUp = autoSignUp;
	}

	public String getAllowedOrigins() {
		return allowedOrigins;
	}

	public void setAllowedOrigins(String allowedOrigins) {
		this.allowedOrigins = allowedOrigins;
	}

	public boolean isEnableWebSocket() {
		return enableWebSocket;
	}

	public void setEnableWebSocket(boolean enableWebSocket) {
		this.enableWebSocket = enableWebSocket;
	}

	public int getWebSocketTimeoutMs() {
		return webSocketTimeoutMs;
	}

	public void setWebSocketTimeoutMs(int webSocketTimeoutMs) {
		this.webSocketTimeoutMs = webSocketTimeoutMs;
	}

	public int getWebSocketHeartbeatMs() {
		return webSocketHeartbeatMs;
	}

	public void setWebSocketHeartbeatMs(int webSocketHeartbeatMs) {
		this.webSocketHeartbeatMs = webSocketHeartbeatMs;
	}

	public boolean isDisableAnalysisFeatures() {
		return disableAnalysisFeatures;
	}

	public void setDisableAnalysisFeatures(boolean disableAnalysisFeatures) {
		this.disableAnalysisFeatures = disableAnalysisFeatures;
	}

	public boolean isEnableDynamicVariables() {
		return enableDynamicVariables;
	}

	public void setEnableDynamicVariables(boolean enableDynamicVariables) {
		this.enableDynamicVariables = enableDynamicVariables;
	}

	public long getVariableCacheTtl() {
		return variableCacheTtl;
	}

	public void setVariableCacheTtl(long variableCacheTtl) {
		this.variableCacheTtl = variableCacheTtl;
	}

	public boolean isEnableVariableInjectionLogging() {
		return enableVariableInjectionLogging;
	}

	public void setEnableVariableInjectionLogging(boolean enableVariableInjectionLogging) {
		this.enableVariableInjectionLogging = enableVariableInjectionLogging;
	}
}