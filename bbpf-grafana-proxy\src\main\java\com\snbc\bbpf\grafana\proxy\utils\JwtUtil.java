package com.snbc.bbpf.grafana.proxy.utils;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jwts;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;

import com.snbc.bbpf.grafana.proxy.config.GrafanaProxyConfig;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.function.Function;

/**
 * JWT工具类
 * 
 * 提供JWT Token的生成、解析和验证功能，用于：
 * 1. 验证来自BBPF系统的JWT Token
 * 2. 提取Token中的用户信息
 * 3. 验证Token的有效性和过期时间
 * 
 * 使用RSA公私钥进行JWT签名和验证，与BBPF系统保持一致
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2024-12-13
 */
@Component
public class JwtUtil {

    private static final Logger logger = LoggerFactory.getLogger(JwtUtil.class);
    
    private static final String ERROR_SET = "用户TOKEN解析错误";
    private static final String AFTER_ROR_SET = "用户TOKEN过期";
    private static final String NO_SUCH_KEY = "转换密钥没有算法";
    private static final String INVALID_KEY_SPEC = "转换密钥KEY失败";
    private static final String IO_EXCEPTION_KEY = "转换密钥IO失败";

    @Autowired
    private GrafanaProxyConfig config;
    
    /**
     * RSA私钥，用于JWT签名
     */
    @Value("${bbpf.system.security.jwt.privateKey:}")
    private String privateKeyStr;
    
    /**
     * RSA公钥，用于JWT验证
     */
    @Value("${bbpf.system.security.jwt.publicKey:}")
    private String publicKeyStr;
    
    /**
     * JWT过期时间（小时）
     */
    @Value("${bbpf.system.security.jwt.expiration:24}")
    private Long expiration;
    
    private PrivateKey privateKey;
    private PublicKey publicKey;
    
    /**
     * 初始化公私钥
     */
    @PostConstruct
    public void init() {
        if (privateKeyStr != null && !privateKeyStr.isEmpty()) {
            privateKey = getPrivateKey(privateKeyStr);
        }
        if (publicKeyStr != null && !publicKeyStr.isEmpty()) {
            publicKey = getPublicKey(publicKeyStr);
        }
    }
    
    /**
     * 获取私钥
     *
     * @param priKey 私钥字符串
     * @return PrivateKey
     */
    private PrivateKey getPrivateKey(String priKey) {
        PrivateKey privateKeyTmp = null;
        PKCS8EncodedKeySpec priPKCS8;
        try {
            priPKCS8 = new PKCS8EncodedKeySpec(Base64Utils.decodeFromString(priKey));
            KeyFactory keyf = KeyFactory.getInstance("RSA");
            privateKeyTmp = keyf.generatePrivate(priPKCS8);
        } catch (NoSuchAlgorithmException ne) {
            logger.error(NO_SUCH_KEY, ne);
        } catch (InvalidKeySpecException ie) {
            logger.error(INVALID_KEY_SPEC, ie);
        } catch (Exception e) {
            logger.error(IO_EXCEPTION_KEY, e);
        }
        return privateKeyTmp;
    }

    /**
     * 获取公钥
     *
     * @param pubKey 公钥字符串
     * @return PublicKey
     */
    private PublicKey getPublicKey(String pubKey) {
        PublicKey publicKeyTmp = null;
        try {
            X509EncodedKeySpec bobPubKeySpec = new X509EncodedKeySpec(
                    Base64Utils.decodeFromString(pubKey));
            KeyFactory keyf = KeyFactory.getInstance("RSA");
            publicKeyTmp = keyf.generatePublic(bobPubKeySpec);
        } catch (NoSuchAlgorithmException ne) {
            logger.error(NO_SUCH_KEY, ne);
        } catch (InvalidKeySpecException iet) {
            logger.error(INVALID_KEY_SPEC, iet);
        } catch (Exception eq) {
            logger.error(IO_EXCEPTION_KEY, eq);
        }
        return publicKeyTmp;
    }

    /**
     * 从Token中提取用户名
     * 
     * @param token JWT Token
     * @return String 用户名
     */
    public String extractUsername(String token) {
        return extractClaim(token, Claims::getSubject);
    }

    /**
     * 从Token中提取用户ID
     * 
     * @param token JWT Token
     * @return String 用户ID
     */
    public String extractUserId(String token) {
        return extractClaim(token, claims -> claims.get("userId", String.class));
    }

    /**
     * 从Token中提取会话ID
     * 
     * @param token JWT Token
     * @return String 会话ID
     */
    public String extractSessionId(String token) {
        return extractClaim(token, claims -> claims.get("sessionId", String.class));
    }

    /**
     * 从Token中提取过期时间
     * 
     * @param token JWT Token
     * @return Date 过期时间
     */
    public Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }

    /**
     * 从Token中提取指定的声明
     * 
     * @param token JWT Token
     * @param claimsResolver 声明解析器
     * @param <T> 返回类型
     * @return T 提取的声明值
     */
    public <T> T extractClaim(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = extractAllClaims(token);
        return claimsResolver.apply(claims);
    }

    /**
     * 提取Token中的所有声明
     * 
     * @param token JWT Token
     * @return Claims 所有声明
     */
    private Claims extractAllClaims(String token) {
        try {
            return Jwts.parserBuilder().setSigningKey(publicKey).build()
                    .parseClaimsJws(new String(Base64Utils.decodeFromString(token),
                            StandardCharsets.UTF_8)).getBody();
        } catch (ExpiredJwtException e) {
            logger.error(AFTER_ROR_SET, e);
            throw new RuntimeException("JWT token expired", e);
        } catch (Exception e) {
            logger.error(ERROR_SET, e);
            throw new RuntimeException("Invalid JWT token", e);
        }
    }

    /**
     * 检查Token是否过期
     * 
     * @param token JWT Token
     * @return boolean true表示已过期，false表示未过期
     */
    public boolean isTokenExpired(String token) {
        try {
            return extractExpiration(token).before(new Date());
        } catch (Exception e) {
            logger.warn("Failed to check token expiration: {}", e.getMessage());
            return true; // 如果无法解析，认为已过期
        }
    }

    /**
     * 验证Token的有效性
     * 
     * @param token JWT Token
     * @param username 期望的用户名
     * @return boolean true表示有效，false表示无效
     */
    public boolean validateToken(String token, String username) {
        try {
            final String extractedUsername = extractUsername(token);
            return (extractedUsername.equals(username) && !isTokenExpired(token));
        } catch (Exception e) {
            logger.warn("Token validation failed: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 验证Token的有效性（不检查用户名）
     * 
     * @param token JWT Token
     * @return boolean true表示有效，false表示无效
     */
    public boolean validateToken(String token) {
        try {
            if (publicKey == null) {
                logger.error("Public key is not initialized");
                return false;
            }
            extractAllClaims(token); // 尝试解析Token
            return !isTokenExpired(token);
        } catch (Exception e) {
            logger.warn("Token validation failed: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 生成JWT Token（用于测试或内部使用）
     * 
     * @param username 用户名
     * @param userId 用户ID
     * @param sessionId 会话ID
     * @return String JWT Token
     */
    public String generateToken(String username, String userId, String sessionId) {
        Claims defaultClaims = Jwts.claims();
        defaultClaims.put("userId", userId);
        defaultClaims.put("userName", username);
        defaultClaims.put("sessionId", sessionId);
        defaultClaims.put("created", 
                Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant()));
        defaultClaims.setSubject(username);
        
        return generateToken(defaultClaims);
    }
    
    /**
     * 生成token
     * 过期时间的最小单位为小时
     * @param claims 具体参数
     * @return TOKEN
     */
    private String generateToken(Claims claims) {
        if (privateKey == null) {
            logger.error("Private key is not initialized");
            throw new RuntimeException("Private key is not available");
        }
        
        return Jwts.builder()
                .setClaims(claims)
                .setExpiration(Date.from(LocalDateTime.now().plusHours(expiration != null ? expiration : 24L)
                        .atZone(ZoneId.systemDefault()).toInstant()))
                .signWith(privateKey)
                .compact();
    }

    /**
     * 从HTTP Authorization头中提取Token
     * 
     * @param authorizationHeader Authorization头的值
     * @return String JWT Token，如果格式不正确则返回null
     */
    public String extractTokenFromHeader(String authorizationHeader) {
        if (authorizationHeader != null && authorizationHeader.startsWith("Bearer ")) {
            return authorizationHeader.substring(7);
        }
        return null;
    }
    
    /**
     * 检查Token是否可以刷新
     * 如果过期了表示可以刷新
     * @param token JWT Token
     * @return boolean true表示可以刷新，false表示不可以
     */
    public boolean canTokenBeRefreshed(String token) {
        try {
            extractAllClaims(token);
            return false; // 如果能正常解析，说明未过期，不需要刷新
        } catch (Exception e) {
            if (e.getCause() instanceof ExpiredJwtException) {
                return true; // 过期了可以刷新
            }
            return false; // 其他错误不能刷新
        }
    }
    
    /**
     * 刷新token
     *
     * @param token 原始token
     * @return String 新的token
     */
    public String refreshToken(String token) {
        try {
            Claims claims = extractAllClaims(token);
            claims.put("created",
                    Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant()));
            return generateToken(claims);
        } catch (Exception e) {
            logger.error("刷新用户TOKEN错误", e);
            return "";
        }
    }
}