package com.snbc.bbpf.bus.system.service.impl;

import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.bus.system.service.PermissionScopeService;
import com.snbc.bbpf.bus.system.service.PermissionService;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.system.db.common.entity.Permission;
import com.snbc.bbpf.system.db.common.entity.PermissionNode;
import com.snbc.bbpf.system.db.common.entity.PermissionScope;
import com.snbc.bbpf.system.db.common.entity.Role;
import com.snbc.bbpf.system.db.common.mapper.PermissionMapper;
import com.snbc.bbpf.system.db.common.mapper.PermissionRoleMapper;
import com.snbc.bbpf.system.db.common.mapper.RoleMapper;
import com.snbc.bbpf.system.db.common.mapper.UserMapper;
import com.snbc.bbpf.system.db.common.vo.PermissionInfo;
import com.snbc.bbpf.system.db.common.vo.PermissionSortVo;
import com.snbc.bbpf.system.db.common.vo.RolePermissionVo;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.type.JavaType;
import org.junit.Rule;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.rules.ExpectedException;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.Month;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyCollection;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

/**
 * @ClassName: PermissionServiceImplTest
 * 权限定义类测试类
 * @module: bbpf-system-manager
 * @Author: yangweipeng
 * @date: 2021/6/23
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 */
class PermissionServiceImplTest {
    @Mock
    PermissionMapper permissionMapper;
    @Mock
    RoleMapper roleMapper;
    @Mock
    UserMapper userMapper;
    @Mock
    PermissionRoleMapper permissionRoleMapper;
    @Mock
    RedisTemplate redisTemplate;
    @Mock
    StringRedisTemplate stringRedisTemplate;
    @Mock
    Logger log;
    @Mock
    ValueOperations valueOperations;
    @Mock
    HashOperations  hashOperations;
    @InjectMocks
    PermissionService permissionServiceImpl;
    @Mock
    PermissionScopeService permissionScopeService;

    List<Permission> permissions;
    private static final ObjectMapper objectMapper = new ObjectMapper();
    @BeforeEach
    void setUp() throws  Exception {
        String strPermissionList = "[\n" +
                "    {\n" +
                "        \"permissionId\":\"-1\",\n" +
                "        \"permissionName\":\"权限\",\n" +
                "        \"parentId\":\"\",\n" +
                "        \"parentName\":\"#\",\n" +
                "        \"permissionLevel\":1,\n" +
                "        \"routingUrl\":null,\n" +
                "        \"permissionType\":\"0\",\n" +
                "        \"permissionImage\":null,\n" +
                "        \"hasEnable\":1,\n" +
                "        \"permissionDesc\":\"权限根节点\",\n" +
                "        \"orderBy\":0,\n" +
                "        \"permissionCode\":\"QBQX\",\n" +
                "        \"sysType\":\"0\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"permissionId\":\"2825a748-031f-47ef-8156-5a4ae1ff0df8\",\n" +
                "        \"permissionType\":\"2\",\n" +
                "        \"permissionName\":\"日志管理\",\n" +
                "        \"permissionCode\":\"logger\",\n" +
                "        \"parentId\":\"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "        \"orderBy\":6,\n" +
                "        \"permissionImage\":\"el-icon-document\",\n" +
                "        \"hasEnable\":1,\n" +
                "        \"routingUrl\":\"#\",\n" +
                "        \"parentName\":\"系统管理\",\n" +
                "        \"permissionDesc\":\"\",\n" +
                "        \"permissionLevel\":4,\n" +
                "        \"sysType\":0\n" +
                "    },\n" +
                "    {\n" +
                "        \"permissionId\":\"202ac195-8d8f-4be5-af56-9b3400de04dd\",\n" +
                "        \"permissionType\":\"2\",\n" +
                "        \"permissionName\":\"配置管理\",\n" +
                "        \"permissionCode\":\"configuration\",\n" +
                "        \"parentId\":\"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "        \"orderBy\":5,\n" +
                "        \"permissionImage\":\"el-icon-hot-water\",\n" +
                "        \"hasEnable\":1,\n" +
                "        \"routingUrl\":\"#\",\n" +
                "        \"parentName\":\"系统管理\",\n" +
                "        \"permissionDesc\":\"配置管理\",\n" +
                "        \"permissionLevel\":4,\n" +
                "        \"sysType\":0\n" +
                "    },\n" +
                "    {\n" +
                "        \"permissionId\":\"2519d688-cccb-4b37-99ab-3f9ba0659b5a\",\n" +
                "        \"permissionType\":\"2\",\n" +
                "        \"permissionName\":\"字典管理\",\n" +
                "        \"permissionCode\":\"dictionary\",\n" +
                "        \"parentId\":\"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "        \"orderBy\":4,\n" +
                "        \"permissionImage\":\"el-icon-goods\",\n" +
                "        \"hasEnable\":1,\n" +
                "        \"routingUrl\":\"#\",\n" +
                "        \"parentName\":\"系统管理\",\n" +
                "        \"permissionDesc\":\"系统管理\",\n" +
                "        \"permissionLevel\":5,\n" +
                "        \"sysType\":0\n" +
                "    },\n" +
                "    {\n" +
                "        \"permissionId\":\"3ef01dea-99ab-44cd-b624-6b9b3f4e2b98\",\n" +
                "        \"permissionType\":\"2\",\n" +
                "        \"permissionName\":\"权限管理\",\n" +
                "        \"permissionCode\":\"permission\",\n" +
                "        \"parentId\":\"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "        \"orderBy\":2,\n" +
                "        \"permissionImage\":\"el-icon-ice-drink\",\n" +
                "        \"hasEnable\":1,\n" +
                "        \"routingUrl\":\"#\",\n" +
                "        \"parentName\":\"系统管理\",\n" +
                "        \"permissionDesc\":\"权限管理\",\n" +
                "        \"permissionLevel\":4,\n" +
                "        \"sysType\":0\n" +
                "    },\n" +
                "    {\n" +
                "        \"permissionId\":\"b4dc76e7-84f4-41dd-b3a4-eab25c15e1bd\",\n" +
                "        \"permissionType\":\"2\",\n" +
                "        \"permissionName\":\"组织机构\",\n" +
                "        \"permissionCode\":\"organization\",\n" +
                "        \"parentId\":\"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "        \"orderBy\":0,\n" +
                "        \"permissionImage\":\"el-icon-grape\",\n" +
                "        \"hasEnable\":1,\n" +
                "        \"routingUrl\":\"#\",\n" +
                "        \"parentName\":\"系统管理\",\n" +
                "        \"permissionDesc\":\"组织机构\",\n" +
                "        \"permissionLevel\":4,\n" +
                "        \"sysType\":0\n" +
                "    },\n" +
                "    {\n" +
                "        \"permissionId\":\"fb7ce873-bdf0-4f61-83ce-0bb7e49da65b\",\n" +
                "        \"permissionType\":\"2\",\n" +
                "        \"permissionName\":\"地域管理\",\n" +
                "        \"permissionCode\":\"area\",\n" +
                "        \"parentId\":\"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "        \"orderBy\":3,\n" +
                "        \"permissionImage\":\"el-icon-help\",\n" +
                "        \"hasEnable\":1,\n" +
                "        \"routingUrl\":\"#\",\n" +
                "        \"parentName\":\"系统管理\",\n" +
                "        \"permissionDesc\":\"系统管理\",\n" +
                "        \"permissionLevel\":4,\n" +
                "        \"sysType\":0\n" +
                "    },\n" +
                "    {\n" +
                "        \"permissionId\":\"ff4066f1-224a-43b9-8060-016e9332e37d\",\n" +
                "        \"permissionType\":\"2\",\n" +
                "        \"permissionName\":\"角色管理\",\n" +
                "        \"permissionCode\":\"role\",\n" +
                "        \"parentId\":\"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "        \"orderBy\":1,\n" +
                "        \"permissionImage\":\"el-icon-user\",\n" +
                "        \"hasEnable\":1,\n" +
                "        \"routingUrl\":\"#\",\n" +
                "        \"parentName\":\"系统管理\",\n" +
                "        \"permissionDesc\":\"角色管理\",\n" +
                "        \"permissionLevel\":4,\n" +
                "        \"sysType\":0\n" +
                "    },\n" +
                "    {\n" +
                "        \"permissionId\":\"24d421e2-fc70-4bc4-bd07-95c8553e886d\",\n" +
                "        \"permissionType\":\"1\",\n" +
                "        \"permissionName\":\"PC后台\",\n" +
                "        \"permissionCode\":\"PCHT\",\n" +
                "        \"parentId\":\"-1\",\n" +
                "        \"orderBy\":7,\n" +
                "        \"permissionImage\":\"\",\n" +
                "        \"hasEnable\":1,\n" +
                "        \"routingUrl\":\"#\",\n" +
                "        \"parentName\":\"权限\",\n" +
                "        \"permissionDesc\":\"PC后台系统\",\n" +
                "        \"permissionLevel\":2,\n" +
                "        \"sysType\":0\n" +
                "    },\n" +
                "    {\n" +
                "        \"permissionId\":\"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "        \"permissionType\":\"2\",\n" +
                "        \"permissionName\":\"系统管理\",\n" +
                "        \"permissionCode\":\"systemManagement\",\n" +
                "        \"parentId\":\"24d421e2-fc70-4bc4-bd07-95c8553e886d\",\n" +
                "        \"orderBy\":1,\n" +
                "        \"permissionImage\":\"el-icon-setting\",\n" +
                "        \"hasEnable\":1,\n" +
                "        \"routingUrl\":\"#\",\n" +
                "        \"parentName\":\"PC后台\",\n" +
                "        \"permissionDesc\":\"系统管理\",\n" +
                "        \"permissionLevel\":3,\n" +
                "        \"sysType\":0\n" +
                "    }\n" +
                "]";
        JavaType javaType = objectMapper.getTypeFactory().constructCollectionType(List.class, Permission.class);
        permissions = objectMapper.readValue(strPermissionList,javaType);
        permissionServiceImpl=new PermissionServiceImpl();
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("获取全部权限定义节点")
    @Tag("@id:23517")
    @Tag("@author:yangweipeng")
    @Tag("@date:2021/6/23")
    void testGetAllPermission() {
        when(permissionMapper.getAllPermission()).thenReturn(Arrays.<Permission>asList(new Permission("permissionId", "permissionType", "permissionName", "permissionCode", "parentName", "parentId", "permissionDesc", Integer.valueOf(0), "permissionImage", LocalDateTime.of(2021, Month.JUNE, 23, 10, 48, 1), LocalDateTime.of(2021, Month.JUNE, 23, 10, 48, 1), Integer.valueOf(0), "permissionPath", Integer.valueOf(0), Integer.valueOf(0), "routingUrl", "createUserId", "createOrgId")));

        List<Permission> result = permissionServiceImpl.getAllPermission();
        Assertions.assertEquals(Arrays.<Permission>asList(new Permission("permissionId", "permissionType", "permissionName", "permissionCode", "parentName", "parentId", "permissionDesc", Integer.valueOf(0), "permissionImage", LocalDateTime.of(2021, Month.JUNE, 23, 10, 48, 1), LocalDateTime.of(2021, Month.JUNE, 23, 10, 48, 1), Integer.valueOf(0), "permissionPath", Integer.valueOf(0), Integer.valueOf(0), "routingUrl", "createUserId", "createOrgId")), result);
    }

    @Test
    @DisplayName("获取根据父节点id获取权限定义节点")
    @Tag("@id:23517")
    @Tag("@author:yangweipeng")
    @Tag("@date:2021/6/23")
    void testGetAllPermissionByParentId() {
        when(permissionMapper.getPermissionByParentId(anyString())).thenReturn(Arrays.<Permission>asList(new Permission("permissionId", "permissionType", "permissionName", "permissionCode", "parentName", "parentId", "permissionDesc", Integer.valueOf(0), "permissionImage", LocalDateTime.of(2021, Month.JUNE, 23, 10, 48, 1), LocalDateTime.of(2021, Month.JUNE, 23, 10, 48, 1), Integer.valueOf(0), "permissionPath", Integer.valueOf(0), Integer.valueOf(0), "routingUrl", "createUserId", "createOrgId")));

        List<Permission> result = permissionServiceImpl.getAllPermissionByParentId("parentId");
        Assertions.assertEquals(Arrays.<Permission>asList(new Permission("permissionId", "permissionType", "permissionName", "permissionCode", "parentName", "parentId", "permissionDesc", Integer.valueOf(0), "permissionImage", LocalDateTime.of(2021, Month.JUNE, 23, 10, 48, 1), LocalDateTime.of(2021, Month.JUNE, 23, 10, 48, 1), Integer.valueOf(0), "permissionPath", Integer.valueOf(0), Integer.valueOf(0), "routingUrl", "createUserId", "createOrgId")), result);
    }

    @Test
    @DisplayName("获取所有子节点以及下属节点")
    @Tag("@id:23517")
    @Tag("@author:yangweipeng")
    @Tag("@date:2021/6/23")
    void testGetAllPermissionById() {
        when(permissionMapper.getAllPermissionById(anyString())).thenReturn(Arrays.<Permission>asList(new Permission("permissionId", "permissionType", "permissionName", "permissionCode", "parentName", "parentId", "permissionDesc", Integer.valueOf(0), "permissionImage", LocalDateTime.of(2021, Month.JUNE, 23, 10, 48, 1), LocalDateTime.of(2021, Month.JUNE, 23, 10, 48, 1), Integer.valueOf(0), "permissionPath", Integer.valueOf(0), Integer.valueOf(0), "routingUrl", "createUserId", "createOrgId")));

        List<Permission> result = permissionServiceImpl.getAllPermissionById("permissionId");
        Assertions.assertEquals(Arrays.<Permission>asList(new Permission("permissionId", "permissionType", "permissionName", "permissionCode", "parentName", "parentId", "permissionDesc", Integer.valueOf(0), "permissionImage", LocalDateTime.of(2021, Month.JUNE, 23, 10, 48, 1), LocalDateTime.of(2021, Month.JUNE, 23, 10, 48, 1), Integer.valueOf(0), "permissionPath", Integer.valueOf(0), Integer.valueOf(0), "routingUrl", "createUserId", "createOrgId")), result);
    }

    @Test
    @DisplayName("测试插入权限定义")
    @Tag("@id:23517")
    @Tag("@author:yangweipeng")
    @Tag("@date:2021/6/23")
    void testInsertPermission() throws Exception {
        try (MockedStatic<CurrentUser> ms = Mockito.mockStatic(CurrentUser.class)){
            Permission permission=new Permission("permissionId", "permissionType", "permissionName", "permissionCode", "parentName", "parentId", "permissionDesc", Integer.valueOf(0), "permissionImage", LocalDateTime.of(2021, Month.JUNE, 23, 10, 48, 1), LocalDateTime.of(2021, Month.JUNE, 23, 10, 48, 1), Integer.valueOf(0), "permissionPath", Integer.valueOf(0), Integer.valueOf(0), "routingUrl", "createUserId", "createOrgId");
            when(permissionMapper.insertSelective(any())).thenReturn(1);
            when(permissionMapper.selectByPrimaryKey(anyString())).thenReturn(permission);
            when(permissionMapper.queryMaxOrderByParentId(anyString())).thenReturn(0);

            int result = permissionServiceImpl.insertPermission(permission);
            Assertions.assertEquals(1, result);
        }
    }

    @Test
    @DisplayName("测试更新权限定义")
    @Tag("@id:23517")
    @Tag("@author:yangweipeng")
    @Tag("@date:2021/6/23")
    void testUpdatePermission() throws Exception {
        Permission permission = new Permission("permissionId", "permissionType", "permissionName", "permissionCode", "parentName", "parentId", "permissionDesc", Integer.valueOf(0), "permissionImage", LocalDateTime.of(2021, Month.JUNE, 23, 10, 48, 1), LocalDateTime.of(2021, Month.JUNE, 23, 10, 48, 1), Integer.valueOf(0), "permissionPath", Integer.valueOf(0), Integer.valueOf(0), "routingUrl", "createUserId", "createOrgId");
        when(permissionMapper.updateByPrimaryKeySelective(any())).thenReturn(1);
        int result = permissionServiceImpl.updatePermission(permission);
        Assertions.assertEquals(1, result);
    }

    @Test
    @DisplayName("测试获取权限树")
    @Tag("@id:23517")
    @Tag("@author:yangweipeng")
    @Tag("@date:2021/6/23")
    void testGetPermissionTree() throws IOException {
        when(permissionMapper.getAllPermissionWithRoot()).thenReturn(permissions);
        String strpermission = "{\n" +
                "    \"permissionId\":\"-1\",\n" +
                "    \"permissionName\":\"权限\",\n" +
                "    \"parentId\":\"\",\n" +
                "    \"parentName\":\"#\",\n" +
                "    \"level\":1,\n" +
                "    \"routingUrl\":null,\n" +
                "    \"permissionType\":\"0\",\n" +
                "    \"permissionIcon\":null,\n" +
                "    \"hasEnable\":1,\n" +
                "    \"remarks\":\"权限根节点\",\n" +
                "    \"orderBy\":0,\n" +
                "    \"permissionCode\":\"QBQX\",\n" +
                "    \"children\":[\n" +
                "        {\n" +
                "            \"permissionId\":\"24d421e2-fc70-4bc4-bd07-95c8553e886d\",\n" +
                "            \"permissionName\":\"PC后台\",\n" +
                "            \"parentId\":\"-1\",\n" +
                "            \"parentName\":\"权限\",\n" +
                "            \"level\":2,\n" +
                "            \"routingUrl\":\"#\",\n" +
                "            \"permissionType\":\"1\",\n" +
                "            \"permissionIcon\":\"\",\n" +
                "            \"hasEnable\":1,\n" +
                "            \"remarks\":\"PC后台系统\",\n" +
                "            \"orderBy\":7,\n" +
                "            \"permissionCode\":\"PCHT\",\n" +
                "            \"children\":[\n" +
                "                {\n" +
                "                    \"permissionId\":\"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "                    \"permissionName\":\"系统管理\",\n" +
                "                    \"parentId\":\"24d421e2-fc70-4bc4-bd07-95c8553e886d\",\n" +
                "                    \"parentName\":\"PC后台\",\n" +
                "                    \"level\":3,\n" +
                "                    \"routingUrl\":\"#\",\n" +
                "                    \"permissionType\":\"2\",\n" +
                "                    \"permissionIcon\":\"el-icon-setting\",\n" +
                "                    \"hasEnable\":1,\n" +
                "                    \"remarks\":\"系统管理\",\n" +
                "                    \"orderBy\":1,\n" +
                "                    \"permissionCode\":\"systemManagement\",\n" +
                "                    \"children\":[\n" +
                "                        {\n" +
                "                            \"permissionId\":\"b4dc76e7-84f4-41dd-b3a4-eab25c15e1bd\",\n" +
                "                            \"permissionName\":\"组织机构\",\n" +
                "                            \"parentId\":\"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "                            \"parentName\":\"系统管理\",\n" +
                "                            \"level\":4,\n" +
                "                            \"routingUrl\":\"#\",\n" +
                "                            \"permissionType\":\"2\",\n" +
                "                            \"permissionIcon\":\"el-icon-grape\",\n" +
                "                            \"hasEnable\":1,\n" +
                "                            \"remarks\":\"组织机构\",\n" +
                "                            \"orderBy\":0,\n" +
                "                            \"permissionCode\":\"organization\",\n" +
                "                            \"children\":null\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"permissionId\":\"ff4066f1-224a-43b9-8060-016e9332e37d\",\n" +
                "                            \"permissionName\":\"角色管理\",\n" +
                "                            \"parentId\":\"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "                            \"parentName\":\"系统管理\",\n" +
                "                            \"level\":4,\n" +
                "                            \"routingUrl\":\"#\",\n" +
                "                            \"permissionType\":\"2\",\n" +
                "                            \"permissionIcon\":\"el-icon-user\",\n" +
                "                            \"hasEnable\":1,\n" +
                "                            \"remarks\":\"角色管理\",\n" +
                "                            \"orderBy\":1,\n" +
                "                            \"permissionCode\":\"role\",\n" +
                "                            \"children\":null\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"permissionId\":\"3ef01dea-99ab-44cd-b624-6b9b3f4e2b98\",\n" +
                "                            \"permissionName\":\"权限管理\",\n" +
                "                            \"parentId\":\"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "                            \"parentName\":\"系统管理\",\n" +
                "                            \"level\":4,\n" +
                "                            \"routingUrl\":\"#\",\n" +
                "                            \"permissionType\":\"2\",\n" +
                "                            \"permissionIcon\":\"el-icon-ice-drink\",\n" +
                "                            \"hasEnable\":1,\n" +
                "                            \"remarks\":\"权限管理\",\n" +
                "                            \"orderBy\":2,\n" +
                "                            \"permissionCode\":\"permission\",\n" +
                "                            \"children\":null\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"permissionId\":\"fb7ce873-bdf0-4f61-83ce-0bb7e49da65b\",\n" +
                "                            \"permissionName\":\"地域管理\",\n" +
                "                            \"parentId\":\"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "                            \"parentName\":\"系统管理\",\n" +
                "                            \"level\":4,\n" +
                "                            \"routingUrl\":\"#\",\n" +
                "                            \"permissionType\":\"2\",\n" +
                "                            \"permissionIcon\":\"el-icon-help\",\n" +
                "                            \"hasEnable\":1,\n" +
                "                            \"remarks\":\"系统管理\",\n" +
                "                            \"orderBy\":3,\n" +
                "                            \"permissionCode\":\"area\",\n" +
                "                            \"children\":null\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"permissionId\":\"2519d688-cccb-4b37-99ab-3f9ba0659b5a\",\n" +
                "                            \"permissionName\":\"字典管理\",\n" +
                "                            \"parentId\":\"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "                            \"parentName\":\"系统管理\",\n" +
                "                            \"level\":5,\n" +
                "                            \"routingUrl\":\"#\",\n" +
                "                            \"permissionType\":\"2\",\n" +
                "                            \"permissionIcon\":\"el-icon-goods\",\n" +
                "                            \"hasEnable\":1,\n" +
                "                            \"remarks\":\"系统管理\",\n" +
                "                            \"orderBy\":4,\n" +
                "                            \"permissionCode\":\"dictionary\",\n" +
                "                            \"children\":null\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"permissionId\":\"202ac195-8d8f-4be5-af56-9b3400de04dd\",\n" +
                "                            \"permissionName\":\"配置管理\",\n" +
                "                            \"parentId\":\"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "                            \"parentName\":\"系统管理\",\n" +
                "                            \"level\":4,\n" +
                "                            \"routingUrl\":\"#\",\n" +
                "                            \"permissionType\":\"2\",\n" +
                "                            \"permissionIcon\":\"el-icon-hot-water\",\n" +
                "                            \"hasEnable\":1,\n" +
                "                            \"remarks\":\"配置管理\",\n" +
                "                            \"orderBy\":5,\n" +
                "                            \"permissionCode\":\"configuration\",\n" +
                "                            \"children\":null\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"permissionId\":\"2825a748-031f-47ef-8156-5a4ae1ff0df8\",\n" +
                "                            \"permissionName\":\"日志管理\",\n" +
                "                            \"parentId\":\"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "                            \"parentName\":\"系统管理\",\n" +
                "                            \"level\":4,\n" +
                "                            \"routingUrl\":\"#\",\n" +
                "                            \"permissionType\":\"2\",\n" +
                "                            \"permissionIcon\":\"el-icon-document\",\n" +
                "                            \"hasEnable\":1,\n" +
                "                            \"remarks\":\"\",\n" +
                "                            \"orderBy\":6,\n" +
                "                            \"permissionCode\":\"logger\",\n" +
                "                            \"children\":null\n" +
                "                        }\n" +
                "                    ]\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ]\n" +
                "}";
        PermissionNode permissionNode = objectMapper.readValue(strpermission, PermissionNode.class);
        PermissionNode result = permissionServiceImpl.getPermissionTree(0);
        Assertions.assertEquals(permissionNode, result);
    }

    @Test
    @DisplayName("测试拖拽排序-目标节点后")
    @Tag("@id:23517")
    @Tag("@author:yangweipeng")
    @Tag("@date:2021/6/23")
    void testPermissionSort() throws Exception {
        String strSort = "{\n" +
                "    \"currentNodeId\": \"2519d688-cccb-4b37-99ab-3f9ba0659b5a\",\n" +
                "    \"targetNodeId\": \"fb7ce873-bdf0-4f61-83ce-0bb7e49da65b\",\n" +
                "    \"parentId\": \"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "    \"type\": \"next\"\n" +
                "}";
        String strPermission="{\n" +
                "        \"permissionId\":\"2519d688-cccb-4b37-99ab-3f9ba0659b5a\",\n" +
                "        \"permissionType\":\"2\",\n" +
                "        \"permissionName\":\"字典管理\",\n" +
                "        \"permissionCode\":\"dictionary\",\n" +
                "        \"parentId\":\"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "        \"orderBy\":4,\n" +
                "        \"permissionImage\":\"el-icon-goods\",\n" +
                "        \"hasEnable\":1,\n" +
                "        \"routingUrl\":\"#\",\n" +
                "        \"parentName\":\"系统管理\",\n" +
                "        \"permissionDesc\":\"系统管理\",\n" +
                "        \"permissionLevel\":5,\n" +
                "        \"sysType\":0\n" +
                "    }";
        Permission permission=objectMapper.readValue(strPermission,Permission.class);
        PermissionSortVo permissionSortVo = objectMapper.readValue(strSort, PermissionSortVo.class);
         when(permissionMapper.selectByPrimaryKey(anyString())).thenReturn(permission);
        when(permissionMapper.getPermissionByParentId(anyString())).thenReturn(permissions);
        when(permissionMapper.updateByPrimaryKeySelective(any())).thenReturn(0);
        when(permissionMapper.queryMaxOrderByParentId(anyString())).thenReturn(0);
        Boolean result = permissionServiceImpl.permissionSort(permissionSortVo);
        Assertions.assertEquals(Boolean.TRUE, result);
    }
    @Test
    @DisplayName("测试拖拽排序-目标节点前")
    @Tag("@id:23517")
    @Tag("@author:yangweipeng")
    @Tag("@date:2021/6/23")
    void testPermissionSortPre() throws Exception {
        String strSort = "{\n" +
                "    \"currentNodeId\": \"2519d688-cccb-4b37-99ab-3f9ba0659b5a\",\n" +
                "    \"targetNodeId\": \"fb7ce873-bdf0-4f61-83ce-0bb7e49da65b\",\n" +
                "    \"parentId\": \"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "    \"type\": \"prev\"\n" +
                "}";
        String strPermission="{\n" +
                "        \"permissionId\":\"2519d688-cccb-4b37-99ab-3f9ba0659b5a\",\n" +
                "        \"permissionType\":\"2\",\n" +
                "        \"permissionName\":\"字典管理\",\n" +
                "        \"permissionCode\":\"dictionary\",\n" +
                "        \"parentId\":\"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "        \"orderBy\":4,\n" +
                "        \"permissionImage\":\"el-icon-goods\",\n" +
                "        \"hasEnable\":1,\n" +
                "        \"routingUrl\":\"#\",\n" +
                "        \"parentName\":\"系统管理\",\n" +
                "        \"permissionDesc\":\"系统管理\",\n" +
                "        \"permissionLevel\":5,\n" +
                "        \"sysType\":0\n" +
                "    }";
        Permission permission=objectMapper.readValue(strPermission,Permission.class);
        PermissionSortVo permissionSortVo = objectMapper.readValue(strSort, PermissionSortVo.class);
        when(permissionMapper.selectByPrimaryKey(anyString())).thenReturn(permission);
        when(permissionMapper.getPermissionByParentId(anyString())).thenReturn(permissions);
        when(permissionMapper.updateByPrimaryKeySelective(any())).thenReturn(0);
        when(permissionMapper.queryMaxOrderByParentId(anyString())).thenReturn(0);
        Boolean result = permissionServiceImpl.permissionSort(permissionSortVo);
        Assertions.assertEquals(Boolean.TRUE, result);
    }
    @Test
    @DisplayName("获取角色资源权限列表-角色列表为空")
    @Tag("@id:23517")
    @Tag("@author:yangweipeng")
    @Tag("@date:2021/6/23")
    void testGetResourcePermissionsByRoleIdsRoleNull() throws  Exception {
        List<String> list =Arrays.<String>asList("1","2");
        when(permissionMapper.selectResourcePermissionListByRoleId(anyString(), anyString())).thenReturn(list);
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        doNothing().when(valueOperations).set(anyString(),anyString(),anyLong(),any(TimeUnit.class));
        List<String> result = permissionServiceImpl.getResourcePermissionsByRoleIds("", "sysType");
        Assertions.assertEquals(list, result);
    }
    @Test
    @DisplayName("测试拖拽排序-目标节点内部")
    @Tag("@id:23517")
    @Tag("@author:yangweipeng")
    @Tag("@date:2021/6/23")
    void testPermissionSortInner() throws Exception {
        String strSort = "{\n" +
                "    \"currentNodeId\": \"2519d688-cccb-4b37-99ab-3f9ba0659b5a\",\n" +
                "    \"targetNodeId\": \"fb7ce873-bdf0-4f61-83ce-0bb7e49da65b\",\n" +
                "    \"parentId\": \"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "    \"type\": \"inner\"\n" +
                "}";
        String strPermission="{\n" +
                "        \"permissionId\":\"2519d688-cccb-4b37-99ab-3f9ba0659b5a\",\n" +
                "        \"permissionType\":\"2\",\n" +
                "        \"permissionName\":\"字典管理\",\n" +
                "        \"permissionCode\":\"dictionary\",\n" +
                "        \"parentId\":\"f7bc0c88-04c8-4483-85dc-4920a7315fc1\",\n" +
                "        \"orderBy\":4,\n" +
                "        \"permissionImage\":\"el-icon-goods\",\n" +
                "        \"hasEnable\":1,\n" +
                "        \"routingUrl\":\"#\",\n" +
                "        \"parentName\":\"系统管理\",\n" +
                "        \"permissionDesc\":\"系统管理\",\n" +
                "        \"permissionLevel\":5,\n" +
                "        \"sysType\":0\n" +
                "    }";
        Permission permission=objectMapper.readValue(strPermission,Permission.class);
        PermissionSortVo permissionSortVo = objectMapper.readValue(strSort, PermissionSortVo.class);
        when(permissionMapper.selectByPrimaryKey(anyString())).thenReturn(permission);
        when(permissionMapper.getPermissionByParentId(anyString())).thenReturn(permissions);
        when(permissionMapper.updateByPrimaryKeySelective(any())).thenReturn(0);
        when(permissionMapper.queryMaxOrderByParentId(anyString())).thenReturn(0);
        Boolean result = permissionServiceImpl.permissionSort(permissionSortVo);
        Assertions.assertEquals(Boolean.TRUE, result);
    }

    @Test
    @DisplayName("获取角色资源权限列表-角色列表不为空")
    @Tag("@id:23517")
    @Tag("@author:yangweipeng")
    @Tag("@date:2021/6/23")
    void testGetResourcePermissionsByRoleIdsRoleNotEmpty() throws  Exception {
        List<String> list =Arrays.<String>asList("1","2");
        when(permissionMapper.selectResourcePermissionListByRoleId(anyString(), anyString())).thenReturn(list);
        when(redisTemplate.opsForHash()).thenReturn(hashOperations);
        doNothing().when(hashOperations).put(anyString(),anyString(),anyString());
        when(redisTemplate.expire(anyString(),anyLong(),any(TimeUnit.class))).thenReturn(true);
        List<String> result = permissionServiceImpl.getResourcePermissionsByRoleIds("1,2", "sysType");
        Assertions.assertEquals(list, result);
    }
    @Rule
    public ExpectedException thrown = ExpectedException.none();

    @Test
    @DisplayName("删除权限定义节点-有节点")
    @Tag("@id:23517")
    @Tag("@author:yangweipeng")
    @Tag("@date:2021/6/23")
    void testDeletePermissions() throws Exception {
        when(permissionMapper.getPermissionByParentId(anyString())).thenReturn(Arrays.<Permission>asList(new Permission("permissionId", "permissionType", "permissionName", "permissionCode", "parentName", "parentId", "permissionDesc", Integer.valueOf(0), "permissionImage", LocalDateTime.of(2021, Month.JUNE, 23, 10, 48, 1), LocalDateTime.of(2021, Month.JUNE, 23, 10, 48, 1), Integer.valueOf(0), "permissionPath", Integer.valueOf(0), Integer.valueOf(0), "routingUrl", "createUserId", "createOrgId")));
        when(redisTemplate.delete(anyCollection())).thenReturn(Long.valueOf("1"));
        when(redisTemplate.delete(anyString())).thenReturn(true);
        Set<String> set=new HashSet<>();
        set.add("1");
        set.add("2");
        when(stringRedisTemplate.keys(anyString())).thenReturn(set);
        when(stringRedisTemplate.delete(anyString())).thenReturn(true);
       try {
           permissionServiceImpl.deletePermissions(new String[]{"2131", "2424"});
       }catch (BusinessException ex){
           Assertions.assertEquals("990504",ex.getCode());
       }

    }

    @Test
    @DisplayName("删除权限定义节点-无节点有角色关联")
    @Tag("@id:23517")
    @Tag("@author:yangweipeng")
    @Tag("@date:2021/6/23")
    void testDeletePermissionsWithRole() throws Exception {
        when(permissionMapper.selectRoleListByPermissionId(anyString())).thenReturn(Arrays.<String>asList("String"));
        when(permissionMapper.getPermissionByParentId(anyString())).thenReturn(null);
        when(redisTemplate.delete(anyCollection())).thenReturn(Long.valueOf("1"));
        when(redisTemplate.delete(anyString())).thenReturn(true);
        PermissionScope permissionScope= new PermissionScope();
        permissionScope.setPermissionId("22332");
        List<PermissionScope> list=new ArrayList<>();
        list.add(permissionScope);
        when(permissionScopeService.getDataScopeList(any())).thenReturn(null);
        try {
            permissionServiceImpl.deletePermissions(new String[]{"2131", "2424"});
        }catch (BusinessException ex){
            Assertions.assertEquals("990502",ex.getCode());
        }

    }
    @Test
    @DisplayName("删除权限定义节点-正常删除")
    @Tag("@id:23517")
    @Tag("@author:yangweipeng")
    @Tag("@date:2021/6/23")
    void testDeletePermissionsNormal() throws Exception {
        when(permissionMapper.selectRoleListByPermissionId(anyString())).thenReturn(null);
        when(permissionMapper.getPermissionByParentId(anyString())).thenReturn(null);
        when(redisTemplate.delete(anyCollection())).thenReturn(Long.valueOf("1"));
        when(redisTemplate.delete(anyString())).thenReturn(true);
        when(permissionScopeService.getDataScopeList(any())).thenReturn(null);
        permissionServiceImpl.deletePermissions(new String[]{"2131", "2424"});

    }
    @Test
    @DisplayName("获取用户所有权限")
    @Tag("@id:23517")
    @Tag("@author:yangweipeng")
    @Tag("@date:2021/6/23")
    void testGetUserAllPermission() {
        RolePermissionVo rolePermissionVoReturn = new RolePermissionVo();
        rolePermissionVoReturn.setRoleId("角色id");
        rolePermissionVoReturn.setRoleName("角色名称");
        List<PermissionInfo> treeList = new ArrayList<>();
        treeList.add(new PermissionInfo("11", "permissionName11",
                1, "parentId", true, null));
        treeList.add(new PermissionInfo("22", "permissionName22",
                2, "11", true, null));
        rolePermissionVoReturn.setPermissionInfo(treeList);
        List<Map<String, String>> mapList = new ArrayList<>();
        List<Permission> permissionList = new ArrayList<>();
        permissionList.add(Permission.builder().permissionId("11").permissionPath("/parentId/11").parentId("parentId").permissionType("1").build());
        permissionList.add(Permission.builder().permissionId("22").permissionPath("/parentId/11/22").parentId("11").permissionType("2").build());
        when(permissionMapper.selectUserPermission(any(),anyString())).thenReturn(permissionList);
        when(userMapper.selectUserOrg4DataRule()).thenReturn(mapList);
        when(roleMapper.selectRoleDetail(anyString())).thenReturn(Role.builder().roleId("222").roleName("角色名称").build());
        when(permissionMapper.selectPermissionIdByRoleId(anyString())).thenReturn(Arrays.asList("11","22"));
        RolePermissionVo rolePermissionVo = permissionServiceImpl.getRoleInfo("0", "userId", "102");
        Assertions.assertEquals(rolePermissionVo.getRoleName(),rolePermissionVoReturn.getRoleName());
    }
   @Test
   @DisplayName("查询权限")
   @Tag("@id:23517")
   @Tag("@author:yangweipeng")
   @Tag("@date:2021/6/23")
    void testQueryPermission() throws Exception {
        Permission permission=new Permission("permissionId", "permissionType", "permissionName", "permissionCode", "parentName", "parentId", "permissionDesc", Integer.valueOf(0), "permissionImage", LocalDateTime.of(2021, Month.JUNE, 23, 10, 48, 1), LocalDateTime.of(2021, Month.JUNE, 23, 10, 48, 1), Integer.valueOf(0), "permissionPath", Integer.valueOf(0), Integer.valueOf(0), "routingUrl", "createUserId", "createOrgId");
        when(permissionMapper.selectByPrimaryKey(anyString())).thenReturn(permission);
        Permission result = permissionServiceImpl.queryPermission("permissionId");
        Assertions.assertEquals(permission,result);
    }

}
