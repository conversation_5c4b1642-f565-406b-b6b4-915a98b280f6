package com.snbc.bbpf.bus.system.service.impl;

import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.system.db.common.entity.Region;
import com.snbc.bbpf.system.db.common.mapper.RegionMapper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: RegionServiceImplTest
 * TODO 类注释/描述
 * @module: bbpf_project
 * @Author: wangsong
 * @date: 2021/7/2
 * copyright 2020 barm Inc. All rights reserver
 */
class RegionServiceImplTest {
    @Mock
    RegionMapper regionMapper;
    @InjectMocks
    RegionServiceImpl regionServiceImpl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("地域新增")
    @Tag("@id:23578")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testAddRegion_ParentCode_null() {
        CommonResp<String> commonResp= new CommonResp();
            List<Region> parents = new ArrayList<>();
            Mockito.when(regionMapper.selectByExample(Mockito.any(),Mockito.any())).thenReturn(parents);

            Region region = new Region();
            region.setParentCode("11");
            regionServiceImpl.addRegion(region,commonResp);
            Assertions.assertEquals(commonResp.getHead().getCode(), "991802");
        }
    @Test
    @DisplayName("地域新增")
    @Tag("@id:23578")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testAddRegion_list_isNotEmpty() {
        CommonResp<String> commonResp= new CommonResp();
        List<Region> parents = new ArrayList<>();
        Mockito.when(regionMapper.selectByExample(Mockito.any(),Mockito.any())).thenReturn(parents);

        List<Region> parents2 = new ArrayList<>();
        Region region = new Region();
        parents2.add(region);
        Mockito.when(regionMapper.selectByExample(Mockito.any(),Mockito.any())).thenReturn(parents2);
        Region reg = new Region();
        regionServiceImpl.addRegion(reg,commonResp);
        Assertions.assertEquals(commonResp.getHead().getCode(), "991801");
    }
    @Test
    @DisplayName("地域新增")
    @Tag("@id:23578")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testAddRegion() {
        CommonResp<String> commonResp= new CommonResp();
        List<Region> parents = new ArrayList<>();
        Mockito.when(regionMapper.selectByExample(Mockito.any(),Mockito.any())).thenReturn(parents);

        List<Region> parents2 = new ArrayList<>();
        Mockito.when(regionMapper.selectByExample(Mockito.any(),Mockito.any())).thenReturn(parents2);

        Mockito.when(regionMapper.insert(Mockito.any())).thenReturn(1);
        Region reg = new Region();
        regionServiceImpl.addRegion(reg,commonResp);
        Assertions.assertEquals(commonResp.getHead().getCode(), "000000");
    }
    @Test
    @DisplayName("地域新增")
    @Tag("@id:23578")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testAddRegion_fail() {
        CommonResp<String> commonResp= new CommonResp();
        List<Region> parents = new ArrayList<>();
        Mockito.when(regionMapper.selectByExample(Mockito.any(),Mockito.any())).thenReturn(parents);

        List<Region> parents2 = new ArrayList<>();
        Mockito.when(regionMapper.selectByExample(Mockito.any(),Mockito.any())).thenReturn(parents2);

        Mockito.when(regionMapper.insert(Mockito.any())).thenReturn(0);
        Region reg = new Region();
        regionServiceImpl.addRegion(reg,commonResp);
        Assertions.assertEquals(commonResp.getHead().getCode(), "994003");
    }


    @Test
    @DisplayName("地域修改")
    @Tag("@id:23581")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testUpdateRegion_Region_null() {
        Mockito.when(regionMapper.selectByPrimaryKey(Mockito.any())).thenReturn(null);
        CommonResp<String> commonResp= new CommonResp();
        Region reg = new Region();
        regionServiceImpl.updateRegion(reg,commonResp);
        Assertions.assertEquals(commonResp.getHead().getCode(), "991802");
    }
    @Test
    @DisplayName("地域修改")
    @Tag("@id:23581")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testUpdateRegion_parents_null() {
        Region dbRegion = new Region();
        Mockito.when(regionMapper.selectByPrimaryKey(Mockito.any())).thenReturn(dbRegion);
        List<Region> parents = new ArrayList<>();
        Mockito.when(regionMapper.selectByExample(Mockito.any(),Mockito.any())).thenReturn(parents);

        CommonResp<String> commonResp= new CommonResp();
        Region reg = new Region();
        reg.setParentCode("111");
        regionServiceImpl.updateRegion(reg,commonResp);
        Assertions.assertEquals(commonResp.getHead().getCode(), "991802");
    }
    @Test
    @DisplayName("地域修改")
    @Tag("@id:23581")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testUpdateRegion() {
        Region dbRegion = new Region();
        Mockito.when(regionMapper.selectByPrimaryKey(Mockito.any())).thenReturn(dbRegion);
        List<Region> parents = new ArrayList<>();
        Region region= new Region();
        parents.add(region);
        Mockito.when(regionMapper.selectByExample(Mockito.any(),Mockito.any())).thenReturn(parents);
        List<Region> child = new ArrayList<>();
        Region region2= new Region();
        child.add(region2);
        Mockito.when(regionMapper.selectByExample(Mockito.any(),Mockito.any())).thenReturn(child);
        Mockito.when(regionMapper.updateByPrimaryKey(Mockito.any())).thenReturn(1);
        Mockito.when(regionMapper.updateByPrimaryKey(Mockito.any())).thenReturn(1);
        CommonResp<String> commonResp= new CommonResp();
        Region reg = new Region();
        reg.setParentCode("111");
        regionServiceImpl.updateRegion(reg,commonResp);
        Assertions.assertEquals(commonResp.getHead().getCode(), "000000");
    }
    @Test
    @DisplayName("地域修改")
    @Tag("@id:23581")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testUpdateRegion_fail() {
        Region dbRegion = new Region();
        Mockito.when(regionMapper.selectByPrimaryKey(Mockito.any())).thenReturn(dbRegion);
        List<Region> parents = new ArrayList<>();
        Region region= new Region();
        parents.add(region);
        Mockito.when(regionMapper.selectByExample(Mockito.any(),Mockito.any())).thenReturn(parents);
        List<Region> child = new ArrayList<>();
        Region region2= new Region();
        child.add(region2);
        Mockito.when(regionMapper.selectByExample(Mockito.any(),Mockito.any())).thenReturn(child);
        Mockito.when(regionMapper.updateByPrimaryKey(Mockito.any())).thenReturn(1);
        Mockito.when(regionMapper.updateByPrimaryKey(Mockito.any())).thenReturn(0);
        CommonResp<String> commonResp= new CommonResp();
        Region reg = new Region();
        reg.setParentCode("111");
        regionServiceImpl.updateRegion(reg,commonResp);
        Assertions.assertEquals(commonResp.getHead().getCode(), "994003");
    }


    @Test
    @DisplayName("地域删除")
    @Tag("@id:23580")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testDeleteRegion_list_size() {
        List<Region> parents = new ArrayList<>();
        Region region= new Region();
        parents.add(region);
        Region region2= new Region();
        parents.add(region2);
        Mockito.when(regionMapper.selectByExample(Mockito.any(),Mockito.any())).thenReturn(parents);

        CommonResp<String> commonResp= new CommonResp();
        regionServiceImpl.deleteRegion("regionCode", commonResp);
        Assertions.assertEquals(commonResp.getHead().getCode(), "991802");
    }
    @Test
    @DisplayName("地域删除")
    @Tag("@id:23580")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testDeleteRegion() {
        List<Region> parents = new ArrayList<>();
        Region region= new Region();
        region.setId("111");
        parents.add(region);

        Mockito.when(regionMapper.selectByExample("regionCode",null)).thenReturn(parents);
        Mockito.when(regionMapper.deleteByPrimaryKey(Mockito.any())).thenReturn(1);
        List<Region> list = new ArrayList<>();
        Mockito.when(regionMapper.selectByExample(null,"111")).thenReturn(list);

        CommonResp<String> commonResp= new CommonResp();
        regionServiceImpl.deleteRegion("regionCode", commonResp);
        Assertions.assertEquals(commonResp.getHead().getCode(), "000000");
    }
    @Test
    @DisplayName("地域删除")
    @Tag("@id:23580")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testDeleteRegion_fail() {
        List<Region> parents = new ArrayList<>();
        Region region= new Region();
        region.setId("111");
        parents.add(region);

        Mockito.when(regionMapper.selectByExample("regionCode",null)).thenReturn(parents);
        Mockito.when(regionMapper.deleteByPrimaryKey(Mockito.any())).thenReturn(0);
        List<Region> list = new ArrayList<>();
        Mockito.when(regionMapper.selectByExample(null,"111")).thenReturn(list);

        CommonResp<String> commonResp= new CommonResp();
        regionServiceImpl.deleteRegion("regionCode", commonResp);
        Assertions.assertEquals(commonResp.getHead().getCode(), "994003");
    }


    @Test
    @DisplayName("地域查询")
    @Tag("@id:23577")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testGetRegionList_list_null() {
        List<Region> list = new ArrayList<>();
        Region region= new Region();
        region.setRegionCode("111");
        list.add(region);
        Mockito.when(regionMapper.selectByExample(Mockito.any(),Mockito.any())).thenReturn(list);
        List<Region> children = new ArrayList<>();
        Region region2 = new Region();
        children.add(region2);
        Mockito.when(regionMapper.selectByExample(null,"111")).thenReturn(children);
        regionMapper.selectByExample(null, region.getRegionCode());
        regionServiceImpl.getRegionList("");
    }

    @Test
    @DisplayName("地域导入")
    @Tag("@id:23583")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testImportRegion() {
        List<Region> parents = new ArrayList<>();
        Mockito.when(regionMapper.selectByExample(Mockito.any(),Mockito.any())).thenReturn(parents);

        List<Region> parents2 = new ArrayList<>();
        Mockito.when(regionMapper.selectByExample(Mockito.any(),Mockito.any())).thenReturn(parents2);
        Mockito.when(regionMapper.insert(Mockito.any())).thenReturn(1);
        List<Region> list = new ArrayList<>();
        Region region = new Region();
        list.add(region);
        CommonResp<String> commonResp= new CommonResp();
        regionServiceImpl.importRegion(list,commonResp);

        Assertions.assertEquals(commonResp.getHead().getCode(), "000000");
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
