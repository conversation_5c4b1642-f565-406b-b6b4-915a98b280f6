/*
 * 版权所有 2009-2024山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.system.db.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * superset用户数据
 *
 * @ClassName: SuperSetUserVo
 * @module: SI-bbpf-system
 * @Author: wangsong
 * @date: 2024/3/16
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SuperSetUserVo {
    private String userId;
    private String userName;
    private List<String> roleIds;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime expireTime;
}
