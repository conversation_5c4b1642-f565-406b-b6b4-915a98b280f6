/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName: PermissionNode
 * 权限树节点对象
 * @module: bbpf-bus-system
 * @Author: yangweipeng
 * @date: 2021/5/24
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PermissionNode {
    /**
     * 节点编号
     */
    private String permissionId;
    /**
     * 节点内容
     */
    private String permissionName;
    /**
     * 父节点编号
     */
    private String parentId;
    /**
     * 父节点名称
     */
    private String parentName;
    /**
     * 级别
     */
    private int level;
    /**
     * 节点url
     */
    private String routingUrl;
    /**
     * 类型
     */
    private String permissionType;
    /**
     * 图标
     */
    private String permissionIcon;

    /**
     * 是否启用
     */
    private Integer hasEnable;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 排序
     */
    private Integer orderBy;

    /**
     *
     *编码
     */
    private String permissionCode;

    /**
     * 孩子节点列表
     */
    private List<PermissionNode> children;

}


