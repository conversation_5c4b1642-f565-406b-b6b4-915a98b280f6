/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.filter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @ClassName: ParameterRequestWrapper
 * @Description: 去除request参数空格
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/8/12
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Slf4j
public class ParameterRequestWrapper extends HttpServletRequestWrapper {

    private Map<String, String[]> params = new HashMap<>();

    public ParameterRequestWrapper(HttpServletRequest request) {
        super(request);
        Map<String, String[]> requestMap = request.getParameterMap();
        this.params.putAll(requestMap);
        this.modifyParameterValues();
    }

    /***
     * @Description: 获取输入流
     * @Author: ws
     * @CreateDate: 2021/6/8 10:25
     * @UpdateDate: 2021/6/8 10:25
     * @return :        com.snbc.bbpf.component.config.CommonResp<java.util.List<com.snbc.bbpf.system.db.common.vo.UserVo>>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public ServletInputStream getInputStream() {
        String json = "";
        log.warn("system.ParameterRequestWrapper.content-type= {}，current url ={}",super.getHeader(HttpHeaders.CONTENT_TYPE),CurrentUser.getRequestUrl());
        try {
            if (!MediaType.APPLICATION_JSON_VALUE.equalsIgnoreCase(super.getHeader(HttpHeaders.CONTENT_TYPE))
            && !MediaType.APPLICATION_JSON_UTF8_VALUE.equalsIgnoreCase(super.getHeader(HttpHeaders.CONTENT_TYPE))) {
                return super.getInputStream();
            }
            json = IOUtils.toString(super.getInputStream(), StandardCharsets.UTF_8);
            log.info("current url= {}, get json{}", CurrentUser.getRequestUrl(),json);
            if (StringUtils.isEmpty(json)) {
                log.info("json is empty");
                return super.getInputStream();
            }
            Map<String, Object> map = jsonStringToMap(json);
            log.debug("Clear the argument after whitespace：{}", map);
            ByteArrayInputStream bis = new ByteArrayInputStream(JSON.toJSONString(map).getBytes(StandardCharsets.UTF_8));
            return new MyServletInputStream(bis);
        } catch (Exception e) {
            log.warn("Failed to get parameters in body",e);
            return new MyServletInputStream(new ByteArrayInputStream(json.getBytes(StandardCharsets.UTF_8)));
        }
    }

    private void modifyParameterValues() {
        Set<String> set = params.keySet();
        Iterator<String> it = set.iterator();
        while (it.hasNext()) {
            String key = it.next();
            String[] values = params.get(key);
            values[0] = values[0].trim();
            params.put(key, values);
        }
    }

    /***
     * @Description: 获取getParameter
     * @Author: ws
     * @param :         name
     * @CreateDate: 2021/6/8 10:25
     * @UpdateDate: 2021/6/8 10:25
     * @return :        com.snbc.bbpf.component.config.CommonResp<java.util.List<com.snbc.bbpf.system.db.common.vo.UserVo>>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public String getParameter(String name) {
        String[] values = params.get(name);
        if (values == null || values.length == 0) {
            return null;
        }
        return values[0];
    }

    /***
     * @Description: 获取getParameterValues
     * @Author: ws
     * @param :         name
     * @CreateDate: 2021/6/8 10:25
     * @UpdateDate: 2021/6/8 10:25
     * @return :        com.snbc.bbpf.component.config.CommonResp<java.util.List<com.snbc.bbpf.system.db.common.vo.UserVo>>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public String[] getParameterValues(String name) {
        return params.get(name);
    }

    /***
     * @Description: 获取当前组织机构及子组织机构下人员列表
     * @Author: WS
     * @CreateDate: 2021/6/8 10:25
     * @UpdateDate: 2021/6/8 10:25
     * @return :        com.snbc.bbpf.component.config.CommonResp<java.util.List<com.snbc.bbpf.system.db.common.vo.UserVo>>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    class MyServletInputStream extends ServletInputStream {
        private ByteArrayInputStream bis;

        public MyServletInputStream(ByteArrayInputStream bis) {
            this.bis = bis;
        }

        @Override
        public boolean isFinished() {
            return true;
        }

        @Override
        public boolean isReady() {
            return true;
        }

        @Override
        public void setReadListener(ReadListener listener) {
            log.debug("execute setReadListener");
        }

        @Override
        public int read() {
            return bis.read();
        }
    }

    /***
     * @Description: 获取jsonStringToMap
     * @Author: ws
     * @param :         jsonString
     * @CreateDate: 2021/6/8 10:25
     * @UpdateDate: 2021/6/8 10:25
     * @return :        com.snbc.bbpf.component.config.CommonResp<java.util.List<com.snbc.bbpf.system.db.common.vo.UserVo>>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    public static Map<String, Object> jsonStringToMap(String jsonString) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = JSONObject.parseObject(jsonString);
        for (Object k : jsonObject.keySet()) {
            Object o = jsonObject.get(k);
            if (o instanceof JSONArray) {
                Iterator<Object> it = ((JSONArray) o).iterator();
                List<Object> list = new ArrayList<>();
                while (it.hasNext()) {
                    Object obj = it.next();
                    if (obj instanceof JSONObject) {
                        list.add(jsonStringToMap(obj.toString()));
                    } else {
                        list.add(obj);
                    }
                }
                map.put(k.toString(), list);
            } else if (o instanceof JSONObject) {
                map.put(k.toString(), jsonStringToMap(o.toString()));
            } else {
                putStr(map,o,k);
            }
        }
        return map;
    }

    private static void putStr(Map<String, Object> map, Object o, Object k) {
        if (o instanceof String) {
            map.put(k.toString(), o.toString().trim());
        } else {
            map.put(k.toString(), o);
        }
    }
}
