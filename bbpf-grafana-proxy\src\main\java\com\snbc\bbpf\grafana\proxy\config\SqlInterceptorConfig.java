package com.snbc.bbpf.grafana.proxy.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * SQL拦截器配置
 */
@Configuration
@ConfigurationProperties(prefix = "bbpf.sql.interceptor")
public class SqlInterceptorConfig {

    /**
     * 是否启用SQL拦截
     */
    private boolean enabled = true;

    /**
     * 是否启用详细日志
     */
    private boolean verboseLogging = false;

    /**
     * 是否在权限获取失败时拒绝访问
     */
    private boolean denyOnPermissionFailure = true;

    /**
     * 默认的权限过滤条件（当用户无特定权限时使用）
     */
    private String defaultPermissionFilter = "1=0";

    /**
     * 需要拦截的API路径模式（正则表达式）
     */
    private String[] interceptPatterns = {
        "/api/ds/query",
        "/api/datasources/proxy/.*"
    };

    /**
     * 不需要拦截的API路径模式（正则表达式）
     */
    private String[] excludePatterns = {
        "/api/health",
        "/api/admin/.*",
        "/api/user/.*"
    };

    /**
     * SQL中需要检查和修改的字段名
     */
    private String[] sqlFields = {
        "rawSql", "sql", "query", "statement", "rawQuery"
    };

    /**
     * 权限过滤字段映射
     * key: 权限字段名, value: 数据库字段名
     */
    private java.util.Map<String, String> permissionFieldMapping = new java.util.HashMap<String, String>() {{
        put("orgId", "org_id");
        put("tenantId", "tenant_id");
        put("deptId", "dept_id");
    }};

    /**
     * 用户字段列表（用于用户权限过滤）
     * 默认为空，避免为不存在该字段的表添加过滤条件
     */
    private java.util.List<String> userFields = new java.util.ArrayList<>();

    /**
     * 拒绝访问时使用的SQL语句
     */
    private String denyAccessSql = "SELECT NULL WHERE 1=0";

    /**
     * 需要完全排除权限过滤的表名列表
     */
    private java.util.List<String> excludeTables = new java.util.ArrayList<>();

    /**
     * 基于表名的权限字段映射配置
     * key: 表名或表名前缀, value: 该表的权限字段配置
     */
    private java.util.Map<String, TablePermissionMapping> tablePermissionMapping = new java.util.HashMap<>();

    /**
     * 表权限映射配置类
     */
    public static class TablePermissionMapping {
        /**
         * 用户字段列表
         */
        private java.util.List<String> userFields = new java.util.ArrayList<>();
        
        /**
         * 组织字段名
         */
        private String orgField;
        
        /**
         * 部门字段名
         */
        private String deptField;
        
        /**
         * 租户字段名
         */
        private String tenantField;
        
        /**
         * 其他自定义权限字段映射
         */
        private java.util.Map<String, String> customFields = new java.util.HashMap<>();

        // Getters and Setters
        public java.util.List<String> getUserFields() {
            return userFields;
        }

        public void setUserFields(java.util.List<String> userFields) {
            this.userFields = userFields;
        }

        public String getOrgField() {
            return orgField;
        }

        public void setOrgField(String orgField) {
            this.orgField = orgField;
        }

        public String getDeptField() {
            return deptField;
        }

        public void setDeptField(String deptField) {
            this.deptField = deptField;
        }

        public String getTenantField() {
            return tenantField;
        }

        public void setTenantField(String tenantField) {
            this.tenantField = tenantField;
        }

        public java.util.Map<String, String> getCustomFields() {
            return customFields;
        }

        public void setCustomFields(java.util.Map<String, String> customFields) {
            this.customFields = customFields;
        }
    }

    // Getters and Setters
    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public boolean isVerboseLogging() {
        return verboseLogging;
    }

    public void setVerboseLogging(boolean verboseLogging) {
        this.verboseLogging = verboseLogging;
    }

    public boolean isDenyOnPermissionFailure() {
        return denyOnPermissionFailure;
    }

    public void setDenyOnPermissionFailure(boolean denyOnPermissionFailure) {
        this.denyOnPermissionFailure = denyOnPermissionFailure;
    }

    public String getDefaultPermissionFilter() {
        return defaultPermissionFilter;
    }

    public void setDefaultPermissionFilter(String defaultPermissionFilter) {
        this.defaultPermissionFilter = defaultPermissionFilter;
    }

    public String[] getInterceptPatterns() {
        return interceptPatterns;
    }

    public void setInterceptPatterns(String[] interceptPatterns) {
        this.interceptPatterns = interceptPatterns;
    }

    public String[] getExcludePatterns() {
        return excludePatterns;
    }

    public void setExcludePatterns(String[] excludePatterns) {
        this.excludePatterns = excludePatterns;
    }

    public String[] getSqlFields() {
        return sqlFields;
    }

    public void setSqlFields(String[] sqlFields) {
        this.sqlFields = sqlFields;
    }

    public java.util.Map<String, String> getPermissionFieldMapping() {
        return permissionFieldMapping;
    }

    public void setPermissionFieldMapping(java.util.Map<String, String> permissionFieldMapping) {
        this.permissionFieldMapping = permissionFieldMapping;
    }

    public java.util.Map<String, String> getPermissionFieldMappings() {
        return permissionFieldMapping;
    }

    public java.util.List<String> getUserFields() {
        return userFields;
    }

    public void setUserFields(java.util.List<String> userFields) {
        this.userFields = userFields;
    }

    public String getDenyAccessSql() {
        return denyAccessSql;
    }

    public void setDenyAccessSql(String denyAccessSql) {
        this.denyAccessSql = denyAccessSql;
    }

    public java.util.Map<String, TablePermissionMapping> getTablePermissionMapping() {
        return tablePermissionMapping;
    }

    public void setTablePermissionMapping(java.util.Map<String, TablePermissionMapping> tablePermissionMapping) {
        this.tablePermissionMapping = tablePermissionMapping;
    }

    public java.util.List<String> getExcludeTables() {
        return excludeTables;
    }

    public void setExcludeTables(java.util.List<String> excludeTables) {
        this.excludeTables = excludeTables;
    }

    /**
     * 根据表名获取权限字段映射配置
     * 支持精确匹配和前缀匹配
     * 
     * @param tableName 表名
     * @return 权限字段映射配置，如果没有找到则返回null
     */
    public TablePermissionMapping getTablePermissionMappingForTable(String tableName) {
        if (tableName == null || tableName.isEmpty()) {
            return null;
        }
        
        // 首先尝试精确匹配
        TablePermissionMapping exactMatch = tablePermissionMapping.get(tableName);
        if (exactMatch != null) {
            return exactMatch;
        }
        
        // 然后尝试前缀匹配（按前缀长度降序排列，优先匹配更具体的前缀）
        return tablePermissionMapping.entrySet().stream()
            .filter(entry -> tableName.startsWith(entry.getKey()))
            .max(java.util.Comparator.comparing(entry -> entry.getKey().length()))
            .map(java.util.Map.Entry::getValue)
            .orElse(null);
    }
}