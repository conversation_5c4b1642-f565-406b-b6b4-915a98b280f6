/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @ClassName: TokenPayload
 * @Description: JWT Token载荷数据封装类
 * @module: si-bbpf-system
 * @Author: Liangjb
 * @date: 2023/11/10
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Data
@Builder
public class TokenPayload {
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 用户名
     */
    private String userName;
    
    /**
     * 密码
     */
    private String password;
    
    /**
     * 角色ID列表
     */
    private List<String> roleIds;
    
    /**
     * 系统类型
     */
    private String sysType;
    
    /**
     * 租户ID
     */
    private String tenantId;
    
    /**
     * 会话ID
     */
    private String sessionId;
}