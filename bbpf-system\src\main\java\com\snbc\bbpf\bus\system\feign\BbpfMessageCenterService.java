package com.snbc.bbpf.bus.system.feign;

import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.component.gray.fegin.FeignConfiguration;
import com.snbc.bbpf.system.db.common.entity.Message;
import com.snbc.bbpf.system.db.common.vo.NoticeDetailVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @ClassName: BbpfMessageCenterService
 * @Description: 调用消息中心服务发送消息
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/6/9
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@FeignClient(value = "bbpf-message-center",configuration = FeignConfiguration.class)
public interface BbpfMessageCenterService {

    @PutMapping("/console/msg/sendTemplateMsg")
    CommonResp sendMsg(Message message);

    @GetMapping("/console/notice/noticeDetail")
    CommonResp<NoticeDetailVo> noticeDetail(@RequestParam("msgId") String msgId);
}

