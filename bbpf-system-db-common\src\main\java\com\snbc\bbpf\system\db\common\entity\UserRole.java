/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * @ClassName:      UserRole.java
 * @Description:    用户角色
 * @module:
 * @Author:         wangsong
 * @date:           2021/5/21 18:18
 * copyright 2020 SNBC. All rights reserver
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserRole {
    /**
     * id
     */
    private String id;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 角色id
     */
    private String roleId;
}
