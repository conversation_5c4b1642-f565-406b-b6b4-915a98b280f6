/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.model;

import com.snbc.bbpf.buslog.annotations.DiffKey;
import com.snbc.bbpf.buslog.annotations.convert.DictFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.Arrays;

/**
 * @ClassName: RoleRelationUser
 * @Description: 用户解绑关联角色
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/5/20
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RoleRelationUser {

    @NotEmpty(message = "用户id不能为空")
    private String[] userIds;
    @NotEmpty(message = "角色id不能为空")
    private String[] roleIds;
    @NotBlank(message = "关联状态不能为空")
    // 0：解除关联 1：关联用户
    @DiffKey(name = "状态",enName = "status")
    @DictFormat(dictJson = "[{'enName':'Add','zhName':'添加','value':'1'}," +
            "{'enName':'Remove','zhName':'移除','value':'0'}]")
    private String status;
    @DiffKey(name = "角色名称",enName = "Role Name")
    private String roleName;
    @DiffKey(name = "用户名称",enName = "User Name")
    private String userNameList;
    @Override
    public String toString() {
        return "RoleRelationUser{" +
                "userIds=" + Arrays.toString(userIds) +
                ", roleIds=" + Arrays.toString(roleIds) +
                ", status='" + status + '\'' +
                '}';
    }
}
