/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.entity.login;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @ProjectName: bus-module-sys
 * @Package: com.snbc.bbpf.organize.domain.module
 * @ClassName: LoginUser
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 登录用户类
 * @Author: Liangjb
 * @CreateDate: 2020/6/9 14:46
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LoginUser {
    /**
     * 手机号、工号、EMAIL
     */
    private String userName;
    /**
     * 用户密码
     */
    private String userPwd;
    /**
     * 系统类型
     */
    @NotBlank(message = "用户登录系统编号不能为空")
    private String sysType;
    /**
     * 验证码类型 calculation：算数验证码 blockPuzzle：滑动验证码 clickWord：文字点选验证码
     */
    private String captchaType;
    /**
     * 用于滑动、点选验证码校验
     */
    private String captchaVerification;
    /**
     *数字验证码
     */
    private String checkCode;
    /**
     * 数字验证码结果
     */
    private String checkResult;
    /**
     * 登录类型
     */
    private String loginType;

}
