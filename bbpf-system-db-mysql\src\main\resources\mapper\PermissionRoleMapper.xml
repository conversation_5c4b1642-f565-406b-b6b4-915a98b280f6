<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snbc.bbpf.system.db.common.mapper.PermissionRoleMapper">
    <resultMap id="BaseResultMap" type="com.snbc.bbpf.system.db.common.entity.PermissionRole">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="permission_id" jdbcType="VARCHAR" property="permissionId"/>
        <result column="role_id" jdbcType="VARCHAR" property="roleId"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, permission_id, role_id
  </sql>
    <insert id="insertPermissionRole" parameterType="java.util.List">
        insert into t_permission_role (id,permission_id, role_id)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix=" (" suffix=")" suffixOverrides=",">
                #{item.id,jdbcType=VARCHAR},
                #{item.permissionId,jdbcType=VARCHAR},
                #{item.roleId,jdbcType=VARCHAR}
            </trim>
        </foreach>
    </insert>
    <select id="selectCountByPermissionId" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(0) from t_permission_role where permission_id = #{permissionId,jdbcType=VARCHAR}
    </select>
    <delete id="deletePermissionByPermissionId" parameterType="java.lang.String">
      delete from t_permission_role where permission_id = #{permissionId,jdbcType=VARCHAR}
  </delete>
    <select id="selectCountByRoleName" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(0) from t_role where role_id = #{roleId,jdbcType=VARCHAR}
    </select>

    <delete id="deletePermissionByRoleId" parameterType="java.lang.String">
      delete from t_permission_role where role_id = #{roleId,jdbcType=VARCHAR}
  </delete>
</mapper>