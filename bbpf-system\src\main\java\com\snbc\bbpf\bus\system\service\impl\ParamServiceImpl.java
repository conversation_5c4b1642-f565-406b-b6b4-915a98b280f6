/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.snbc.bbpf.bus.system.constans.NumberConstant;
import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.mapstruct.ClassConvertorMapper;
import com.snbc.bbpf.bus.system.resp.CallBaseResponse;
import com.snbc.bbpf.bus.system.service.DictService;
import com.snbc.bbpf.bus.system.service.ParamService;
import com.snbc.bbpf.system.db.common.entity.Param;
import com.snbc.bbpf.system.db.common.mapper.ParamMapper;
import com.snbc.bbpf.system.db.common.vo.DictValueVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Pattern;

/**
 * @ClassName: Param
 * @Description: 参数配置具体实现类
 * @module: si-bbpf-system
 * @Author: wjc1
 * @date: 2021/5/17 14:23:00
 */
@Service
public class ParamServiceImpl implements ParamService {
    @Autowired
    private ParamMapper paramMapper;
    @Autowired
    private DictService dictService;
    @Autowired
    ParamServiceImpl(ParamMapper paramMapper,DictService dictService){
        this.paramMapper=paramMapper;
        this.dictService=dictService;
    }
    /**
     * @description: 修改参数配置
     * @param param 参数配置
     * @author: wjc1
     * @time: 2021/6/7 16:13
     */
    @Override
    @Transactional(rollbackFor = BusinessException.class)
    public void saveOrUpdateParam(Param param) throws BusinessException {
        String createUserId = param.getCreateUserId();
        ErrorMessage.USER_INVALID.assertNotNull(createUserId);

        // paramId 不为空，则为更新操作
        Param paramExsit = null;
        if (StringUtils.isNotBlank(param.getParamId())) {
            paramExsit = paramMapper.selectByPrimaryKey(param.getParamId());
            ErrorMessage.PARAM_ID_NOT_EXIST.assertNotNull(paramExsit);
            //更新时验证paramCode是否重复
            if(!paramExsit.getParamCode().equals(param.getParamCode())){
                ErrorMessage.PARAM_EXIST.assertNull(paramMapper.selectByParamCode(param.getParamCode()));
            }
        } else {
            // 校验配置编码是否存在: 应该不存在
            ErrorMessage.PARAM_EXIST.assertNull(paramMapper.selectByParamCode(param.getParamCode()));
        }
        if (StringUtils.isNotBlank(param.getParamId())) {
            paramExsit = ClassConvertorMapper.INSTANCE.covertParam(param);
            paramExsit.setUpdateTime(LocalDateTime.now());
            paramMapper.updateByPrimaryKeySelective(paramExsit);
        } else {
            param.setParamId(UUID.randomUUID().toString());
            param.setCreateTime(LocalDateTime.now());
            param.setUpdateTime(param.getCreateTime());
            paramMapper.insertSelective(param);
        }
    }
    /**
     * @description: 删除典表配置
     * @param paramIds 配置
     * @author: wjc1
     * @time: 2021/6/7 16:13
     */
    @Override
    @Transactional(rollbackFor = BusinessException.class)
    public void batchDeleteParam(String paramIds) throws BusinessException {
        String[] paramArray = paramIds.split(Pattern.quote(","));
        for (String paramId : paramArray) {
            ErrorMessage.PARAM_DELETE_FAIL.assertEquals(paramMapper.deleteByPrimaryKey(paramId), NumberConstant.NO_ONE);
        }
    }
    /**
     * @description: 根据ID获取参数配置值
     * @param paramId 参数ID
     * @author: wjc1
     * @time: 2021/6/7 16:13
     */
    @Override
    public Param getParamByPrimary(String paramId) throws BusinessException {
        return paramMapper.selectByPrimaryKey(paramId);
    }
    /**
     * @description: 根据Code获取参数配置值
     * @paramParam 参数Code
     * @author: wjc1
     * @time: 2021/6/7 16:13
     */
    @Override
    public Param getParamByCode(String paramCode) throws BusinessException {
        return paramMapper.selectByParamCode(paramCode);
    }
    /**
     * @description: 根据MAP获取参数配置值
     * @param map 参数合集
     * @author: wjc1
     * @time: 2021/6/7 16:13
     */
    @Override
    public List<Param> getParamByMap(Map<String, Object> map) throws BusinessException {
        CallBaseResponse head = CallBaseResponse.builder().build();
        head.setCode(ErrorMessage.SUCCESS.getCode());
        head.setMessage(ErrorMessage.SUCCESS.getMessage());
        return paramMapper.queryParamByMap(map);
    }
    /**
     * @description: 获取参数配置值CODE
     * @author: wjc1
     * @time: 2021/6/7 16:13
     */
    @Override
    public List<String> queryParamCode() throws BusinessException {
        String typeCode= "param_type";
        List<String> pList = new ArrayList<>();
        List<DictValueVo> dictValues = dictService.getDictValueList(typeCode);
        for(DictValueVo d:dictValues){
            pList.add(d.getValueName());
        }
        return pList;
    }
    /**
     * @description: 查询参数配置值典表
     * @param paramName 配置名称
     * @param paramTypeName 配置CODE
     * @param pageNum 配置页码
     * @param pageSize 配置数量
     * @Return PageInfo<Param> 参数配置
     * @author: wjc1
     * @time: 2021/6/7 16:13
     */
    @Override
    public PageInfo<Param> queryParamListPage(String paramName, String paramTypeName, Integer pageNum, Integer pageSize) throws BusinessException {
        PageMethod.startPage(pageNum, pageSize);
        Map<String, Object> map = new HashMap<>();
        map.put("paramName", paramName);
        map.put("paramTypeName", paramTypeName);
        return new PageInfo<>(paramMapper.queryParamByMap(map));
    }

}
