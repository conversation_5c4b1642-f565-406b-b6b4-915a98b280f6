package com.snbc.bbpf.bus.system.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;

/**
 * @ClassName: ActuatorSecurityConfig
 * @Description: Security配置
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/8/20
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Configuration
@EnableWebSecurity
public class ActuatorSecurityConfig extends WebSecurityConfigurerAdapter {
    @Value("${management.endpoints.web.base-path}")
    private String actuatorBasePath;
    @Value("${bbpf.security.captcha.path}")
    private String securityCaptchaPath;

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.csrf().disable();
        http.authorizeRequests()
                //拦截的url
                .antMatchers(securityCaptchaPath).authenticated()
                //其他的请求全部放行
                .anyRequest().permitAll()
                .and().httpBasic();
    }
}
