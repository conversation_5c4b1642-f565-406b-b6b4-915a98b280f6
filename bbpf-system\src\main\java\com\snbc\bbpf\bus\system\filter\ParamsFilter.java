/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.filter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * @ClassName: ParamsFilter
 * @Description: 参数过滤器
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/8/12
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Component
@Slf4j
@WebFilter(urlPatterns = "/**", filterName = "ParamsFilter")
public class ParamsFilter implements Filter {
    private static final String PREFIXPATH = "console";
    /***
     * @Description:    过滤的具体方法
     * @Author:         wangsong
     * @param :         request
     * @param :         response
     * @CreateDate:     2021/5/19 16:18
     * @UpdateDate:     2021/5/19 16:18
     * @return :        List<Map>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest hsRequest = (HttpServletRequest) request;
        String path = hsRequest.getRequestURI().substring(hsRequest.getContextPath().length()).replaceAll("[/]+$", "");
        if (path.contains(PREFIXPATH) && !path.endsWith("/addfile") && !path.endsWith("/editfile")) {
            ParameterRequestWrapper parmsRequest = new ParameterRequestWrapper(hsRequest);
            chain.doFilter(parmsRequest, response);
        } else {
            chain.doFilter(request, response);
        }
    }
    /***
     * @Description:    初始化配置
     * @Author:         wangsong
     * @param :         filterConfig
     * @CreateDate:     2021/5/19 16:18
     * @UpdateDate:     2021/5/19 16:18
     * @return :        List<Map>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.debug("execute init");
    }

    @Override
    public void destroy() {
        log.debug("execute destroy");
    }
}
