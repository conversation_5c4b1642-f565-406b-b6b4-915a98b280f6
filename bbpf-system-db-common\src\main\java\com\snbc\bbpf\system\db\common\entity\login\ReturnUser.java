/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.entity.login;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @ProjectName: bus-module-sys
 * @Package: com.snbc.bbpf.organize.domain.module
 * @ClassName: ReturnUser
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 登录用户返回类
 * @Author: Liangjb
 * @CreateDate: 2020/6/9 14:46
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ReturnUser {
	/**
	 * 用户名
	 */
	private String userName;
	/**
	 * Token
	 */
	private String token;
	/**
	 *
	 * 禁用启用 0禁用,1.启用
	 */
	private Integer hasLock;
	/**
	 * 工号
	 */
	private String jobNumber;
	/**
	 * 登录时间
	 */
	private LocalDateTime loginTime;
	/**
	 * 用户描述
	 */
	private String userDesc;
	/**
	 * 用户头像
	 */
	private String avatar;
	/**
	 * 用户状态 0请假中 1生病中 2出差中 3会议中 4外出中 5忙碌中 6调休中
	 */
	private String userStatus;
	/**
	 *在职离职 1：在职0：离职
	 */
	private String hasResign;
	/**
	 * 是否初始化密码
	 */
	private String isOriginPwd;
	/**
	 * 密码是否过期
	 */
	private Boolean isPwdExpired;
}
