/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.controller;

import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.bus.system.service.UserExportService;
import com.snbc.bbpf.bus.system.service.UserImportNewService;
import com.snbc.bbpf.bus.system.service.UserImportService;
import com.snbc.bbpf.bus.system.service.UserOptService;
import com.snbc.bbpf.bus.system.service.UserOrgService;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.bus.system.utils.ResultUtil;
import com.snbc.bbpf.buslog.annotations.Buslog;
import com.snbc.bbpf.buslog.annotations.OperationType;
import com.snbc.bbpf.system.db.common.dto.AdjustDepartmentDto;
import com.snbc.bbpf.system.db.common.dto.RemoveUserDto;
import com.snbc.bbpf.system.db.common.dto.UserDto;
import com.snbc.bbpf.system.db.common.vo.OrgDirectorVo;
import com.snbc.bbpf.system.db.common.vo.OrgUserPageVo;
import com.snbc.bbpf.system.db.common.vo.TenantClerkVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
/**
 * @ClassName: UserController
 * @Description: 用户控制器
 * @module: si-bbpf-system
 * @Author: wangsong
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@RestController
@RequestMapping("/console/user")
@Slf4j
public class UserOptController {
    @Autowired
    private UserOptService userService;
    @Autowired
    private UserImportService userImportService;
    @Autowired
    private UserExportService userExportService;
    @Autowired
    private UserOrgService userOrgService;
    @Autowired
    private UserImportNewService userImportNewService;
    /**
     * @Description: 新增用户
     * @Author:  wjc1
     * @param :         userDto 用户请求参数
     * @CreateDate: 2021/5/19 11:08
     * @UpdateDate: 2021/5/19 11:08
     * @return :        CommonResp
     */
    @PutMapping(value = "addUser",consumes = {"application/json"})
    @Buslog(opration = "addUser", zh = "新增用户【姓名：${userName}，手机号：${phone}】",
            en="New user [name:${userName}, mobile phone number:${phone}]",type = UserDto.class,target = "userManage")
    public CommonResp<Object> addUser(@Validated @RequestBody UserDto userDto) {
        userDto.setCreateUserId(CurrentUser.getUserId());
        ErrorMessage.USER_ID_EXIST.assertNull(userDto.getUserId());
        userImportService.saveUser(userDto);
        return CommonResp.builder().head(ResultUtil.success()).build();
    }
    /**
     * @Description: 更新用户, 0716修改为 编辑时创建人不变
     * @Author:  wjc1
     * @param :  userDto 修改的用户信息
     * @CreateDate: 2021/5/19 11:08
     * @UpdateDate: 2021/5/19 11:08
     * @return :        CommonResp
     */
    @PutMapping(value = "updateUser",consumes = {"application/json"})
    @Buslog(oprationType = OperationType.MODIFY,opration = "modifyUser", target = "userManage",type =UserDto.class )
    public CommonResp<Object> updateUser(@Validated @RequestBody UserDto userDto) {
        ErrorMessage.USER_ID_IS_NULL.assertNotNull(userDto.getUserId());
        userDto.setCreateUserId(CurrentUser.getUserId());
        userImportService.updateUser(userDto);
        return CommonResp.builder().head(ResultUtil.success()).build();
    }

    /**
     * @Description: 查询用户列表:按更新时间倒叙:如果orgId=0根目录查所有，不为根目录只查询该组织机构下的用户，不查询子组织机构下用户
     * @Author:  wjc1
     * @param : orgId 组织机构id
     * @param : pageNum 页数
     * @param : pageSize 每页条数
     * @param : queryParam 查询条件 用户名或手机号
     * @CreateDate: 2021/5/19 11:08
     * @UpdateDate: 2021/5/19 11:08
     * @return :        CommonResp
     */
    @GetMapping("userListPage")
    public CommonResp<PageInfo<OrgUserPageVo>> userListPage(@RequestParam String orgId
            ,@RequestParam Integer pageNum
            ,@RequestParam Integer pageSize
            ,@RequestParam String queryParam){
        PageInfo<OrgUserPageVo> pageInfo=userService.queryUserListPage(orgId,pageNum,pageSize,queryParam);
        return CommonResp.<PageInfo<OrgUserPageVo>>builder().head(ResultUtil.success()).body(pageInfo).build();
    }
    /***
     * @Description:    获取当前组织机构下人员列表
     * @Author:         jiafei
     * @param :         orgId
     * @CreateDate:     2021/6/8 10:25
     * @UpdateDate:     2021/6/8 10:25
     * @return :        com.snbc.bbpf.component.config.CommonResp<java.util.List<com.snbc.bbpf.system.db.common.vo.UserVo>>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @GetMapping("userList")
    public CommonResp<List<OrgDirectorVo>> userList(@Validated @RequestParam String orgId) {
        List<OrgDirectorVo> orgDirectorVoList = userExportService.queryUserList(orgId);
        return CommonResp.<List<OrgDirectorVo>>builder().head(ResultUtil.success()).body(orgDirectorVoList).build();
    }
    /***
     * @Description:    获取当前组织机构及子组织机构下人员列表
     * @Author:         jiafei
     * @param :         orgId
     * @CreateDate:     2021/6/8 10:25
     * @UpdateDate:     2021/6/8 10:25
     * @return :        com.snbc.bbpf.component.config.CommonResp<java.util.List<com.snbc.bbpf.system.db.common.vo.UserVo>>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @GetMapping("userAllList")
    public CommonResp<List<TenantClerkVo>> userAllList(@RequestParam String orgId){
        List<TenantClerkVo> tenantClerkVoList = userExportService.userAllList(orgId);
        return CommonResp.<List<TenantClerkVo>>builder().head(ResultUtil.success()).body(tenantClerkVoList).build();
    }
    /**
     * @Description: 调整部门
     * 先剔除用户的oldOrgId(根节点0 的不移除)，然后再绑定
     * @Author:  wjc1
     * @param :         adjustDepartmentDto
     * @CreateDate: 2021/5/20 19:07
     * @UpdateDate: 2021/5/20 19:07
     * @return :        CommonResp
     */
    @PostMapping(value="/adjustDepartment",consumes = {"application/json"})
    @Buslog(opration = "adjustOrg", zh = "将用户［${userNames}］调整到［${orgNames}］", en="Adjust user [${userNames}] to department [orgNames}]",target = "userManage")
    public CommonResp<Object> adjustDepartment(@Validated @RequestBody AdjustDepartmentDto adjustDepartmentDto){
        userService.adjustDepartment(adjustDepartmentDto);
        return CommonResp.builder().head(ResultUtil.success()).build();
    }


    /**
     * @Description: 下载导入用户模板
     * @Author:  wjc1
     * @param : request HttpServletRequest
     * @param : response HttpServletResponse
     * @CreateDate: 2021/5/21 14:38
     * @UpdateDate: 2021/5/21 14:38
     */
    @GetMapping("downloadUserTemplate")
    public void downloadUserTemplate(HttpServletRequest request, HttpServletResponse response) throws IOException {
        userExportService.downloadUserTemplate(request,response);
    }


    @PutMapping(value = "/removeUser2Org",consumes = {"application/json"})
    @Buslog(opration = "adjustOrg", zh = "将用户［${userNames}］从部门［${orgNames}］中移除",
            en="Remove user [#${userNames}] from department [${orgNames}]",target = "userManage")
    public CommonResp<Object> removeUser2Org(@RequestBody RemoveUserDto removeUserDto){
        userOrgService.removeUser2Org(removeUserDto.getUserIds(),removeUserDto.getOrgId());
        return CommonResp.builder().head(ResultUtil.success()).build();
    }

    @PutMapping("importUserNew")
    @Buslog(target = "userManage", opration = "importExportUser", zh = "导入了用户", en = "Import the users")
    public CommonResp<Object> importUserNew(MultipartFile file,HttpServletResponse response) throws Exception {
        userImportNewService.importUserNew(file, response);
        return CommonResp.builder().head(ResultUtil.success()).build();
    }
    @GetMapping("exportUserNew")
    @Buslog(opration = "importExportUser", zh = "导出全部用户", en="Export the users",target = "userManage")
    public CommonResp<Object> exportUserNew(@RequestParam String orgId) {
        userImportNewService.exportUserNew(orgId);
        return CommonResp.builder().head(ResultUtil.success()).build();
    }

    @GetMapping("exportPartUserNew")
    @Buslog(opration = "importExportUser", zh = "导出选中用户", en="Some users are exported",target = "userManage")
    public CommonResp<Object> exportPartUserNew(@RequestParam String orgId,@RequestParam String userIds) {
        userImportNewService.exportPartUserNew(orgId,userIds);
        return CommonResp.builder().head(ResultUtil.success()).build();
    }
}
