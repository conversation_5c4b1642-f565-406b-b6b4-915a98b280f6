package com.snbc.bbpf.bus.system.service.impl;

import com.snbc.bbpf.bus.system.constans.NumberConstant;
import com.snbc.bbpf.bus.system.enums.SmsKeyEnum;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.handler.UserLoginFailHandler;
import com.snbc.bbpf.bus.system.service.SmsService;
import com.snbc.bbpf.bus.system.utils.BossDES3Util;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.bus.system.utils.SendMsgUtil;
import com.snbc.bbpf.system.db.common.entity.User;
import com.snbc.bbpf.system.db.common.mapper.UserLoginMapper;
import com.snbc.bbpf.system.db.common.mapper.UserMapper;
import com.snbc.bbpf.system.db.common.vo.CheckSmsCodeVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @ClassName: SmsServiceImpl
 * @Description: 下发短信验证码
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2022/1/19
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Service
@Slf4j
public class SmsServiceImpl implements SmsService {
    @Autowired
    private UserLoginMapper userLoginMapper;
    @Autowired
    private UserMapper userMapper;
    /***
      * @Description:    下发短信验证码
      * @Author:         wangsong
      * @param :         userphone
      * @param :         msgType
      * @CreateDate:     2022/1/19 15:59
      * @UpdateDate:     2022/1/19 15:59
      * @return :        void
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public void getPhoneSmsCode(String userPhone, Integer msgType) {
        //手机号加密
        String encryptPhone = BossDES3Util.encrypt(userPhone);
        User user = userLoginMapper.selectUserByPhone(encryptPhone);
        //验证手机号是否注册， 为空表示不存在，抛异常
        ErrorMessage.PHONE_UNREGISTERED.assertNotNull(user);
        //判断是否是验证码登录  用户被锁定不允许发送登录验证码
        if (msgType.equals(NumberConstant.NO_ZERO)) {
            UserLoginFailHandler.queryUserLoginStatus(user);
            //验证用户冻结, 不等于1 为冻结，抛异常
            ErrorMessage.USE_FREEZE.assertEquals(NumberConstant.NO_ONE == user.getHasLock(), true);
        }
        //下发验证码
        SendMsgUtil.buildSMSMessages(msgType, userPhone, user.getUserId());
    }

    @Override
    public void checkSmsCode(CheckSmsCodeVo checkSmsCode) {
        String userId = checkSmsCode.getUserId();
        if (StringUtils.isNotBlank(checkSmsCode.getPhone())) {
            //手机号加密
            String encryptPhone = BossDES3Util.encrypt(checkSmsCode.getPhone());
            User user = userLoginMapper.selectUserByPhone(encryptPhone);
            if(null!=user){
                userId = user.getUserId();
            }
        }
        SendMsgUtil.checkSmsCode(String.format(Objects.requireNonNull(SmsKeyEnum.getStatusName(checkSmsCode.getMsgType())), userId),
                checkSmsCode.getSmsCode(), checkSmsCode.getIsDelSmsCode());
    }

    @Override
    public void currentUserSendSmsCode(Integer msgType) {
        // 验证 userId 有效性
        User userExist = userMapper.selectByPrimaryKey(CurrentUser.getUserId());
        ErrorMessage.USER_NOT_EXIST.assertNotNull(userExist);
        //手机号解密
        String userPhone = BossDES3Util.decrypt(userExist.getPhone());
        //下发验证码
        SendMsgUtil.buildSMSMessages(msgType, userPhone, CurrentUser.getUserId());
    }
}
