<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snbc.bbpf.system.db.common.mapper.DictValueMapper" >
  <resultMap id="BaseResultMap" type="com.snbc.bbpf.system.db.common.entity.DictValue" >
    <constructor >
      <idArg column="value_id" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="type_code" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="value_name" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="value_code" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="parent_id" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="value_desc" jdbcType="VARCHAR" javaType="java.lang.String" />
    </constructor>
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    value_id, type_code, value_name, value_code, parent_id, value_desc
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from t_dict_value where 1=1
    <if test="typeCode != null and typeCode !=''" >
      AND type_code = #{typeCode,jdbcType=VARCHAR}
    </if>
    <if test="valueCode != null and valueCode !=''" >
      AND value_code = #{valueCode,jdbcType=VARCHAR}
    </if>
    <if test="valueName != null and valueName !=''" >
      AND value_name like concat('%',#{valueName,jdbcType=VARCHAR},'%')
    </if>
      order by value_code
  </select>

  <select id="selectByValueIds" resultMap="BaseResultMap" parameterType="java.util.List">
    select
    <include refid="Base_Column_List" />
    from t_dict_value where 1=1
      <if test="valueIds != null and valueIds.size() > 0">
        AND value_id IN
        <foreach item="item" index="index" collection="valueIds" open="("  close=")" separator=",">
          #{item}
        </foreach>
      </if>
  </select>

  <select id="selectByTypeLike" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from t_dict_value where 1=1
    <if test="typeCode != null and typeCode !=''" >
      AND type_code =#{typeCode,jdbcType=VARCHAR}
    </if>
    <if test="parentId != null and parentId !=''" >
      AND parent_id =#{parentId,jdbcType=VARCHAR}
    </if>
    order by value_code
  </select>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from t_dict_value
    where value_id = #{valueId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from t_dict_value
    where value_id = #{valueId,jdbcType=VARCHAR};
    update t_dict_value set parent_id='' where parent_id = #{valueId,jdbcType=VARCHAR};
  </delete>

  <insert id="insert" parameterType="com.snbc.bbpf.system.db.common.entity.DictValue" >
    insert into t_dict_value (value_id, type_code, value_name,
      value_code, parent_id, value_desc
      )
    values (#{valueId,jdbcType=VARCHAR}, #{typeCode,jdbcType=VARCHAR}, #{valueName,jdbcType=VARCHAR},
      #{valueCode,jdbcType=VARCHAR}, #{parentId,jdbcType=VARCHAR}, #{valueDesc,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.snbc.bbpf.system.db.common.entity.DictValue" >
    insert into t_dict_value
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="valueId != null" >
        value_id,
      </if>
      <if test="typeCode != null" >
        type_code,
      </if>
      <if test="valueName != null" >
        value_name,
      </if>
      <if test="valueCode != null" >
        value_code,
      </if>
      <if test="parentId != null" >
        parent_id,
      </if>
      <if test="valueDesc != null" >
        value_desc,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="valueId != null" >
        #{valueId,jdbcType=VARCHAR},
      </if>
      <if test="typeCode != null" >
        #{typeCode,jdbcType=VARCHAR},
      </if>
      <if test="valueName != null" >
        #{valueName,jdbcType=VARCHAR},
      </if>
      <if test="valueCode != null" >
        #{valueCode,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null" >
        #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="valueDesc != null" >
        #{valueDesc,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByExampleSelective" parameterType="map" >
    update t_dict_value
    <set >
      <if test="record.valueId != null" >
        value_id = #{record.valueId,jdbcType=VARCHAR},
      </if>
      <if test="record.typeCode != null" >
        type_code = #{record.typeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.valueName != null" >
        value_name = #{record.valueName,jdbcType=VARCHAR},
      </if>
      <if test="record.valueCode != null" >
        value_code = #{record.valueCode,jdbcType=VARCHAR},
      </if>
      <if test="record.parentId != null" >
        parent_id = #{record.parentId,jdbcType=VARCHAR},
      </if>
      <if test="record.valueDesc != null" >
        value_desc = #{record.valueDesc,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update t_dict_value
    set value_id = #{record.valueId,jdbcType=VARCHAR},
      type_code = #{record.typeCode,jdbcType=VARCHAR},
      value_name = #{record.valueName,jdbcType=VARCHAR},
      value_code = #{record.valueCode,jdbcType=VARCHAR},
      parent_id = #{record.parentId,jdbcType=VARCHAR},
      value_desc = #{record.valueDesc,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.snbc.bbpf.system.db.common.entity.DictValue" >
    update t_dict_value
    <set >
      <if test="typeCode != null" >
        type_code = #{typeCode,jdbcType=VARCHAR},
      </if>
      <if test="valueName != null" >
        value_name = #{valueName,jdbcType=VARCHAR},
      </if>
      <if test="valueCode != null" >
        value_code = #{valueCode,jdbcType=VARCHAR},
      </if>
        parent_id = #{parentId,jdbcType=VARCHAR},
        value_desc = #{valueDesc,jdbcType=VARCHAR},
    </set>
    where value_id = #{valueId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.snbc.bbpf.system.db.common.entity.DictValue" >
    update t_dict_value
    set type_code = #{typeCode,jdbcType=VARCHAR},
      value_name = #{valueName,jdbcType=VARCHAR},
      value_code = #{valueCode,jdbcType=VARCHAR},
      parent_id = #{parentId,jdbcType=VARCHAR},
      value_desc = #{valueDesc,jdbcType=VARCHAR}
    where value_id = #{valueId,jdbcType=VARCHAR}
  </update>

  <select id="selectOnlyValue"  resultType="java.lang.Integer">
    select count(value_id) cid from t_dict_value
    where type_code=#{typeCode,jdbcType=VARCHAR} and
          (value_code=#{valueCode,jdbcType=VARCHAR} or value_name=#{valueName,jdbcType=VARCHAR})
    <if test="valueId != null" >
      and value_id!=#{valueId,jdbcType=VARCHAR}
    </if>
  </select>
  <select id="getMultipleDictValues" resultType="com.snbc.bbpf.system.db.common.dto.DictValueDto">
    SELECT
    t1.value_code valueCode,
    t1.value_name valueName,
    t2.type_code typeCode
    FROM
    t_dict_value t1
    LEFT JOIN t_dict_type t2 ON t1.type_code = t2.type_code
    WHERE 1=1
    <if test="dictTypeCodes != null and dictTypeCodes.size() != 0">
      AND t1.type_code in
      <foreach item="item" index="index" collection="dictTypeCodes"
               open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    order by t1.value_code
  </select>

</mapper>
