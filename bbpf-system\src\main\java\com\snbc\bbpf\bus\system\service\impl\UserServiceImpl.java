/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service.impl;

import com.google.gson.Gson;
import com.snbc.bbpf.bus.system.constans.CommonConstant;
import com.snbc.bbpf.bus.system.constans.NumberConstant;
import com.snbc.bbpf.bus.system.enums.BusTemplateCodeEnum;
import com.snbc.bbpf.bus.system.enums.PwdStrengthEnum;
import com.snbc.bbpf.bus.system.enums.UserLockStatusEnum;
import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.mapstruct.ClassConvertorMapper;
import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.bus.system.service.UserService;
import com.snbc.bbpf.bus.system.utils.AllOrg;
import com.snbc.bbpf.bus.system.utils.BossDES3Util;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.bus.system.utils.JasyptEncryptUtil;
import com.snbc.bbpf.bus.system.utils.RedisCheckCodeLoginFlag;
import com.snbc.bbpf.bus.system.utils.ResultUtil;
import com.snbc.bbpf.bus.system.utils.SendMsgUtil;
import com.snbc.bbpf.commons.strings.security.parse.MobilePhoneParse;
import com.snbc.bbpf.component.security.utils.WeakPwdCheckService;
import com.snbc.bbpf.system.db.common.entity.Message;
import com.snbc.bbpf.system.db.common.entity.Org;
import com.snbc.bbpf.system.db.common.entity.User;
import com.snbc.bbpf.system.db.common.mapper.RoleMapper;
import com.snbc.bbpf.system.db.common.mapper.UserLoginMapper;
import com.snbc.bbpf.system.db.common.mapper.UserMapper;
import com.snbc.bbpf.system.db.common.mapper.UserRoleMapper;
import com.snbc.bbpf.system.db.common.vo.NoticeUserVo;
import com.snbc.bbpf.system.db.common.vo.OrgVo;
import com.snbc.bbpf.system.db.common.vo.UpdatePwdVo;
import com.snbc.bbpf.system.db.common.vo.UpdateUserPwdVo;
import com.snbc.bbpf.system.db.common.vo.UserVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.snbc.bbpf.bus.system.constans.CommonConstant.SMS_FORGOT_CIPHER;
import static com.snbc.bbpf.bus.system.constans.CommonConstant.UPDATE_USER_PHONE;

/**
 * @ClassName: UserServiceImpl
 * @Description: 用户业务处理
 * @module: si-bbpf-system
 * @Author: wangsong
 * @date: 2021/5/15
 *        Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all
 *        rights reserved.
 */
@Slf4j
@Service
public class UserServiceImpl implements UserService {
    @Autowired
    private RoleMapper roleMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private UserLoginMapper userLoginMapper;
    @Autowired
    private UserRoleMapper userRoleMapper;
    @Autowired
    private RedisCheckCodeLoginFlag redisCheckCodeLoginFlag;
    @Autowired
    private WeakPwdCheckService weakPwdCheckService;
    @Value("${bbpf.boss.login.pwdStrength:high}")
    private String pwdStrength;
    @Autowired
    private AllOrg allOrg;
    /***
     * @Description: 查询用户信息
     * @Author: wangsong
     * @param : userId
     * @CreateDate: 2021/5/15 17:18
     * @UpdateDate: 2021/5/15 17:18
     * @return : com.snbc.bbpf.system.db.common.dto.UserDto
     *         Copyright© Shandong New Beiyang Information Technology Co., Ltd. .
     *         all rights reserved.
     */
    @Override
    public UserVo getDetail(String userId) {
        User user = userMapper.selectByPrimaryKey(userId);
        ErrorMessage.USER_NOT_EXIST.assertNotNull(user);
        UserVo userVo = ClassConvertorMapper.INSTANCE.covertUserVo(user);
        // 手机号 先解密再脱敏
        MobilePhoneParse parsePhone = new MobilePhoneParse();
        userVo.setPhone(parsePhone.parseString(BossDES3Util.decrypt(userVo.getPhone())));
        // 查询用户的角色组织机构信息
        return getUserVo(userId, user, userVo);
    }

    private UserVo getUserVo(String userId, User user, UserVo userVo) {
        // 没有数据权限查出来的数据:当前所属的角色
        List<Map<String, String>> ownedRoleList = userMapper.selectRoleByUserNoDataAuth(userId);
        // 数据权限是个人
        List<Map<String, String>> dateRuleRoleList = null;
        //查询该用户所属的角色列表
        List<String> roleIdList = userRoleMapper.selectRoleByUserId(userId);
        // 包含超级管理员：返回所有角色
        if (roleIdList.contains(String.valueOf(NumberConstant.F_NO_ONE))) {
            dateRuleRoleList = roleMapper.getAllRoleList(userId);
        } else {
            dateRuleRoleList = roleMapper.getOwnRoleList();
        }

        List<Map<String, String>> orgListNoDataAuth = userMapper.selectOrgByUserNoDataAuth(userId);
        List<Map<String, String>> orgList = userMapper.selectUserOrg4DataRule();
        //角色数据转换
        convertMap(ownedRoleList, dateRuleRoleList,"roleId");
        //组织机构数据转换
        convertMap(orgListNoDataAuth, orgList,"orgId");

        userVo.setOrgInfo(orgListNoDataAuth);
        userVo.setRoleInfo(ownedRoleList);
        userVo.setUserStatus(user.getUserStatus());
        return userVo;
    }

    /**
     * 判断map中的值是否能在前端移除
     * @param ownedList 用户所属的
     * @param dateRuleList  用户数据权限的
     */
    private static void convertMap(List<Map<String, String>> ownedList,List<Map<String, String>> dateRuleList,String key){
        log.debug("owned ={}, dataRule={}",ownedList,dateRuleList);
        // 转换： 判断orgList中角色是否有数据权限
        for (Map<String, String> owned : ownedList) {
            owned.put("canRemove", "false");
            // 如果不包含，设置成false
            if (judgeContains(dateRuleList,owned.get(key))){
                owned.put("canRemove", "true");
            }
        }
    }
    /**
     * 判断是否  包含返回true,不包含返回false
     * @param mapList
     * @param value
     * @return
     */
    private static boolean judgeContains(List<Map<String, String>> mapList,String value){
        for (Map<String, String> map : mapList) {
            if (map.containsValue(value)){
                return true;
            }
        }
        return false;
    }

    @Override
    public UserVo getUserById(String userId) {
        User user = userMapper.selectByPrimaryKey(userId);
        ErrorMessage.USER_NOT_EXIST.assertNotNull(user);
        UserVo userVo = ClassConvertorMapper.INSTANCE.covertUserVo(user);
        // 手机号 先解密再脱敏
        userVo.setPhone(BossDES3Util.decrypt(userVo.getPhone()));
        // 查询用户的角色组织机构信息
        return getUserVo(userId, user, userVo);
    }
    @Override
    public List<Map<String,String>> getUserOrgInfo4Role() {
        // 所属组织机构
        List<Map<String, String>> orgListNoDataAuth = userMapper.selectOrgByUserNoDataAuth(CurrentUser.getUserId());
        //数据权限的组织机构
        List<Map<String, String>> orgList = userMapper.selectUserOrg4DataRule();
        //组织机构数据转换
        convertMap(orgListNoDataAuth, orgList,"orgId");
        return orgListNoDataAuth;
    }

    @Override
    public User queryByJobNumber(String jobNumber) {
        return userMapper.selectByJobNumber(jobNumber);
    }

    @Override
    public boolean isAdmin(String userId) {
        List<String> listRole=userRoleMapper.selectRoleByUserId(userId);
        return !listRole.isEmpty()&&listRole.contains("-1");
    }

    @Override
    public void updatePwd(UpdatePwdVo userVo, String smsCodePrefix) {
        // 新密码
        String newPwd = new String(BossDES3Util.decrypt(userVo.getNewPwd(), CommonConstant.KEY, CommonConstant.DES_KEY_IV));
        // 确认密码
        String confirmPwd = new String(BossDES3Util.decrypt(userVo.getConfirmPwd(), CommonConstant.KEY, CommonConstant.DES_KEY_IV));
        if (!newPwd.equals(confirmPwd)) {
            // 两次密码不相等抛出异常
            throw new BusinessException(ErrorMessage.PWD_ERROR.getMessage(), ErrorMessage.PWD_ERROR.getCode(), null);
        }
        //密码强度校验
        if (!PwdStrengthEnum.getRegular(pwdStrength).matcher(confirmPwd).matches()) {
            throw new BusinessException(ErrorMessage.PWD_STRENGTH_ERROR.getMessage(), ErrorMessage.PWD_STRENGTH_ERROR.getCode(), null);
        }
        Map<Boolean, String> result = weakPwdCheckService.checkWeakPwd(newPwd, userVo.getUserId());
        String strPwd = MapUtils.getString(result, false, "");
        if (strPwd != null && !"".equals(strPwd)) {
            throw new BusinessException(strPwd, ErrorMessage.WEAK_PASSWORD.getCode());
        }
        String smsCodeKey = String.format(smsCodePrefix, userVo.getUserId());
        // 校验短信验证码
        SendMsgUtil.checkSmsCode(smsCodeKey, userVo.getSmsCaptcha(), true);
        // 密码加密 修改密码
        userMapper.updatePwd(JasyptEncryptUtil.encrypt(confirmPwd, CommonConstant.KEY_IV), userVo.getUserId());
    }

    @Override
    @Transactional(rollbackFor = BusinessException.class)
    public void updateUserStatus(String userId, Integer status) {
        // 验证 userId 有效性
        User userExsit = userMapper.selectByPrimaryKey(userId);
        ErrorMessage.USER_NOT_EXIST.assertNotNull(userExsit);
        // 验证 状态 是否为0 或1
        ErrorMessage.USER_STATUS_INVALID.assertNotNull(UserLockStatusEnum.getStatusName(status));
        // 禁用时删除redis中的token信息，SYSTYPE默认为零，并移除用户t_org用户主管表
        if (UserLockStatusEnum.TLOCK.getStatus().equals(status)) {
            redisCheckCodeLoginFlag.delKey(CommonConstant.USER_LOGIN_STATUS,
                    CommonConstant.ZERO + CommonConstant.JOINER + userId);
            // 20220108 wjc 禁用发送消息
            userLockMessageNotice(userId, userExsit,true);
        } else {
            //解锁时清除用户登录失败五次的锁定状态
            userMapper.loginUnLock(userId);
            // 20220929 启用发送消息
            userLockMessageNotice(userId, userExsit,false);
        }
        userMapper.updateByPrimaryKeySelective(User.builder().userId(userId).hasLock(status).build());
    }

    /**
     * 20220108 wjc1 禁用用户发送消息通知
     *
     * @param userId    被禁用的用户Id
     * @param userExsit 被禁用的用户
     * @param flag  true 禁用
     */
    private void userLockMessageNotice(String userId, User userExsit,boolean flag) {
        String sendId = CurrentUser.getUserId();
        new Thread(() ->
            userLockMessageNoticeHandle(userId, userExsit, flag, sendId)
        ).start();
    }

    private void userLockMessageNoticeHandle(String userId, User userExsit, boolean flag, String sendId) {
        String phone = BossDES3Util.decrypt(userExsit.getPhone());
        String[] receivePhoneArr = { phone };
        String[] receiveUserIdArr = { userExsit.getUserId() };
        String templateCode= BusTemplateCodeEnum.ENABLE_USER.getName();
        if(flag) {
            templateCode= BusTemplateCodeEnum.DISABLE_USER.getName();
        }
        Message message = Message.builder().receiveNos(receiveUserIdArr).receiverPhone(receivePhoneArr).templateCode(templateCode)
                .templateParamJsonArr(convertTemplateParamJsonArr(userExsit.getUserName(), userExsit.getJobNumber()))
                .msgTitle(BusTemplateCodeEnum.getTitleByCode(templateCode)).senderId(sendId).build();
        SendMsgUtil.sendShortMessage(message);
        String[] userIdArr = {userId};
        message.setReceiveNos(userIdArr);
        SendMsgUtil.sendSysMessage(message);
    }

    /**
     * 转换模板参数
     * @param userName
     * @param jobNumber
     * @return
     */
    private static String[] convertTemplateParamJsonArr(String userName,String jobNumber){
        Map<String,String> templateParamMap=new HashMap<>();
        templateParamMap.put("userName", userName);
        templateParamMap.put("jobNumber", jobNumber);
        return new String[]{new Gson().toJson(templateParamMap)};
    }
    /***
     * @Description: 修改头像
     * @Author: liangjunbin
     * @param : userVo
     * @CreateDate: 2021/5/18 16:42
     * @UpdateDate: 2021/5/18 16:42
     *              Copyright© Shandong New Beiyang Information Technology Co., Ltd.
     *              . all rights reserved.
     */
    @Override
    public void updateAvatar(String userAvatar, String userId) {
        // 验证 userId 有效性
        User userExsit = userMapper.selectByPrimaryKey(userId);
        ErrorMessage.USER_NOT_EXIST.assertNotNull(userExsit);
        userLoginMapper.updateAvatar(userAvatar, userId);
    }

    /***
     * @Description: 发送短信验证码
     * @Author: wangsong
     * @param : userId
     * @CreateDate: 2021/6/9 18:35
     * @UpdateDate: 2021/6/9 18:35
     * @return : void
     *         Copyright© Shandong New Beiyang Information Technology Co., Ltd. .
     *         all rights reserved.
     */
    @Override
    public CommonResp smsVerifCode(String userId) {
        // 验证 userId 有效性
        User userExist = userMapper.selectByPrimaryKey(userId);
        ErrorMessage.USER_NOT_EXIST.assertNotNull(userExist);
        // 手机号解密
        String phone = BossDES3Util.decrypt(userExist.getPhone());
        SendMsgUtil.buildSMSMessages(NumberConstant.NO_TWO,phone,userId);
        return CommonResp.builder().head(ResultUtil.success()).build();
    }

    /***
     * @Description: 修改用户状态
     * @Author: wangsong
     * @param : userId
     * @param : userStatus
     * @CreateDate: 2021/7/19 15:12
     * @UpdateDate: 2021/7/19 15:12
     * @return : void
     *         Copyright© Shandong New Beiyang Information Technology Co., Ltd. .
     *         all rights reserved.
     */
    @Override
    public void updateUserCondition(String userId, int userStatus) {
        // 验证 userId 有效性
        User userExsit = userMapper.selectByPrimaryKey(userId);
        ErrorMessage.USER_NOT_EXIST.assertNotNull(userExsit);
        User user = User.builder().userId(userId).userStatus(userStatus).build();
        userMapper.updateByPrimaryKeySelective(user);
    }



    @Override
    public void updateUserPwd(UpdateUserPwdVo userVo) throws Exception {
        String encryptPhone = BossDES3Util.encrypt(userVo.getPhone());
        User user = userLoginMapper.selectUserByPhone(encryptPhone);
        // 验证手机号是否注册
        if (null == user) {
            throw new BusinessException(ErrorMessage.PHONE_UNREGISTERED.getMessage(),
                    ErrorMessage.PHONE_UNREGISTERED.getCode());
        }
        userVo.setUserId(user.getUserId());
        updatePwd(userVo, SMS_FORGOT_CIPHER);
    }

    @Override
    public void updateUserPhone(String phone, String smsVerifCode) {
        //校验验证码
        SendMsgUtil.checkSmsCode(String.format(UPDATE_USER_PHONE,CurrentUser.getUserId()), smsVerifCode, true);
        //手机号加密
        String encryptPhone = BossDES3Util.encrypt(phone);
        User user = userMapper.selectByPhone(encryptPhone);
        //验证该手机号下是否有用户
        if (Optional.ofNullable(user).isPresent()){
            throw new BusinessException(ErrorMessage.PHONE_BIND_USER.getMessage()
                    ,ErrorMessage.PHONE_BIND_USER.getCode());
        }
        userMapper.updateByPrimaryKeySelective(User.builder().userId(CurrentUser.getUserId()).phone(encryptPhone).build());
    }

    /***
      * @Description:    修改手机号时验证手机号是否已绑定用户
      * @Author:         wangsong
      * @param :         phone
      * @CreateDate:     2022/7/4 17:02
      * @UpdateDate:     2022/7/4 17:02
      * @return :        void
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public void getSmsCodeAndVerifPhoneBind(String phone) {
        //手机号加密
        String encryptPhone = BossDES3Util.encrypt(phone);
        int isBind = userMapper.checkPhoneIsBind(encryptPhone);
        if (isBind > NumberConstant.NO_ZERO) {
            throw new BusinessException(ErrorMessage.PHONE_BIND_USER.getMessage(),
                    ErrorMessage.PHONE_BIND_USER.getCode());
        }
        SendMsgUtil.buildSMSMessages(NumberConstant.NO_THREE, phone, CurrentUser.getUserId());
    }

    @Override
    public List<NoticeUserVo> noticeUserList(String orgId, List<String> userIds, String userNamePhone) {
        if (StringUtils.isBlank(orgId)){
            orgId="0";
        }
        String phone = BossDES3Util.encrypt(userNamePhone);
        List<NoticeUserVo> userList = null;
        boolean isAdmin = allOrg.isAdmin(CurrentUser.getUserId());
        // 是超管
        if(isAdmin){
            List<String> orgPathList = allOrg.processAdmin(orgId);
            //超管：组织机构及下级下的用户
            userList = userMapper.noticeUserListConsole(orgPathList,userNamePhone,phone);
        }else {
            // 数据权限的组织机构
            List<OrgVo> orgIdPathListDataRule = allOrg.getDataRuleOrg(CurrentUser.getUserId());
            //true 前端的orgId是所属组织机构的上级
            boolean isSub = allOrg.isSub(orgIdPathListDataRule, orgId);
            Org orgExsit = allOrg.getOrgByOrgId(orgId);
            //点击的是前端 是 数据权限的组织机构的上级
            if(isSub){
                List<String> orgIdList = orgIdPathListDataRule.stream().map(OrgVo::getOrgId).collect(Collectors.toList());
                userList = userMapper.noticeUserListConsoleByRoleId(orgIdList,userNamePhone,phone);
            }else {
                //如果点击的是末节点
                List<Org> childList = allOrg.queryOrgListByParentId(orgExsit.getParentId());
                if(CollectionUtils.isEmpty(childList)){
                    userList = userMapper.noticeUserListConsoleByRoleId(Collections.singletonList(orgId),userNamePhone,phone);
                }else {
                    //查询当前orgId的下级，
                    List<String> orgIdList = allOrg.processGeneralUser(orgIdPathListDataRule, orgExsit.getOrgPath());
                    userList = userMapper.noticeUserListConsoleByRoleId(orgIdList,userNamePhone,phone);
                }
            }
        }
        MobilePhoneParse parsePhone = new MobilePhoneParse();
        userList.forEach(noticeUserVo ->
            noticeUserVo.setPhone(parsePhone.parseString(BossDES3Util.decrypt(noticeUserVo.getPhone())))
        );
        return userList;
    }
}
