package com.snbc.bbpf.grafana.proxy.interceptor;

import com.snbc.bbpf.grafana.proxy.filter.JwtAuthenticationFilter;
import com.snbc.bbpf.grafana.proxy.service.DynamicTemplateVariableService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;

/**
 * 仪表盘响应拦截器
 * 
 * 拦截Grafana仪表盘API的响应，动态注入模板变量：
 * 1. 拦截仪表盘获取API的响应
 * 2. 解析响应JSON并注入用户权限变量
 * 3. 返回修改后的响应给客户端
 * 
 * 这个拦截器实现了"动态注入模板变量"方案的核心逻辑，
 * 相比"拦截并修改SQL"方案具有以下优势：
 * - 通用性高：支持所有数据源类型
 * - 侵入性低：对Grafana透明
 * - 安全性高：利用Grafana变量机制
 * - 维护性好：权限逻辑集中管理
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2024-12-13
 */
@Component
public class DashboardResponseInterceptor implements HandlerInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(DashboardResponseInterceptor.class);

    @Autowired
    private DynamicTemplateVariableService dynamicTemplateVariableService;

    /**
     * 响应包装器，用于捕获响应内容
     */
    public static class ResponseWrapper extends javax.servlet.http.HttpServletResponseWrapper {
        private ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        private PrintWriter writer;
        private boolean getOutputStreamCalled = false;
        private boolean getWriterCalled = false;

        public ResponseWrapper(HttpServletResponse response) {
            super(response);
        }

        @Override
        public javax.servlet.ServletOutputStream getOutputStream() throws IOException {
            if (getWriterCalled) {
                throw new IllegalStateException("getWriter() has already been called on this response.");
            }
            getOutputStreamCalled = true;
            return new javax.servlet.ServletOutputStream() {
                @Override
                public void write(int b) throws IOException {
                    outputStream.write(b);
                }
                
                @Override
                public boolean isReady() {
                    return true;
                }
                
                @Override
                public void setWriteListener(javax.servlet.WriteListener writeListener) {
                    // Not implemented for this wrapper
                }
            };
        }

        @Override
        public PrintWriter getWriter() throws IOException {
            if (getOutputStreamCalled) {
                throw new IllegalStateException("getOutputStream() has already been called on this response.");
            }
            if (writer == null) {
                getWriterCalled = true;
                writer = new PrintWriter(new java.io.OutputStreamWriter(outputStream, StandardCharsets.UTF_8), true);
            }
            return writer;
        }

        public byte[] getResponseData() {
            if (writer != null) {
                writer.flush();
            }
            return outputStream.toByteArray();
        }

        public String getResponseString() {
            return new String(getResponseData(), StandardCharsets.UTF_8);
        }
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 检查是否需要拦截此请求
        String requestURI = request.getRequestURI();
        
        if (!dynamicTemplateVariableService.shouldInterceptResponse(requestURI)) {
            return true; // 不需要拦截，继续处理
        }

        // 获取用户ID
        String userId = JwtAuthenticationFilter.getUserId(request);
        if (userId == null) {
            logger.debug("[仪表盘响应拦截器] 未找到用户ID，跳过响应拦截");
            return true;
        }

        logger.debug("[仪表盘响应拦截器] 准备拦截响应: {} for user: {}", requestURI, userId);
        
        // 将用户ID存储到请求属性中，供后续处理使用
        request.setAttribute("interceptUserId", userId);
        request.setAttribute("interceptApiPath", requestURI);
        
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 检查是否需要处理响应
        String userId = (String) request.getAttribute("interceptUserId");
        String apiPath = (String) request.getAttribute("interceptApiPath");
        
        if (userId == null || apiPath == null) {
            return; // 不需要处理
        }

        try {
            // 这里我们需要在实际的代理服务中处理响应拦截
            // 因为HandlerInterceptor无法直接修改响应体
            // 实际的响应拦截逻辑将在GrafanaProxyServiceImpl中实现
            logger.debug("[仪表盘响应拦截器] 响应处理完成: {} for user: {}", apiPath, userId);
            
        } catch (Exception e) {
            logger.error("[仪表盘响应拦截器] 处理响应时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理响应内容（由代理服务调用）
     * 
     * @param responseBody 原始响应体
     * @param userId 用户ID
     * @param apiPath API路径
     * @return 修改后的响应体
     */
    public String processResponse(String responseBody, String userId, String apiPath) {
        try {
            if (!dynamicTemplateVariableService.shouldInterceptResponse(apiPath)) {
                return responseBody;
            }

            logger.info("[仪表盘响应拦截器] 开始处理响应: {} for user: {}", apiPath, userId);
            
            String modifiedResponse = dynamicTemplateVariableService.interceptAndInjectVariables(
                responseBody, userId, apiPath);
            
            if (!modifiedResponse.equals(responseBody)) {
                logger.info("[仪表盘响应拦截器] 成功注入动态模板变量: {} for user: {}", apiPath, userId);
            }
            
            return modifiedResponse;
            
        } catch (Exception e) {
            logger.error("[仪表盘响应拦截器] 处理响应时发生错误: {}", e.getMessage(), e);
            return responseBody; // 出错时返回原始响应
        }
    }

    /**
     * 检查响应是否为JSON格式
     */
    private boolean isJsonResponse(HttpServletResponse response) {
        String contentType = response.getContentType();
        return contentType != null && contentType.toLowerCase().contains("application/json");
    }

    /**
     * 检查是否为仪表盘相关的API
     */
    private boolean isDashboardApi(String requestURI) {
        return requestURI != null && (
            requestURI.contains("/api/dashboards/uid/") ||
            requestURI.contains("/api/dashboards/db/") ||
            requestURI.contains("/api/dashboards/id/")
        );
    }

    /**
     * 获取响应内容类型
     */
    private String getResponseContentType(HttpServletResponse response) {
        String contentType = response.getContentType();
        return contentType != null ? contentType : "unknown";
    }

    /**
     * 记录拦截统计信息
     */
    private void logInterceptionStats(String apiPath, String userId, boolean modified, long processingTime) {
        if (modified) {
            logger.info("[仪表盘响应拦截器] 拦截统计 - API: {}, 用户: {}, 已修改: true, 处理时间: {}ms", 
                apiPath, userId, processingTime);
        } else {
            logger.debug("[仪表盘响应拦截器] 拦截统计 - API: {}, 用户: {}, 已修改: false, 处理时间: {}ms", 
                apiPath, userId, processingTime);
        }
    }
}