package com.snbc.bbpf.grafana.proxy.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.ResourceAccessException;

import java.util.*;

/**
 * Grafana API客户端
 * 
 * 负责与Grafana API进行交互，包括：
 * 1. 仪表盘变量管理
 * 2. 仪表盘配置获取和更新
 * 3. 数据源管理
 * 4. 组织和用户管理
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2024-12-13
 */
@Component
public class GrafanaApiClient {

    private static final Logger logger = LoggerFactory.getLogger(GrafanaApiClient.class);

    @Value("${grafana.url:http://localhost:3000}")
    private String grafanaUrl;

    @Value("${grafana.admin.token:}")
    private String adminToken;

    @Value("${grafana.admin.username:admin}")
    private String adminUsername;

    @Value("${grafana.admin.password:admin}")
    private String adminPassword;

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    public GrafanaApiClient() {
        this.restTemplate = new RestTemplate();
        
        // 添加拦截器设置User-Agent头，确保Grafana兼容性
        this.restTemplate.getInterceptors().add((request, body, execution) -> {
            request.getHeaders().set("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
            return execution.execute(request, body);
        });
        
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 获取仪表盘配置
     */
    public Map<String, Object> getDashboard(String dashboardId) {
        try {
            String url = grafanaUrl + "/api/dashboards/uid/" + dashboardId;
            HttpHeaders headers = createAuthHeaders();
            HttpEntity<String> entity = new HttpEntity<>(headers);

            ResponseEntity<Map> response = restTemplate.exchange(
                url, HttpMethod.GET, entity, Map.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                logger.debug("Successfully retrieved dashboard: {}", dashboardId);
                return response.getBody();
            } else {
                logger.warn("Failed to get dashboard {}: {}", dashboardId, response.getStatusCode());
                return null;
            }

        } catch (HttpClientErrorException e) {
            logger.error("HTTP error getting dashboard {}: {} - {}", 
                dashboardId, e.getStatusCode(), e.getResponseBodyAsString());
            return null;
        } catch (ResourceAccessException e) {
            logger.error("Network error getting dashboard {}: {}", dashboardId, e.getMessage());
            return null;
        } catch (Exception e) {
            logger.error("Unexpected error getting dashboard {}: {}", dashboardId, e.getMessage());
            return null;
        }
    }

    /**
     * 更新仪表盘配置
     */
    public boolean updateDashboard(String dashboardId, Map<String, Object> dashboardConfig) {
        try {
            String url = grafanaUrl + "/api/dashboards/db";
            HttpHeaders headers = createAuthHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建更新请求体
            Map<String, Object> updateRequest = new HashMap<>();
            updateRequest.put("dashboard", dashboardConfig);
            updateRequest.put("overwrite", true);
            updateRequest.put("message", "Updated by BBPF Proxy - Variable injection");

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(updateRequest, headers);

            ResponseEntity<Map> response = restTemplate.exchange(
                url, HttpMethod.POST, entity, Map.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                logger.info("Successfully updated dashboard: {}", dashboardId);
                return true;
            } else {
                logger.warn("Failed to update dashboard {}: {}", dashboardId, response.getStatusCode());
                return false;
            }

        } catch (HttpClientErrorException e) {
            logger.error("HTTP error updating dashboard {}: {} - {}", 
                dashboardId, e.getStatusCode(), e.getResponseBodyAsString());
            return false;
        } catch (ResourceAccessException e) {
            logger.error("Network error updating dashboard {}: {}", dashboardId, e.getMessage());
            return false;
        } catch (Exception e) {
            logger.error("Unexpected error updating dashboard {}: {}", dashboardId, e.getMessage());
            return false;
        }
    }

    /**
     * 获取数据源列表
     */
    public List<Map<String, Object>> getDataSources() {
        try {
            String url = grafanaUrl + "/api/datasources";
            HttpHeaders headers = createAuthHeaders();
            HttpEntity<String> entity = new HttpEntity<>(headers);

            ResponseEntity<List> response = restTemplate.exchange(
                url, HttpMethod.GET, entity, List.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                logger.debug("Successfully retrieved data sources");
                return response.getBody();
            } else {
                logger.warn("Failed to get data sources: {}", response.getStatusCode());
                return new ArrayList<>();
            }

        } catch (Exception e) {
            logger.error("Error getting data sources: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 获取组织列表
     */
    public List<Map<String, Object>> getOrganizations() {
        try {
            String url = grafanaUrl + "/api/orgs";
            HttpHeaders headers = createAuthHeaders();
            HttpEntity<String> entity = new HttpEntity<>(headers);

            ResponseEntity<List> response = restTemplate.exchange(
                url, HttpMethod.GET, entity, List.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                logger.debug("Successfully retrieved organizations");
                return response.getBody();
            } else {
                logger.warn("Failed to get organizations: {}", response.getStatusCode());
                return new ArrayList<>();
            }

        } catch (Exception e) {
            logger.error("Error getting organizations: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 测试Grafana连接
     */
    public boolean testConnection() {
        try {
            String url = grafanaUrl + "/api/health";
            HttpHeaders headers = createAuthHeaders();
            HttpEntity<String> entity = new HttpEntity<>(headers);

            ResponseEntity<Map> response = restTemplate.exchange(
                url, HttpMethod.GET, entity, Map.class);

            boolean isHealthy = response.getStatusCode() == HttpStatus.OK;
            logger.debug("Grafana connection test: {}", isHealthy ? "SUCCESS" : "FAILED");
            return isHealthy;

        } catch (Exception e) {
            logger.error("Grafana connection test failed: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取仪表盘变量配置
     */
    public List<Map<String, Object>> getDashboardVariables(String dashboardId) {
        Map<String, Object> dashboard = getDashboard(dashboardId);
        if (dashboard == null) {
            return new ArrayList<>();
        }

        try {
            Map<String, Object> dashboardData = (Map<String, Object>) dashboard.get("dashboard");
            if (dashboardData != null) {
                Object templatingObj = dashboardData.get("templating");
                if (templatingObj instanceof Map) {
                    Map<String, Object> templating = (Map<String, Object>) templatingObj;
                    Object listObj = templating.get("list");
                    if (listObj instanceof List) {
                        return (List<Map<String, Object>>) listObj;
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Error parsing dashboard variables for {}: {}", dashboardId, e.getMessage());
        }

        return new ArrayList<>();
    }

    /**
     * 更新仪表盘变量
     */
    public boolean updateDashboardVariables(String dashboardId, List<Map<String, Object>> variables) {
        Map<String, Object> dashboard = getDashboard(dashboardId);
        if (dashboard == null) {
            return false;
        }

        try {
            Map<String, Object> dashboardData = (Map<String, Object>) dashboard.get("dashboard");
            if (dashboardData != null) {
                // 更新变量配置
                Map<String, Object> templating = new HashMap<>();
                templating.put("list", variables);
                dashboardData.put("templating", templating);

                // 更新仪表盘
                return updateDashboard(dashboardId, dashboardData);
            }
        } catch (Exception e) {
            logger.error("Error updating dashboard variables for {}: {}", dashboardId, e.getMessage());
        }

        return false;
    }

    /**
     * 创建认证头
     */
    private HttpHeaders createAuthHeaders() {
        HttpHeaders headers = new HttpHeaders();
        
        if (adminToken != null && !adminToken.trim().isEmpty()) {
            // 使用API Token认证
            headers.set("Authorization", "Bearer " + adminToken);
        } else {
            // 使用基本认证
            String auth = adminUsername + ":" + adminPassword;
            String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes());
            headers.set("Authorization", "Basic " + encodedAuth);
        }
        
        headers.set("Content-Type", "application/json");
        return headers;
    }

    /**
     * 创建权限变量配置
     */
    public Map<String, Object> createPermissionVariable(String name, String value, String description) {
        Map<String, Object> variable = new HashMap<>();
        variable.put("name", name);
        variable.put("type", "constant");
        variable.put("label", description);
        variable.put("description", description);
        variable.put("hide", 2); // 隐藏变量
        variable.put("skipUrlSync", true);
        
        Map<String, Object> query = new HashMap<>();
        query.put("query", value);
        variable.put("query", query);
        
        Map<String, Object> current = new HashMap<>();
        current.put("value", value);
        current.put("text", value);
        variable.put("current", current);
        
        List<Map<String, Object>> options = new ArrayList<>();
        Map<String, Object> option = new HashMap<>();
        option.put("value", value);
        option.put("text", value);
        option.put("selected", true);
        options.add(option);
        variable.put("options", options);
        
        return variable;
    }

    /**
     * 验证变量值安全性
     */
    public boolean isVariableValueSafe(String value) {
        if (value == null || value.trim().isEmpty()) {
            return false;
        }
        
        // 检查SQL注入风险
        String lowerValue = value.toLowerCase();
        String[] dangerousKeywords = {
            "drop", "delete", "truncate", "insert", "update", 
            "exec", "execute", "sp_", "xp_", "--", "/*", "*/",
            "union", "select", "script", "javascript", "vbscript"
        };
        
        for (String keyword : dangerousKeywords) {
            if (lowerValue.contains(keyword)) {
                logger.warn("Potentially dangerous keyword '{}' found in variable value: {}", keyword, value);
                return false;
            }
        }
        
        return true;
    }

    /**
     * 清理变量值
     */
    public String sanitizeVariableValue(String value) {
        if (value == null) {
            return "";
        }
        
        // 移除潜在的危险字符
        return value.replaceAll("[<>\"'&;]", "")
                   .replaceAll("--.*", "")
                   .replaceAll("/\\*.*?\\*/", "")
                   .trim();
    }
}