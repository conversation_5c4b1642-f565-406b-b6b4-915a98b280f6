package com.snbc.bbpf.bus.system.strategy;

import com.snbc.bbpf.bus.system.config.LDAPConfig;
import com.snbc.bbpf.bus.system.constans.CommonConstant;
import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.strategy.impl.LdapLoginStrategy;
import com.snbc.bbpf.bus.system.utils.ContainerUtil;
import com.snbc.bbpf.component.captcha.model.common.NumConstant;
import com.snbc.bbpf.system.db.common.entity.User;
import com.snbc.bbpf.system.db.common.entity.login.LoginUser;

/**
 * @ClassName: LoginContext
 * @Description: 登录策略上下文
 * @module: SI-bbpf-system
 * @Author: wangsong
 * @date: 2023/5/17
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
public class LoginContext {
    private LoginStrategy loginStrategy;

    /***
      * @Description:    选择登录策略
      * @Author:         WangSong
      * @param :         isLdap
      * @param :         loginTypeByConfig
      * @param :         loginTypeByPage
      * @param :         ldapConfig
      * @return:
      * @CreateDate:     2023/5/17 18:16
      * @UpdateDate:     2023/5/17 18:16
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
    */
    public LoginContext(Integer isLdap, String loginTypeByConfig, String loginTypeByPage, LDAPConfig ldapConfig) throws BusinessException {
        // 如果页面上设置的登录类型为短信验证码登录，且配置文件中开启了短信登录方式，则使用SmsLoginStrategy作为登录策略
        if (loginTypeByPage.equals(String.valueOf(CommonConstant.ONE)) && loginTypeByConfig.contains(CommonConstant.SMS_LOGIN_TYPE)) {
            this.loginStrategy = (LoginStrategy) ContainerUtil.getBean("smsLoginStrategy");
            return;
        }
        // 如果勾选了LDAP登录，且配置文件中配置了LDAP登录方式，则使用LdapLoginStrategy作为登录策略
        if ((NumConstant.ONE == isLdap) && loginTypeByConfig.contains(CommonConstant.LDAP_LOGIN_TYPE)) {
            LdapLoginStrategy ldapLoginStrategy = (LdapLoginStrategy) ContainerUtil.getBean("ldapLoginStrategy");
            ldapLoginStrategy.setType(ldapConfig.getType());
            ldapLoginStrategy.setDomain(ldapConfig.getDomain());
            this.loginStrategy = ldapLoginStrategy;
            return;
        }
        // 如果配置文件中开启了账号密码登录方式，则使用AccountLoginStrategy作为登录策略
        if (loginTypeByConfig.contains(CommonConstant.ACCOUNT_LOGIN_TYPE)) {
            this.loginStrategy = (LoginStrategy) ContainerUtil.getBean("accountLoginStrategy");
        }
        // 如果没有匹配到任何登录策略，则会抛出自定义的业务异常，提示用户名或密码错误
        if (null == this.loginStrategy){
            throw new BusinessException(ErrorMessage.USERNAME_OR_PASSWORD_ERROR.getMessage(),ErrorMessage.USERNAME_OR_PASSWORD_ERROR.getCode());
        }

    }

    /***
      * @Description:    执行各个方式的代码验证
      * @Author:         WangSong
      * @param :         loginUser
      * @param :         user
      * @return:         boolean
      * @CreateDate:     2023/5/17 18:20
      * @UpdateDate:     2023/5/17 18:20
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
    */
    public boolean executeVerify(LoginUser loginUser, User user) throws Exception {
        return loginStrategy.verify(loginUser, user);
    }
}
