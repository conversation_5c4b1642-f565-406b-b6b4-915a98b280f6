# Grafana WebSocket代理配置指南

## 概述

本文档说明如何配置和使用Grafana WebSocket代理功能，支持 `ws://g.xinbeiyang.info/api/live/ws`等WebSocket连接。

## WebSocket代理流程图

### 整体架构流程

```mermaid
sequenceDiagram
    participant Client as 前端客户端<br/>(Grafana报表页面)
    participant Proxy as BBPF代理服务<br/>(WebSocket代理)
    participant <PERSON><PERSON> as <PERSON><PERSON>服务器<br/>(Live WebSocket)
  
    Note over Client,<PERSON>ana: WebSocket连接建立阶段
    Client->>Proxy: 1. WebSocket握手请求<br/>ws://g.xinbeiyang.info/api/live/ws
    Note over Proxy: 2. 身份验证<br/>- 解析JWT Token<br/>- 验证用户权限<br/>- 提取用户ID
    Proxy->>Grafana: 3. 建立到Grafana的WebSocket连接<br/>ws://grafana-server/api/live/ws
    Grafana-->>Proxy: 4. 连接确认
    Proxy-->>Client: 5. 握手成功响应
  
    Note over Client,Grafana: 消息转发阶段
    Client->>Proxy: 6. 发送消息到代理
    Note over Proxy: 7. 消息处理<br/>- 添加用户身份信息<br/>- 转发到对应Grafana连接
    Proxy->>Grafana: 8. 转发消息到Grafana
    Grafana-->>Proxy: 9. Grafana响应消息
    Note over Proxy: 10. 响应处理<br/>- 路由到对应客户端<br/>- 记录日志
    Proxy-->>Client: 11. 转发响应到客户端
  
    Note over Client,Grafana: 连接关闭阶段
    Client->>Proxy: 12. 关闭连接
    Proxy->>Grafana: 13. 关闭Grafana连接
    Note over Proxy: 14. 清理资源<br/>- 移除连接映射<br/>- 释放内存
```

### 详细技术流程

```mermaid
flowchart TD
    A[前端Grafana报表页面] --> B{发起WebSocket连接}
    B --> C["请求: ws://g.xinbeiyang.info/api/live/ws"]
    C --> D[WebSocketHandshakeInterceptor]
  
    D --> E{身份验证}
    E -->|JWT Token| F[解析Token获取用户ID]
    E -->|Auth Proxy| G[使用Auth Proxy头]
    E -->|Query Param| H[从查询参数获取Token]
  
    F --> I[验证通过]
    G --> I
    H --> I
    E -->|验证失败| J[拒绝连接]
  
    I --> K[WebSocketConfig路由]
    K --> L[GrafanaWebSocketProxyHandler]
  
    L --> M["建立到Grafana的连接<br/>ws://grafana-server/api/live/ws"]
    M --> N{Grafana连接成功?}
    N -->|是| O[建立连接映射]
    N -->|否| P[返回连接错误]
  
    O --> Q[双向消息转发]
    Q --> R["客户端 ↔ 代理 ↔ Grafana"]
  
    R --> S{连接状态}
    S -->|正常| Q
    S -->|异常/关闭| T[清理连接映射]
  
    T --> U[记录日志]
    U --> V[连接结束]
  
    style A fill:#e1f5fe
    style L fill:#f3e5f5
    style M fill:#e8f5e8
    style Q fill:#fff3e0
```

### 核心组件交互图

```mermaid
classDiagram
    class WebSocketConfig {
        +registerWebSocketHandlers()
        +configureMessageBroker()
        -handshakeInterceptor: WebSocketHandshakeInterceptor
        -proxyHandler: GrafanaWebSocketProxyHandler
    }
  
    class WebSocketHandshakeInterceptor {
        +beforeHandshake()
        +afterHandshake()
        -validateJwtToken()
        -extractUserId()
    }
  
    class GrafanaWebSocketProxyHandler {
        +afterConnectionEstablished()
        +handleMessage()
        +handleTransportError()
        +afterConnectionClosed()
        -clientSessions: Map
        -grafanaConnections: Map
    }
  
    class GrafanaWebSocketHandler {
        +afterConnectionEstablished()
        +handleMessage()
        +handleTransportError()
        +afterConnectionClosed()
    }
  
    WebSocketConfig --> WebSocketHandshakeInterceptor
    WebSocketConfig --> GrafanaWebSocketProxyHandler
    GrafanaWebSocketProxyHandler --> GrafanaWebSocketHandler
  
  
```

## 数据流转详解

### 1. 连接建立流程

1. **前端发起连接**：Grafana报表页面中的JavaScript代码发起WebSocket连接请求
2. **代理接收请求**：BBPF代理服务接收到 `ws://g.xinbeiyang.info/api/live/ws`的连接请求
3. **身份验证**：`WebSocketHandshakeInterceptor`验证JWT Token或Auth Proxy信息
4. **建立上游连接**：代理服务向Grafana服务器建立WebSocket连接
5. **连接映射**：在内存中建立客户端会话与Grafana连接的映射关系

### 2. 消息转发机制

- **客户端→Grafana**：客户端消息通过代理转发到对应的Grafana连接
- **Grafana→客户端**：Grafana的响应消息通过连接映射路由回对应的客户端
- **身份注入**：代理在转发消息时自动注入用户身份信息
- **错误处理**：任何环节的错误都会被捕获并记录日志

### 3. 连接管理

- **心跳检测**：定期发送心跳包检测连接状态
- **自动清理**：连接断开时自动清理相关资源和映射
- **并发控制**：支持多个客户端同时连接
- **资源限制**：防止连接数过多导致资源耗尽

## 功能特性

- **WebSocket代理转发**：将客户端WebSocket连接代理到Grafana服务器
- **身份验证**：支持JWT token认证和Auth Proxy模式
- **连接管理**：自动处理连接建立、消息转发和连接清理
- **错误处理**：完善的错误处理和日志记录
- **SockJS支持**：提供WebSocket降级方案

## 配置说明

### 1. 依赖配置

已在 `pom.xml`中添加WebSocket依赖：

```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-websocket</artifactId>
</dependency>
```

### 2. 应用配置

在 `application-local.properties`中添加WebSocket配置：

```properties
# WebSocket配置
spring.websocket.enabled=true
spring.websocket.max-text-message-buffer-size=8192
spring.websocket.max-binary-message-buffer-size=8192
spring.websocket.session-timeout=300000

# Grafana代理WebSocket配置
bbpf.grafana.proxy.enable-websocket=true
bbpf.grafana.proxy.websocket-timeout-ms=30000
bbpf.grafana.proxy.websocket-heartbeat-ms=25000
```

### 3. 核心组件

#### WebSocketConfig

- 配置WebSocket处理器和拦截器
- 注册WebSocket端点：`/api/live/ws`和 `/api/live/ws/native`
- 支持SockJS降级

#### GrafanaWebSocketProxyHandler

- 处理WebSocket连接的代理转发
- 管理客户端和Grafana服务器之间的连接映射
- 实现双向消息转发

#### WebSocketHandshakeInterceptor

- 在WebSocket握手阶段进行身份验证
- 从JWT token中提取用户信息
- 将用户ID存储到WebSocket会话属性中

## 使用方法

### 1. 客户端连接

#### JavaScript示例

```javascript
// 连接到WebSocket代理
const websocket = new WebSocket('ws://g.xinbeiyang.info/api/live/ws');

websocket.onopen = function(event) {
    console.log('WebSocket连接已建立');
};

websocket.onmessage = function(event) {
    console.log('收到消息:', event.data);
};

websocket.onclose = function(event) {
    console.log('WebSocket连接已关闭');
};

websocket.onerror = function(error) {
    console.error('WebSocket错误:', error);
};
```

#### 带认证的连接

```javascript
// 如果需要在查询参数中传递token（某些客户端无法设置header）
const websocket = new WebSocket('ws://g.xinbeiyang.info/api/live/ws?token=your_jwt_token');
```

### 2. 测试页面

访问 `http://g.xinbeiyang.info/websocket-test.html`可以测试WebSocket连接功能。

## 端点说明

| 端点                    | 描述                          | 支持协议           |
| ----------------------- | ----------------------------- | ------------------ |
| `/api/live/ws`        | 主要WebSocket端点，支持SockJS | WebSocket + SockJS |
| `/api/live/ws/native` | 原生WebSocket端点             | WebSocket          |

## 认证机制

### 1. JWT Token认证

- 从HTTP请求头中的 `Authorization`字段获取JWT token
- 解析token获取用户ID
- 将用户ID传递给Grafana服务器

### 2. Auth Proxy模式

- 如果启用Auth Proxy模式，会在请求头中添加 `X-WEBAUTH-USER`
- 支持Grafana的Auth Proxy认证机制

### 3. 查询参数认证

- 支持通过查询参数传递token：`?token=your_jwt_token`
- 适用于某些无法设置HTTP头的客户端

## 错误处理

### 常见错误及解决方案

1. **连接被拒绝**

   - 检查JWT token是否有效
   - 确认用户是否有访问权限
   - 查看服务器日志获取详细错误信息
2. **连接超时**

   - 检查网络连接
   - 确认Grafana服务器是否正常运行
   - 调整超时配置
3. **消息发送失败**

   - 确认WebSocket连接状态
   - 检查消息格式是否正确
   - 查看服务器端日志

## 监控和日志

### 日志级别

- `DEBUG`：详细的连接和消息转发日志
- `INFO`：连接建立和关闭事件
- `WARN`：认证失败和连接异常
- `ERROR`：严重错误和异常

### 关键日志

```
# 连接建立
WebSocket connection established: session-id
WebSocket connection for user: user-id

# 消息转发
Received message from client session-id: message-content
Forwarded message to Grafana for session: session-id

# 连接关闭
WebSocket connection closed for session session-id: close-status
```

## 性能优化

### 1. 连接池管理

- 合理设置最大连接数
- 及时清理无效连接
- 监控连接使用情况

### 2. 消息缓冲

- 配置合适的消息缓冲区大小
- 避免大消息阻塞连接
- 实现消息压缩（如需要）

### 3. 心跳机制

- 配置合适的心跳间隔
- 及时检测连接状态
- 自动重连机制

## 安全考虑

### 1. 认证和授权

- 严格验证JWT token
- 实施细粒度权限控制
- 定期更新认证密钥

### 2. 跨域配置

- 生产环境中限制允许的源域名
- 避免使用通配符 `*`
- 实施CORS策略

### 3. 连接限制

- 限制单个用户的连接数
- 实施连接频率限制
- 监控异常连接行为

## 故障排除

### 1. 检查清单

- [ ] WebSocket依赖是否正确添加
- [ ] 配置文件是否正确设置
- [ ] Grafana服务器是否支持WebSocket
- [ ] 网络防火墙是否允许WebSocket连接
- [ ] JWT token是否有效

### 2. 调试步骤

1. 启用详细日志记录
2. 使用测试页面验证连接
3. 检查浏览器开发者工具的网络面板
4. 查看服务器端日志
5. 使用WebSocket客户端工具测试

### 3. 常用调试命令

```bash
# 检查端口是否开放
telnet g.xinbeiyang.info 8081

# 使用curl测试HTTP升级
curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" -H "Sec-WebSocket-Version: 13" -H "Sec-WebSocket-Key: test" http://g.xinbeiyang.info/api/live/ws
```

## 更新日志

### v2.0.0 (2024-12-13)

- 初始WebSocket代理功能实现
- 支持JWT认证和Auth Proxy模式
- 添加SockJS降级支持
- 实现连接管理和错误处理
- 提供测试页面和文档

---

如有问题，请查看日志文件或联系开发团队。
