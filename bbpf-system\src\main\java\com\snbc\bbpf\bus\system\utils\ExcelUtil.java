package com.snbc.bbpf.bus.system.utils;

import com.snbc.bbpf.bus.system.constans.NumberConstant;
import com.snbc.bbpf.bus.system.exception.BusinessException;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * @ClassName: ExcelUtil.java
 * @Description: 导出excel
 * @module:
 * @Author: wangsong
 * @date: 2022/7/14 18:03
 * copyright 2020 SNBC. All rights reserver
 */

public class ExcelUtil {
    /**
     * @Author: yh
     * @Description: excel导出
     * @Date: Created in 14:29  2018/11/27.
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(ExcelUtil.class);
    //设置列的公式
    private static final Integer TWOHUNDREDFIFTYSIX = 256;
    private static final Integer THIRTYTHOUSAND = 30000;
    private static final short TWELVE = 12;

    /**
     * xlsx方式
     *
     * @param response
     * @param fileName
     * @param titleColumn
     * @param titleName
     * @param titleSize
     * @param dataList
     */
    public static void writeBigExcel(HttpServletResponse response, String fileName, String[] titleColumn,
                                     String[] titleName, int[] titleSize, List<?> dataList, String[] sumData) throws BusinessException {
        try (HSSFWorkbook swbook = new HSSFWorkbook()) {
            OutputStream out = response.getOutputStream();
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            String nowTime = DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now());
            String lastfileName = URLEncoder.encode(fileName + nowTime + ".xls", StandardCharsets.UTF_8.toString());
            response.addHeader("Content-Disposition", "attachment;fileName=" + lastfileName);
            int sheetNum = 1;
            int rowNum = 0;
            Sheet sheet = swbook.createSheet(fileName +sheetNum);
            Row titleNameRow;
            if (sumData != null) {
                rowNum = NumberConstant.NO_ONE;
                Row sumRow = sheet.createRow(NumberConstant.NO_ZERO);
                for (int index = NumberConstant.NO_ZERO; index < sumData.length; index++) {
                    Cell cell = sumRow.createCell(index);
                    cell.setCellValue(sumData[index]);
                }
                //写入excel的表头
                titleNameRow = sheet.createRow(NumberConstant.NO_ONE);
            } else {
                titleNameRow = swbook.getSheet(fileName + sheetNum).createRow(NumberConstant.NO_ZERO);
            }
            rowNum += NumberConstant.NO_ONE;
            HSSFCellStyle style = cellStyle(swbook);
            //设置图案背景色
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            style.setFillForegroundColor(IndexedColors.PALE_BLUE.getIndex());
            //字体加粗
            style.getFont(swbook).setBold(true);
            for (int i = NumberConstant.NO_ZERO; i < titleName.length; i++) {
                sheet.setColumnWidth(i, titleSize[i] * TWOHUNDREDFIFTYSIX);
                //设置宽度
                Cell cell = titleNameRow.createCell(i);
                cell.setCellValue(titleName[i]);
                cell.setCellStyle(style);
            }
            //写入到excel中
            createExcel(dataList, titleColumn, fileName, titleName, titleSize, swbook, rowNum);
            swbook.write(out);
        } catch (Exception e) {
            LOGGER.error("导出excel异常{}", e);
        }
    }

    private static HSSFCellStyle cellStyle(HSSFWorkbook swbook) {
        HSSFCellStyle style = swbook.createCellStyle();
        style.setBorderBottom(BorderStyle.THIN); //下边框
        style.setBorderLeft(BorderStyle.THIN);//左边框
        style.setBorderTop(BorderStyle.THIN);//上边框
        style.setBorderRight(BorderStyle.THIN);//右边框
        HSSFFont font = swbook.createFont();
        //设置字号
        font.setFontHeightInPoints(TWELVE);
        //设置字体名称
        font.setFontName("宋体");
        style.setFont(font);
        return style;
    }

    /***
     * @Description: 创建excel
     * @Author: wangsong
     * @param :         dataList
     * @param :         titleColumn
     * @param :         fileName
     * @param :         titleName
     * @param :         titleSize
     * @param :         workbook
     * @CreateDate: 2020/8/27 14:27
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/8/27 14:27
     * @return :        void
     */
    private static void createExcel(List<?> dataList, String[] titleColumn, String fileName,
                                    String[] titleName, int[] titleSize, HSSFWorkbook workbook, int num)
            throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        int rowIndex;
        int sheetNum=1;
        if (!dataList.isEmpty() && titleColumn.length > NumberConstant.NO_ZERO) {
            HSSFCellStyle style = cellStyle(workbook);
            for (int index = NumberConstant.NO_ZERO; index < dataList.size(); index++) {
                sheetNum = headerProcess(index, sheetNum, workbook, fileName, titleName, titleSize);
                if (index < THIRTYTHOUSAND) {
                    rowIndex = index + num;
                } else {
                    rowIndex = index - THIRTYTHOUSAND * ((index) / THIRTYTHOUSAND) + num;
                }
                Object obj = dataList.get(index);
                Class clazz = obj.getClass();
                Row dataRow = workbook.getSheet(fileName + sheetNum).createRow(rowIndex);
                createColumn(titleColumn, clazz, obj, dataRow, style);
            }
        }
    }

    /***
     * @Description: 写入excel列数据
     * @Author: wangsong
     * @param :         titleColumn
     * @param :         clazz
     * @param :         obj
     * @param :         dataRow
     * @param :         rowIndex
     * @param style
     * @CreateDate: 2020/8/27 14:28
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/8/27 14:28
     * @return :        void
     */
    private static void createColumn(String[] titleColumn, Class clazz, Object obj, Row dataRow, HSSFCellStyle style)
            throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        for (int columnIndex = NumberConstant.NO_ZERO; columnIndex < titleColumn.length; columnIndex++) {
            String title = titleColumn[columnIndex].trim();
            if (!"".equals(title)) {
                // 获取返回类型// 使其首字母大写
                String upperCaseTitle = Character.toUpperCase(title.charAt(NumberConstant.NO_ZERO)) + title.substring(NumberConstant.NO_ONE);
                String methodName = "get" + upperCaseTitle;
                Method method = clazz.getDeclaredMethod(methodName);
                method.getReturnType().getName();
                Object object = method.invoke(obj);
                String data = method.invoke(obj) == null ? "" : object.toString();
                Cell cell = dataRow.createCell(columnIndex);
                cell.setCellValue(data);
                cell.setCellStyle(style);
            }
        }
    }

    /***
     * @Description: 表头处理
     * @Author: wangsong
     * @param :         index
     * @param :         workbook
     * @param :         fileName
     * @param :         titleName
     * @param :         titleSize
     * @CreateDate: 2022/7/14 19:00
     * @UpdateDate: 2022/7/14 19:00
     * @return :        void
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    private static int headerProcess(int index, int sheetNum, HSSFWorkbook workbook, String fileName, String[] titleName, int[] titleSize) {
        if (index != NumberConstant.NO_ZERO && index % THIRTYTHOUSAND == NumberConstant.NO_ZERO) {
            HSSFCellStyle style = cellStyle(workbook);
            //设置图案背景色
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            style.setFillForegroundColor(IndexedColors.PALE_BLUE.getIndex());
            //字体加粗
            style.getFont(workbook).setBold(true);
            sheetNum = sheetNum + NumberConstant.NO_ONE;
            Sheet sheet = workbook.createSheet(fileName + sheetNum);
            //写入excel的表头
            Row titleNameRow = workbook.getSheet(fileName + sheetNum).createRow(NumberConstant.NO_ZERO);
            for (int i = NumberConstant.NO_ZERO; i < titleName.length; i++) {
                //设置宽度
                sheet.setColumnWidth(i, titleSize[i] * TWOHUNDREDFIFTYSIX);
                Cell cell = titleNameRow.createCell(i);
                cell.setCellValue(titleName[i]);
                cell.setCellStyle(style);
            }
        }
        return sheetNum;
    }
}
