/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.mapper;

import com.snbc.bbpf.system.db.common.entity.RolePermissionScope;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @ClassName:      RolePermissionScopeMapper.java
 * @Description:    角色、权限、数据权限mapper
 * @module:
 * @Author:         wangsong
 * @date:           2021/5/19 18:15
 * copyright 2020 SNBC. All rights reserver
 */

@Mapper
public interface RolePermissionScopeMapper {
    void insertRolePermissionScope(List<RolePermissionScope> rolePermissionScopeList);

    void deleteSocpeByRoleId(String roleId);
}
