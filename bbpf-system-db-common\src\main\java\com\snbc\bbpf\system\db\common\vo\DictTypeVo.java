/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * @ClassName: DictTypeVo
 * 提供阿里云的文件上传下载功能
 * @module: bbpf-bus-system
 * @Author: liuyi
 * @date: 2021/5/18
 * copyright 2020 barm Inc. All rights reserver
 */
@Data
public class DictTypeVo {

    @NotBlank(message = "字典类型编码不可以为空")
    @Size(max = 50, message = "字典类型编号长度不可以超过50")
    private String typeCode;

    @NotBlank(message = "字典类型名称不可以为空")
    @Size(max = 100, message = "字典类型名称长度不可以超过100")
    private String typeName;

    public DictTypeVo() {
    }

    public DictTypeVo(String typeCode, String typeName) {
        this.typeCode = typeCode;
        this.typeName = typeName;
    }
}
