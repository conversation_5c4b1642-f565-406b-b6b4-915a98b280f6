/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * @ClassName: UserRequest
 * 导入用户请求实体类
 * @module: si-bbpf-system
 * @Author: wjc1
 * @date: 2021/5/19 14:07
 */
@Data
public class UserExportDtoNew {

    @JsonIgnore
    @ExcelIgnore
    private String userId;
    /**
     * 姓名
     */
    @ExcelProperty(index = 0, value = {"姓名"})
    private String userName;
    /**
     * 工号
     */
    @ExcelProperty(index = 1, value = {"工号"})
    private String jobNumber;
    /**
     * 电话
     */
    @ExcelProperty(index = 2, value = {"手机号"})
    private String phone;
    /**
     * email
     */
    @ExcelProperty(index = 3, value = {"邮箱"})
    private String email;
    /**
     * 部门
     * */
    @ExcelProperty(index = 4, value = {"部门"})
    private String orgNames;
    @ExcelProperty(index = 5, value = {"部门描述"})
    private String orgRemarks;
    @ExcelProperty(index = 6, value = {"是否为部门主管(是/否)"})
    private String isManagers;
    /**
     * 角色
     * */
    @ExcelProperty(index = 7, value = {"角色"})
    private String roleNames;
    /**
     * 状态
     */
    @ExcelProperty(index = 8, value = {"状态"})
    private String hasLock;

}
