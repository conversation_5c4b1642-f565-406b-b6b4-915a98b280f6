/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.mapper;

import com.snbc.bbpf.system.db.common.entity.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface ParamMapper {
    int deleteByPrimaryKey(String paramId);

    int insert(Param param);

    int insertSelective(Param param);

    Param selectByPrimaryKey(String paramId);

    Param selectByParamCode(String paramCode);

    int updateByPrimaryKeySelective(Param param);

    int updateByPrimaryKey(Param param);
    /**
     * 根据map获取配置信息
     * @param map
     * @return
     */
    List<Param> queryParamByMap(Map<String, Object> map);
    /**
     * 查询所有的paramCode
     * @return
     */
    List<String> queryParamCode();
}
