/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.mapper;

import com.snbc.bbpf.component.dataauth.annotation.IgnoreAuth;
import com.snbc.bbpf.system.db.common.dto.RoleNamesDto;
import com.snbc.bbpf.system.db.common.entity.Role;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: RoleMapper
 * @Description: 角色持久层
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/5/18
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Mapper
public interface RoleMapper {
    /***
      * @Description:    获取roleid
      * @Author:         wangsong
      * @param :         userId
      * @param userId
     * @CreateDate:     2021/5/19 16:21
      * @UpdateDate:     2021/5/19 16:21
      * @return :        java.util.List<java.util.Map>
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    List<Map<String,String>> getAllRoleList(@Param("userId") String userId);

    /**
     * 根据 组织机构列表查询所属角色
     * @return
     */
    List<Map<String,String>> getOwnRoleList();

    /**
     * 数据权限为个人时，查询角色数据
     * @return
     */
    List<Map<String,String>> getOwnRoleListBySelf();

    void updateRole(Role role);

    int selectCountByRoleName(String roleName);

    void insertRole(Role role);

    void deleteByPrimaryKey(String roleId);

    Role selectRoleDetail(String roleId);
    /**
     * 根据userId获取角色列表（包括用户所在组的角色）
     * @param userId
     * @return
     */
    @IgnoreAuth
    List<Role> queryRoleListByUserId(@Param("userId") String userId);

    /**
     * wjc1 用户导入用到
     * 根据 角色名 查询角色id
     * */
    String selectIdsByRoleNames(@Param("belongRoleNames")List<String> belongRoleNames);

    /**
     * wjc1 用户导入用到
     * 根据 角色名 查询角色id
     * */
    String selectRoleNames(@Param("roleIds")List<String> roleIds);
    /**
     * 判断是否唯一角色名称
     */
    int selectOnlyRoleName(@Param("roleName") String roleName, @Param("roleId") String roleId);

    List<RoleNamesDto> selectRoleNamesByUserId(@Param("userIdList") List<String> exportUserIdList);

    /**
     * 重置角色
     * @param restSql
     */
    void resetRole(@Param("restSql") String restSql,@Param("roleId") String roleId);

    String getRestSql(String roleId);

    /**
     * 反显 角色所属的组织机构
     * @param roleId
     * @return
     */
    List<Map<String, String>> selectOrgByRoleId(String roleId);

    List<String> getRoleIdsByUserId(String userId);

    String getRoleIdByRoleCode(String roleCode);

    /**
     * 根据组织机构id查询角色
     * @param orgIds
     * @return
     */
    List<Map<String, String>> getRolesByOrgId(List<String> orgIds);
}
