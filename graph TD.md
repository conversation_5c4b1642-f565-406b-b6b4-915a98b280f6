graph TD
    subgraph BBPF系统
        A[用户登录BBPF] --> B(获取BBPF JWT Token);
        C[BBPF前端/应用] --> D{请求访问Grafana资源};
    end

    subgraph Grafana代理服务
        D --> E[拦截请求];
        E --> F{验证BBPF JWT Token};
        F -- Token有效 --> G[实时调用BBPF权限API查询用户权限];
        G --> H{根据权限处理请求};
        H -- 允许访问 --> I[转发请求至Grafana];
        H -- 权限不足/无效 --> J[拒绝访问/返回错误];
    end

    subgraph BBPF后端
        K[BBPF权限管理模块]
        L[BBPF权限API]
        G --> L;
    end

    subgraph Grafana
        M[Grafana实例]
        I --> M;
        M --> N[返回Grafana内容/数据];
    end

    N --> I; 
    I --> D; 
    J --> D;

    P[运维人员/图表设计者] -->|直接登录| M;