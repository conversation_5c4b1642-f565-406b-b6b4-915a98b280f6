package com.snbc.bbpf.bus.system.controller;

import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.bus.system.service.SmsService;
import com.snbc.bbpf.bus.system.utils.ResultUtil;
import com.snbc.bbpf.system.db.common.vo.CheckSmsCodeVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName: SmsController
 * @Description: 发送短信验证码
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2022/1/19
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@RestController
@RequestMapping("/console/sms")
public class SmsController {

    @Autowired
    private SmsService smsService;

    @Value("${bbpf.system.captcha.ischeck:true}")
    private String isCheckCaptcha;

    /***
     * @Description:    发送短信验证码
     * @Author:         wangsong
     * msgtype	是
     * 0 验证码登录，1 找回密码
     * @CreateDate:     2021/6/9 16:52
     * @UpdateDate:     2021/6/9 16:52
     * @return :        com.snbc.bbpf.component.config.CommonResp<java.lang.Object>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @GetMapping("/getSmsVerifCode")
    public CommonResp smsVerifCode(@RequestParam String userPhone, @RequestParam Integer msgType) throws Exception {
        smsService.getPhoneSmsCode(userPhone,msgType);
        return CommonResp.builder().head(ResultUtil.success()).build();
    }

    /***
      * @Description:    给当前用户发送手机验证码
      * @Author:         wangsong
      * @param :         msgType
      * @CreateDate:     2022/7/6 9:47
      * @UpdateDate:     2022/7/6 9:47
      * @return :        com.snbc.bbpf.component.config.CommonResp
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @GetMapping("/getCurrentUserSmsCode")
    public CommonResp currentUserSendSmsCode(@RequestParam Integer msgType) throws Exception {
        smsService.currentUserSendSmsCode(msgType);
        return CommonResp.builder().head(ResultUtil.success()).build();
    }

    /***
     * @Description:验证手机短信验证码
     * @Author: 阳伟鹏
     *
     * @CreateDate: 2021/5/19 16:27
     * @UpdateDate: 2021/5/19 16:27
     * @return :        com.snbc.bbpf.component.config.CommonResp
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @PostMapping (path = "/checkSmsCode")
    public CommonResp  checkSmsCode(@Validated @RequestBody CheckSmsCodeVo checkSmsCode) throws Exception {
        smsService.checkSmsCode(checkSmsCode);
        return CommonResp.builder().head(ResultUtil.success()).build();
    }


    /***
      * @Description:    配合UI自动化测试增加是否验证验证码判断
      * @Author:         wangsong
      * @CreateDate:     2022/4/24 11:04
      * @UpdateDate:     2022/4/24 11:04
      * @return :        com.snbc.bbpf.component.config.CommonResp
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @GetMapping("/getIsCheckCaptcha")
    public CommonResp getIsVerifCaptchaPropties(){
        return CommonResp.builder().head(ResultUtil.success()).body(isCheckCaptcha).build();
    }
}
