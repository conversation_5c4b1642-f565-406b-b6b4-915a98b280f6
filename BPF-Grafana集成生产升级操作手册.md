# BPF-Grafana集成生产升级操作手册

## 📋 升级前准备清单

### 环境检查
- [ ] 确认当前系统版本和配置
- [ ] 检查服务器资源使用情况
- [ ] 验证网络连接和防火墙配置
- [ ] 确认备份策略和恢复方案
- [ ] 准备升级所需的镜像和配置文件

### 人员准备
- [ ] 升级负责人：技术负责人
- [ ] 运维工程师：负责环境操作
- [ ] 测试工程师：负责功能验证
- [ ] 业务负责人：负责业务验收
- [ ] 应急联系人：24小时待命

### 时间安排
- **升级窗口**: 凌晨2:00-6:00（业务低峰期）
- **预计耗时**: 3-4小时
- **回滚时限**: 升级后2小时内
- **验收时限**: 升级后24小时内

## 🔧 升级操作步骤

### 第一阶段：服务停止与备份 (30分钟)

#### 1.1 服务状态检查
```bash
# 检查当前服务状态
docker-compose ps
systemctl status nginx

# 检查服务健康状态
curl -f http://localhost:8080/actuator/health
curl -f http://localhost:3000/api/health

# 记录当前版本信息
docker images | grep grafana-proxy
docker images | grep grafana
```

#### 1.2 通知用户服务维护
```bash
# 在Grafana中发布维护通知
curl -X POST http://localhost:3000/api/annotations \
  -H "Content-Type: application/json" \
  -d '{
    "text": "系统维护中，预计4小时后恢复服务",
    "tags": ["maintenance"],
    "time": '$(date +%s000)'
  }'
```

#### 1.3 优雅停止服务
```bash
# 停止接收新请求（Nginx配置维护页面）
sudo cp /etc/nginx/maintenance.conf /etc/nginx/sites-enabled/default
sudo nginx -s reload

# 等待现有请求处理完成
sleep 30

# 停止应用服务
docker-compose stop grafana-proxy
docker-compose stop grafana

# 验证服务已停止
docker-compose ps
```

#### 1.4 数据备份
```bash
# 创建备份目录
BACKUP_DIR="/backup/$(date +%Y%m%d_%H%M%S)"
mkdir -p $BACKUP_DIR

# 备份MySQL数据
mysqldump -h mysql-master -u root -p bbpf_grafana > $BACKUP_DIR/mysql_backup.sql
mysqldump -h mysql-master -u root -p grafana > $BACKUP_DIR/grafana_backup.sql

# 备份Redis数据
redis-cli -h redis-master BGSAVE
cp /var/lib/redis/dump.rdb $BACKUP_DIR/redis_backup.rdb

# 备份Grafana数据目录
cp -r /var/lib/grafana $BACKUP_DIR/grafana_data

# 备份配置文件
cp -r /opt/bbpf-grafana/config $BACKUP_DIR/config
cp docker-compose.yml $BACKUP_DIR/

# 验证备份完整性
ls -la $BACKUP_DIR
echo "备份完成，路径：$BACKUP_DIR"
```

### 第二阶段：镜像更新与配置升级 (60分钟)

#### 2.1 拉取新版本镜像
```bash
# 拉取最新版本镜像
docker pull bbpf/grafana-proxy:v2.1.0
docker pull grafana/grafana-enterprise:10.0.0

# 验证镜像完整性
docker images | grep -E "grafana-proxy|grafana-enterprise"

# 标记当前版本为备份
docker tag bbpf/grafana-proxy:latest bbpf/grafana-proxy:backup-$(date +%Y%m%d)
docker tag grafana/grafana-enterprise:latest grafana/grafana-enterprise:backup-$(date +%Y%m%d)
```

#### 2.2 更新配置文件
```bash
# 备份当前配置
cp config/application-prod.yml config/application-prod.yml.backup
cp docker-compose.yml docker-compose.yml.backup

# 应用新配置
cp config/application-prod-v2.1.0.yml config/application-prod.yml
cp docker-compose-v2.1.0.yml docker-compose.yml

# 验证配置文件语法
yaml-lint config/application-prod.yml
docker-compose config
```

#### 2.3 数据库结构升级
```bash
# 执行数据库升级脚本
mysql -h mysql-master -u root -p bbpf_grafana < scripts/upgrade_v2.1.0.sql

# 验证数据库结构
mysql -h mysql-master -u root -p -e "SHOW TABLES;" bbpf_grafana
mysql -h mysql-master -u root -p -e "DESCRIBE user_permissions;" bbpf_grafana
```

### 第三阶段：服务启动与验证 (90分钟)

#### 3.1 启动服务
```bash
# 启动数据库和缓存服务
docker-compose up -d mysql redis

# 等待数据库就绪
while ! mysqladmin ping -h mysql-master --silent; do
    echo "等待MySQL启动..."
    sleep 5
done

# 启动应用服务
docker-compose up -d grafana-proxy grafana

# 检查服务启动状态
docker-compose ps
docker-compose logs grafana-proxy
docker-compose logs grafana
```

#### 3.2 健康检查
```bash
# 等待服务完全启动
sleep 60

# 检查服务健康状态
curl -f http://localhost:8080/actuator/health
curl -f http://localhost:3000/api/health

# 检查数据库连接
curl -f http://localhost:8080/actuator/health/db

# 检查Redis连接
curl -f http://localhost:8080/actuator/health/redis
```

#### 3.3 功能验证
```bash
# 获取测试Token
TEST_TOKEN=$(curl -s -X POST http://localhost:8080/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","password":"testpass"}' | jq -r '.token')

# 测试认证功能
curl -H "Authorization: Bearer $TEST_TOKEN" http://localhost:8080/api/user

# 测试权限控制
curl -H "Authorization: Bearer $TEST_TOKEN" http://localhost:8080/api/dashboards

# 测试SQL拦截
curl -X POST http://localhost:8080/api/datasources/proxy/1/query \
  -H "Authorization: Bearer $TEST_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"sql":"SELECT * FROM business_data LIMIT 10"}'

# 测试iframe集成
curl -H "Authorization: Bearer $TEST_TOKEN" \
  "http://localhost:8080/d/dashboard-001?orgId=1&kiosk=tv"
```

#### 3.4 性能验证
```bash
# 运行性能测试脚本
./scripts/performance-test.sh

# 检查响应时间
for i in {1..10}; do
  time curl -s http://localhost:8080/api/dashboards > /dev/null
done

# 检查内存使用
docker stats --no-stream
```

### 第四阶段：恢复服务与监控 (30分钟)

#### 4.1 恢复负载均衡
```bash
# 恢复Nginx正常配置
sudo cp /etc/nginx/sites-available/default /etc/nginx/sites-enabled/default
sudo nginx -t
sudo nginx -s reload

# 验证负载均衡
curl -I http://your-domain.com/
```

#### 4.2 启用监控告警
```bash
# 启用Prometheus监控
docker-compose up -d prometheus

# 启用Grafana监控面板
curl -X POST http://localhost:3000/api/dashboards/db \
  -H "Content-Type: application/json" \
  -d @monitoring/system-dashboard.json

# 启用告警规则
curl -X POST http://localhost:9090/api/v1/rules \
  -H "Content-Type: application/json" \
  -d @monitoring/alert-rules.json
```

#### 4.3 通知升级完成
```bash
# 发送升级完成通知
curl -X POST http://localhost:3000/api/annotations \
  -H "Content-Type: application/json" \
  -d '{
    "text": "系统升级完成，服务已恢复正常",
    "tags": ["upgrade", "completed"],
    "time": '$(date +%s000)'
  }'

# 通知相关人员
echo "BPF-Grafana集成系统升级完成" | mail -s "升级通知" <EMAIL>
```

## 🔄 回滚操作步骤

### 触发回滚条件
- 服务启动失败超过30分钟
- 关键功能验证失败
- 性能指标严重下降
- 业务方要求紧急回滚

### 快速回滚步骤

#### 1. 停止当前服务
```bash
docker-compose down
```

#### 2. 恢复镜像版本
```bash
# 回滚到备份版本
docker tag bbpf/grafana-proxy:backup-$(date +%Y%m%d) bbpf/grafana-proxy:latest
docker tag grafana/grafana-enterprise:backup-$(date +%Y%m%d) grafana/grafana-enterprise:latest
```

#### 3. 恢复配置文件
```bash
cp config/application-prod.yml.backup config/application-prod.yml
cp docker-compose.yml.backup docker-compose.yml
```

#### 4. 恢复数据库
```bash
# 如果数据库有变更，恢复备份
mysql -h mysql-master -u root -p bbpf_grafana < $BACKUP_DIR/mysql_backup.sql
mysql -h mysql-master -u root -p grafana < $BACKUP_DIR/grafana_backup.sql
```

#### 5. 重启服务
```bash
docker-compose up -d
```

#### 6. 验证回滚
```bash
# 检查服务状态
curl -f http://localhost:8080/actuator/health

# 验证核心功能
./scripts/smoke-test.sh
```

## 📊 升级验收标准

### 功能验收
- [ ] 用户登录功能正常
- [ ] JWT认证机制工作正常
- [ ] 权限控制精确有效
- [ ] SQL拦截功能正常
- [ ] iframe集成功能正常
- [ ] 数据导出功能正常
- [ ] 多租户隔离有效

### 性能验收
- [ ] 页面加载时间 < 3秒
- [ ] API响应时间 < 500ms
- [ ] SQL拦截处理时间 < 50ms
- [ ] 系统可用性 > 99.9%
- [ ] 内存使用率 < 80%
- [ ] CPU使用率 < 70%

### 安全验收
- [ ] HTTPS通信正常
- [ ] JWT Token安全机制有效
- [ ] 权限控制无绕过风险
- [ ] SQL注入防护有效
- [ ] 访问日志记录完整

## 🚨 应急处理预案

### 常见问题处理

#### 1. 服务启动失败
```bash
# 检查日志
docker-compose logs grafana-proxy
docker-compose logs grafana

# 检查端口占用
netstat -tlnp | grep :8080
netstat -tlnp | grep :3000

# 检查磁盘空间
df -h

# 重启服务
docker-compose restart
```

#### 2. 数据库连接失败
```bash
# 检查数据库状态
mysqladmin -h mysql-master ping

# 检查连接配置
cat config/application-prod.yml | grep datasource

# 重启数据库
docker-compose restart mysql
```

#### 3. Redis连接失败
```bash
# 检查Redis状态
redis-cli -h redis-master ping

# 检查Redis配置
cat config/application-prod.yml | grep redis

# 重启Redis
docker-compose restart redis
```

#### 4. 权限验证失败
```bash
# 清除权限缓存
redis-cli -h redis-master FLUSHDB

# 重新加载权限配置
curl -X POST http://localhost:8080/admin/reload-permissions

# 检查权限配置
mysql -h mysql-master -u root -p -e "SELECT * FROM user_permissions LIMIT 5;" bbpf_grafana
```

### 联系方式
- **技术负责人**: 张三 (13800138000)
- **运维工程师**: 李四 (13800138001)
- **DBA**: 王五 (13800138002)
- **安全工程师**: 赵六 (13800138003)
- **业务负责人**: 钱七 (13800138004)

## 📝 升级记录模板

```
升级记录
========
升级日期: ____年__月__日
升级时间: __:__ - __:__
升级版本: v__.__.__
升级负责人: ________

升级内容:
- [ ] 功能更新: ________________
- [ ] 性能优化: ________________
- [ ] 安全加固: ________________
- [ ] Bug修复: ________________

验收结果:
- [ ] 功能验收: 通过/失败
- [ ] 性能验收: 通过/失败
- [ ] 安全验收: 通过/失败

问题记录:
问题1: ________________________
解决方案: ______________________

问题2: ________________________
解决方案: ______________________

升级总结:
_________________________________
_________________________________

签字确认:
技术负责人: ________  日期: ______
运维负责人: ________  日期: ______
业务负责人: ________  日期: ______
```

---

**文档版本**: V1.0  
**编写日期**: 2024年6月30日  
**编写人员**: 运维团队  
**审核状态**: 待审核  
**下次更新**: 升级完成后更新实际操作记录