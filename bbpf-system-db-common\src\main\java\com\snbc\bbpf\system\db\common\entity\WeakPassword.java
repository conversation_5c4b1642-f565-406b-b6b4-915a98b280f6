/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 弱密码管理对象 t_weak_password
 *
 * <AUTHOR>
 * @date 2023-01-16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WeakPassword {

    /** 主键id */
    private String id;
    /** 若密码 */
    private String pwd;
    /** 创建人 */
    private String createUserId;
    /** 创建时间 */
    private LocalDateTime createTime;
    /** 更新人 */
    private String updateUserId;
    /** 更新时间 */
    private LocalDateTime updateTime;


}
