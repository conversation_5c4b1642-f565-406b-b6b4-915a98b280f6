<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snbc.bbpf.system.db.common.mapper.UserRoleMapper" >
  <resultMap id="BaseResultMap" type="com.snbc.bbpf.system.db.common.entity.UserRole" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <id column="user_id" property="userId" jdbcType="VARCHAR" />
    <id column="role_id" property="roleId" jdbcType="VARCHAR" />
  </resultMap>

  <delete id="deleteByPrimaryKey" parameterType="com.snbc.bbpf.system.db.common.entity.UserRole" >
    delete from t_user_role where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.snbc.bbpf.system.db.common.entity.UserRole" >
    insert into t_user_role (id,user_id, role_id)
    values (#{id,jdbcType=VARCHAR},#{userId,jdbcType=VARCHAR}, #{roleId,jdbcType=VARCHAR})
  </insert>
  <delete id="deleteByUserId" parameterType="java.lang.String">
		DELETE FROM t_user_role WHERE user_id = #{userId,jdbcType=VARCHAR}
  </delete>

  <select id="queryRoleListByRoleId" resultMap="BaseResultMap"
		parameterType="java.lang.String">
		SELECT
		user_id,role_id
		FROM t_user_role
		WHERE role_id = #{roleId,jdbcType=VARCHAR}
	</select>

    <select id="selectUserCountByRole" parameterType="java.lang.String" resultType="java.lang.Integer">
    select count(0) from t_user_role where role_id = #{roleId,jdbcType=VARCHAR}
  </select>

    <insert id="insertRelation" parameterType="java.util.List">
        delete from t_user_role where id in
        <foreach collection="list" item="item" index="index"  open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>;
        insert into t_user_role (id,user_id, role_id)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix=" (" suffix=")" suffixOverrides=",">
                #{item.id,jdbcType=VARCHAR},
                #{item.userId,jdbcType=VARCHAR},
                #{item.roleId,jdbcType=VARCHAR}
            </trim>
        </foreach>
    </insert>

    <select id="selectAlreadyUserRole" parameterType="java.util.List" resultType="com.snbc.bbpf.system.db.common.entity.UserRole">
        SELECT
            id,
        user_id as userId,
        role_id as roleId
        FROM
          t_user_role
        WHERE (user_id, role_id) IN
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            (#{item.userId,jdbcType=VARCHAR},
            #{item.roleId,jdbcType=VARCHAR})
        </foreach>
    </select>

    <select id="selectRoleByUserId" resultType="java.lang.String">
        select role_id from t_user_role where user_id = #{userId,jdbcType = VARCHAR}
    </select>

    <delete id="deleteRelation" parameterType="java.util.List">
        delete from t_user_role where
        <foreach collection="list" item="item" separator=" or " index="index">
            (user_id = #{item.userId,jdbcType=VARCHAR}
            and role_id = #{item.roleId,jdbcType=VARCHAR})
        </foreach>
    </delete>
</mapper>
