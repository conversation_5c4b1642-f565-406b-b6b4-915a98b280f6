Head() ::= <<
<?xml version="1.0"?>
<?mso-application progid="Excel.Sheet"?>
<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:o="urn:schemas-microsoft-com:office:office"
 xmlns:x="urn:schemas-microsoft-com:office:excel"
 xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:html="http://www.w3.org/TR/REC-html40">
 <DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">
  <Created>1996-12-17T01:32:42Z</Created>
  <LastSaved>2013-08-02T09:21:24Z</LastSaved>
  <Version>16.00</Version>
 </DocumentProperties>
 <OfficeDocumentSettings xmlns="urn:schemas-microsoft-com:office:office">
  <RemovePersonalInformation/>
 </OfficeDocumentSettings>
 <ExcelWorkbook xmlns="urn:schemas-microsoft-com:office:excel">
  <WindowHeight>7560</WindowHeight>
  <WindowWidth>20490</WindowWidth>
  <WindowTopX>0</WindowTopX>
  <WindowTopY>0</WindowTopY>
  <ProtectStructure>False</ProtectStructure>
  <ProtectWindows>False</ProtectWindows>
 </ExcelWorkbook>
 <Styles>
  <Style ss:ID="Default" ss:Name="Normal">
   <Alignment ss:Vertical="Bottom"/>
   <Borders/>
   <Font ss:FontName="宋体" x:CharSet="134" ss:Size="12"/>
   <Interior/>
   <NumberFormat/>
   <Protection/>
  </Style>
  <Style ss:ID="s84">
   <Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s105">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Font ss:FontName="宋体" x:CharSet="134" ss:Size="12" ss:Bold="1"/>
   <Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s106">
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="宋体" x:CharSet="134" ss:Size="12" ss:Color="#000000"
    ss:Bold="1"/>
   <Interior ss:Color="#9BC2E6" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s107">
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="宋体" x:CharSet="134" ss:Size="12" ss:Bold="1"/>
   <Interior ss:Color="#9BC2E6" ss:Pattern="Solid"/>
  </Style>
 </Styles>
                  >>
BodyWeek(operator) ::= <<
$operator:{
<Worksheet ss:Name="$operator.sheet$">
<Table ss:ExpandedColumnCount="7" ss:ExpandedRowCount="66000" x:FullColumns="1"
   x:FullRows="1" ss:DefaultColumnWidth="54" ss:DefaultRowHeight="14.25">
   <Column ss:AutoFitWidth="0" ss:Width="73.5"/>
   <Column ss:Index="4" ss:AutoFitWidth="0" ss:Width="75"/>
   <Column ss:AutoFitWidth="0" ss:Width="83.25"/>
   <Column ss:AutoFitWidth="0" ss:Width="108"/>
   <Column ss:AutoFitWidth="0" ss:Width="78.75"/>
   <Row ss:AutoFitHeight="0" ss:Height="32.25">
    <Cell ss:MergeAcross="6" ss:StyleID="s105"><Data ss:Type="String">$operator.title$</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20.25" ss:StyleID="s84">
    <Cell ss:StyleID="s106"><Data ss:Type="String">日志时间</Data></Cell>
    <Cell ss:StyleID="s107"><Data ss:Type="String">操作员</Data></Cell>
    <Cell ss:StyleID="s107"><Data ss:Type="String">登录IP</Data></Cell>
    <Cell ss:StyleID="s107"><Data ss:Type="String">操作类型</Data></Cell>
    <Cell ss:StyleID="s107"><Data ss:Type="String">操作模块</Data></Cell>
    <Cell ss:StyleID="s107"><Data ss:Type="String">日志内容</Data></Cell>
    <Cell ss:StyleID="s107"><Data ss:Type="String">备注</Data></Cell>
   </Row>
   $operator.logs:{it|
   <Row>
        <Cell><Data ss:Type="String">$it.createTime$</Data></Cell>
        <Cell><Data ss:Type="String">$it.userName$</Data></Cell>
        <Cell><Data ss:Type="String">$it.ip$</Data></Cell>
        <Cell><Data ss:Type="String">$it.logType$</Data></Cell>
        <Cell><Data ss:Type="String">$it.logTarget$</Data></Cell>
        <Cell><Data ss:Type="String">$it.logContent$</Data></Cell>
        <Cell><Data ss:Type="String">$it.remarks$</Data></Cell>
   </Row>}$
  </Table>
</Worksheet>}$
                     >>