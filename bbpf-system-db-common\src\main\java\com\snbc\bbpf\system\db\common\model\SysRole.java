/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.model;

import com.snbc.bbpf.buslog.annotations.DiffKey;
import com.snbc.bbpf.buslog.annotations.UnionDisplayKey;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @ClassName: SysRole
 * @Description: 用于前端传参
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/5/19
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SysRole {
    private String roleId;

    @NotBlank(message = "角色名称不能为空")
    @DiffKey(name = "角色名称",enName = "Role Name")
    @UnionDisplayKey
    private String roleName;
    @DiffKey(name = "角色描述",enName = "Role Desc")
    private String roleDesc;
    @DiffKey(name = "权限id",enName = "Permission Id")
    private String[] permissionIds;
    /**
     * 所属组织机构id
     * */
    @DiffKey(name = "所属组织机构",enName = "Affiliated Org")
    @NotBlank(message = "所属组织机构不能为空")
    private String belongOrgIds;
}
