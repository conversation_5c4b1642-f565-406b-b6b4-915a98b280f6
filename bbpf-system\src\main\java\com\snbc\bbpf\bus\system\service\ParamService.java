/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service;

import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.system.db.common.entity.Param;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: Param
 * @Description: 参数配置具体接口类
 * @module: si-bbpf-system
 * @Author: wjc1
 * @date: 2021/5/17 14:23:00
 */
public interface ParamService {
	/**
	 * 添加配置信息
	 * @see Param
	 * @return 影响行数
	 * @throws BusinessException
	 */
	void saveOrUpdateParam(Param param)throws BusinessException;

	/**
	 * 批量删除param
	 * @param paramIds  由 逗号 拼接的主键id
	 * @return 影响行数
	 */
	void batchDeleteParam(String paramIds)  throws BusinessException;
	/**
	 * 根据配置ID查询基本信息
	 * @param paramId 主键id
	 * @return Param
	 */
	Param getParamByPrimary(String paramId) throws BusinessException;
	/**
	 * 根据配置编码查询基本信息
	 * @param paramCode 配置编码
	 * @return Param
	 */
	Param getParamByCode(String paramCode) throws BusinessException;

	/**
	 * 根据查询条件获取配置列表
	 * @param map map集合
	 * @return
	 */
	List<Param> getParamByMap(Map<String, Object> map) throws BusinessException;

    /**
     * 获取所有的paramcode
     * @return
     */
	List<String> queryParamCode() throws BusinessException;

	PageInfo<Param> queryParamListPage(String paramName, String paramTypeName ,Integer pageNum, Integer pageSize)throws BusinessException;

}
