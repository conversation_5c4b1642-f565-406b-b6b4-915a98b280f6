/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service;


import com.snbc.bbpf.system.db.common.dto.CheckSmsCodeDto;
import com.snbc.bbpf.system.db.common.entity.login.ReturnUser;
import com.snbc.bbpf.system.db.common.vo.QrcodeVo;
import com.snbc.bbpf.system.db.common.vo.UserBindVo;

import java.util.List;


/**
 * @ClassName: ThirdLoginService
 * @Description: 用户三方扫码登录业务处理,
 * 用户绑定、解绑、查询二维码状态、获取二维码，获取二维码绑定情况
 * 更改
 * @module: si-bbpf-umis
 * @Author: liangjb
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
public interface ThirdLoginService {

    /**
     * 获得用户绑定二维码状态
     * @param bindId
     * @return
     * @throws Exception
     */
    String getUserBindQRcodeState(String bindId) throws Exception;

    /**
     * 检验手机验证码获得用户绑定二维码
     * @param checkSmsCodeDto
     * @return
     * @throws Exception
     */
    QrcodeVo checkSmsCode( CheckSmsCodeDto checkSmsCodeDto) throws Exception;
    /**
     * 解除用户绑定
     * @param bindType
     * @return
     * @throws Exception
     */
    String delUserThirdBind(String bindType) throws Exception;


    /**
     * 获得用户登录二维码状态
     * @param bindId
     * @return
     * @throws Exception
     */
    ReturnUser getUserLoginQRcodeState(String bindId) throws Exception ;

    /**
     * 获得用户登录二维码
     * @param thirdType
     * @return
     * @throws Exception
     */
    QrcodeVo getUserLoginQRcode(String thirdType) throws Exception ;

    /**
     * 获得用户登录绑定详情
     * @return
     * @throws Exception
     */
    List<UserBindVo> getUserBindInfo() throws Exception ;
}
