/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ProjectName: bus-module-sys
 * @Package: com.snbc.bbpf.organize.domain.entity.organization
 * @ClassName: OrgSort
 * @Description: java类作用描述
 * @Author: wangsong
 * @CreateDate: 2020/2/28 16:13
 * @UpdateUser: wangsong
 * @UpdateDate: 2020/2/28 16:13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrgSort {

    private String thisNodeId;

    private String targetNodeId;

    private String parentId;

    private String sortType;
}
