package com.snbc.bbpf.bus.system.service.impl;

import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.system.constans.CommonConstant;
import com.snbc.bbpf.bus.system.service.OrgService;
import com.snbc.bbpf.bus.system.utils.AllOrg;
import com.snbc.bbpf.bus.system.utils.BossDES3Util;
import com.snbc.bbpf.bus.system.utils.CheckOrgPermission;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.system.db.common.dto.AdjustDepartmentDto;
import com.snbc.bbpf.system.db.common.dto.OrgNamesDto;
import com.snbc.bbpf.system.db.common.dto.RoleNamesDto;
import com.snbc.bbpf.system.db.common.entity.UserOrg;
import com.snbc.bbpf.system.db.common.mapper.OrgMapper;
import com.snbc.bbpf.system.db.common.mapper.RoleMapper;
import com.snbc.bbpf.system.db.common.mapper.UserMapper;
import com.snbc.bbpf.system.db.common.mapper.UserOrgMapper;
import com.snbc.bbpf.system.db.common.vo.OrgUserPageVo;
import com.snbc.bbpf.system.db.common.vo.OrgVo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * @ClassName: UserOptServiceImplTest
 * 调整部门
 * @module: si-bbpf-system
 * @Author: wjc1
 * @date: 2021/7/7 16:17
 */
class UserOptServiceImplTest {
    @Mock
    private UserMapper userMapper;
    @Mock
    private UserOrgMapper userOrgMapper;
    @Mock
    private OrgService orgService;
    @InjectMocks
    private UserOptServiceImpl userOptServiceImpl;
    @Mock
    private RedisTemplate redisTemplate;
    @Mock
    CheckOrgPermission checkOrgPermission;
    @Mock
    private OrgMapper orgMapper;
    @Mock
    private AllOrg allOrg;
    @Mock
    private RoleMapper roleMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("测试调整部门-根组织机构不移除")
    @Tag("@id:28019")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/6")
    void testAdjustDepartment_orgId_0() {
        try(MockedStatic<CurrentUser> currentUserMockedStatic = Mockito.mockStatic(CurrentUser.class)) {
            //组装数据
            AdjustDepartmentDto dto = new AdjustDepartmentDto();
            dto.setUserIds("a,b,c");
            dto.setOldOrgId("0");
            dto.setNewOrgIds("cd,ef");

            when(userOrgMapper.insertOrUpdate(any(UserOrg.class))).thenReturn(1);
            when(userOrgMapper.deleteByOrgIdUserId(anyString(), anyString())).thenReturn(0);

            userOptServiceImpl.adjustDepartment(dto);
            verify(userOrgMapper, times(0)).deleteByOrgIdUserId(anyString(), anyString());
            int insertNum = dto.getUserIds().split(CommonConstant.JWT_FILTER_CHAR).length * dto.getNewOrgIds().split(CommonConstant.JWT_FILTER_CHAR).length;
            verify(userOrgMapper, times(insertNum)).insertOrUpdate(any(UserOrg.class));
        }
    }

    @Test
    @DisplayName("测试调整部门-非根组织机构")
    @Tag("@id:28019")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/6")
    void testAdjustDepartment_orgId_not_0() {
        try(MockedStatic<CurrentUser> currentUserMockedStatic = Mockito.mockStatic(CurrentUser.class)) {
            //组装数据
            AdjustDepartmentDto dto = new AdjustDepartmentDto();
            dto.setUserIds("a,b,c");
            dto.setOldOrgId("ab");
            dto.setNewOrgIds("cd,ef");

            when(userOrgMapper.insertOrUpdate(any(UserOrg.class))).thenReturn(1);
            when(userOrgMapper.deleteByOrgIdUserId(anyString(), anyString())).thenReturn(1);

            userOptServiceImpl.adjustDepartment(dto);
            verify(userOrgMapper, times(dto.getUserIds().split(CommonConstant.JWT_FILTER_CHAR).length)).deleteByOrgIdUserId(anyString(), anyString());
            int insertNum = dto.getUserIds().split(CommonConstant.JWT_FILTER_CHAR).length * dto.getNewOrgIds().split(CommonConstant.JWT_FILTER_CHAR).length;
            verify(userOrgMapper, times(insertNum)).insertOrUpdate(any(UserOrg.class));
        }
    }

    @Test
    @DisplayName("测试用户列表_组织机构id错误")
    @Tag("@id:23524")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/6")
    void testQueryUserListPage_orgId_error() {
        when(userMapper.selectUserRole4DataRule()).thenReturn(Collections.<Map<String, String>>singletonList(new HashMap<String, String>() {{
            put("String", "String");
        }}));
        when(userMapper.selectUserOrg4DataRule()).thenReturn(Collections.<Map<String, String>>singletonList(new HashMap<String, String>() {{
            put("String", "String");
        }}));
        when(orgService.getOrgListById(anyString(), anyString())).thenReturn(null);
        try {
            PageInfo<OrgUserPageVo> result = userOptServiceImpl.queryUserListPage("orgId", 0, 0, "queryParam");
        } catch (Exception e) {
            //Assertions.fail("businessException is not expected"+e);
            //Assertions.assertEquals(ErrorMessage.ORG_NOT_EXIST.getMessage(),e.getMessage());
        }
    }

    //@Test
    @DisplayName("测试用户列表_查询条件是完整手机号_解密失败")
    @Tag("@id:23524")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/6")
    void testQueryUserListPage() throws Exception {
        when(userMapper.selectUserRole4DataRule()).thenReturn(Collections.<Map<String, String>>singletonList(new HashMap<String, String>() {{
            put("String", "String");
        }}));
        when(userMapper.selectUserOrg4DataRule()).thenReturn(Collections.<Map<String, String>>singletonList(new HashMap<String, String>() {{
            put("String", "String");
        }}));
        OrgUserPageVo orgUserPageVo = new OrgUserPageVo();
        orgUserPageVo.setOrgNames("ceshi001,ceshi002");
        orgUserPageVo.setRoleNames("角色1,角色2");
        orgUserPageVo.setEmail("<EMAIL>");
        orgUserPageVo.setJobNumber("0012");
        orgUserPageVo.setUserId("0");
        orgUserPageVo.setPhone("U2383HDH623");

        OrgNamesDto orgNamesDto = new OrgNamesDto();
        orgNamesDto.setUserId("0");
        orgNamesDto.setOrgNames("组织机构1");
        when(orgMapper.selectOrgNamesByUserId(Collections.singletonList("0"))).thenReturn(Collections.singletonList(orgNamesDto));
        RoleNamesDto roleNamesDto = new RoleNamesDto();
        roleNamesDto.setUserId("0");
        roleNamesDto.setRoleNames("角色1");
        when(roleMapper.selectRoleNamesByUserId(Collections.singletonList("0"))).thenReturn(Collections.singletonList(roleNamesDto));

        when(userMapper.queryUserListPage(any(), anyString(), anyString())).thenReturn(Collections.singletonList(orgUserPageVo));
        when(orgService.getOrgListById(anyString(), anyString())).thenReturn(Collections.singletonList(new OrgVo()));

        try (MockedStatic<CurrentUser> ms = Mockito.mockStatic(CurrentUser.class)) {
            List<String> orgPathList = new ArrayList<>();
            orgPathList.add("/0");
            Mockito.when(allOrg.getOrgPathList(Mockito.any(), Mockito.any())).thenReturn(orgPathList);
            String queryParam = "***********";
            PageInfo<OrgUserPageVo> result = userOptServiceImpl.queryUserListPage("orgId", 0, 10, queryParam);
        } catch (Exception e) {
            Assertions.fail("businessException is not expected" + e);
        }
    }

    //@Test
    @DisplayName("测试用户列表_查询条件是完整手机号_解密失败")
    @Tag("@id:23524")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/6")
    void testQueryUserListPage_success() throws Exception {
        when(userMapper.selectUserRole4DataRule()).thenReturn(Collections.<Map<String, String>>singletonList(new HashMap<String, String>() {{
            put("String", "String");
        }}));
        when(userMapper.selectUserOrg4DataRule()).thenReturn(Collections.<Map<String, String>>singletonList(new HashMap<String, String>() {{
            put("String", "String");
        }}));
        OrgUserPageVo orgUserPageVo = new OrgUserPageVo();
        orgUserPageVo.setOrgNames("ceshi001,ceshi002");
        orgUserPageVo.setRoleNames("角色1,角色2");
        orgUserPageVo.setEmail("<EMAIL>");
        orgUserPageVo.setJobNumber("0012");
        orgUserPageVo.setPhone(BossDES3Util.encrypt("***********"));

        when(userMapper.queryUserListPage(any(), anyString(), anyString())).thenReturn(Collections.singletonList(orgUserPageVo));
        when(orgService.getOrgListById(anyString(), anyString())).thenReturn(Collections.singletonList(new OrgVo()));

        try (MockedStatic<CurrentUser> ms = Mockito.mockStatic(CurrentUser.class)) {
            List<String> orgPathList = new ArrayList<>();
            orgPathList.add("");
            Mockito.when(allOrg.getOrgPathList(Mockito.any(), Mockito.any())).thenReturn(orgPathList);
            String queryParam = "***********";
            PageInfo<OrgUserPageVo> result = userOptServiceImpl.queryUserListPage("orgId", 0, 0, queryParam);
            Assertions.assertEquals("150****3970", result.getList().get(0).getPhone());
        } catch (Exception e) {
            Assertions.fail("businessException is not expected" + e);
        }
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
