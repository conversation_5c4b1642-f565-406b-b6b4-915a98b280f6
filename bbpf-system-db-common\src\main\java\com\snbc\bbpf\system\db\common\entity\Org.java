/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
/**
 * @ClassName: Org
 * @Description: 组织机构
 * @module: si-bbpf-system
 * @Author: jiafei
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Org {

    /**
     * 组织机构编号
     */
    private String orgId;
    /**
     * 组织名称
     */
    @NotBlank(message = "组织名称不能为空")
    private String orgName;
    /**
     * 组织编码
     */
    private String orgCode;
    /**
     * 父级组织编号
     */
    private String parentId;
    /**
     * 顺序
     */
    private Integer sequence;
    /**
     * 组织层级 从1开始为顶级
     */
    private Integer orgLevel;
    /**
     * 组织路径
     */
    private String orgPath;
    /**
     * 组织状态  0：禁用 1：启用
     */
    private Integer orgStatus;
    /**
     * 组织描述
     */
    private String orgDesc;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 部门主管
     */
    private String supervisor;
    /**
     * 主管手机号
     */
    private String supervisorPhone;
    /**
     * 创建人id
     */
    private String createUserId;
    /**
     * 创建人所属组织机构
     */
    private String createOrgId;


}
