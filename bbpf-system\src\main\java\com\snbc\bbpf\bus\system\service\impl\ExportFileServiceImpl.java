/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.snbc.bbpf.bus.system.mapstruct.ClassConvertorMapper;
import com.snbc.bbpf.bus.system.service.IExportFileService;
import com.snbc.bbpf.bus.system.utils.AllOrg;
import com.snbc.bbpf.system.db.common.dto.ExportFileDto;
import com.snbc.bbpf.system.db.common.entity.ExportFile;
import com.snbc.bbpf.system.db.common.entity.ExportFileOrg;
import com.snbc.bbpf.system.db.common.mapper.ExportFileMapper;
import com.snbc.bbpf.system.db.common.mapper.ExportFileOrgMapper;
import com.snbc.bbpf.system.db.common.vo.ExportFileQuery;
import com.snbc.bbpf.system.db.common.vo.ExportFileVo;
import com.snbc.bbpf.system.db.common.vo.OrgVo;
import com.snbc.component.filestorage.fileutil.FileCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 导出文件管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-11-29
 */
@Service
@Slf4j
public class ExportFileServiceImpl implements IExportFileService {
    @Autowired
    private ExportFileMapper exportFileMapper;

    @Autowired
    private FileCommonUtil fileCommonUtil;
    @Autowired
    private AllOrg allOrg;
    @Autowired
    private ExportFileOrgMapper exportFileOrgMapper;
    /**
     * 查询导出文件管理
     * 
     * @param exportId 导出文件管理主键
     * @return 导出文件管理
     */
    @Override
    public ExportFileVo selectExportFileByExportId(String exportId) {
        ExportFile exportFile = exportFileMapper.selectExportFileByExportId(exportId);
        return ClassConvertorMapper.INSTANCE.covertExportFileVo(exportFile);
    }

    /**
     * 查询导出文件管理列表
     * 
     * @param exportFileDto 导出文件管理
     * @return 导出文件管理
     */
    @Override
    public PageInfo<ExportFileVo> selectExportFileList(ExportFileQuery exportFileDto) {
        PageMethod.startPage(exportFileDto.getPageNum(), exportFileDto.getPageSize());
        List<ExportFile> list = exportFileMapper.selectExportFileList(exportFileDto);
        PageInfo<ExportFile> page = new PageInfo<>(list);
        PageInfo<ExportFileVo> pageInfo = new PageInfo<>(ClassConvertorMapper.INSTANCE.covertExportFileVoList(list));
        pageInfo.setPageNum(exportFileDto.getPageNum());
        pageInfo.setPageSize(exportFileDto.getPageSize());
        pageInfo.setPages(page.getPages());
        pageInfo.setTotal(page.getTotal());
        pageInfo.calcByNavigatePages(page.getNavigatePages());
        return pageInfo;
    }

    /**
     * 新增导出文件管理
     *
     * @param exportFileDto 导出文件管理
     */
    @Override
    public void insertExportFile(ExportFileDto exportFileDto) {
        ExportFile exportFile = ClassConvertorMapper.INSTANCE.covertExportFile(exportFileDto);
        exportFile.setCreateTime(LocalDateTime.now());
        exportFileMapper.insertExportFile(exportFile);
        // 返回当前人数据权限的组织机构
        List<OrgVo> orgList = allOrg.getDataRuleOrg(exportFile.getCreateUser());
        if (CollectionUtils.isEmpty(orgList)) {
            log.warn("insertExportFile orgList is null");
            return;
        }
        //插入 与组织机构关联表
        List<ExportFileOrg> exportFileOrgList = Lists.newArrayList();
        orgList.forEach(org -> {
            ExportFileOrg exportFileOrg = ExportFileOrg.builder()
                    .exportId(exportFile.getExportId())
                    .orgId(org.getOrgId())
                    .id(UUID.randomUUID().toString()).build();
            exportFileOrgList.add(exportFileOrg);
        });
        exportFileOrgMapper.insert(exportFileOrgList);
    }

    /**
     * 修改导出文件管理
     *
     * @param exportFileDto 导出文件管理
     */
    @Override
    public void updateExportFile(ExportFileDto exportFileDto) {
        ExportFile exportFile = ClassConvertorMapper.INSTANCE.covertExportFile(exportFileDto);
        exportFileMapper.updateExportFile(exportFile);
    }

    /**
     * 批量删除导出文件管理
     * 
     * @param exportIds 需要删除的导出文件管理主键
     * @return 受影响结果数
     */
    @Override
    public int deleteExportFileByExportIds(String[] exportIds) {
        return exportFileMapper.deleteExportFileByExportIds(exportIds);
    }

    /**
     * 删除导出文件管理信息
     * 
     * @param exportId 导出文件管理主键
     * @return 受影响结果数
     */
    @Override
    public int deleteExportFileByExportId(String exportId) {
        ExportFile exportFile= exportFileMapper.selectExportFileByExportId(exportId);
        if(StringUtils.isNotBlank(exportFile.getUrl())) {
            fileCommonUtil.delete(exportFile.getUrl());
        }
        exportFileOrgMapper.delete(exportId);
        return exportFileMapper.deleteExportFileByExportId(exportId);
    }
}
