package com.snbc.bbpf.system.db.common.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CheckSmsCodeVo {
    /**
     * 手机号码
     */
    private String phone;
    /**
     * 短信验证码
     */
    @NotBlank
    private String smsCode;

    /**
     * 系统标识
     */
    @NotNull
    private Integer msgType;

    /**
     * 系统标识
     */
    @NotNull
    private Boolean isDelSmsCode;

    private String userId;
}
