package com.snbc.bbpf.bus.system.service.impl;

import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.bus.system.service.NoticeService;
import com.snbc.bbpf.bus.system.service.OrgService;
import com.snbc.bbpf.bus.system.utils.CheckOrgPermission;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.bus.system.utils.SendMsgUtil;
import com.snbc.bbpf.system.db.common.mapper.UserMapper;
import com.snbc.bbpf.system.db.common.mapper.UserRoleMapper;
import com.snbc.bbpf.system.db.common.vo.NoticeDetailVo;
import com.snbc.bbpf.system.db.common.vo.NoticeOrgVo;
import com.snbc.bbpf.system.db.common.vo.NoticeUserVo;
import com.snbc.bbpf.system.db.common.vo.OrgVo;
import com.snbc.bbpf.system.db.common.vo.OrgsItem;
import com.snbc.bbpf.system.db.common.vo.UsersItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * @ClassName: NoticeServiceImpl
 * @Description: 消息公告实现类
 * @module: SI-bbpf-message-center-opt
 * @Author: wangsong
 * @date: 2023/1/4
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Slf4j
@Service
public class NoticeServiceImpl implements NoticeService {
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private OrgService orgService;
    @Autowired
    private CheckOrgPermission checkOrgPermission;
    @Autowired
    private UserRoleMapper userRoleMapper;
    /***
     * @Description: 信息公告详情
     * @Author: WangSong
     * @param :         msgId
     * @return: com.snbc.bbpf.bus.messagecenter.vo.NoticeDetailVo
     * @CreateDate: 2023/1/5 20:13
     * @UpdateDate: 2023/1/5 20:13
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public NoticeDetailVo noticeDetail(String msgId) {
        CommonResp<NoticeDetailVo> vo = SendMsgUtil.getNoticeDetail(msgId);
        if(!ErrorMessage.SUCCESS.getCode().equals(vo.getHead().getCode())){
            throw  new BusinessException(vo.getHead().getMessage(), vo.getHead().getCode());
        }
        NoticeDetailVo noticeDetailVo = vo.getBody();
        List<String> listRole = userRoleMapper.selectRoleByUserId(CurrentUser.getUserId());
        boolean isAdmin =  !listRole.isEmpty()&&listRole.contains("-1");
        if(!CollectionUtils.isEmpty(noticeDetailVo.getOrgs())){
            List<String> orgIdList = noticeDetailVo.getOrgs().stream().map(OrgsItem::getOrgId).collect(Collectors.toList());
            // 1、 查询信息公告通知的组织机构
            List<NoticeOrgVo> noticeOrgList = orgService.getNoticeOrgName(orgIdList);
            //1.1 查询当前人所拥有数据权限的组织机构
            List<OrgsItem> orgListVo = getOrgsItems(noticeOrgList, isAdmin);
            noticeDetailVo.setOrgs(orgListVo);
        }
        if(!CollectionUtils.isEmpty(noticeDetailVo.getUsers())){
            // 判断用户是否能移除
            //2.查询信息公告的通知人
            List<String> userIdList = noticeDetailVo.getUsers().stream().map(UsersItem::getUserId).collect(Collectors.toList());
            List<NoticeUserVo> noticeUserVoList = userMapper.noticeUserList(userIdList);
            //2.1 查询数据权限下组织机构下的所有用户
            List<UsersItem> userListVo = getUsersItems(isAdmin, noticeUserVoList);
            noticeDetailVo.setUsers(userListVo);
        }
        return noticeDetailVo;
    }

    /**
     * 转换人员
     * @param isAdmin
     * @param noticeUserVoList
     * @return
     */
    @NotNull
    private List<UsersItem> getUsersItems(boolean isAdmin, List<NoticeUserVo> noticeUserVoList) {
        List<Map<String, String>> userDataRule;
        if (!isAdmin) {
            List<OrgVo> orgVoList = checkOrgPermission.checkOrg(CurrentUser.getUserId(), null);
            List<String> ownedOrgIdList = orgVoList.stream().map(OrgVo::getOrgPath).collect(Collectors.toList());
            userDataRule = userMapper.getUserListByOrgAndSub(ownedOrgIdList);
        } else {
            userDataRule = null;
        }
        log.debug("notice detail users data auth is={}",userDataRule);
        List<UsersItem> userListVo = Lists.newArrayList();
        if(!CollectionUtils.isEmpty(noticeUserVoList)){
            noticeUserVoList.forEach(noticeUserVo ->{
                UsersItem orgsItem = UsersItem.builder().userId(noticeUserVo.getUserId()).userName(noticeUserVo.getUserName()).canRemove("false").build();
                //1.2、判断是否能移除
                if(isAdmin || judgeContains(userDataRule,noticeUserVo.getUserId())){
                    orgsItem.setCanRemove("true");
                }
                userListVo.add(orgsItem);
            });
        }
        return userListVo;
    }

    /**
     * 转换 组织机构
     * @param noticeOrgList
     * @param isAdmin
     * @return
     */
    @NotNull
    private List<OrgsItem> getOrgsItems(List<NoticeOrgVo> noticeOrgList, boolean isAdmin) {
        List<Map<String, String>> dataRuleList;
        if (!isAdmin) {
            List<OrgVo> orgVoList = checkOrgPermission.checkOrg(CurrentUser.getUserId(), null);
            dataRuleList = orgVoList.stream().map(org -> {
                Map<String, String> map = new HashMap<>();
                map.put("orgId", org.getOrgId());
                return map;
            }).collect(Collectors.toList());
        } else {
            dataRuleList = null;
        }
        log.debug("notice detail orgList dataAuth is={}",dataRuleList);
        List<OrgsItem> orgListVo = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(noticeOrgList)) {
            noticeOrgList.forEach(noticeOrgVo ->{
                OrgsItem orgsItem = OrgsItem.builder().orgId(noticeOrgVo.getOrgId()).orgName(noticeOrgVo.getOrgName()).canRemove("false").build();
                //1.2、判断是否能移除
                if(isAdmin || judgeContains(dataRuleList,noticeOrgVo.getOrgId())){
                    orgsItem.setCanRemove("true");
                }
                orgListVo.add(orgsItem);
            });
        }
        return orgListVo;
    }

    /**
     * 判断是否  包含返回true,不包含返回false
     * @param mapList
     * @param value
     * @return
     */
    private static boolean judgeContains(List<Map<String, String>> mapList,String value){
        for (Map<String, String> map : mapList) {
            if (map.containsValue(value)){
                return true;
            }
        }
        return false;
    }
}
