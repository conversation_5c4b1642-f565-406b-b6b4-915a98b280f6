/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.vo;

import com.snbc.bbpf.buslog.annotations.DiffKey;
import com.snbc.bbpf.buslog.annotations.convert.DictFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: PermissionStatusVo
 * 权限状态Vo对象
 * @module: bbpf-bus-system
 * @Author: yangweipeng
 * @date: 2021/5/20
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PermissionStatusVo {
    private String permissionId;
    @DiffKey(name = "是否启用",enName = "Enabled")
    @DictFormat(dictJson = "[{'enName':'enabled permission','zhName':'启用权限','value':'1'}," +
            "{'enName':'disabled permission','zhName':'禁用权限','value':'0'}]")
    private String hasEnable;
}
