/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.mapper;

import com.snbc.bbpf.system.db.common.dto.DictValueDto;
import com.snbc.bbpf.system.db.common.entity.DictValue;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * @ClassName: DictValueMapper
 * @Description: 字典值Mapper
 * @module: bbpf-bus-system
 * @Author: liuyi
 * @date: 2021/5/18
 * copyright 2020 barm Inc. All rights reserver
 */
@Mapper
public interface DictValueMapper {

    /**
     * @description:  * @param valueId
     * @return: int
     * @author: liuyi
     * @time: 2021/5/20 10:38
     */
    int deleteByPrimaryKey(String valueId);

    /**
     * @description:  * @param record
     * @return: int
     * @author: liuyi
     * @time: 2021/5/20 10:39
     */
    int insert(DictValue dictValue);

    /**
     * @description:  * @param record
     * @return: int
     * @author: liuyi
     * @time: 2021/5/20 10:39
     */
    int insertSelective(DictValue dictValue);

    /**
     * @description:  * @param record
     * @return: int
     * @author: liuyi
     * @time: 2021/5/20 10:39
     */
    int updateByPrimaryKeySelective(DictValue dictValue);

    /**
     * @description:  * @param dictValueExample
     * @return: java.util.List<com.snbc.bbpf.system.db.common.entity.DictValue>
     * @author: liuyi
     * @time: 2021/5/20 10:39
     */
    List<DictValue> selectByExample(@Param("typeCode") String typeCode,@Param("valueCode") String valueCode,@Param("valueName") String valueName);

    List<DictValue> selectByTypeLike(@Param("typeCode") String typeCode,@Param("parentId") String parentId);

    List<DictValue> selectByValueIds(List<String> valueIds);

    DictValue selectByPrimaryKey(String valueId);
    /**
     * @description:  * @param dictValueExample
     * @return: java.util.List<com.snbc.bbpf.system.db.common.entity.DictValue>
     * @author: liangjunbin
     * @time: 2021/5/20 10:39
     */
    int selectOnlyValue(@Param("typeCode") String typeCode,@Param("valueCode") String valueCode
            ,@Param("valueName") String valueName,@Param("valueId") String valueId);


    List<DictValueDto> getMultipleDictValues(List<String> dictTypeCodes);
}
