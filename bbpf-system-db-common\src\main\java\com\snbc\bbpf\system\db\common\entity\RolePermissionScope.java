/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * @ClassName:      RolePermissionScope.java
 * @Description:    角色、权限、数据权限
 * @module:
 * @Author:         wangsong
 * @date:           2021/5/21 18:17
 * copyright 2020 SNBC. All rights reserver
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RolePermissionScope {
    /**
     * id
     */
    private String id;
    /**
     * 角色id
     */
    private String roleId;
    /**
     * 权限id
     */
    private String permissionId;
    /**
     * 数据权限id
     */
    private String scopeId;
}
