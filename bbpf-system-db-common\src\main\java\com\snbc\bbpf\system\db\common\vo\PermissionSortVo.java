/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: PermissionSortVo
 * 权限树拖拽排序Vo对象
 * @module: bbpf-bus-system
 * @Author: yangweipeng
 * @date: 2021/5/20
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PermissionSortVo {

    private String currentNodeId;

    private String targetNodeId;

    private String parentId;

    private String type;
}
