/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.mapper;

import com.snbc.bbpf.system.db.common.entity.DictType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * @ClassName: DictTypeMapper
 * @Description: 字典类型Mapper
 * @module: bbpf-bus-system
 * @Author: liuyi
 * @date: 2021/5/18
 * copyright 2020 barm Inc. All rights reserver
 */
@Mapper
public interface DictTypeMapper {

    /**
     * @description:  * @param dictTypeExample
     * @return: java.util.List<com.snbc.bbpf.system.db.common.entity.DictType>
     * @author: liuyi
     * @time: 2021/5/20 10:40
     */
    List<DictType> selectByExample(String typeCode);

    /**
     * @description:  * @param typeCode
     * @return: int
     * @author: liuyi
     * @time: 2021/5/20 10:40
     */
    int deleteByTypeCode(String typeCode);

    /**
     * @description:  * @param record
     * @return: int
     * @author: liuyi
     * @time: 2021/5/20 10:40
     */
    int insert(DictType dictType);

    /**
     * @description:  * @param record
     * @return: int
     * @author: liuyi
     * @time: 2021/5/20 10:43
     */
    int insertSelective(DictType dictType);

    /**
     * @description:
     * @param id
     * @return: com.snbc.bbpf.system.db.common.entity.DictType
     * @author: liuyi
     * @time: 2021/6/8 13:21
     */
    DictType selectByPrimaryKey(String id);


    /**
    * @description:  * @param record
    * @return: int
    * @author: liuyi
    * @time: 2021/5/20 10:43
    */
    int updateByPrimaryKeySelective(DictType dictType);


    /**
     * @description:  * @param
     * @return: java.util.List<com.snbc.bbpf.system.db.common.entity.DictType>
     * @author: liuyi
     * @time: 2021/5/20 10:43
     */
    List<DictType> quertAllDictType();

    /**
     * @description:  * @param typeName
     * @return: java.util.List<com.snbc.bbpf.system.db.common.entity.DictType>
     * @author: liuyi
     * @time: 2021/5/20 10:44
     */
    List<DictType> queryDictTypeByName(@Param("typeName") String typeName);
}
