package com.snbc.bbpf.bus.system.pool;

import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 异步执行线程池
 */
public class ThreadPool {
    /**
     * 核心线程数
     */
    private static final int CORE_SIZE = 1;
    /**
     * 线程池大小
     */
    private static final int POOL_SIZE = 5;
    /**
     * 活跃秒数
     */
    private static final long KEEPALIVE = 30L;
    /**
     * 线程队列大小
     */
    private static final int QUEUE_SIZE = 3000;

    /**
     * 初始化一个线程池
     */
    private static ThreadPoolExecutor executor;

    /**
     * 初始化线程池
     */
    private static synchronized void initThreadPool() {
        if (executor == null) {
            executor = new ThreadPoolExecutor(
                    CORE_SIZE,
                    POOL_SIZE,
                    KEEPALIVE,
                    TimeUnit.SECONDS,
                    new LinkedBlockingQueue(QUEUE_SIZE),
                    new ThreadPoolExecutor.CallerRunsPolicy()
            );
        }
    }

    /**
     * 异步执行一个任务
     */
    public static void execute(Runnable command) {
        if (executor == null) {
            initThreadPool();
        }
        executor.execute(command);
    }

    /**
     * 异步执行一个任务 返回future
     */
    public static <V> Future<V> submit(Callable<V> callable) {
        if (executor == null) {
            initThreadPool();
        }
        return executor.submit(callable);
    }
}
