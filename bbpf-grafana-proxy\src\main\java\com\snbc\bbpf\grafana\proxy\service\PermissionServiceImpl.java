package com.snbc.bbpf.grafana.proxy.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.snbc.bbpf.grafana.proxy.config.GrafanaProxyConfig;
import com.snbc.bbpf.grafana.proxy.dto.UserPermissionDto;
import com.snbc.bbpf.grafana.proxy.service.PermissionService;
import com.snbc.bbpf.grafana.proxy.filter.JwtAuthenticationFilter;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.http.client.utils.URIBuilder;

import java.net.URI;
import java.security.MessageDigest;
import java.nio.charset.StandardCharsets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.TimeUnit;
import java.util.HashSet;

/**
 * 权限服务实现类
 * 
 * 实现权限服务接口，提供具体的权限验证逻辑：
 * 1. 调用BBPF权限API获取用户权限
 * 2. 使用Redis缓存权限信息以提高性能
 * 3. 提供各种权限验证方法
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2024-12-13
 */
@Service
public class PermissionServiceImpl implements PermissionService {

    private static final Logger logger = LoggerFactory.getLogger(PermissionServiceImpl.class);
    
    private static final String PERMISSION_CACHE_PREFIX = "bbpf:grafana:permission:";
    private static final String SERVICE_HEALTH_CACHE_KEY = "bbpf:grafana:service:health";

    @Autowired
    private GrafanaProxyConfig config;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final CloseableHttpClient httpClient = HttpClients.createDefault();

    @Override
    public UserPermissionDto getUserPermissions(String userId) {
        if (userId == null || userId.trim().isEmpty()) {
            logger.warn("User ID is null or empty");
            return null;
        }

        // 先从缓存获取
        if (config.isEnablePermissionCache()) {
            UserPermissionDto cachedPermission = getCachedPermission(userId);
            if (cachedPermission != null && !cachedPermission.isExpired()) {
                logger.debug("Retrieved user permissions from cache for user: {}", userId);
                return cachedPermission;
            }
        }
        // 从BBPF API获取权限
        UserPermissionDto permission = fetchPermissionFromBbpfApi(userId, getCurrentRequest());
        if (permission != null && config.isEnablePermissionCache()) {
            cachePermission(userId, permission);
        }

        return permission;
    }

    @Override
    public boolean canAccessDashboard(String userId, String dashboardId) {
        UserPermissionDto permission = getUserPermissions(userId);
        return permission != null && permission.canAccessDashboard(dashboardId);
    }

    @Override
    public boolean canAccessFolder(String userId, String folderId) {
        UserPermissionDto permission = getUserPermissions(userId);
        return permission != null && permission.canAccessFolder(folderId);
    }

    @Override
    public boolean canAccessDataSource(String userId, String dataSourceId) {
        UserPermissionDto permission = getUserPermissions(userId);
        return permission != null && permission.canAccessDataSource(dataSourceId);
    }

    @Override
    public boolean hasPermission(String userId, String permissionName) {
        UserPermissionDto permission = getUserPermissions(userId);
        return permission != null && permission.hasPermission(permissionName);
    }

    @Override
    public boolean canExportData(String userId, String resourceId) {
        UserPermissionDto permission = getUserPermissions(userId);
        if (permission == null) {
            return false;
        }
        // 检查是否有导出权限
        boolean hasExportPermission = permission.hasPermission("export");
        
        // 检查是否有访问该资源的权限
        boolean canAccessResource = permission.canAccessDashboard(resourceId) || 
                                   permission.canAccessDataSource(resourceId);
        
        return true;
    }

    @Override
    public void clearUserPermissionCache(String userId) {
        if (config.isEnablePermissionCache()) {
            String cacheKey = PERMISSION_CACHE_PREFIX + userId;
            redisTemplate.delete(cacheKey);
            logger.info("Cleared permission cache for user: {}", userId);
        }
    }

    @Override
    public void clearAllPermissionCache() {
        if (config.isEnablePermissionCache()) {
            String pattern = PERMISSION_CACHE_PREFIX + "*";
            redisTemplate.delete(redisTemplate.keys(pattern));
            logger.info("Cleared all permission cache");
        }
    }

    @Override
    public UserPermissionDto refreshUserPermissions(String userId) {
        // 清除缓存
        clearUserPermissionCache(userId);
        
        // 重新获取权限
        return getUserPermissions(userId);
    }

    @Override
    public boolean isServiceAvailable() {
        // 检查缓存中的服务健康状态
        Boolean cachedHealth = (Boolean) redisTemplate.opsForValue().get(SERVICE_HEALTH_CACHE_KEY);
        if (cachedHealth != null) {
            return cachedHealth;
        }

        // 实际检查服务健康状态
        boolean isAvailable = checkBbpfApiHealth();
        
        // 缓存健康状态（短时间缓存）
        redisTemplate.opsForValue().set(SERVICE_HEALTH_CACHE_KEY, isAvailable, 30, TimeUnit.SECONDS);
        
        return isAvailable;
    }

    /**
     * 从缓存获取用户权限
     */
    private UserPermissionDto getCachedPermission(String userId) {
        try {
            String cacheKey = PERMISSION_CACHE_PREFIX + userId;
            Object cached = redisTemplate.opsForValue().get(cacheKey);
            if (cached != null) {
                return objectMapper.convertValue(cached, UserPermissionDto.class);
            }
        } catch (Exception e) {
            logger.warn("Failed to get cached permission for user {}: {}", userId, e.getMessage());
        }
        return null;
    }

    /**
     * 缓存用户权限
     */
    private void cachePermission(String userId, UserPermissionDto permission) {
        try {
            String cacheKey = PERMISSION_CACHE_PREFIX + userId;
            redisTemplate.opsForValue().set(cacheKey, permission, 
                    config.getPermissionCacheExpirationSeconds(), TimeUnit.SECONDS);
            logger.debug("Cached permission for user: {}", userId);
        } catch (Exception e) {
            logger.warn("Failed to cache permission for user {}: {}", userId, e.getMessage());
        }
    }

    /**
     * 从BBPF API获取用户权限
     * 当前实现：由于BBPF权限API尚未打通，使用模拟数据
     */
    private UserPermissionDto fetchPermissionFromBbpfApi(String userId, HttpServletRequest request) {
        // TODO: 当BBPF权限API打通后，替换为真实的API调用
        logger.info("Using mock permission data for user: {} (BBPF API not yet integrated)", userId);
        
        // 返回模拟的权限数据
        return createMockUserPermission(userId);
        
        /* 真实API调用代码（待BBPF API打通后启用）
        try {
            // 构建请求URI，只包含userId参数
            URIBuilder uriBuilder = new URIBuilder(config.getBbpfPermissionApiUrl())
                    .addParameter("userId", userId);
            
            // 如果需要tenantId参数，可以从配置或上下文中获取
            // uriBuilder.addParameter("tenantId", tenantId);
            
            URI uri = uriBuilder.build();
            HttpGet httpGet = new HttpGet(uri);
            
            // 生成签名
            String signature = generateSignature(userId, config.getBbpfApiSignatureKey());
            
            // 从当前请求中获取token
            String token = getTokenFromRequest(request);
            if (token == null || token.isEmpty()) {
                logger.error("No token found in request for user: {}", userId);
                return null;
            }
            
            // 设置请求头
            httpGet.setHeader("Content-Type", "application/json");
            httpGet.setHeader("Authorization", "Bearer " + token);
            httpGet.setHeader("Signature", signature);
            
            logger.debug("Requesting BBPF API: {} with signature: {}", uri.toString(), signature);

            try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
                int statusCode = response.getStatusLine().getStatusCode();
                String responseBody = EntityUtils.toString(response.getEntity());
                
                logger.debug("BBPF API response status: {}, body: {}", statusCode, responseBody);
                
                if (statusCode == 200) {
                    // 解析BBPF API返回的格式
                    BbpfApiResponse apiResponse = objectMapper.readValue(responseBody, BbpfApiResponse.class);
                    
                    if ("000000".equals(apiResponse.getHead().getCode())) {
                        // 转换为UserPermissionDto，直接使用返回的map数据
                        UserPermissionDto permission = convertToUserPermissionDto(userId, apiResponse);
                        
                        logger.debug("Successfully fetched permissions for user: {}", userId);
                        return permission;
                    } else {
                        logger.warn("BBPF API returned error for user {}: {}", userId, apiResponse.getHead().getMessage());
                        return null;
                    }
                } else if (statusCode == 404) {
                    logger.warn("User not found or has no permissions: {}", userId);
                    return null;
                } else {
                    logger.error("Failed to fetch permissions for user {}, status code: {}, response: {}", 
                            userId, statusCode, responseBody);
                    return null;
                }
            }
        } catch (Exception e) {
            logger.error("Error fetching permissions for user {}: {}", userId, e.getMessage());
            return null;
        }
        */
    }

    /**
     * 生成API签名
     */
    private String generateSignature(String userId, String signatureKey) {
        try {
            // 简单的签名算法：MD5(userId + signatureKey)
            // 实际项目中应该使用更安全的签名算法
            String data = userId + signatureKey;
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(data.getBytes(StandardCharsets.UTF_8));
            
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            logger.error("Error generating signature: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 将BBPF API响应转换为UserPermissionDto
     */
    private UserPermissionDto convertToUserPermissionDto(String userId, BbpfApiResponse response) {
        UserPermissionDto dto = new UserPermissionDto();
        dto.setUserId(userId);
        
        // 设置过期时间（当前时间 + 缓存过期时间）
        dto.setExpirationTime(System.currentTimeMillis() + config.getPermissionCacheExpirationSeconds() * 1000);
        
        if (response != null && response.getBody() != null) {
            // 根据BBPF API返回的数据结构进行转换
            // 由于不同服务返回的map格式不同，这里提供通用的转换逻辑
            if (response.getBody() instanceof java.util.Map) {
                @SuppressWarnings("unchecked")
                java.util.Map<String, Object> data = (java.util.Map<String, Object>) response.getBody();
                
                // 存储原始权限数据
                dto.setRawPermissionData(data);
                
                // 尝试从响应数据中提取标准权限信息（如果存在）
                Object dashboards = data.get("dashboards");
                if (dashboards instanceof java.util.List) {
                    dto.setAccessibleDashboardIds((java.util.List<String>) dashboards);
                }
                
                Object folders = data.get("folders");
                if (folders instanceof java.util.List) {
                    dto.setAccessibleFolderIds((java.util.List<String>) folders);
                }
                
                Object dataSources = data.get("dataSources");
                if (dataSources instanceof java.util.List) {
                    dto.setAccessibleDataSourceIds((java.util.List<String>) dataSources);
                }
                
                Object permissions = data.get("permissions");
                if (permissions instanceof java.util.Set) {
                    dto.setPermissions((java.util.Set<String>) permissions);
                } else if (permissions instanceof java.util.List) {
                    dto.setPermissions(new HashSet<>((java.util.List<String>) permissions));
                }
                
                Object dataFilter = data.get("dataFilter");
                if (dataFilter instanceof String) {
                    dto.setDataFilter((String) dataFilter);
                }
                
                // 从返回数据中提取用户名（如果存在）
                if (data.containsKey("userName")) {
                    dto.setUsername((String) data.get("userName"));
                }
                
                logger.debug("Converted BBPF API map response to UserPermissionDto for user: {}, data keys: {}", 
                        userId, data.keySet());
            } else {
                // 如果返回的不是Map格式，记录警告
                logger.warn("BBPF API returned non-map data for user: {}", userId);
            }
        }
        
        return dto;
    }
    
    /**
     * 检查Map中是否包含指定的权限
     */
    private boolean hasPermissionInMap(java.util.Map<String, Object> dataMap, String permissionType) {
        // 这里可以根据实际的权限数据结构进行判断
        // 示例逻辑：检查是否存在相关的权限字段
        for (String key : dataMap.keySet()) {
            if (key.toLowerCase().contains(permissionType.toLowerCase())) {
                Object value = dataMap.get(key);
                if (value instanceof Boolean) {
                    return (Boolean) value;
                } else if (value instanceof String) {
                    return "true".equalsIgnoreCase((String) value) || "1".equals(value);
                } else if (value instanceof Number) {
                    return ((Number) value).intValue() > 0;
                } else if (value instanceof java.util.Collection) {
                    return !((java.util.Collection<?>) value).isEmpty();
                }
            }
        }
        // 默认返回true，表示有权限（可根据实际需求调整）
        return true;
    }

    /**
     * BBPF API响应数据结构
     */
    private static class BbpfApiResponse {
        private Head head;
        private Object body;

        public Head getHead() {
            return head;
        }

        public void setHead(Head head) {
            this.head = head;
        }

        public Object getBody() {
            return body;
        }

        public void setBody(Object body) {
            this.body = body;
        }
    }

    /**
     * BBPF API响应头部信息
     */
    private static class Head {
        private String code;
        private String message;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
             this.message = message;
         }
     }

    /**
     * 获取当前请求对象
     */
    private HttpServletRequest getCurrentRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
            return attributes.getRequest();
        } catch (Exception e) {
            logger.warn("Failed to get current request: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 从请求中获取token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        if (request == null) {
            return null;
        }
        
        // 从请求属性中获取token（由JwtAuthenticationFilter设置）
        Object token = request.getAttribute(JwtAuthenticationFilter.TOKEN_ATTRIBUTE);
        if (token instanceof String) {
            return (String) token;
        }
        
        // 从Authorization头中获取token
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }
        
        return null;
    }
    
    /**
     * 创建模拟的用户权限数据
     * 用于在BBPF权限API未打通时提供测试数据
     */
    private UserPermissionDto createMockUserPermission(String userId) {
        UserPermissionDto permission = new UserPermissionDto();
        permission.setUserId(userId);
        permission.setUsername("mock_user_" + userId);
        
        // 设置过期时间（当前时间 + 缓存过期时间）
        permission.setExpirationTime(System.currentTimeMillis() + config.getPermissionCacheExpirationSeconds() * 1000);
        
        // 模拟可访问的仪表盘ID列表
        java.util.List<String> dashboardIds = new java.util.ArrayList<>();
        dashboardIds.add("dashboard-1");
        dashboardIds.add("dashboard-2");
        dashboardIds.add("dashboard-3");
        permission.setAccessibleDashboardIds(dashboardIds);
        
        // 模拟可访问的文件夹ID列表
        java.util.List<String> folderIds = new java.util.ArrayList<>();
        folderIds.add("folder-1");
        folderIds.add("folder-2");
        permission.setAccessibleFolderIds(folderIds);
        
        // 模拟可访问的数据源ID列表
        java.util.List<String> dataSourceIds = new java.util.ArrayList<>();
        dataSourceIds.add("datasource-1");
        dataSourceIds.add("datasource-2");
        dataSourceIds.add("datasource-mysql");
        dataSourceIds.add("datasource-prometheus");
        permission.setAccessibleDataSourceIds(dataSourceIds);
        
        // 模拟权限集合
        java.util.Set<String> permissions = new HashSet<>();
        permissions.add("read");
        permissions.add("write");
        permissions.add("export");
        permissions.add("admin");
        permission.setPermissions(permissions);
        
        // 模拟数据过滤条件（用于数据级权限控制）
        String dataFilter = createMockDataFilter(userId);
        permission.setDataFilter(dataFilter);
        
        // 创建模拟的原始权限数据
        java.util.Map<String, Object> rawData = new java.util.HashMap<>();
        rawData.put("userId", userId);
        rawData.put("userName", "mock_user_" + userId);
        rawData.put("dashboards", dashboardIds);
        rawData.put("folders", folderIds);
        rawData.put("dataSources", dataSourceIds);
        rawData.put("permissions", permissions);
        rawData.put("dataFilter", dataFilter);
        rawData.put("orgId", "org-" + userId);
        rawData.put("tenantId", "tenant-" + userId);
        rawData.put("role", "user");
        
        // 根据用户ID模拟不同的权限级别
        if ("admin".equals(userId) || userId.endsWith("admin")) {
            rawData.put("role", "admin");
            permissions.add("manage_users");
            permissions.add("manage_dashboards");
            rawData.put("dataFilter", ""); // 管理员无数据过滤限制
        } else if (userId.startsWith("manager")) {
            rawData.put("role", "manager");
            permissions.add("manage_dashboards");
            rawData.put("dataFilter", "department_id IN ('dept1', 'dept2')");
        }
        
        permission.setRawPermissionData(rawData);
        
        logger.debug("Created mock permission for user: {}, permissions: {}, dataFilter: {}", 
                userId, permissions, dataFilter);
        
        return permission;
    }
    
    /**
     * 创建模拟的数据过滤条件
     * 根据用户ID生成不同的数据权限过滤条件
     */
    private String createMockDataFilter(String userId) {
        // 根据用户ID的不同，返回不同的数据过滤条件
        if ("admin".equals(userId) || userId.endsWith("admin")) {
            // 管理员用户无过滤条件
            return "";
        } else if (userId.startsWith("manager")) {
            // 管理者用户可以看到多个部门的数据
            return "department_id IN ('dept1', 'dept2', 'dept3')";
        } else if (userId.startsWith("user")) {
            // 普通用户只能看到自己部门的数据
            String deptId = "dept" + (userId.hashCode() % 5 + 1); // 模拟分配到不同部门
            return "department_id = '" + deptId + "'";
        } else {
            // 默认用户权限 - 不添加额外的数据过滤条件，依赖表级配置
            return "";
        }
    }

    /**
     * 检查BBPF API健康状态
     */
    private boolean checkBbpfApiHealth() {
        String healthUrl = config.getBbpfPermissionApiUrl() + "/health";
        
        HttpGet request = new HttpGet(healthUrl);
        request.setHeader("Content-Type", "application/json");
        
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            int statusCode = response.getStatusLine().getStatusCode();
            boolean isHealthy = statusCode == 200;
            
            if (config.isEnableVerboseLogging()) {
                logger.debug("BBPF API health check result: {}, status code: {}", isHealthy, statusCode);
            }
            
            return isHealthy;
        } catch (Exception e) {
            logger.warn("BBPF API health check failed: {}", e.getMessage());
            return false;
        }
    }
}