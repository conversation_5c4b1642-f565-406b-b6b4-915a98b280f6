package com.snbc.bbpf.grafana.proxy.interceptor;

import com.snbc.bbpf.grafana.proxy.filter.JwtAuthenticationFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * WebSocket握手拦截器
 * 
 * 在WebSocket连接建立前进行身份验证，并将用户信息存储到WebSocket会话属性中
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2024-12-13
 */
@Component
public class WebSocketHandshakeInterceptor implements HandshakeInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(WebSocketHandshakeInterceptor.class);

    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response,
                                   WebSocketHandler wsHandler, Map<String, Object> attributes) throws Exception {
        
        logger.info("WebSocket handshake interceptor - beforeHandshake for URI: {}", request.getURI());
        
        if (request instanceof ServletServerHttpRequest) {
            ServletServerHttpRequest servletRequest = (ServletServerHttpRequest) request;
            HttpServletRequest httpRequest = servletRequest.getServletRequest();
            
            // 记录请求详细信息
            logger.info("WebSocket handshake request details - URI: {}, Headers: {}", 
                       request.getURI(), request.getHeaders());
            
            // 从JWT token中获取用户ID
            String userId = JwtAuthenticationFilter.getUserId(httpRequest);
            logger.info("User ID from JWT filter: {}", userId);
            
            if (userId != null && !userId.isEmpty()) {
                // 将用户ID存储到WebSocket会话属性中
                attributes.put("userId", userId);
                logger.info("WebSocket handshake successful for user: {}", userId);
                return true;
            } else {
                // 检查Authorization header
                String authHeader = httpRequest.getHeader("Authorization");
                logger.warn("WebSocket handshake failed - no valid authentication. Auth header: {}, User ID: {}", 
                           authHeader != null ? "present" : "absent", userId);
                
                // 设置响应状态码为401而不是500
                response.setStatusCode(org.springframework.http.HttpStatus.UNAUTHORIZED);
                return false;
            }
        }
        
        logger.warn("WebSocket handshake failed - invalid request type: {}", request.getClass().getName());
        response.setStatusCode(org.springframework.http.HttpStatus.BAD_REQUEST);
        return false;
    }

    @Override
    public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response,
                               WebSocketHandler wsHandler, Exception exception) {
        
        if (exception != null) {
            logger.error("WebSocket handshake failed: {}", exception.getMessage(), exception);
        } else {
            logger.debug("WebSocket handshake completed successfully");
        }
    }
}