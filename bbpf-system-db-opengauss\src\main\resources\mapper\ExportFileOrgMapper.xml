<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snbc.bbpf.system.db.common.mapper.ExportFileOrgMapper" >
  <resultMap id="BaseResultMap" type="com.snbc.bbpf.system.db.common.entity.ExportFileOrg" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <id column="export_id" property="exportId" jdbcType="VARCHAR" />
    <id column="org_id" property="orgId" jdbcType="VARCHAR" />
  </resultMap>

    <insert id="insert" parameterType="com.snbc.bbpf.system.db.common.entity.ExportFileOrg" >
      insert into t_export_file_org (id,export_id, org_id)
      VALUES
      <foreach collection="exportFileOrgList" item="test" separator=",">
          (#{test.id},            #{test.exportId},            #{test.orgId})
      </foreach>
    </insert>
    <delete id="delete" parameterType="string">
        delete from t_export_file_org
        where export_id =#{exportId}
    </delete>
</mapper>
