/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.snbc.bbpf.system.db.common.model.RoleRelationUser;
import com.snbc.bbpf.system.db.common.model.SysRole;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: RoleService
 * @Description: 角色业务处理
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/5/18
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
public interface RoleService {
    /***
      * @Description:
      * @Author:         wangsong
      * @param :         userId
      * @param userId
     * @CreateDate:     2021/5/19 16:18
      * @UpdateDate:     2021/5/19 16:18
      * @return :        List<Map>
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    List<Map<String,String>> getRoleList(String userId);

    void updateRole(SysRole sysRole);

    void addRole(SysRole sysRole,String userId);

    void roleRelationUser(RoleRelationUser roleRelationUser);

    void delRole(String roleId) throws JsonProcessingException;


    void resetRole(String roleId);

    List<Map<String, String>> getRoleListBasedOrg(List<String> orgId);

}
