/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.mapper;

import com.snbc.bbpf.system.db.common.entity.UserOrg;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName: UserRoleMapper
 * 用户组织机构mapper
 * @module: si-bbpf-system
 * @Author: wjc1
 * @date: 2021/5/19 18:19
 */
@Mapper
public interface UserOrgMapper {

    int insert(UserOrg userOrg);
    // 不存在插入，存在更新
    int insertOrUpdate(UserOrg userOrg);
    /**
     * 根据用户id删除用户组织机构
     * @param userId 用户id
     */
    int deleteByUserId(String userId);

    int deleteByOrgId(String orgId);

    int deleteByOrgIdUserId(@Param("orgId") String orgId, @Param("userId")String userId);
    /**
      * @Description: 根据组织机构下用户数量
      * @Author:  wjc1
      * @param :  orgId 组织机构id
      * @CreateDate: 2021/5/20 14:11
      * @UpdateDate: 2021/5/20 14:11
       * @return :  用户数量
     */
    int selectUserCountByOrgId(String orgId);
    /**
     * 根据userId查询所有关系
     *
     * @param userId
     * @return 列表
     */
    String queryOrgListByUserId(String userId);

    int deleteUserRoleByUserIdsAndOrg(@Param("userIdList")List<String> userIdList,@Param("orgId") String orgId);

    List<UserOrg> queryOrgListVoByUserId(String userId);

    int deleteByPrimaryKey(String id);

    int updateByPrimaryKeySelective(UserOrg userOrg);
    //以下为新增的SQL语句，主要用户批量处理获取组织机构
    List<UserOrg> selectOrgListByUserIdList(@Param("userIdList")List<String> userIdList);
    //以下为新增的SQL语句，主要用户批量处理删除用户组织机构
    int deleteOrgByOrgIdList(@Param("orgIdList")List<String> orgIdList);
    //以下为新增的SQL语句，主要用户批量处理跟新用户组织机构
    int insertOrgListByUserIdList(@Param("userIdList")List<UserOrg> userIdList);
}
