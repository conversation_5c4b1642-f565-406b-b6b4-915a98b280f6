/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service;

import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.system.db.common.vo.BusLogVo;
import com.snbc.bbpf.system.db.common.vo.ExportLogVo;
import com.snbc.bbpf.system.db.common.vo.LogQuery;

import java.util.List;

/**
 * @ClassName: BusLogService
 * BusLogService 接口类
 * @module: si-bbpf-system
 * @Author: wjc1 增加的注释
 * @date: 2021/5/25 16:56
 */
public interface LogService {

    PageInfo getLogList(LogQuery logQuery);

    List<ExportLogVo> exportLogs(LogQuery logQuery);

    void addLogsByFeign(BusLogVo busLogVo);

    void retentionDays();
}
