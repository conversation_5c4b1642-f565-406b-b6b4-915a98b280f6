/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: RoleScopePermission
 * @Description: 角色数据权限信息
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/5/21
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class RoleScopePermission {
    /**
     * 数据权限id
     */
    private String scopeId;
    /**
     * 数据权限名称
     */
    private String scopeName;

    /**
     * 数据权限类型
     */
    private Integer scopeType;
}
