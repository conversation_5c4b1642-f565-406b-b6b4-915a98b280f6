package com.snbc.bbpf.bus.system.service.impl;

import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.system.db.common.entity.PermissionScope;
import com.snbc.bbpf.system.db.common.mapper.PermissionScopeMapper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

/**
 * @ClassName: PermissionScopeServiceImplTest
 * 数据权限测试类
 * @module: bbpf-system-manager
 * @Author: yangweipeng
 * @date: 2021/6/25
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 */
class PermissionScopeServiceImplTest {
    @Mock
    PermissionScopeMapper permissionScopeMapper;
    @InjectMocks
    PermissionScopeServiceImpl permissionScopeServiceImpl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }


    @Test
    @DisplayName("新增数据权限配置")
    @Tag("@id:23517")
    @Tag("@author:yangweipeng")
    @Tag("@date:2021/6/25")
    void testAddDataScope() {
        try(MockedStatic<CurrentUser> user=Mockito.mockStatic(CurrentUser.class)) {
            when(permissionScopeMapper.insertSelective(any())).thenReturn(0);
            when(CurrentUser.getUserId()).thenReturn("111");
            PermissionScope permissionScope=new PermissionScope("scopeId", "permissionId",
                    "scopeName","","", "scopeCode",
                    "scopeColumn", 0, "scopeField", "scopeClass",
                    "remarks", LocalDateTime.now(),LocalDateTime.now(), "createUserId","scopeExt");
            permissionScopeServiceImpl.addDataScope(permissionScope);
        }

    }

    @Test
    @DisplayName("数据权限配置修改")
    @Tag("@id:23517")
    @Tag("@author:yangweipeng")
    @Tag("@date:2021/6/25")
    void testUpdateDataScope() {
        when(permissionScopeMapper.updateByPrimaryKeySelective(any())).thenReturn(0);
        PermissionScope permissionScope=new PermissionScope("scopeId", "permissionId","","", "scopeName", "scopeCode",
                "scopeColumn", 0, "scopeField", "scopeClass",
                "remarks", LocalDateTime.now(),LocalDateTime.now(), "createUserId","scopeExt");
        permissionScopeServiceImpl.updateDataScope(permissionScope);
    }

    @Test
    @DisplayName("获取数据权限详情")
    @Tag("@id:23517")
    @Tag("@author:yangweipeng")
    @Tag("@date:2021/6/25")
    void testGetDataScopeDetail() {
        PermissionScope permissionScope=new PermissionScope("scopeId", "permissionId","","", "scopeName", "scopeCode",
                "scopeColumn", 0, "scopeField", "scopeClass",
                "remarks", LocalDateTime.now(),LocalDateTime.now(), "createUserId","scopeExt");
        when(permissionScopeMapper.selectByPrimaryKey(anyString())).thenReturn(permissionScope);

        PermissionScope result = permissionScopeServiceImpl.getDataScopeDetail("scopeId");
        Assertions.assertEquals(permissionScope, result);
    }

    @Test
    @DisplayName("删除数据权限定义")
    @Tag("@id:23517")
    @Tag("@author:yangweipeng")
    @Tag("@date:2021/6/25")
    void testDelDataScope() {
        permissionScopeServiceImpl.delDataScope(new String[]{"scopeIds"});
    }

    @Test
    @DisplayName("获取数据权限列表")
    @Tag("@id:23517")
    @Tag("@author:yangweipeng")
    @Tag("@date:2021/6/25")
    void testGetDataScopeList() {
        PermissionScope permissionScope=new PermissionScope("scopeId", "permissionId","","", "scopeName", "scopeCode",
                "scopeColumn", 0, "scopeField", "scopeClass", "remarks",
                LocalDateTime.now(),LocalDateTime.now(), "createUserId","scopeExt");
        when(permissionScopeMapper.selectbyList(any())).
                thenReturn(Collections.<PermissionScope>singletonList(permissionScope));
        List<PermissionScope> result = permissionScopeServiceImpl.getDataScopeList(permissionScope);
        Assertions.assertEquals(Collections.<PermissionScope>singletonList(permissionScope), result);
    }
}
