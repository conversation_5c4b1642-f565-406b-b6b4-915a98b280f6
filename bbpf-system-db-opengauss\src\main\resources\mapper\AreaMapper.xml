<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snbc.bbpf.system.db.common.mapper.AreaMapper" >
  <resultMap id="BaseResultMap" type="com.snbc.bbpf.system.db.common.entity.Area" >
    <constructor >
      <idArg column="area_id" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="area_code" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="area_name" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="area_level" jdbcType="INTEGER" javaType="java.lang.Integer" />
      <arg column="parent_code" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="create_time" jdbcType="TIMESTAMP" javaType="java.time.LocalDateTime" />
    </constructor>
  </resultMap>

  <sql id="Base_Column_List" >
    area_id, area_code, area_name, area_level, parent_code, create_time
  </sql>
  <select id="getArea" resultType="com.snbc.bbpf.system.db.common.dto.AreaDto" parameterType="java.lang.String" >
      select
        area_code areaCode, area_name areaName
      from t_area
      <choose>
          <when test="areaCode != null and areaCode != ''">
              where parent_code = #{areaCode}
          </when>
          <otherwise>
              where parent_code = '000000'
          </otherwise>
      </choose>
      order by area_code asc
  </select>
  <select id="getAllArea" resultType="java.util.Map">
       select
          area_code "areaCode", area_name "areaName",parent_code
      from t_area
      order by area_code asc
  </select>
</mapper>
