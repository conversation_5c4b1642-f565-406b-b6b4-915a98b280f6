package com.snbc.bbpf.grafana.proxy.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.snbc.bbpf.grafana.proxy.config.GrafanaProxyConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Grafana认证服务
 * 负责处理Grafana的认证Token获取和管理
 */
@Service
public class GrafanaAuthService {

    private static final Logger logger = LoggerFactory.getLogger(GrafanaAuthService.class);
    private static final String GRAFANA_SESSION_CACHE_KEY = "grafana:session:";

    @Autowired
    private GrafanaProxyConfig config;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private RestTemplate restTemplate;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 获取Grafana认证Token
     * 优先使用配置的API Token，如果没有则尝试登录获取Session
     */
    public String getAuthToken(String userId) {
        // 如果配置了API Token，直接返回
        if (config.getGrafanaApiToken() != null && !config.getGrafanaApiToken().trim().isEmpty()) {
            String token = config.getGrafanaApiToken().trim();
            // 检查是否已经包含Bearer前缀，避免重复添加
            if (token.startsWith("Bearer ")) {
                if (config.isEnableVerboseLogging()) {
                    logger.info("Using configured Grafana API token (already has Bearer prefix) for user: {}", userId);
                }
                return token;
            } else {
                if (config.isEnableVerboseLogging()) {
                    logger.info("Using configured Grafana API token (adding Bearer prefix) for user: {}", userId);
                }
                return "Bearer " + token;
            }
        }

        // 尝试从缓存获取Session Token
        String cachedToken = (String) redisTemplate.opsForValue().get(GRAFANA_SESSION_CACHE_KEY + userId);
        if (cachedToken != null) {
            if (config.isEnableVerboseLogging()) {
                logger.info("Using cached Grafana session token for user: {}", userId);
            }
            return cachedToken;
        }

        // 尝试登录获取Session Token
        return loginAndGetSessionToken(userId);
    }

    /**
     * 登录Grafana并获取Session Token
     */
    private String loginAndGetSessionToken(String userId) {
        try {
            String loginUrl = config.getGrafanaBaseUrl() + "/login";
            
            // 构建登录请求
            Map<String, String> loginData = new HashMap<>();
            loginData.put("user", config.getGrafanaAdminUsername());
            loginData.put("password", config.getGrafanaAdminPassword());

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, String>> request = new HttpEntity<>(loginData, headers);

            if (config.isEnableVerboseLogging()) {
                logger.info("Attempting to login to Grafana for user: {} with username: {}", 
                    userId, config.getGrafanaAdminUsername());
            }

            ResponseEntity<String> response = restTemplate.postForEntity(loginUrl, request, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                // 从响应头中获取Session Cookie
                String sessionCookie = extractSessionCookie(response.getHeaders());
                if (sessionCookie != null) {
                    // 缓存Session Token（1小时）
                    redisTemplate.opsForValue().set(GRAFANA_SESSION_CACHE_KEY + userId, 
                        sessionCookie, 3600, TimeUnit.SECONDS);
                    
                    logger.info("Successfully obtained Grafana session token for user: {}", userId);
                    return sessionCookie;
                }
            }

            logger.warn("Failed to login to Grafana for user: {}, status: {}", 
                userId, response.getStatusCode());
            return null;

        } catch (Exception e) {
            logger.error("Error during Grafana login for user: {}, error: {}", userId, e.getMessage());
            return null;
        }
    }

    /**
     * 从响应头中提取Session Cookie
     */
    private String extractSessionCookie(HttpHeaders headers) {
        if (headers.get("Set-Cookie") != null) {
            for (String cookie : headers.get("Set-Cookie")) {
                if (cookie.startsWith("grafana_session=")) {
                    String sessionValue = cookie.split(";")[0];
                    return "Cookie: " + sessionValue;
                }
            }
        }
        return null;
    }

    /**
     * 创建Grafana API Key（需要管理员权限）
     */
    public String createApiKey(String keyName, String role) {
        try {
            String apiUrl = config.getGrafanaBaseUrl() + "/api/auth/keys";
            
            Map<String, Object> keyData = new HashMap<>();
            keyData.put("name", keyName);
            keyData.put("role", role); // Viewer, Editor, Admin
            keyData.put("secondsToLive", 86400); // 24小时

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            // 使用管理员认证
            if (config.getGrafanaApiToken() != null && !config.getGrafanaApiToken().trim().isEmpty()) {
                headers.set("Authorization", "Bearer " + config.getGrafanaApiToken());
            } else {
                String auth = config.getGrafanaAdminUsername() + ":" + config.getGrafanaAdminPassword();
                String encodedAuth = java.util.Base64.getEncoder().encodeToString(auth.getBytes());
                headers.set("Authorization", "Basic " + encodedAuth);
            }

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(keyData, headers);

            ResponseEntity<String> response = restTemplate.postForEntity(apiUrl, request, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                JsonNode jsonResponse = objectMapper.readTree(response.getBody());
                String apiKey = jsonResponse.get("key").asText();
                
                logger.info("Successfully created Grafana API key: {}", keyName);
                return apiKey;
            }

            logger.warn("Failed to create Grafana API key: {}, status: {}", 
                keyName, response.getStatusCode());
            return null;

        } catch (Exception e) {
            logger.error("Error creating Grafana API key: {}, error: {}", keyName, e.getMessage());
            return null;
        }
    }

    /**
     * 验证Token是否有效
     */
    public boolean validateToken(String token) {
        try {
            String apiUrl = config.getGrafanaBaseUrl() + "/api/user";
            
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", token);

            HttpEntity<Void> request = new HttpEntity<>(headers);

            ResponseEntity<String> response = restTemplate.exchange(
                apiUrl, HttpMethod.GET, request, String.class);

            return response.getStatusCode() == HttpStatus.OK;

        } catch (Exception e) {
            if (config.isEnableVerboseLogging()) {
                logger.debug("Token validation failed: {}", e.getMessage());
            }
            return false;
        }
    }

    /**
     * 清除用户的缓存Token
     */
    public void clearUserToken(String userId) {
        redisTemplate.delete(GRAFANA_SESSION_CACHE_KEY + userId);
        logger.info("Cleared cached Grafana token for user: {}", userId);
    }

    /**
     * 获取Basic Auth头
     */
    public String getBasicAuthHeader() {
        if (config.getGrafanaAdminUsername() != null && config.getGrafanaAdminPassword() != null) {
            String auth = config.getGrafanaAdminUsername() + ":" + config.getGrafanaAdminPassword();
            String encodedAuth = java.util.Base64.getEncoder().encodeToString(auth.getBytes());
            return "Basic " + encodedAuth;
        }
        return null;
    }
}