package com.snbc.bbpf.bus.system.utils;

import com.snbc.bbpf.bus.system.constans.NumberConstant;
import com.snbc.bbpf.system.db.common.entity.Org;
import com.snbc.bbpf.system.db.common.mapper.OrgMapper;
import com.snbc.bbpf.system.db.common.mapper.UserRoleMapper;
import com.snbc.bbpf.system.db.common.vo.OrgVo;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName: AllOrg
 * @Description: 获取用户所有组织机构
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/9/8
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Component
public class AllOrg {
    @Autowired
    private OrgMapper orgMapper;
    @Autowired
    private UserRoleMapper userRoleMapper;
    @Autowired
    private CheckOrgPermission checkOrgPermission;


    /**
     * 判断是否为超级管理员角色
     * @return
     */
    public boolean isAdmin(String userId){
        List<String> roleIdList = userRoleMapper.selectRoleByUserId(userId);
        return !CollectionUtils.isEmpty(roleIdList) && roleIdList.contains(String.valueOf(NumberConstant.F_NO_ONE));
    }
    /***
     * @Description: 获取本组织及下属组织机的id
     * @Author: wangsong
     * @param :         userId
     * @param :         orgId
     * @CreateDate: 2021/9/9 15:40
     * @UpdateDate: 2021/9/9 15:40
     * @return :        java.util.List<java.lang.String>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    public List<String> getOrgPathList(String userId, String orgId) {
        List<String> roleIdList = userRoleMapper.selectRoleByUserId(userId);
        //超级管理员直接返回，sql会判断roleIdList是否为空  为空查询所有
        if (roleIdList.contains(String.valueOf(NumberConstant.F_NO_ONE))) {
            return processAdmin(orgId);
        } else {
            return Lists.newArrayList();
        }
    }

    /**
     * true 前端的orgId是所属组织机构的上级
     * @return
     */
    public boolean isSub(Collection<OrgVo> orgIdPathListDataRule, String orgId){
        //查询用户的组织机构:带数据权限
        List<String> orgIdList = orgIdPathListDataRule.stream().map(OrgVo::getOrgId).collect(Collectors.toList());
        return !orgIdList.contains(orgId);
    }

    /**
     *
     * @param orgIdPathListDataRule 数据权限
     * @param orgPath 前端所选的组织机构的orgPath
     * @return
     */
    public List<String> processGeneralUser(Iterable<OrgVo> orgIdPathListDataRule, String orgPath) {
        //如果前端请求的orgid在用户所拥有的组织机构中，那就查询该组织机构及下属组织机构的path
        List<String> orgPathList1 = new ArrayList<>();
        //1、在orgIdPathList 中把orgId的下级找出来
        for (OrgVo o : orgIdPathListDataRule) {
            if (!"0".equals(o.getOrgId()) && o.getOrgPath().startsWith(orgPath)){
                orgPathList1.add(o.getOrgId());
            }
        }
        return orgPathList1;
    }
    /**
     * 获取具有数据权限的当然人的组织机构
     * @param userId
     * @return
     */
    public List<OrgVo> getDataRuleOrg(String userId){
        if (isAdmin(userId)){
            return Lists.newArrayList();
        }
        return checkOrgPermission.checkOrg(userId, null);
    }
    /**
     * 获取组织机构根据id
     * @param orgId
     * @return
     */
    public Org getOrgByOrgId(String orgId){
        return orgMapper.selectByPrimaryKey(orgId);
    }
    /**
     * 获取组织机构根据id
     * @param orgId
     * @return
     */
    public List<Org> queryOrgListByParentId(String orgId){
        return orgMapper.queryOrgListByParentId(orgId);
    }
    /***
      * @Description:    处理超级管理员的组织机构
      * @Author:         wangsong
      * @param :         orgId
      * @CreateDate:     2021/9/13 10:40
      * @UpdateDate:     2021/9/13 10:40
      * @return :        java.util.List<java.lang.String>
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    public List<String> processAdmin(String orgId) {
        List<String> orgPathList = new ArrayList<>();
        if (orgId.equals(String.valueOf(NumberConstant.NO_ZERO))) {
            orgPathList.add("/0");
            //查询所有用户
        } else {
            //查询组织机构信息
            Org org = orgMapper.selectByPrimaryKey(orgId);
            orgPathList.add(org.getOrgPath());
        }
        return orgPathList;
    }
}
