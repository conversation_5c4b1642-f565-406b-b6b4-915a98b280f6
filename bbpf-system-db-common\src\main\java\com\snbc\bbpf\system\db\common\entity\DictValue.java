/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.entity;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * @ClassName: DictValue
 * 提供阿里云的文件上传下载功能
 * @module: bbpf-bus-system
 * @Author: liuyi
 * @date: 2021/5/18
 * copyright 2020 barm Inc. All rights reserve
 */
@Data
public class DictValue {

    private String valueId;
    /** 字典类型编码 */
    @NotBlank(message = "字典类型编码不可为空")
    @Size(max = 50, message = "字典类型长度必须在1到50之间")
    private String typeCode;
    /** 字典值名称 */
    @NotBlank(message = "字典值名称不可为空")
    @Size(max = 100, message = "字典值名称长度必须在1到100之间")
    private String valueName;
    /** 字典值编码 */
    @NotBlank(message = "字典值编码不可为空")
    @Size(max = 100, message = "字典值编码长度不能超过100")
    private String valueCode;
    /** 字典值父级 */
    @Size(max = 36, message = "字典值父级Id长度不能超过36")
    private String parentId;
    /** 字典描述 */
    @Size(max = 500, message = "字典值描述长度不能超过500")
    private String valueDesc;
    /** 字典值初始化 */
    public DictValue(String typeCode, String valueName, String valueCode) {
        this.typeCode = typeCode;
        this.valueName = valueName;
        this.valueCode = valueCode;
    }
    /** 字典值初始化 */
    public DictValue(String valueId, String typeCode, String valueName, String valueCode, String parentId, String valueDesc) {
        this.valueId = valueId;
        this.valueName = valueName;
        this.typeCode = typeCode;
        this.valueDesc = valueDesc;
        this.valueCode = valueCode;
        this.parentId = parentId;
    }

    public DictValue() {
    }
}
