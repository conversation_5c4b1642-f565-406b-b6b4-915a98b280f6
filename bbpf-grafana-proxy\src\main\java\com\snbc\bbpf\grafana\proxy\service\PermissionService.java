package com.snbc.bbpf.grafana.proxy.service;

import com.snbc.bbpf.grafana.proxy.dto.UserPermissionDto;

/**
 * 权限服务接口
 * 
 * 定义了权限相关的核心业务逻辑，包括：
 * 1. 从BBPF系统获取用户权限
 * 2. 验证用户对特定资源的访问权限
 * 3. 权限缓存管理
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2024-12-13
 */
public interface PermissionService {

    /**
     * 根据用户ID获取用户权限信息
     * 
     * @param userId 用户ID
     * @return UserPermissionDto 用户权限信息，如果用户不存在或无权限则返回null
     */
    UserPermissionDto getUserPermissions(String userId);

    /**
     * 验证用户是否有访问指定仪表盘的权限
     * 
     * @param userId 用户ID
     * @param dashboardId 仪表盘ID
     * @return boolean true表示有权限，false表示无权限
     */
    boolean canAccessDashboard(String userId, String dashboardId);

    /**
     * 验证用户是否有访问指定文件夹的权限
     * 
     * @param userId 用户ID
     * @param folderId 文件夹ID
     * @return boolean true表示有权限，false表示无权限
     */
    boolean canAccessFolder(String userId, String folderId);

    /**
     * 验证用户是否有访问指定数据源的权限
     * 
     * @param userId 用户ID
     * @param dataSourceId 数据源ID
     * @return boolean true表示有权限，false表示无权限
     */
    boolean canAccessDataSource(String userId, String dataSourceId);

    /**
     * 验证用户是否有指定的操作权限
     * 
     * @param userId 用户ID
     * @param permission 权限名称（如：view, edit, export等）
     * @return boolean true表示有权限，false表示无权限
     */
    boolean hasPermission(String userId, String permission);

    /**
     * 验证用户是否有导出数据的权限
     * 
     * @param userId 用户ID
     * @param resourceId 资源ID（仪表盘ID或数据源ID）
     * @return boolean true表示有权限，false表示无权限
     */
    boolean canExportData(String userId, String resourceId);

    /**
     * 清除用户权限缓存
     * 
     * @param userId 用户ID
     */
    void clearUserPermissionCache(String userId);

    /**
     * 清除所有权限缓存
     */
    void clearAllPermissionCache();

    /**
     * 刷新用户权限（强制从BBPF系统重新获取）
     * 
     * @param userId 用户ID
     * @return UserPermissionDto 最新的用户权限信息
     */
    UserPermissionDto refreshUserPermissions(String userId);

    /**
     * 检查权限服务是否可用
     * 
     * @return boolean true表示可用，false表示不可用
     */
    boolean isServiceAvailable();
}