/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.vo;

import com.snbc.bbpf.buslog.annotations.DiffKey;
import com.snbc.bbpf.buslog.annotations.UnionDisplayKey;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * @ClassName: DictValueVo
 * 提供阿里云的文件上传下载功能
 * @module: bbpf-bus-system
 * @Author: liuyi
 * @date: 2021/5/19
 * copyright 2020 barm Inc. All rights reserver
 */

@Data
public class DictValueVo {

    private String valueId;


    @NotBlank(message = "字典值名称不可以为空")
    @Size(max = 100, message = "字典值名称长度不可以超过100")
    @DiffKey(name = "字典值名称",enName = "Value Name")
    @UnionDisplayKey
    private String valueName;

    @NotBlank(message = "字典类型编码不可以为空")
    @Size(max = 50, message = "字典类型编号长度不可以超过50")
    private String typeCode;

    @NotBlank(message = "字典值编码不可以为空")
    @Size(max = 100, message = "字典值编码长度不可以超过100")
    @DiffKey(name = "字典值编码",enName = "Value Code")
    private String valueCode;

    @Size(max = 500, message = "字典值编码描述长度不可以超过500")
    @DiffKey(name = "字典值描述",enName = "Value Desc")
    private String valueDesc;


    @Size(max = 36, message = "字典值编码父级长度不可以超过36")
    private String parentId;
    private String parentValueCode;
    private String parentValueName;
    private String typeName;
}
