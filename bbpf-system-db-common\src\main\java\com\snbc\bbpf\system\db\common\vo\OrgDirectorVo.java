package com.snbc.bbpf.system.db.common.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: OrgDirectorVo
 * @Description: 编辑组织机构选择用户为主管vo
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/9/2
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class OrgDirectorVo {
    private String userId;

    private String userName;

    private String phone;

}
