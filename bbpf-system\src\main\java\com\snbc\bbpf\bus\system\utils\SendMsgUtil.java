/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.utils;

import com.google.gson.Gson;
import com.snbc.bbpf.bus.system.constans.NumberConstant;
import com.snbc.bbpf.bus.system.enums.BusTemplateCodeEnum;
import com.snbc.bbpf.bus.system.enums.MessageChannelEnum;
import com.snbc.bbpf.bus.system.enums.MessageTypeEnum;
import com.snbc.bbpf.bus.system.enums.ReceiverGroupEnum;
import com.snbc.bbpf.bus.system.enums.SmsKeyEnum;
import com.snbc.bbpf.bus.system.enums.SysCodeEnum;
import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.feign.BbpfMessageCenterService;
import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.system.db.common.entity.Message;
import com.snbc.bbpf.system.db.common.vo.NoticeDetailVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * @ClassName: SendMsgUtil
 * 发送消息
 * @module: si-bbpf-system
 * @Author: wjc1
 * @date: 2022/1/11 11:04
 */
@Component
@Slf4j
public class SendMsgUtil {
    private static int smsCodeVerifCount;
    private static int smsCodeValidtime;
    //手机号验证码验证次数分隔符
    private static final String SMSCODECOUNTSEPARATOR = "&";
    public static final Pattern COMPILE = Pattern.compile(SMSCODECOUNTSEPARATOR);

    public static final SendMsgUtil sendMsgUtil = new SendMsgUtil();

    @Autowired
    private BbpfMessageCenterService messageService;
    @Autowired
    private RedisTemplate<String,String> redisTemplate;
    @Value("${bbpf.captcha.cache.verifcount:5}")
    public void setSmsCodeVerifCount(int verifcount){
        smsCodeVerifCount = verifcount;
    }
    @Value("${bbpf.captcha.cache.validtime:300}")
    public void setSmsCodeValidtime(int validtime){
        smsCodeValidtime = validtime;
    }

    @PostConstruct
    public void init() {
        sendMsgUtil.messageService = this.messageService;
        sendMsgUtil.redisTemplate = this.redisTemplate;
    }

    /**
     * 发送短信
     *
     * @param message 消息
     */
    public static CommonResp sendShortMessage(Message message) {
        return sendMsgUtil.messageService.sendMsg(convertShortMessage(message));
    }

    /**
     * 发送系统消息
     *
     * @param message 消息
     */
    public static void sendSysMessage(Message message) {
        sendMsgUtil.messageService.sendMsg(convertSysMessage(message));
    }
    /**
     * 获取信息公告消息详情
     *
     * @param msgId 消息
     */
    public static CommonResp<NoticeDetailVo> getNoticeDetail(String msgId) {
        return sendMsgUtil.messageService.noticeDetail(msgId);
    }

    /**
     * wjc1 构造短信内容
     *
     * @param message 消息
     * @return Message
     */
    private static Message convertShortMessage(Message message) {
        message.setMsgChannelCode(MessageChannelEnum.SMS.getStatus());//短信
        message.setSysCode(SysCodeEnum.BOSS.getStatusName());
        message.setMsgModel(NumberConstant.NO_ONE);
        //系统消息
        message.setMessageType(MessageTypeEnum.NO1.getStatus());
        message.setReceiverGroup(ReceiverGroupEnum.BOSS.getStatusName());
        return message;
    }

    /**
     * wjc1 构造系统消息内容
     *
     * @param message 消息
     * @return Message
     */
    private static Message convertSysMessage(Message message) {
        message.setMsgChannelCode(MessageChannelEnum.SYS.getStatus());
        message.setSysCode(SysCodeEnum.BOSS.getStatusName());
        message.setMsgModel(NumberConstant.NO_ONE);
        //系统消息
        message.setMessageType(MessageTypeEnum.NO1.getStatus());
        message.setReceiverGroup(ReceiverGroupEnum.BOSS.getStatusName());
        return message;
    }

    /***
      * @Description:    下发短信验证码
      * @Author:         wangsong
      * @param :         msgType
      * @param :         userphone
      * @param :         userId
      * @CreateDate:     2022/2/15 9:51
      * @UpdateDate:     2022/2/15 9:51
      * @return :        void
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    public static void buildSMSMessages(Integer msgType, String userPhone, String userId) {
        //发送手机号及用户id
        String[] receivePhoneArr = {userPhone};
        String[] receiveUserIdArr = {userId};
        //生成6位随机数验证码
        String smsCode = RandomStringUtils.random(NumberConstant.NO_SIX, false, true);
        Message message = Message.builder().receiveNos(receiveUserIdArr).receiverPhone(receivePhoneArr)
                .templateCode(getTemplateCode(String.valueOf(msgType)))
                .templateParamJsonArr(convertTemplateParamJsonArr(smsCode))
                .msgTitle(BusTemplateCodeEnum.getTitleByCode(String.valueOf(msgType)))
                .senderId(CurrentUser.getUserId()).build();
        //下发短信
        CommonResp commonResp = sendShortMessage(message);
        //判断是否发送成功
        if (ErrorMessage.SUCCESS.getCode().equals(commonResp.getHead().getCode())) {
            //msgType:0是boss端手机号登录  1是忘记密码 2修改密码，41 绑定微信，42 绑定钉钉
            String smsKey = String.format(SmsKeyEnum.getStatusName(msgType), userId);
            invalidVerificationCode(smsKey, smsCode);
        }
    }

    /***
     *
      * @Description:    根据类型获取下发短信内容
      * @param :         smsCode
      * @CreateDate:     2022/2/15 9:53a
      * @UpdateDate:     2023/3/14 9:53
      * @return :        java.lang.String
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    private static String[] convertTemplateParamJsonArr(String smsCode){
        Map<String,String> templateParamMap=new HashMap<>();
        templateParamMap.put("code", smsCode);
        return new String[]{new Gson().toJson(templateParamMap)};
    }

    /**
     * 获取 业务模板编码
     * @param msgType
     * @return
     */
    private static String getTemplateCode(String msgType){
        String name= BusTemplateCodeEnum.getNameByCode(msgType);
        if(StringUtils.isBlank(name)) {
            return BusTemplateCodeEnum.DEFAULT_CODE.getName();
        }
        return name;
    }
    /***
      * @Description:    redis存储短信验证码
      * @Author:         wangsong
      * @param :         fuzzyKey
      * @param :         smsKey
      * @param :         smsCode
      * @CreateDate:     2022/2/15 9:53
      * @UpdateDate:     2022/2/15 9:53
      * @return :        void
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    private static void invalidVerificationCode(String smsKey,String smsCode){
        //该用户之前发送的短信验证码失效
        Set keys = sendMsgUtil.redisTemplate.keys(smsKey + "*");
        if (CollectionUtils.isNotEmpty(keys)) {
            sendMsgUtil.redisTemplate.delete(keys);
        }
        //验证码存入redis，失效5分钟（可配置）,验证码值：六位随机数 + & + 验证次数
        sendMsgUtil.redisTemplate.opsForValue().set(smsKey,smsCode
                + SMSCODECOUNTSEPARATOR + NumberConstant.NO_ZERO,smsCodeValidtime, TimeUnit.SECONDS);
    }

    /***
      * @Description:    校验短信验证码
      * @Author:         wangsong
      * @param :         smsCodeKey
      * @param :         smsCode
      * @CreateDate:     2022/2/15 9:53
      * @UpdateDate:     2022/2/15 9:53
      * @return :        void
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    public static void checkSmsCode(String smsCodeKey,String smsCode,boolean isDelSmsCode) {
        //验证码是否存在
        ErrorMessage.SMS_CODE_ERROR.assertEquals(sendMsgUtil.redisTemplate.hasKey(smsCodeKey),true);
        //获取手机验证码及验证次数
        String redisSmsCodeCount = sendMsgUtil.redisTemplate.opsForValue().get(smsCodeKey);
        //验证码及验证次数数组
        String[] smsCodeCountArr = COMPILE.split(redisSmsCodeCount);
        //获取短信验证码校验次数
        int verifCount = Integer.parseInt(smsCodeCountArr[NumberConstant.NO_ONE]);
        //验证码输入错误，验证次数+1
        verifCount++;
        //超过最大错误次数抛出异常: 不等于false时抛出异常
        ErrorMessage.SMS_CODE_INVALIDATION.assertEquals(verifCount > smsCodeVerifCount,false);
        //短信验证码
        String redisSmsCode = smsCodeCountArr[NumberConstant.NO_ZERO];
        if (!redisSmsCode.equals(smsCode)) {
            Long expirationTime = sendMsgUtil.redisTemplate.getExpire(smsCodeKey, TimeUnit.SECONDS);
            sendMsgUtil.redisTemplate.opsForValue()
                    .set(smsCodeKey, smsCodeCountArr[NumberConstant.NO_ZERO] + SMSCODECOUNTSEPARATOR + verifCount,
                            expirationTime,TimeUnit.SECONDS);
            throw new BusinessException(ErrorMessage.SMS_CODE_ERROR.getMessage(), ErrorMessage.SMS_CODE_ERROR.getCode());
        }
        //是否删除验证码，忘记密码需要二次验证所以第一次校验不删除
        if (isDelSmsCode) {
            //删除验证码
            sendMsgUtil.redisTemplate.delete(smsCodeKey);
        }
    }

    /**
     * 通用下发短信消息
     * @param userPhone
     * @param userId
     * @param templateParam
     */
    public static CommonResp sendGeneralMsg(String userPhone, String userId, String templateParam){
        //发送手机号及用户id
        String[] receivePhoneArr = {userPhone};
        String[] receiveUserIdArr = {userId};
        Message message = Message.builder().receiveNos(receiveUserIdArr).receiverPhone(receivePhoneArr)
                .templateCode(BusTemplateCodeEnum.RANDOM_PWD.getName())
                .templateParamJsonArr(new String[]{templateParam})
                .msgTitle(BusTemplateCodeEnum.RANDOM_PWD.getTitle()).senderId(CurrentUser.getUserId()).build();
        //下发短信
        return sendShortMessage(message);
    }
}
