<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snbc.bbpf.system.db.common.mapper.RoleOrgMapper">
    <resultMap id="BaseResultMap" type="com.snbc.bbpf.system.db.common.entity.RoleOrg">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <id column="role_id" property="roleId" jdbcType="VARCHAR"/>
        <id column="org_id" property="orgId" jdbcType="VARCHAR"/>
    </resultMap>

    <insert id="insert" parameterType="com.snbc.bbpf.system.db.common.entity.RoleOrg">
        insert into t_role_org (id,role_id, org_id)
        VALUES
        <foreach collection="roleOrgList" item="test" separator=",">
            (#{test.id}, #{test.roleId}, #{test.orgId})
        </foreach>
    </insert>
    <delete id="deleteByRoleId" parameterType="java.lang.String">
        DELETE
        FROM t_role_org
        WHERE role_id = #{roleId,jdbcType=VARCHAR}
    </delete>
    <select id="selectRoleByOrgId" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(0)
        from t_role_org
        where org_id = #{orgId,jdbcType=VARCHAR}
    </select>
</mapper>
