/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service;

import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.system.db.common.dto.NavigationDto;
import com.snbc.bbpf.system.db.common.dto.NavigationPermissionDto;
import com.snbc.bbpf.system.db.common.entity.Navigation;
import com.snbc.bbpf.system.db.common.vo.NavigationVo;

import java.util.List;

/**
 * 顶部导航功能Service接口
 *
 * <AUTHOR>
 * @date 2022-12-27
 */
public interface INavigationService {
    /**
     * 查询顶部导航功能
     *
     * @param navigationId 顶部导航功能主键
     * @return 顶部导航功能
     */
    NavigationVo selectNavigationByNavigationId(String navigationId);

    /**
     * 获取导航权限关系
     * @param navigationId
     * @return
     */
    List<String> getRelationship(String navigationId);
    /**
     * 保存导航关系
     * @param navigationPermissionDto
     */
    void  saveNavigationPermission(NavigationPermissionDto navigationPermissionDto);
    /**
     * 分页查询顶部导航功能列表
     *
     * @param navigationDto 顶部导航功能
     * @param pageSize 分页大小
     * @param pageNum 分页数
     * @return 顶部导航功能集合
     */
    PageInfo<Navigation> selectNavigationList(NavigationDto navigationDto, int pageSize, int pageNum);

    /**
     * 查询顶部导航功能列表
     *
     * @return 顶部导航功能集合
     */
    List<NavigationVo> getNavigationList();
    /**
     * 新增顶部导航功能
     *
     * @param navigationDto 顶部导航功能
     * @return 受影响结果数
     */
    int insertNavigation(NavigationDto navigationDto);

    /**
     * 修改顶部导航功能
     *
     * @param navigationDto 顶部导航功能
     * @return 受影响结果数
     */
    int updateNavigation(NavigationDto navigationDto);

    /**
     * 批量删除顶部导航功能
     *
     * @param navigationIds 需要删除的顶部导航功能主键集合
     * @return 受影响结果数
     */
    int deleteNavigationByNavigationIds(String[] navigationIds);

    /**
     * 删除顶部导航功能信息
     *
     * @param navigationId 顶部导航功能主键
     * @return 受影响结果数
     */
    int deleteNavigationByNavigationId(String navigationId);
}
