package com.snbc.bbpf.grafana.proxy.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.snbc.bbpf.grafana.proxy.config.SqlInterceptorConfig;
import com.snbc.bbpf.grafana.proxy.dto.UserPermissionDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.HashSet;
import java.util.stream.Collectors;

/**
 * SQL拦截服务
 * 负责拦截和修改Grafana数据查询请求中的SQL语句，添加用户权限过滤条件
 */
@Service
public class SqlInterceptorService {

    private static final Logger logger = LoggerFactory.getLogger(SqlInterceptorService.class);

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private ObjectMapper objectMapper;
    
    @Autowired
    private SqlInterceptorConfig config;

    // 需要拦截的API路径模式
    private static final Pattern QUERY_API_PATTERN = Pattern.compile("/api/ds/query");
    private static final Pattern DATASOURCE_QUERY_PATTERN = Pattern.compile("/api/datasources/proxy/\\d+/.*");
    
    // SQL表名提取的正则表达式模式 - 支持带反引号的表名和数据库名
    private static final Pattern TABLE_NAME_PATTERN = Pattern.compile(
        "(?i)\\b(?:FROM|JOIN|UPDATE|INTO)\\s+(?:`?[a-zA-Z_][a-zA-Z0-9_.-]*`?\\.)?(`?[a-zA-Z_][a-zA-Z0-9_]*`?)",
        Pattern.CASE_INSENSITIVE
    );
    
    // SQL别名处理模式
    private static final Pattern TABLE_ALIAS_PATTERN = Pattern.compile(
        "(?i)\\b([a-zA-Z_][a-zA-Z0-9_]*(?:\\.[a-zA-Z_][a-zA-Z0-9_]*)?)\\s+(?:AS\\s+)?([a-zA-Z_][a-zA-Z0-9_]*)",
        Pattern.CASE_INSENSITIVE
    );
    
    // SQL语句模式匹配 - 支持带反引号的表名和数据库名
    private static final Pattern SELECT_PATTERN = Pattern.compile(
        "(?i)\\bSELECT\\b.*?\\bFROM\\b\\s+(`?[a-zA-Z_][a-zA-Z0-9_.-]*`?\\.)?`?[a-zA-Z_][a-zA-Z0-9_]*`?", 
        Pattern.DOTALL
    );
    
    private static final Pattern WHERE_PATTERN = Pattern.compile(
        "(?i)\\bWHERE\\b", 
        Pattern.DOTALL
    );

    /**
     * 拦截请求的主入口方法
     * 
     * @param requestBody 原始请求体
     * @param userId 用户ID
     * @return 修改后的请求体
     */
    public String interceptRequest(String requestBody, String userId) {
        // 检查是否启用SQL拦截
        if (!config.isEnabled()) {
            if (config.isVerboseLogging()) {
                logger.debug("SQL interceptor is disabled, skipping interception");
            }
            return requestBody;
        }
        
        // 从当前线程上下文获取API路径（需要在调用处设置）
        String apiPath = getCurrentApiPath();
        
        if (!shouldInterceptRequest(apiPath)) {
            if (config.isVerboseLogging()) {
                logger.debug("API path '{}' does not match intercept patterns, skipping", apiPath);
            }
            return requestBody;
        }
        
        if (config.isVerboseLogging()) {
            logger.info("Intercepting SQL request for user '{}' on path '{}'", userId, apiPath);
        }
        
        return interceptAndModifyRequest(requestBody, userId, apiPath);
    }
    
    /**
     * 检查是否需要拦截此API请求
     * 
     * @param apiPath API路径
     * @return true如果需要拦截
     */
    public boolean shouldInterceptRequest(String apiPath) {
        if (apiPath == null || apiPath.isEmpty()) {
            return false;
        }
        
        // 首先检查排除模式
        for (String excludePattern : config.getExcludePatterns()) {
            if (apiPath.matches(excludePattern)) {
                return false;
            }
        }
        
        // 检查是否匹配需要拦截的API模式
        for (String interceptPattern : config.getInterceptPatterns()) {
            if (apiPath.matches(interceptPattern)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 从线程本地存储获取当前API路径
     */
    private static final ThreadLocal<String> CURRENT_API_PATH = new ThreadLocal<>();
    
    public static void setCurrentApiPath(String apiPath) {
        CURRENT_API_PATH.set(apiPath);
    }
    
    public static String getCurrentApiPath() {
        return CURRENT_API_PATH.get();
    }
    
    public static void clearCurrentApiPath() {
        CURRENT_API_PATH.remove();
    }

    /**
     * 拦截并修改请求体中的SQL查询
     * 
     * @param requestBody 原始请求体
     * @param userId 用户ID
     * @param apiPath API路径
     * @return 修改后的请求体
     */
    public String interceptAndModifyRequest(String requestBody, String userId, String apiPath) {
        if (requestBody == null || requestBody.trim().isEmpty()) {
            return requestBody;
        }

        try {
            // 获取用户权限信息
            UserPermissionDto userPermission = permissionService.getUserPermissions(userId);
            if (userPermission == null) {
                logger.warn("No permission found for user: {}, denying data access", userId);
                if (config.isDenyOnPermissionFailure()) {
                    return modifyRequestToDenyAccess(requestBody);
                } else {
                    return requestBody;
                }
            }

            // 解析请求体JSON
            JsonNode rootNode = objectMapper.readTree(requestBody);
            
            if (apiPath.contains("/api/ds/query")) {
                // 处理数据源查询API
                return modifyDatasourceQuery(rootNode, userPermission);
            } else if (apiPath.contains("/api/datasources/proxy/")) {
                // 处理数据源代理查询
                return modifyProxyQuery(requestBody, userPermission);
            }
            
            return requestBody;
            
        } catch (Exception e) {
            logger.error("Error intercepting SQL request for user {}: {}", userId, e.getMessage(), e);
            if (config.isDenyOnPermissionFailure()) {
                // 出错时拒绝访问
                return modifyRequestToDenyAccess(requestBody);
            } else {
                // 不拒绝访问，返回原始请求
                return requestBody;
            }
        }
    }

    /**
     * 修改数据源查询请求
     */
    private String modifyDatasourceQuery(JsonNode rootNode, UserPermissionDto userPermission) throws Exception {
        if (!rootNode.isObject()) {
            return objectMapper.writeValueAsString(rootNode);
        }

        ObjectNode objectNode = (ObjectNode) rootNode;
        JsonNode queriesNode = objectNode.get("queries");
        
        if (queriesNode != null && queriesNode.isArray()) {
            ArrayNode queriesArray = (ArrayNode) queriesNode;
            
            for (int i = 0; i < queriesArray.size(); i++) {
                JsonNode queryNode = queriesArray.get(i);
                if (queryNode.isObject()) {
                    ObjectNode queryObject = (ObjectNode) queryNode;
                    
                    // 修改SQL查询
                    modifySqlInQueryObject(queryObject, userPermission);
                }
            }
        }
        
        return objectMapper.writeValueAsString(objectNode);
    }

    /**
     * 修改代理查询请求
     */
    private String modifyProxyQuery(String requestBody, UserPermissionDto userPermission) {
        try {
            // 尝试解析为JSON
            JsonNode rootNode = objectMapper.readTree(requestBody);
            if (rootNode.isObject()) {
                ObjectNode objectNode = (ObjectNode) rootNode;
                modifySqlInQueryObject(objectNode, userPermission);
                return objectMapper.writeValueAsString(objectNode);
            }
        } catch (Exception e) {
            // 如果不是JSON，可能是直接的SQL查询
            logger.debug("Request body is not JSON, treating as direct SQL query");
        }
        
        // 直接修改SQL字符串
        String permissionFilter = buildPermissionFilter(userPermission, requestBody);
        if (permissionFilter != null && !permissionFilter.trim().isEmpty()) {
            return modifySqlWithPermissionFilter(requestBody, permissionFilter);
        }
        return requestBody;
    }

    /**
     * 在查询对象中修改SQL
     */
    private void modifySqlInQueryObject(ObjectNode queryObject, UserPermissionDto userPermission) {
        // 使用配置中的SQL字段列表
        String[] sqlFieldsArray = config.getSqlFields();
        List<String> sqlFields = Arrays.asList(sqlFieldsArray);
        
        for (String field : sqlFields) {
            JsonNode sqlNode = queryObject.get(field);
            if (sqlNode != null && sqlNode.isTextual()) {
                String originalSql = sqlNode.asText();
                
                // 构建权限过滤条件
                String permissionFilter = buildPermissionFilter(userPermission, originalSql);
                
                if (permissionFilter == null || permissionFilter.trim().isEmpty()) {
                    if (config.isVerboseLogging()) {
                        logger.debug("No permission filter generated for user: {} in field: {}", userPermission.getUserId(), field);
                    }
                    continue; // 没有权限过滤条件，跳过此字段
                }
                
                // 修改SQL查询
                String modifiedSql = modifySqlWithPermissionFilter(originalSql, permissionFilter);
                queryObject.put(field, modifiedSql);
                
                if (config.isVerboseLogging()) {
                    logger.debug("Modified SQL in field '{}' for user: {}", field, userPermission.getUserId());
                }
            }
        }
    }

    /**
     * 修改SQL字符串，添加权限过滤条件
     */
    private String modifySqlWithPermissionFilter(String originalSql, String permissionFilter) {
        if (originalSql == null || originalSql.trim().isEmpty()) {
            return originalSql;
        }

        String sql = originalSql.trim();
        
        // 打印原始SQL
        logger.info("[SQL拦截器] 原始SQL: {}", originalSql);
        logger.info("[SQL拦截器] 权限过滤条件: {}", permissionFilter);
        
        // 只处理SELECT语句
        if (!sql.toLowerCase().startsWith("select")) {
            logger.info("[SQL拦截器] 非SELECT语句，不处理: {}", sql);
            return originalSql;
        }

        try {
            if (permissionFilter == null || permissionFilter.trim().isEmpty()) {
                logger.info("[SQL拦截器] 无权限过滤条件，返回原始SQL");
                return originalSql;
            }

            // 检查SQL是否已有WHERE子句
            Matcher whereMatcher = WHERE_PATTERN.matcher(sql);
            
            if (whereMatcher.find()) {
                // 已有WHERE子句，添加AND条件
                int whereIndex = whereMatcher.start();
                String beforeWhere = sql.substring(0, whereIndex);
                String afterWhere = sql.substring(whereIndex);
                
                // 在WHERE后添加权限条件
                String modifiedSql = beforeWhere + "WHERE (" + permissionFilter + ") AND " + 
                                   afterWhere.substring(5); // 移除原来的WHERE
                
                logger.info("[SQL拦截器] 最终生成的SQL (已有WHERE): {}", modifiedSql);
                return modifiedSql;
            } else {
                // 没有WHERE子句，添加WHERE条件
                // 找到FROM子句后的位置
                Matcher selectMatcher = SELECT_PATTERN.matcher(sql);
                if (selectMatcher.find()) {
                    int fromEnd = selectMatcher.end();
                    
                    // 查找GROUP BY, ORDER BY, LIMIT等子句
                    String[] clauses = {"GROUP BY", "ORDER BY", "HAVING", "LIMIT", "OFFSET"};
                    int insertPosition = sql.length();
                    
                    for (String clause : clauses) {
                        Pattern clausePattern = Pattern.compile("(?i)\\b" + clause + "\\b");
                        Matcher clauseMatcher = clausePattern.matcher(sql);
                        if (clauseMatcher.find(fromEnd)) {
                            insertPosition = Math.min(insertPosition, clauseMatcher.start());
                        }
                    }
                    
                    String beforeWhere = sql.substring(0, insertPosition).trim();
                    String afterWhere = sql.substring(insertPosition).trim();
                    
                    String modifiedSql = beforeWhere + " WHERE " + permissionFilter;
                    if (!afterWhere.isEmpty()) {
                        modifiedSql += " " + afterWhere;
                    }
                    
                    logger.info("[SQL拦截器] 最终生成的SQL (新增WHERE): {}", modifiedSql);
                    return modifiedSql;
                }
            }
            
        } catch (Exception e) {
            logger.error("[SQL拦截器] 修改SQL时发生错误: {}", e.getMessage());
        }
        
        logger.info("[SQL拦截器] 返回原始SQL (未修改): {}", originalSql);
        return originalSql;
    }

    /**
     * 从SQL语句中提取表名
     * 
     * @param sql SQL语句
     * @return 表名集合
     */
    private Set<String> extractTableNames(String sql) {
        Set<String> tableNames = new HashSet<>();
        
        if (sql == null || sql.trim().isEmpty()) {
            return tableNames;
        }
        
        // 移除SQL注释和多余空格
        String cleanSql = sql.replaceAll("--.*?\\n", " ")
                            .replaceAll("/\\*.*?\\*/", " ")
                            .replaceAll("\\s+", " ")
                            .trim();
        
        Matcher matcher = TABLE_NAME_PATTERN.matcher(cleanSql);
        while (matcher.find()) {
            String tableName = matcher.group(1);
            if (tableName != null && !tableName.trim().isEmpty()) {
                // 移除反引号
                tableName = tableName.replaceAll("`", "");
                
                // 移除数据库名前缀（如果存在）
                if (tableName.contains(".")) {
                    tableName = tableName.substring(tableName.lastIndexOf(".") + 1);
                }
                
                tableNames.add(tableName.toLowerCase());
            }
        }
        
        if (config.isVerboseLogging()) {
            logger.debug("Extracted table names from SQL: {}", tableNames);
        }
        
        return tableNames;
    }
    
    /**
     * 构建权限过滤条件
     * 
     * @param userPermission 用户权限信息
     * @param sql SQL语句（用于提取表名）
     * @return 权限过滤条件字符串
     */
    private String buildPermissionFilter(UserPermissionDto userPermission, String sql) {
        StringBuilder filter = new StringBuilder();
        
        // 提取SQL中的表名
        Set<String> tableNames = extractTableNames(sql);
        logger.info("[SQL拦截器] 从SQL中提取的表名: {}", tableNames);
        
        // 检查是否有表在排除列表中
        List<String> excludeTables = config.getExcludeTables();
        if (excludeTables != null && !excludeTables.isEmpty()) {
            for (String tableName : tableNames) {
                if (excludeTables.contains(tableName.toLowerCase())) {
                    logger.info("[SQL拦截器] 表 {} 在排除列表中，跳过权限过滤", tableName);
                    return ""; // 返回空字符串，表示不添加任何过滤条件
                }
            }
        }
        
        // 检查数据过滤权限
        Object dataFilter = userPermission.getRawPermissionValue("dataFilter");
        if (dataFilter != null && !dataFilter.toString().trim().isEmpty()) {
            String dataFilterStr = dataFilter.toString().trim();
            if (!"null".equalsIgnoreCase(dataFilterStr) && !"undefined".equalsIgnoreCase(dataFilterStr)) {
                filter.append(dataFilterStr);
            }
        }
        
        // 根据表名应用特定的权限字段映射
        boolean hasTableSpecificMapping = false;
        for (String tableName : tableNames) {
            SqlInterceptorConfig.TablePermissionMapping tableMapping = config.getTablePermissionMappingForTable(tableName);
            if (tableMapping != null) {
                logger.info("[SQL拦截器] 找到表 {} 的特定权限映射配置", tableName);
                hasTableSpecificMapping = true;
                String tableFilter = buildTableSpecificFilter(userPermission, tableMapping);
                logger.info("[SQL拦截器] 表 {} 的特定过滤条件: {}", tableName, tableFilter);
                if (!tableFilter.isEmpty()) {
                    if (filter.length() > 0) {
                        filter.append(" AND ");
                    }
                    filter.append("(").append(tableFilter).append(")");
                }
                break; // 使用第一个匹配的表配置
            }
        }
        
        // 如果没有表特定的映射，使用全局默认配置
        if (!hasTableSpecificMapping) {
            logger.info("[SQL拦截器] 未找到表特定权限映射，使用全局默认配置");
            String globalFilter = buildGlobalPermissionFilter(userPermission);
            logger.info("[SQL拦截器] 全局过滤条件: {}", globalFilter);
            if (!globalFilter.isEmpty()) {
                if (filter.length() > 0) {
                    filter.append(" AND ");
                }
                filter.append(globalFilter);
            }
        }
        
        String result = filter.toString().trim();
        if (config.isVerboseLogging()) {
            logger.debug("Built permission filter for user {} with tables {}: {}", 
                userPermission.getUserId(), tableNames, result);
        }
        
        return result;
    }
    
    /**
     * 构建表特定的权限过滤条件
     * 
     * @param userPermission 用户权限信息
     * @param tableMapping 表权限映射配置
     * @return 权限过滤条件字符串
     */
    private String buildTableSpecificFilter(UserPermissionDto userPermission, SqlInterceptorConfig.TablePermissionMapping tableMapping) {
        StringBuilder filter = new StringBuilder();
        String userId = userPermission.getUserId();
        
        logger.info("[SQL拦截器] 构建表特定过滤条件，用户ID: {}", userId);
        
        // 添加用户字段过滤
        List<String> userFields = tableMapping.getUserFields();
        logger.info("[SQL拦截器] 表特定用户字段配置: {}", userFields);
        if (userId != null && !userFields.isEmpty()) {
            StringBuilder userFilter = new StringBuilder("(");
            for (int i = 0; i < userFields.size(); i++) {
                if (i > 0) {
                    userFilter.append(" OR ");
                }
                userFilter.append(userFields.get(i)).append(" = '").append(userId).append("'");
            }
            userFilter.append(")");
            filter.append(userFilter);
            logger.info("[SQL拦截器] 添加用户字段过滤: {}", userFilter.toString());
        }
        
        // 添加组织字段过滤
        String orgField = tableMapping.getOrgField();
        Object orgId = userPermission.getRawPermissionValue("orgId");
        if (orgField != null && orgId != null) {
            if (filter.length() > 0) {
                filter.append(" AND ");
            }
            filter.append(orgField).append(" = '").append(orgId.toString()).append("'");
        }
        
        // 添加部门字段过滤
        String deptField = tableMapping.getDeptField();
        Object deptId = userPermission.getRawPermissionValue("deptId");
        if (deptField != null && deptId != null) {
            if (filter.length() > 0) {
                filter.append(" AND ");
            }
            filter.append(deptField).append(" = '").append(deptId.toString()).append("'");
        }
        
        // 添加租户字段过滤
        String tenantField = tableMapping.getTenantField();
        Object tenantId = userPermission.getRawPermissionValue("tenantId");
        if (tenantField != null && tenantId != null) {
            if (filter.length() > 0) {
                filter.append(" AND ");
            }
            filter.append(tenantField).append(" = '").append(tenantId.toString()).append("'");
        }
        
        // 添加自定义字段过滤
        for (String customKey : tableMapping.getCustomFields().keySet()) {
            String customField = tableMapping.getCustomFields().get(customKey);
            Object customValue = userPermission.getRawPermissionValue(customKey);
            if (customField != null && customValue != null) {
                if (filter.length() > 0) {
                    filter.append(" AND ");
                }
                filter.append(customField).append(" = '").append(customValue.toString()).append("'");
            }
        }
        
        return filter.toString();
    }
    
    /**
     * 构建全局权限过滤条件
     * 
     * @param userPermission 用户权限信息
     * @return 权限过滤条件字符串
     */
    private String buildGlobalPermissionFilter(UserPermissionDto userPermission) {
        StringBuilder filter = new StringBuilder();
        
        logger.info("[SQL拦截器] 构建全局权限过滤条件");
        
        // 使用配置中的权限字段映射
        logger.info("[SQL拦截器] 权限字段映射配置: {}", config.getPermissionFieldMappings());
        for (String permissionKey : config.getPermissionFieldMappings().keySet()) {
            Object permissionValue = userPermission.getRawPermissionValue(permissionKey);
            if (permissionValue != null) {
                String dbField = config.getPermissionFieldMappings().get(permissionKey);
                if (filter.length() > 0) {
                    filter.append(" AND ");
                }
                filter.append(dbField).append(" = '").append(permissionValue.toString()).append("'");
                logger.info("[SQL拦截器] 添加权限字段过滤: {} = {}", dbField, permissionValue);
            }
        }
        
        // 添加用户ID过滤（如果配置了用户字段）
        String userId = userPermission.getUserId();
        List<String> userFields = config.getUserFields();
        logger.info("[SQL拦截器] 全局用户字段配置: {}", userFields);
        if (userId != null && !userFields.isEmpty()) {
            if (filter.length() > 0) {
                filter.append(" AND ");
            }
            
            StringBuilder userFilter = new StringBuilder("(");
            for (int i = 0; i < userFields.size(); i++) {
                if (i > 0) {
                    userFilter.append(" OR ");
                }
                userFilter.append(userFields.get(i)).append(" = '").append(userId).append("'");
            }
            userFilter.append(")");
            filter.append(userFilter);
            logger.info("[SQL拦截器] 添加全局用户字段过滤: {}", userFilter.toString());
        }
        
        return filter.toString();
    }

    /**
     * 修改请求以拒绝数据访问
     */
    private String modifyRequestToDenyAccess(String requestBody) {
        try {
            JsonNode rootNode = objectMapper.readTree(requestBody);
            if (rootNode.isObject()) {
                ObjectNode objectNode = (ObjectNode) rootNode;
                JsonNode queriesNode = objectNode.get("queries");
                
                if (queriesNode != null && queriesNode.isArray()) {
                    ArrayNode queriesArray = (ArrayNode) queriesNode;
                    
                    for (int i = 0; i < queriesArray.size(); i++) {
                        JsonNode queryNode = queriesArray.get(i);
                        if (queryNode.isObject()) {
                            ObjectNode queryObject = (ObjectNode) queryNode;
                            
                            // 使用配置中的SQL字段列表
                            String[] sqlFieldsArray = config.getSqlFields();
                            List<String> sqlFields = Arrays.asList(sqlFieldsArray);
                            for (String field : sqlFields) {
                                if (queryObject.has(field)) {
                                    queryObject.put(field, config.getDenyAccessSql());
                                }
                            }
                        }
                    }
                }
                
                return objectMapper.writeValueAsString(objectNode);
            }
        } catch (Exception e) {
            logger.error("Error modifying request to deny access: {}", e.getMessage());
        }
        
        return requestBody;
    }
}