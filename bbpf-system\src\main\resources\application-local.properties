server.port=9010
spring.application.name=bbpf-system-manager
server.servlet.session.cookie.http-only=true
server.servlet.session.cookie.secure=true
spring.main.allow-circular-references=true
#spring.main.web-application-type=reactive
spring.mvc.pathmatch.matching-strategy=ant_path_matcher

logging.level.root = info
logging.level.com.snbc.bbpf = info
jasypt.encryptor.password=snbcpwde
jasypt.encryptor.algorithm=PBEWithMD5AndDES

spring.datasource.url = **************************************************************************************************************************************************************************************
spring.datasource.username = ENC(Lcqm2iLDeczttkCPt9+9wcI+z6caw/No)
spring.datasource.password = ENC(nY/8N/j6ocNTChN+isJaxSjMddDqTh2aI/eSOqndu0o=)
spring.datasource.driver-class-name = com.mysql.cj.jdbc.Driver
mybatis.mapper-locations = classpath*:mapper/*Mapper.xml
#测试环境下去掉sql打印
mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
#导入用户后存放excle的位置: 目录必须提前建立好
importuer.path=C\:/ssms-upload
#导入用户模板的位置
importuser.template.path=C\:/ssms-upload/importUser.zip
bbpf.gray.common.nameSpace=bbpf.gray
bbpf.gray.common.xml=
#redis库
spring.redis.database = 0
# Redis服务器地址
spring.redis.host = 127.0.0.1
# Redis服务器连接端口
spring.redis.port = 6379
# Redis服务器连接密码
spring.redis.password =
# 连接池最大连接数（使用负值表示没有限制）
spring.redis.max-active = 200
# 连接池最大阻塞等待时间（使用负值表示没有限制）
spring.redis.max-wait = -1
# 连接池中的最大空闲连接
spring.redis.max-idle = 10
# 连接池中的最小空闲连接
spring.redis.min-idle = 0
# 连接超时时间（毫秒）
spring.redis.timeout =
# 连接池中的最小空闲连接

bbpf.system.redis.cache.ttl = 60
#eureka配置
eureka.instance.lease-renewal-interval-in-second = 5
eureka.instance.leaseRenewalIntervalInSeconds = 20
eureka.instance.leaseExpirationDurationInSeconds = 30
eureka.client.registerWithEureka = false
eureka.client.fetchRegistry = false
eureka.instance.prefer-ip-address = true
eureka.client.serviceUrl.defaultZone = http://*************:15010/eureka

spring.rabbitmq.host = 127.0.0.1
spring.rabbitmq.port = 5672
spring.rabbitmq.username = guest
spring.rabbitmq.password = guest
spring.rabbitmq.virtual-host = bbpf

spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.serialization.write-dates-as-timestamps=false

#新增三方登录微信配置
wx.appid = wxfa63aa44efd62157
wx.appsecret = fe07349cd5e1441fc7efcb435fe9d005
wx.redirectUrl = http://dev-boss.newbeiyang.cn/bossapi/bbpfsystem/console/third/wxCallback
wx.userAuthUrl = https://open.weixin.qq.com/connect/oauth2/authorize?appid=%s&redirect_uri=%s&response_type=code&scope=snsapi_userinfo&state=%s#wechat_redirect
wx.getAccessTokenUrl = https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code
wx.getSuccesUrl = http://dev-tenant.newbeiyang.cn/bindSuccess.html
wx.getErrorUrl = http://dev-tenant.newbeiyang.cn/bindFailure.html
wx.getUserInfoUrl = https://api.weixin.qq.com/sns/userinfo?access_token=%s&openid=%s&lang=zh_CN

#分页pageHelper
pagehelper.helper-dialect=mysql
pagehelper.reasonable=true
pagehelper.support-methods-arguments=true
#是否需要数据权限
bbpf.security.dataauth.enable=true
bbpf.security.dataauth.authServiceName=http://127.0.0.1:9010

bbpf.system.security.jwt.header: Authorization
bbpf.system.security.jwt.secret: w-oasis123456
#JWT过期时间，由以前单位为604800 秒 改为 24小时
bbpf.system.security.jwt.expiration: 24
bbpf.system.security.jwt.privateKey: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCJ+bo+0vwtr8bGuaRtmzGSNtZJxJZuKh2di3sYMe4isj01ixz+1CQPI278EHFeW1jUuEPDc+pW9BBWjsP+iugNvezJbJlvhAHv4GQ31Y1BsEqhgYhJltzwjy20G2rgHzm9pa0AqrM5kDgFUuPfDP/+rs6o3T1mNj+tM/3nOtA6DuEKhcKX3AQIWzDzpnNUI4HbW37PgerM8hCOPmoMRTuVsw6wZ1aOjnDVugmrj+Z1BQpU6ONaKpradU4vyONd5cWde4Xz28MAPGw55QVUCVflQtXHGGOjA1NEgPby1InG6QIdyxBEhsUoVw7LufB6Smk1xAm03UCFRxi4fsakCXFFAgMBAAECggEACAggfl27zlEIKjNOTg8VsKQ+jybBdwI+nRgsfzkQ2rYzd/V0J6Vjv/nZERVH9C0vG5rqXutVfeBRPyiqYhvl7D7dFNCckfBGSJdwXguxYFyJy6MFsvr0O5CqIWzjKM7IhrCpRHurWTN6/******************************+656215k4BdDkFuJtt1wW+/dTzbPNZykbMem26RVPniJ5JuTE2cbq6QiCrtzdAaE1NEFTnZHdQ5ZqY7kuy+wd52BJHlQnZCdX/SzSevu8aWR/hXXbISSI6vMSOt9g3IliOIJOzdS84EnLYCLBvuY3vjcjtazOXOIV3FTwsObeNQKBgQDKdL2mTVJBHPjDkVs39Hc6+KkOKOSYMoGG6Z+/eQcDrruaXIVuC+V3/d2dG4BcraukuggUykgo5R/eaJnwkBq1PNZU7FstEo71IZXivnE/ITby1ZLCyJ/sm7y/JjpL37hgYcJk/3S+p8dXGw3qIFWXOMQoAskFe/EHIpO1uFiddwKBgQCud1dYdkkZWLOyLh6ihqxSJ7nFEKoh43su6vNLtE9qvJ5BK478RPC6CZ+ltbx5CW6LsFDPtbLhQLZnHMTj1S4wBG6rlhQ7c8SsaE8ogKSvifDZfBDC8ReAuuKalFQ0HJaC38O7kQ/2/+EAougoVDAYwOH1XpFPHww0c5MXgeXmIwKBgFeiULU7ou8sa0G8GZAO8c1E9Wh7qvd2ZTrQxaVL7g1aBTq78cXAwINAED2BOf7j7fhPzk+xf6q0Aydyf6/xsJ3ix6Pa61yHO/o/n3GWnc6FwhM6/1vxG8h/YSQcl/9fx59wsYSmTxJ37YF25H8DoAjlOYNYMUt+asZ74RQ3x6PhAoGBAJNZmfzN0klFAGfxyc/svGeaw6xrubVrgmOK7jc3L4fvjB1wo4/uzf3iGOMFDgyP6byzCl2TkWPrmuKVirj+GMdXRv1GgQoBac9PPePwWqcjcrbMsP9kTjxcGv0BX+ivaHNad97X0ssDK860yC0fhYuyhGUPHNzdVRqLrmTBQiFPAoGAeDig34zSzC7O/NMOwUE7Q2ZHuBIWnEZ5oUE8CEzn3mVd+UAN7JjnIPF90MXOtNwqcPRdUECdD4/sRw8rUN/MmmPMbOtSKdPc0A0hwz5aIMeRf3jValCEn7dOl0Gh+y4MfoOi0Ct47bXEjKI3k/IaVs9VaQjUi1iAx5sAITJ9UOE=
bbpf.system.security.jwt.publicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAifm6PtL8La/GxrmkbZsxkjbWScSWbiodnYt7GDHuIrI9NYsc/tQkDyNu/BBxXltY1LhDw3PqVvQQVo7D/oroDb3syWyZb4QB7+BkN9WNQbBKoYGISZbc8I8ttBtq4B85vaWtAKqzOZA4BVLj3wz//q7OqN09ZjY/rTP95zrQOg7hCoXCl9wECFsw86ZzVCOB21t+z4HqzPIQjj5qDEU7lbMOsGdWjo5w1boJq4/mdQUKVOjjWiqa2nVOL8jjXeXFnXuF89vDADxsOeUFVAlX5ULVxxhjowNTRID28tSJxukCHcsQRIbFKFcOy7nwekppNcQJtN1AhUcYuH7GpAlxRQIDAQAB
bbpf.system.security.jwt.tokenHead: Bearer
bbpf.system.security.jwt.exceptUrl:


#platform.application.security.enabled=false
#登录先关的私钥
bbpf.system.RSA.privateKey = MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCJ+bo+0vwtr8bGuaRtmzGSNtZJxJZuKh2di3sYMe4isj01ixz+1CQPI278EHFeW1jUuEPDc+pW9BBWjsP+iugNvezJbJlvhAHv4GQ31Y1BsEqhgYhJltzwjy20G2rgHzm9pa0AqrM5kDgFUuPfDP/+rs6o3T1mNj+tM/3nOtA6DuEKhcKX3AQIWzDzpnNUI4HbW37PgerM8hCOPmoMRTuVsw6wZ1aOjnDVugmrj+Z1BQpU6ONaKpradU4vyONd5cWde4Xz28MAPGw55QVUCVflQtXHGGOjA1NEgPby1InG6QIdyxBEhsUoVw7LufB6Smk1xAm03UCFRxi4fsakCXFFAgMBAAECggEACAggfl27zlEIKjNOTg8VsKQ+jybBdwI+nRgsfzkQ2rYzd/V0J6Vjv/nZERVH9C0vG5rqXutVfeBRPyiqYhvl7D7dFNCckfBGSJdwXguxYFyJy6MFsvr0O5CqIWzjKM7IhrCpRHurWTN6/******************************+656215k4BdDkFuJtt1wW+/dTzbPNZykbMem26RVPniJ5JuTE2cbq6QiCrtzdAaE1NEFTnZHdQ5ZqY7kuy+wd52BJHlQnZCdX/SzSevu8aWR/hXXbISSI6vMSOt9g3IliOIJOzdS84EnLYCLBvuY3vjcjtazOXOIV3FTwsObeNQKBgQDKdL2mTVJBHPjDkVs39Hc6+KkOKOSYMoGG6Z+/eQcDrruaXIVuC+V3/d2dG4BcraukuggUykgo5R/eaJnwkBq1PNZU7FstEo71IZXivnE/ITby1ZLCyJ/sm7y/JjpL37hgYcJk/3S+p8dXGw3qIFWXOMQoAskFe/EHIpO1uFiddwKBgQCud1dYdkkZWLOyLh6ihqxSJ7nFEKoh43su6vNLtE9qvJ5BK478RPC6CZ+ltbx5CW6LsFDPtbLhQLZnHMTj1S4wBG6rlhQ7c8SsaE8ogKSvifDZfBDC8ReAuuKalFQ0HJaC38O7kQ/2/+EAougoVDAYwOH1XpFPHww0c5MXgeXmIwKBgFeiULU7ou8sa0G8GZAO8c1E9Wh7qvd2ZTrQxaVL7g1aBTq78cXAwINAED2BOf7j7fhPzk+xf6q0Aydyf6/xsJ3ix6Pa61yHO/o/n3GWnc6FwhM6/1vxG8h/YSQcl/9fx59wsYSmTxJ37YF25H8DoAjlOYNYMUt+asZ74RQ3x6PhAoGBAJNZmfzN0klFAGfxyc/svGeaw6xrubVrgmOK7jc3L4fvjB1wo4/uzf3iGOMFDgyP6byzCl2TkWPrmuKVirj+GMdXRv1GgQoBac9PPePwWqcjcrbMsP9kTjxcGv0BX+ivaHNad97X0ssDK860yC0fhYuyhGUPHNzdVRqLrmTBQiFPAoGAeDig34zSzC7O/NMOwUE7Q2ZHuBIWnEZ5oUE8CEzn3mVd+UAN7JjnIPF90MXOtNwqcPRdUECdD4/sRw8rUN/MmmPMbOtSKdPc0A0hwz5aIMeRf3jValCEn7dOl0Gh+y4MfoOi0Ct47bXEjKI3k/IaVs9VaQjUi1iAx5sAITJ9UOE=
bbpf.boss.login.type=sms,account
#是否多端登录
bbpf.system.allow.multiport.login = false
#系统获取菜单的默认父级节点
bbpf.system.menu.parentid = 24d421e2-fc70-4bc4-bd07-95c8553e886d
#验证码类型 redis过内存mem
bbpf.captcha.cache.type = redis
bbpf.oss.file.upload.size = ********
bbpf.oss.file.modify.suffix= false
#文件上传，下载需要用到的环境，本地:local，又拍云:youPaiYun，阿里云:aliYun，MinIo:minIo
bbpf.oss.file.util.class=aliyun
bbpf.oss.config.uploadPath = tenantconfig/dev/user/
bbpf.oss.file.suffixName=.doc,.docx,.pdf,.jpg,.xls,.xlsx,.png,.bmp,.jpeg,.gif
#又拍云配置项定义:
bbpf.oss.upyun.config.bucketName = bbpf2
bbpf.oss.upyun.config.userName = bbpf2
bbpf.oss.upyun.config.password = wG41haP16Y8dKKR0mhybPIrPMyVizhyt
#bbpf.oss.upyun.config.uploadPath=user/avatar/
#服务文件域名
bbpf.oss.upyun.config.fileUrl =https://test-pic.newbeiyang.cn/

bbpf.exportFile.uploadPath = system/dev/exportfile/
#阿里云oss配置定义:
# oss连接区域地址（如：华北地区对应的地址）
bbpf.oss.aliyun.oss.endpoint = https://oss-cn-hangzhou.aliyuncs.com
# 访问身份验证中用到用户标识
bbpf.oss.aliyun.oss.accessKeyId =LTAI5tDEw479owWsYG1AJF4V
# 用户用于加密签名字符串和oss用来验证签名字符串的密钥
bbpf.oss.aliyun.oss.accessKeySecret =******************************
#在控制台创建的bucketName
bbpf.oss.aliyun.oss.bucketName =bbpf-test
bbpf.oss.aliyun.config.fileUrl=https://test-image-bbpf.xinbeiyang.info/
#bbpf.oss.upyun.config.uploadPath=user/avatar/
#文件大小配置
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

spring.redis.jedis.pool.max-active=8
spring.redis.jedis.pool.max-wait=-1
spring.redis.jedis.pool.max-idle=10
spring.redis.jedis.pool.min-idle=2
#健康检查端口
#management.server.port=9090
spring.security.user.name=admin
spring.security.user.password=snbc1234
#springboot2.0之后，在Http环境下将默认的endpoint只设置为info和health，要想开启其他的监控功能，需要手动配置
management.endpoints.web.exposure.include=*
# 健康检查路径
management.endpoints.web.base-path=/monitor/*
#security认证验证码接口
bbpf.security.captcha.path = /*/getSecondVerifCaptcha
#登陆成功跳转的地址
bbpf.security.success.url = https://dev-bbpfboss.xinbeiyang.info/bossapi/bbpfsystem/monitor
#安全检查,需要使用security认证
#management.security.enabled=false
#redis检查
management.health.redis.enabled=false
#MongoDB检查
management.health.mongo.enabled=false

bbpf.security.weakpwdcheck.enabled=false
bbpf.boss.login.pwdStrength = low

bbpf.system.default.pwd=FcFYhsO/9FbYeZ+1hHw3tg==

#新增三方登录相关配置
bbpf.system.sms.outTime = 300
bbpf.system.snowk.workerId = 7
bbpf.system.snowk.datacenterId = 13
bbpf.system.snowk.sequence = 456

### 调度中心部署根地址 [选填]：如调度中心集群部署存在多个地址则用逗号分隔。执行器将会使用该地址进行"执行器心跳注册"和"任务结果回调"；为空则关闭自动注册；
xxl.job.admin.addresses = https://dev-bbpfboss.xinbeiyang.info/xxljob/
### 执行器通讯TOKEN [选填]：非空时启用；
xxl.job.accessToken = default_token
### 执行器AppName [选填]：执行器心跳注册分组依据；为空则关闭自动注册
xxl.job.executor.appname = ${spring.application.name}
### 执行器端口号 [选填]：小于等于0则自动获取；默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口；
xxl.job.executor.port = 9998
### 执行器运行日志文件存储磁盘路径 [选填] ：需要对该路径拥有读写权限；为空则使用默认路径；
xxl.job.executor.logpath = /app/data/snbclogs/bbpf/xxl-job/xxl-job-jobhandler
### 执行器日志文件保存天数 [选填] ： 过期日志自动清理, 限制值大于等于3时生效; 否则, 如-1, 关闭自动清理功能；
xxl.job.executor.logretentiondays = 30
xxl.job.executor.enable = false

bbpf.ldap.ldapUrl=LDAP://*************:389
bbpf.ldap.domain=byintra
bbpf.ldap.baseDn=OU=\u5317\u5206\u516c\u53f8,OU=\u65b0\u5317\u6d0b,dc=byintra,dc=com
bbpf.ldap.adminUser=cn=admin,dc=byintra,dc=com
bbpf.ldap.adminPass=123456
bbpf.ldap.filter=(&(objectClass=person)(name=*))
bbpf.ldap.type=open

bbpf.system.baseUrl = https://s/