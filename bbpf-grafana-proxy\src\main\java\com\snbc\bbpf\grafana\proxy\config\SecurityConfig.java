package com.snbc.bbpf.grafana.proxy.config;

import com.snbc.bbpf.grafana.proxy.filter.JwtAuthenticationFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;

/**
 * Spring Security配置类
 * 
 * 配置安全策略：
 * 1. 禁用默认的表单登录和HTTP Basic认证
 * 2. 配置JWT认证过滤器
 * 3. 设置CORS策略
 * 4. 配置会话管理策略
 * 5. 定义访问权限规则
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2024-12-13
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig extends WebSecurityConfigurerAdapter {

	@Autowired
	private JwtAuthenticationFilter jwtAuthenticationFilter;

	@Autowired
	private GrafanaProxyConfig grafanaProxyConfig;

	@Override
	protected void configure(HttpSecurity http) throws Exception {
        http
                // 禁用CSRF，因为我们使用JWT
                .csrf(csrf -> csrf.disable())
                // 配置CORS
                .cors(cors -> cors.configurationSource(corsConfigurationSource()))
                // 配置会话管理 - 无状态
                .sessionManagement(management -> management
                        .sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                // 配置访问权限
                .authorizeRequests(requests -> requests
                        // 健康检查端点允许匿名访问
                        .antMatchers("/health", "/actuator/**").permitAll()
                        // 错误页面允许匿名访问
                        .antMatchers("/error").permitAll()
                        // 静态资源允许匿名访问
                        .antMatchers("/favicon.ico", "/api/**", "/public/**").permitAll()
                        // Grafana代理路径需要认证
                        .antMatchers("/d/**", "/render/**", "/public/dashboards/**").authenticated()
                        // 其他所有请求都需要认证
                        .anyRequest().authenticated())
                // 禁用默认的登录页面
                .formLogin(login -> login.disable())
                // 禁用HTTP Basic认证
                .httpBasic(basic -> basic.disable())
                // 禁用默认的登出
                .logout(logout -> logout.disable());
		// 添加JWT认证过滤器
		http.addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);
	}

	/**
	 * CORS配置
	 * 允许前端应用跨域访问代理服务
	 */
	@Bean
	public CorsConfigurationSource corsConfigurationSource() {
		CorsConfiguration configuration = new CorsConfiguration();

		// 允许的源（从配置文件读取）
		String[] origins = grafanaProxyConfig.getAllowedOrigins().split(",");
		configuration.setAllowedOriginPatterns(Arrays.asList(origins));

		// 允许的HTTP方法
		configuration.setAllowedMethods(Arrays.asList(
				"GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"));

		// 允许的请求头
		configuration.setAllowedHeaders(Arrays.asList(
				"Authorization",
				"Content-Type",
				"X-Requested-With",
				"Accept",
				"Origin",
				"Access-Control-Request-Method",
				"Access-Control-Request-Headers",
				"X-BBPF-*"));

		// 允许发送凭证
		configuration.setAllowCredentials(true);

		// 预检请求的缓存时间（秒）
		configuration.setMaxAge(3600L);

		// 暴露的响应头
		configuration.setExposedHeaders(Arrays.asList(
				"Access-Control-Allow-Origin",
				"Access-Control-Allow-Credentials",
				"X-BBPF-User-ID",
				"X-BBPF-Proxy"));

		UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
		source.registerCorsConfiguration("/**", configuration);

		return source;
	}
}