@echo off
echo Starting BBPF Grafana Proxy Service...
echo.

REM 设置Java环境变量
set JAVA_OPTS=-Xms512m -Xmx1024m -Dspring.profiles.active=local

REM 切换到项目目录
cd /d "%~dp0"

REM 尝试使用Maven运行
echo Trying to run with Maven...
mvn spring-boot:run -Dspring-boot.run.profiles=local

if %ERRORLEVEL% NEQ 0 (
    echo Maven failed, trying to compile first...
    mvn clean compile
    if %ERRORLEVEL% EQU 0 (
        echo Compilation successful, running application...
        mvn spring-boot:run -Dspring-boot.run.profiles=local
    ) else (
        echo Compilation failed. Please check your Maven and Java installation.
        echo Make sure Maven is in your PATH and JAVA_HOME is set correctly.
        pause
    )
) else (
    echo Application started successfully!
)

pause