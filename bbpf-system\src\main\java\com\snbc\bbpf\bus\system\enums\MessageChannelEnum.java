/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.enums;

/**
 * @ClassName: MessageTypeEnum
 * 消息渠道：消息渠道1系统2邮件3短信4微信5钉钉
 * @module: si-bbpf-system
 * @Author: wjc1
 * @date: 2021/5/20 13:32
 */
public enum MessageChannelEnum {
    SYS(1, "系统","sys"),
    MAIL(2, "邮件","mail"),
    SMS(3, "短信","sms"),
    WECHAT(4, "微信","wechat"),
    DINGDING(5, "钉钉","dingding");

    private Integer status;
    private String statusName;
    private String code;

    MessageChannelEnum(Integer status, String statusName,String code) {
        this.status = status;
        this.statusName = statusName;
        this.code=code;
    }

    public Integer getStatus() {
        return status;
    }

    public String getStatusName() {
        return statusName;
    }

    public String getCode() {
        return code;
    }
}
