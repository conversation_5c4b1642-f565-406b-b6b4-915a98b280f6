{"java.configuration.runtimes": [{"name": "JavaSE-1.8", "path": "D:\\Program Files\\java\\jdk1.8.0_121", "default": true}], "terminal.integrated.env.windows": {"JAVA_HOME": "D:\\Program Files\\java\\jdk1.8.0_121", "PATH": "D:\\Program Files\\java\\jdk1.8.0_121\\bin;${env:PATH}"}, "maven.terminal.customEnv": [{"environmentVariable": "JAVA_HOME", "value": "D:\\Program Files\\java\\jdk1.8.0_121"}], "java.import.gradle.java.home": "D:\\Program Files\\java\\jdk1.8.0_121", "terminal.integrated.defaultProfile.windows": "JavaSE-1.8 Project", "terminal.integrated.profiles.windows": {"JavaSE-1.8 Project": {"overrideName": true, "env": {"PATH": "D:\\Program Files\\java\\jdk1.8.0_121\\bin;${env:PATH}", "JAVA_HOME": "D:\\Program Files\\java\\jdk1.8.0_121"}, "path": "powershell"}}, "java.compile.nullAnalysis.mode": "automatic"}