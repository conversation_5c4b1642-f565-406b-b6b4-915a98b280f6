/*
 * 版权所有 2009-2024山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.bus.system.utils;

import org.jasypt.intf.service.JasyptStatelessService;

/**
 * @version V1.0
 * @author: lin_shen
 * @date: 2018/9/16
 * @Description: Jasypt加密工具类
 */
public enum JasyptEncryptUtil {
    /**
     * 唯一实例,使工具类单例化
     */
    INSTANCE;

    private static JasyptStatelessService jasyptStatelessService=new JasyptStatelessService();

    public static String encrypt(String input,String password,String algorithm){
        return jasyptStatelessService.encrypt(input,password,null,null,algorithm,null,null,null,null,
                null,null,null,null,null,null,
                null,null,null,null,null,null,null);
    }

    /**
     * 使用默认加密算法:PBEWithMD5AndDES
     * PBEWithHmacSHA512AndAES_128
     * @param input 要加密的内容
     * @param password 密钥
     * @return
     */
    public static String encrypt(String input,String password){
        return encrypt(input,password,"PBEWithMD5AndDES");
    }
    /**
     * 使用默认加密算法:PBEWithMD5AndDES
     * PBEWithHmacSHA512AndAES_128
     * @param input 要加密的内容
     * @param password 密钥
     * @return
     */
    public static String desencrypt(String input,String password){
        return jasyptStatelessService.decrypt(input,password,null,null,"PBEWithMD5AndDES",null,null
                ,null,null,null,null,null,null,
                null,null,null,null,null,null,null,
                null,null);
    }
}