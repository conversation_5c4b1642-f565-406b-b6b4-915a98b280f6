package com.snbc.bbpf.grafana.proxy.controller;

import com.snbc.bbpf.grafana.proxy.filter.JwtAuthenticationFilter;
import com.snbc.bbpf.grafana.proxy.service.DynamicTemplateVariableService;
import com.snbc.bbpf.grafana.proxy.interceptor.DashboardResponseInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 动态模板变量测试控制器
 * 
 * 提供测试和调试接口，用于验证动态模板变量功能：
 * 1. 测试变量生成
 * 2. 测试响应拦截
 * 3. 获取服务状态
 * 4. 调试信息输出
 * 
 * 这个控制器主要用于开发和测试阶段，
 * 帮助验证"动态注入模板变量"方案的实现效果。
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2024-12-13
 */
@RestController
@RequestMapping("/api/test/dynamic-variables")
public class DynamicVariableTestController {

    private static final Logger logger = LoggerFactory.getLogger(DynamicVariableTestController.class);

    @Autowired
    private DynamicTemplateVariableService dynamicTemplateVariableService;

    @Autowired
    private DashboardResponseInterceptor dashboardResponseInterceptor;

    /**
     * 测试动态变量生成
     * 
     * @param request HTTP请求
     * @return 生成的变量映射
     */
    @GetMapping("/generate")
    public ResponseEntity<Map<String, Object>> testVariableGeneration(HttpServletRequest request) {
        try {
            String userId = JwtAuthenticationFilter.getUserId(request);
            if (userId == null) {
                return ResponseEntity.badRequest().body(createErrorResponse("用户ID不能为空"));
            }

            logger.info("[动态变量测试] 开始测试变量生成 for user: {}", userId);

            Map<String, Object> variables = dynamicTemplateVariableService.generateUserPermissionVariables(userId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("userId", userId);
            response.put("variableCount", variables.size());
            response.put("variables", variables);
            response.put("timestamp", System.currentTimeMillis());

            logger.info("[动态变量测试] 变量生成成功，共生成 {} 个变量", variables.size());
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("[动态变量测试] 变量生成失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(createErrorResponse("变量生成失败: " + e.getMessage()));
        }
    }

    /**
     * 测试响应拦截和变量注入
     * 
     * @param request HTTP请求
     * @param dashboardJson 测试用的仪表盘JSON
     * @return 注入变量后的JSON
     */
    @PostMapping("/inject")
    public ResponseEntity<Map<String, Object>> testVariableInjection(
            HttpServletRequest request, 
            @RequestBody String dashboardJson) {
        try {
            String userId = JwtAuthenticationFilter.getUserId(request);
            if (userId == null) {
                return ResponseEntity.badRequest().body(createErrorResponse("用户ID不能为空"));
            }

            logger.info("[动态变量测试] 开始测试变量注入 for user: {}", userId);

            // 模拟仪表盘API路径
            String apiPath = "/api/dashboards/uid/test-dashboard";
            
            String originalJson = dashboardJson;
            String modifiedJson = dashboardResponseInterceptor.processResponse(originalJson, userId, apiPath);
            
            boolean isModified = !originalJson.equals(modifiedJson);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("userId", userId);
            response.put("apiPath", apiPath);
            response.put("isModified", isModified);
            response.put("originalLength", originalJson.length());
            response.put("modifiedLength", modifiedJson.length());
            response.put("originalJson", originalJson);
            response.put("modifiedJson", modifiedJson);
            response.put("timestamp", System.currentTimeMillis());

            logger.info("[动态变量测试] 变量注入完成，是否修改: {}", isModified);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("[动态变量测试] 变量注入失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(createErrorResponse("变量注入失败: " + e.getMessage()));
        }
    }

    /**
     * 获取服务状态
     * 
     * @return 服务状态信息
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getServiceStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            status.put("success", true);
            status.put("dynamicTemplateVariableService", dynamicTemplateVariableService != null ? "已启用" : "未启用");
            status.put("dashboardResponseInterceptor", dashboardResponseInterceptor != null ? "已启用" : "未启用");
            status.put("timestamp", System.currentTimeMillis());
            status.put("version", "2.0.0");
            status.put("feature", "动态注入模板变量");

            return ResponseEntity.ok(status);

        } catch (Exception e) {
            logger.error("[动态变量测试] 获取服务状态失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(createErrorResponse("获取服务状态失败: " + e.getMessage()));
        }
    }

    /**
     * 测试特定API路径是否需要拦截
     * 
     * @param apiPath API路径
     * @return 拦截结果
     */
    @GetMapping("/should-intercept")
    public ResponseEntity<Map<String, Object>> testShouldIntercept(@RequestParam String apiPath) {
        try {
            boolean shouldIntercept = dynamicTemplateVariableService.shouldInterceptResponse(apiPath);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("apiPath", apiPath);
            response.put("shouldIntercept", shouldIntercept);
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("[动态变量测试] 测试拦截判断失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(createErrorResponse("测试拦截判断失败: " + e.getMessage()));
        }
    }

    /**
     * 获取示例仪表盘JSON用于测试
     * 
     * @return 示例JSON
     */
    @GetMapping("/sample-dashboard")
    public ResponseEntity<Map<String, Object>> getSampleDashboard() {
        try {
            String sampleJson = "{\n" +
                "  \"dashboard\": {\n" +
                "    \"id\": 1,\n" +
                "    \"uid\": \"test-dashboard\",\n" +
                "    \"title\": \"测试仪表盘\",\n" +
                "    \"templating\": {\n" +
                "      \"list\": [\n" +
                "        {\n" +
                "          \"name\": \"existing_var\",\n" +
                "          \"type\": \"query\",\n" +
                "          \"query\": \"SELECT DISTINCT column FROM table\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    \"panels\": [\n" +
                "      {\n" +
                "        \"id\": 1,\n" +
                "        \"title\": \"测试面板\",\n" +
                "        \"targets\": [\n" +
                "          {\n" +
                "            \"rawSql\": \"SELECT * FROM users WHERE user_id = '$_user_id'\"\n" +
                "          }\n" +
                "        ]\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n" +
                "}";

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("sampleJson", sampleJson);
            response.put("description", "这是一个示例仪表盘JSON，包含现有变量和使用$_user_id变量的查询");
            response.put("usage", "可以将此JSON用于测试变量注入功能");
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("[动态变量测试] 获取示例仪表盘失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(createErrorResponse("获取示例仪表盘失败: " + e.getMessage()));
        }
    }

    /**
     * 创建错误响应
     * 
     * @param message 错误消息
     * @return 错误响应映射
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> error = new HashMap<>();
        error.put("success", false);
        error.put("error", message);
        error.put("timestamp", System.currentTimeMillis());
        return error;
    }

    /**
     * 健康检查接口
     * 
     * @return 健康状态
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "DynamicTemplateVariableService");
        health.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(health);
    }
}