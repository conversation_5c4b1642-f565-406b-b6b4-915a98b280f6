# BBPF Grafana Proxy Local Environment Configuration
# 本地运行的完整配置

# 应用基础配置
spring.application.name=bbpf-grafana-proxy
server.port=8080
server.servlet.session.cookie.http-only=true
server.servlet.session.cookie.secure=false
server.compression.enabled=true
server.compression.mime-types=text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
server.compression.min-response-size=1024

# Spring框架配置
spring.main.allow-circular-references=true

# 日志配置
logging.level.root=info
logging.level.com.snbc.bbpf=info
logging.level.com.snbc.bbpf.grafana.proxy=debug
logging.level.org.springframework.web=debug
logging.level.org.springframework.security=debug
logging.level.redis.clients.jedis=warn
logging.level.org.apache.http=warn

# 加密配置
jasypt.encryptor.password=snbcpwde
jasypt.encryptor.algorithm=PBEWithMD5AndDES

# 数据源配置（如果需要）
# spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
# spring.datasource.url=***********************************************************************************************************************************************************
# spring.datasource.username=root
# spring.datasource.password=ENC(password)

# Redis配置
spring.redis.database=0
spring.redis.host=127.0.0.1
spring.redis.port=6379
# spring.redis.password= # 生产环境中请使用环境变量或安全的配置方式
spring.redis.timeout=3000ms
spring.redis.jedis.pool.max-active=200
spring.redis.jedis.pool.max-wait=-1
spring.redis.jedis.pool.max-idle=10
spring.redis.jedis.pool.min-idle=0

# Jackson配置
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.serialization.write-dates-as-timestamps=false

# 编码配置 - 解决中文乱码问题
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true

# 文件上传配置
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# BBPF Grafana代理核心配置
# Grafana实际服务地址（内部访问）
bbpf.grafana.proxy.grafana-base-url=http://localhost:3000
# 确保代理基础URL正确，与Nginx配置匹配
bbpf.grafana.proxy.proxy-base-url=http://g.xinbeiyang.info

# 静态资源配置
spring.web.resources.static-locations=classpath:/static/,classpath:/public/
spring.web.resources.cache.period=3600
spring.web.resources.add-mappings=true
# 修正属性名以匹配配置类
bbpf.grafana.proxy.grafana-admin-username=admin
bbpf.grafana.proxy.grafana-admin-password= 123456
bbpf.grafana.proxy.permission-api-url=https://dev-bbpfboss.xinbeiyang.info/bossapi/bbpfsystem/api/datapermission
bbpf.grafana.proxy.token-auth-url=https://dev-bbpfboss.xinbeiyang.info/bossapi/bbpfsystem/api/getUser
bbpf.grafana.proxy.bbpf-api-timeout=5000
bbpf.grafana.proxy.grafana-api-token= glsa_YKm1PdUCFHaPHRgVcATIDLV42qHAv8tQ_1943da2a
bbpf.grafana.proxy.bbpf-api-signature-key=
# 启用详细日志以便调试数据获取问题
# 临时禁用Auth Proxy模式，避免与API Token认证冲突
bbpf.grafana.proxy.enable-auth-proxy=false

# WebSocket配置
# 启用WebSocket支持
spring.websocket.enabled=true
# WebSocket消息大小限制（字节）
spring.websocket.max-text-message-buffer-size=8192
spring.websocket.max-binary-message-buffer-size=8192
# WebSocket会话超时时间（毫秒）
spring.websocket.session-timeout=300000
bbpf.grafana.proxy.proxy-timeout=30000

# 系统级JWT配置
bbpf.system.security.jwt.expiration=1
bbpf.system.security.jwt.privateKey = MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCJ+bo+0vwtr8bGuaRtmzGSNtZJxJZuKh2di3sYMe4isj01ixz+1CQPI278EHFeW1jUuEPDc+pW9BBWjsP+iugNvezJbJlvhAHv4GQ31Y1BsEqhgYhJltzwjy20G2rgHzm9pa0AqrM5kDgFUuPfDP/+rs6o3T1mNj+tM/3nOtA6DuEKhcKX3AQIWzDzpnNUI4HbW37PgerM8hCOPmoMRTuVsw6wZ1aOjnDVugmrj+Z1BQpU6ONaKpradU4vyONd5cWde4Xz28MAPGw55QVUCVflQtXHGGOjA1NEgPby1InG6QIdyxBEhsUoVw7LufB6Smk1xAm03UCFRxi4fsakCXFFAgMBAAECggEACAggfl27zlEIKjNOTg8VsKQ+jybBdwI+nRgsfzkQ2rYzd/V0J6Vjv/nZERVH9C0vG5rqXutVfeBRPyiqYhvl7D7dFNCckfBGSJdwXguxYFyJy6MFsvr0O5CqIWzjKM7IhrCpRHurWTN6/1xW5XoVQNceMphGZbfMtyFqq1YyHt7+656215k4BdDkFuJtt1wW+/dTzbPNZykbMem26RVPniJ5JuTE2cbq6QiCrtzdAaE1NEFTnZHdQ5ZqY7kuy+wd52BJHlQnZCdX/SzSevu8aWR/hXXbISSI6vMSOt9g3IliOIJOzdS84EnLYCLBvuY3vjcjtazOXOIV3FTwsObeNQKBgQDKdL2mTVJBHPjDkVs39Hc6+KkOKOSYMoGG6Z+/eQcDrruaXIVuC+V3/d2dG4BcraukuggUykgo5R/eaJnwkBq1PNZU7FstEo71IZXivnE/ITby1ZLCyJ/sm7y/JjpL37hgYcJk/3S+p8dXGw3qIFWXOMQoAskFe/EHIpO1uFiddwKBgQCud1dYdkkZWLOyLh6ihqxSJ7nFEKoh43su6vNLtE9qvJ5BK478RPC6CZ+ltbx5CW6LsFDPtbLhQLZnHMTj1S4wBG6rlhQ7c8SsaE8ogKSvifDZfBDC8ReAuuKalFQ0HJaC38O7kQ/2/+EAougoVDAYwOH1XpFPHww0c5MXgeXmIwKBgFeiULU7ou8sa0G8GZAO8c1E9Wh7qvd2ZTrQxaVL7g1aBTq78cXAwINAED2BOf7j7fhPzk+xf6q0Aydyf6/xsJ3ix6Pa61yHO/o/n3GWnc6FwhM6/1vxG8h/YSQcl/9fx59wsYSmTxJ37YF25H8DoAjlOYNYMUt+asZ74RQ3x6PhAoGBAJNZmfzN0klFAGfxyc/svGeaw6xrubVrgmOK7jc3L4fvjB1wo4/uzf3iGOMFDgyP6byzCl2TkWPrmuKVirj+GMdXRv1GgQoBac9PPePwWqcjcrbMsP9kTjxcGv0BX+ivaHNad97X0ssDK860yC0fhYuyhGUPHNzdVRqLrmTBQiFPAoGAeDig34zSzC7O/NMOwUE7Q2ZHuBIWnEZ5oUE8CEzn3mVd+UAN7JjnIPF90MXOtNwqcPRdUECdD4/sRw8rUN/MmmPMbOtSKdPc0A0hwz5aIMeRf3jValCEn7dOl0Gh+y4MfoOi0Ct47bXEjKI3k/IaVs9VaQjUi1iAx5sAITJ9UOE=
bbpf.system.security.jwt.publicKey = MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAifm6PtL8La/GxrmkbZsxkjbWScSWbiodnYt7GDHuIrI9NYsc/tQkDyNu/BBxXltY1LhDw3PqVvQQVo7D/oroDb3syWyZb4QB7+BkN9WNQbBKoYGISZbc8I8ttBtq4B85vaWtAKqzOZA4BVLj3wz//q7OqN09ZjY/rTP95zrQOg7hCoXCl9wECFsw86ZzVCOB21t+z4HqzPIQjj5qDEU7lbMOsGdWjo5w1boJq4/mdQUKVOjjWiqa2nVOL8jjXeXFnXuF89vDADxsOeUFVAlX5ULVxxhjowNTRID28tSJxukCHcsQRIbFKFcOy7nwekppNcQJtN1AhUcYuH7GpAlxRQIDAQAB
bbpf.system.security.jwt.tokenHead = Bearer

# 权限缓存配置
bbpf.grafana.proxy.enable-permission-cache=true
bbpf.grafana.proxy.permission-cache-expiration-seconds=1800
bbpf.system.redis.cache.ttl=60

# 性能配置
bbpf.grafana.proxy.enable-request-logging=true
bbpf.grafana.proxy.enable-verbose-logging=true
bbpf.grafana.proxy.max-concurrent-requests=100

# 动态模板变量配置
bbpf.grafana.proxy.enable-dynamic-variables=true
bbpf.grafana.proxy.variable-cache-ttl=300
bbpf.grafana.proxy.enable-variable-injection-logging=true

# 安全配置
bbpf.grafana.proxy.enable-csrf-protection=false
# CORS配置 - 允许的源（多个源用逗号分隔）
# 生产环境应配置具体域名，开发环境可使用通配符
bbpf.grafana.proxy.allowed-origins=http://localhost:3000,http://127.0.0.1:3000,http://g.xinbeiyang.info,https://g.xinbeiyang.info

# 健康检查配置
bbpf.grafana.proxy.health-check-interval-seconds=30
bbpf.grafana.proxy.health-check-timeout-seconds=5

# Spring Security配置
spring.security.user.name=admin
spring.security.user.password=snbc1234

# 监控配置
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoints.web.base-path=/monitor/*
management.health.redis.enabled=false
management.health.mongo.enabled=false
management.endpoint.health.show-details=always

# 开发环境特定配置
spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true

# 调试配置
debug=false
trace=false

# SQL拦截器配置
# 启用SQL拦截功能
bbpf.sql.interceptor.enabled=true
# 启用详细日志（用于调试）
bbpf.sql.interceptor.verbose-logging=true
# 权限获取失败时是否拒绝访问
bbpf.sql.interceptor.deny-on-permission-failure=true
# 默认权限过滤条件
bbpf.sql.interceptor.default-permission-filter=1=0
# 拒绝访问时使用的SQL语句
bbpf.sql.interceptor.deny-access-sql=SELECT NULL WHERE 1=0

# 需要拦截的API路径模式
bbpf.sql.interceptor.intercept-patterns[0]=/api/ds/query
bbpf.sql.interceptor.intercept-patterns[1]=/api/datasources/proxy/.*
bbpf.sql.interceptor.intercept-patterns[2]=/api/tsdb/query

# 不需要拦截的API路径模式
bbpf.sql.interceptor.exclude-patterns[0]=/api/health
bbpf.sql.interceptor.exclude-patterns[1]=/api/admin/.*
bbpf.sql.interceptor.exclude-patterns[2]=/api/user/.*
bbpf.sql.interceptor.exclude-patterns[3]=/api/login
bbpf.sql.interceptor.exclude-patterns[4]=/api/logout

# SQL中需要检查和修改的字段名
bbpf.sql.interceptor.sql-fields[0]=rawSql
bbpf.sql.interceptor.sql-fields[1]=sql
bbpf.sql.interceptor.sql-fields[2]=query
bbpf.sql.interceptor.sql-fields[3]=statement
bbpf.sql.interceptor.sql-fields[4]=rawQuery
bbpf.sql.interceptor.sql-fields[5]=expr

# 基础权限字段映射（全局默认）
bbpf.sql.interceptor.permission-field-mapping.orgId=org_id
# 默认用户字段列表（当没有为特定表配置时使用）
# 注释掉全局默认用户字段，避免为不存在该字段的表添加过滤条件
# bbpf.sql.interceptor.user-fields[0]=create_user_id

# 基于表名的权限字段映射配置（根据您的实际表名配置）
# t_log表配置 - 日志表权限过滤
bbpf.sql.interceptor.table-permission-mapping.t_log.user-fields[0]=user_id
# t_user表配置 - 用户表权限过滤
bbpf.sql.interceptor.table-permission-mapping.t_user.user-fields[0]=create_user_id
# t_role表配置 - 角色表权限过滤
bbpf.sql.interceptor.table-permission-mapping.t_role.user-fields[0]=create_user_id
# t_org表配置 - 组织表权限过滤
bbpf.sql.interceptor.table-permission-mapping.t_org.user-fields[0]=create_user_id
# 完全排除权限过滤的表列表
bbpf.sql.interceptor.exclude-tables[0]=t_upgrade_plan

# bbpf.sql.interceptor.table-permission-mapping.t_upgrade_plan.user-fields=
# 注释掉 t_upgrade_plan 表的用户字段配置，使其不进行任何权限过滤
# 支持前缀匹配的配置示例（如果您有其他以t_开头的表）
# t_*表通用配置 - 适用于所有以t_开头的表（优先级低于精确匹配）
# 注释掉通配符配置，避免为不存在该字段的表添加过滤条件
# bbpf.sql.interceptor.table-permission-mapping.t_*.user-fields[0]=create_user_id

# Grafana分析功能控制配置
# 是否禁用Grafana分析功能（Explore和Inspect）
# true: 禁用分析功能，false: 启用分析功能
bbpf.grafana.proxy.disable-analysis-features=true


