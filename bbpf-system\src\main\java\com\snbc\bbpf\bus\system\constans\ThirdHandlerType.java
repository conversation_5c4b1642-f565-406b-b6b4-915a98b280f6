package com.snbc.bbpf.bus.system.constans;


import com.snbc.bbpf.bus.system.enums.ThirdBindEnum;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <p>
 *  三方登录自定义类型
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-04
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
public @interface ThirdHandlerType {
    /**
     * 策略类型
     * @return
     */
    ThirdBindEnum value();
}
