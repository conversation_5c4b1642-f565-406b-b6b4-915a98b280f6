# Grafana 代理服务设计文档

## 1. 概述

本文档旨在阐述 Grafana 代理服务（bbpf-grafana-proxy）的核心设计、系统架构和关键流程。该服务作为 Grafana 的前端代理，旨在提供一个统一的身份验证和数据权限控制层，使得用户可以通过平台的 JWT Token 安全地访问 Grafana，并根据其权限查看相应的数据。

主要功能点：
- **统一认证**：通过 JWT Filter 拦截请求，实现基于平台 JWT 的单点登录。
- **HTTP 代理**：将用户的常规 HTTP 请求（如仪表盘查询）代理到后端的 Grafana 服务。
- **WebSocket 代理**：支持 Grafana Live 等功能的 WebSocket 连接代理。
- **权限控制**：基于用户权限动态修改 Grafana 查询语句，实现数据隔离。

## 2. 系统架构

系统由用户、Grafana 代理服务和后端的 Grafana 实例组成。代理服务是核心，负责处理所有来自用户的请求，并与 Grafana 进行安全通信。

```mermaid
graph TD
    subgraph "用户端"
        A[用户浏览器]
    end

    subgraph "Grafana 代理服务 (Spring Boot)"
        B[Controller/Filter]
        C[Proxy Services]
        D[WebSocket Handler]
        E[Grafana API Client]
        F[权限服务]
        G[JWT 工具]
    end

    subgraph "后端服务"
        H[Grafana 实例]
        I[Redis 缓存]
    end

    A -- HTTPS/WSS 请求 --> B
    B -- 验证请求 --> G
    B -- 传递请求 --> C
    A -- WebSocket 连接请求 --> D
    C -- 代理 HTTP 请求 --> E
    D -- 代理 WebSocket 消息 --> H
    E -- 调用 Grafana API --> H
    C -- 查询用户权限 --> F
    F -- 缓存/获取权限 --> I

    style B fill:#f9f,stroke:#333,stroke-width:2px
    style D fill:#f9f,stroke:#333,stroke-width:2px
```

## 3. 核心流程

### 3.1 统一认证与 HTTP 代理流程

用户通过浏览器访问代理服务，代理服务验证 JWT Token，然后将请求转发给 Grafana。

```mermaid
sequenceDiagram
    participant User as 用户
    participant Proxy as Grafana 代理
    participant Grafana as Grafana 实例

    User->>+Proxy: 发起 HTTP 请求 (携带 JWT)
    Proxy->>Proxy: JwtAuthenticationFilter 拦截请求
    Proxy->>Proxy: JwtUtil 解析并验证 JWT
    alt JWT 有效
        Proxy->>Proxy: 获取用户权限信息
        Proxy->>Proxy: GrafanaProxyService 处理请求
        Proxy->>+Grafana: 转发 HTTP 请求
        Grafana-->>-Proxy: 返回响应
        Proxy-->>-User: 返回最终响应
    else JWT 无效或过期
        Proxy-->>User: 返回 401 未授权
    end
```

### 3.2 WebSocket 代理流程

为了支持 Grafana Live 等实时功能，代理服务需要处理 WebSocket 连接。认证过程在握手阶段完成。

```mermaid
sequenceDiagram
    participant User as 用户
    participant Interceptor as WebSocketHandshakeInterceptor
    participant Handler as GrafanaWebSocketProxyHandler
    participant Grafana as Grafana 实例

    User->>+Interceptor: 发起 WebSocket 握手请求 (携带认证信息)
    Interceptor->>Interceptor: 验证用户身份 (如 JWT)
    alt 认证成功
        Interceptor-->>User: 握手成功
        User->>+Handler: 建立 WebSocket 连接
        Handler->>+Grafana: 建立后端 WebSocket 连接
        loop 消息代理
            User-->>Handler: 发送消息
            Handler-->>Grafana: 转发消息
            Grafana-->>Handler: 接收消息
            Handler-->>User: 转发消息
        end
    else 认证失败
        Interceptor-->>-User: 拒绝连接
    end
```

## 4. 模块设计

项目代码结构清晰，主要模块如下：

- **`proxy.controller.GrafanaProxyController`**:
  - 职责：接收所有 `/{path:.*}` 的 HTTP 请求，作为代理的统一入口点。
  - 调用 `GrafanaProxyService` 来处理实际的代理逻辑。

- **`proxy.filter.JwtAuthenticationFilter`**:
  - 职责：Spring Security 过滤器，在每个请求到达 Controller 之前执行。
  - 从请求头中提取 JWT，使用 `JwtUtil` 进行验证，并将认证信息存入 SecurityContext。

- **`proxy.service`**:
  - **`GrafanaProxyService`**: HTTP 代理的核心服务，负责构建对后端 Grafana 的请求、转发并返回响应。
  - **`PermissionService`**: 权限服务，根据用户信息从 Redis 或数据库获取其数据权限。
  - **`GrafanaVariableService`**: 负责处理 Grafana 模板变量的查询，可能会根据用户权限进行过滤。

- **`proxy.handler.GrafanaWebSocketProxyHandler`**:
  - 职责：处理 WebSocket 连接的生命周期和消息转发。
  - 在与客户端建立连接后，它会与后端 Grafana 建立一个对应的连接，并在两者之间双向代理消息。

- **`proxy.interceptor.WebSocketHandshakeInterceptor`**:
  - 职责：在 WebSocket 握手阶段进行拦截，用于身份验证。
  - 只有通过验证的连接才允许升级为 WebSocket 协议。

- **`proxy.config`**:
  - **`SecurityConfig`**: 配置 Spring Security，如禁用 CSRF、定义过滤器链等。
  - **`WebSocketConfig`**: 配置 WebSocket Endpoints 和处理器。
  - **`GrafanaProxyConfig`**: 存放 Grafana 相关的配置，如后端 URL。

- **`proxy.client.GrafanaApiClient`**:
  - 职责：一个封装了的 HTTP 客户端，专门用于与后端 Grafana API 进行通信。

## 5. 关键配置

应用的关键配置项位于 `application.properties` 文件中，并可能根据不同的 profile (`local`, `docker`) 有所不同。

| 属性名 | 描述 | 示例 |
| --- | --- | --- |
| `grafana.url` | 后端 Grafana 实例的地址 | `http://localhost:3000` |
| `jwt.secret` | 用于签发和验证 JWT 的密钥 | `your-very-secret-key` |
| `jwt.expiration` | JWT 的过期时间（毫秒） | `86400000` |
| `spring.redis.host` | Redis 服务器地址 | `localhost` |
| `spring.redis.port` | Redis 服务器端口 | `6379` |

## 6. 部署与运行

1.  **打包**: 使用 Maven 进行打包。
    ```bash
    mvn clean package
    ```
2.  **运行**: 使用 `java -jar` 命令运行，并指定相应的 profile。
    ```bash
    # 使用 local 配置运行
    java -jar target/bbpf-grafana-proxy-*.jar --spring.profiles.active=local

    # 使用 docker 配置运行
    java -jar target/bbpf-grafana-proxy-*.jar --spring.profiles.active=docker
    ```
也可以直接执行项目根目录下的 `run.bat` 脚本。
