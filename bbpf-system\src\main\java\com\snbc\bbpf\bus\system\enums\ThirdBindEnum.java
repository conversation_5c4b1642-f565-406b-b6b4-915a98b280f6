package com.snbc.bbpf.bus.system.enums;

/**
 * 三方绑定策略自定义类型
 *
 */
public enum ThirdBindEnum {
    /**
     * 微信
     */
    WEIXIN("41","微信"),
    DINGDING("42","钉钉"),
    ALI("43","阿里");
    private String type;
    private String desc;
    /**
     *
     * @param type
     * @param desc
     */
    ThirdBindEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }
    public String getType() {
        return type;
    }
    public String getDesc() {
        return desc;
    }

}
