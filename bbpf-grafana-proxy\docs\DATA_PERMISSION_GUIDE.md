# Grafana数据级权限控制指南

本文档介绍如何使用BBPF Grafana代理服务实现数据级权限控制。

## 概述

数据级权限控制通过Grafana动态变量机制实现，代理服务会根据用户权限自动为仪表盘注入权限相关的变量，这些变量可以在SQL查询中使用，从而实现数据过滤。

## 架构设计

```
用户请求 → JWT认证 → 权限获取 → 变量生成 → 仪表盘注入 → Grafana渲染
```

### 核心组件

1. **PermissionService**: 负责获取用户权限信息
2. **GrafanaVariableService**: 负责生成和管理权限变量
3. **GrafanaApiClient**: 负责与Grafana API交互
4. **GrafanaProxyController**: 提供权限变量相关的API端点

## 权限变量类型

### 1. 用户信息变量

- `user_id`: 当前用户ID
- `user_name`: 当前用户名
- `user_role`: 用户角色
- `user_dept`: 用户部门
- `user_org`: 用户组织

### 2. 数据权限变量

- `data_filter_condition`: 数据过滤条件
- `accessible_orgs`: 可访问的组织列表
- `accessible_depts`: 可访问的部门列表
- `accessible_projects`: 可访问的项目列表

### 3. 访问权限变量

- `perm_dashboards`: 可访问的仪表盘ID列表
- `perm_datasources`: 可访问的数据源ID列表
- `perm_folders`: 可访问的文件夹ID列表

## 使用方法

### 1. 在SQL查询中使用变量

```sql
-- 基于用户ID过滤数据
SELECT * FROM user_data 
WHERE user_id = '${user_id}'

-- 基于组织权限过滤
SELECT * FROM organization_data 
WHERE org_id IN (${accessible_orgs})

-- 基于复杂权限条件过滤
SELECT * FROM business_data 
WHERE ${data_filter_condition}

-- 基于部门权限过滤
SELECT * FROM department_data 
WHERE dept_id IN (${accessible_depts})
AND created_by = '${user_id}'
```

### 2. 在仪表盘中配置变量

#### 创建权限变量

1. 进入仪表盘设置
2. 选择"Variables"选项卡
3. 添加新变量，类型选择"Constant"
4. 变量名使用权限相关前缀（如`user_`, `perm_`, `data_filter_`等）
5. 变量值将由代理服务自动注入

#### 变量配置示例

```json
{
  "name": "user_id",
  "type": "constant",
  "label": "用户ID",
  "hide": 2,
  "query": {
    "query": "${user_id}"
  }
}
```

### 3. API使用

#### 获取用户权限变量

```bash
GET /api/variables/user-permissions
Authorization: Bearer <JWT_TOKEN>
```

响应示例：
```json
{
  "variables": {
    "user_id": "user123",
    "user_role": "manager",
    "accessible_orgs": "1,2,3",
    "data_filter_condition": "org_id IN (1,2,3) AND dept_id = 5"
  },
  "userId": "user123",
  "timestamp": 1703123456789
}
```

#### 设置仪表盘变量

```bash
POST /api/dashboards/{dashboardId}/variables
Authorization: Bearer <JWT_TOKEN>
```

响应示例：
```json
{
  "message": "Dashboard variables set successfully",
  "dashboardId": "dashboard123",
  "userId": "user123",
  "timestamp": 1703123456789
}
```

## 配置说明

### 1. 应用配置

在`application-grafana.yml`中配置：

```yaml
grafana:
  variables:
    enabled: true
    prefix: "bbpf_"
    cache-ttl: 300
    data-permission:
      user-id-var: "user_id"
      org-id-var: "org_id"
      filter-var: "data_filter"
```

### 2. 权限模拟配置

开发环境可以启用权限模拟：

```yaml
bbpf:
  permission:
    mock:
      enabled: true
      admin-users:
        - admin
        - administrator
```

## 安全考虑

### 1. 变量值验证

- 自动检测SQL注入风险
- 过滤危险字符和关键词
- 限制变量值长度和格式

### 2. 权限检查

- 验证用户对仪表盘的访问权限
- 检查变量设置权限
- 记录权限变更日志

### 3. 缓存安全

- 权限信息缓存加密
- 定期清理过期缓存
- 支持强制刷新权限

## 最佳实践

### 1. 变量命名规范

- 使用统一的前缀（如`user_`, `org_`, `perm_`）
- 变量名要有明确的含义
- 避免使用保留关键字

### 2. SQL查询优化

- 在WHERE子句中优先使用权限变量
- 避免在SELECT子句中使用权限变量
- 合理使用索引提高查询性能

### 3. 权限设计

- 遵循最小权限原则
- 设计层次化的权限结构
- 定期审查和更新权限配置

## 故障排除

### 1. 变量未生效

检查项：
- 用户是否已认证
- 权限服务是否正常
- 变量名是否正确
- Grafana连接是否正常

### 2. 权限获取失败

检查项：
- BBPF权限API是否可用
- 用户权限是否存在
- 缓存是否过期
- 网络连接是否正常

### 3. SQL查询错误

检查项：
- 变量值是否正确
- SQL语法是否正确
- 数据库连接是否正常
- 权限过滤条件是否合理

## 监控和日志

### 1. 关键指标

- 权限变量生成成功率
- 仪表盘变量设置成功率
- 权限获取响应时间
- 缓存命中率

### 2. 日志配置

```yaml
logging:
  level:
    com.snbc.bbpf.grafana.proxy.service.GrafanaVariableService: DEBUG
    com.snbc.bbpf.grafana.proxy.client.GrafanaApiClient: DEBUG
```

### 3. 健康检查

访问`/health`端点检查服务状态：

```json
{
  "status": "UP",
  "details": {
    "grafana": "UP",
    "permissionService": "UP"
  }
}
```

## 示例场景

### 场景1：部门级数据隔离

用户只能查看自己部门的数据：

```sql
SELECT * FROM sales_data 
WHERE dept_id = '${user_dept}'
AND date >= '2024-01-01'
```

### 场景2：多组织数据访问

管理员可以查看多个组织的数据：

```sql
SELECT org_name, SUM(revenue) as total_revenue
FROM organization_revenue 
WHERE org_id IN (${accessible_orgs})
GROUP BY org_name
```

### 场景3：基于角色的数据过滤

不同角色看到不同级别的数据：

```sql
SELECT 
  CASE 
    WHEN '${user_role}' = 'admin' THEN customer_name
    WHEN '${user_role}' = 'manager' THEN CONCAT(LEFT(customer_name, 3), '***')
    ELSE '***'
  END as customer,
  order_amount
FROM orders
WHERE ${data_filter_condition}
```

## 版本历史

- v2.0.0: 初始版本，支持基本的权限变量注入
- v2.0.1: 增加安全验证和缓存优化
- v2.0.2: 支持复杂权限条件和多级权限

## 技术支持

如有问题，请联系BBPF技术团队或查看相关文档：

- 项目文档：`docs/`目录
- API文档：`/swagger-ui.html`
- 健康检查：`/health`
- 监控指标：`/metrics`