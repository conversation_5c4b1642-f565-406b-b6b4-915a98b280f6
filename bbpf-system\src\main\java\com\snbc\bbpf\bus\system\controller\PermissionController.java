/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.controller;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.snbc.bbpf.bus.system.mapstruct.ClassConvertorMapper;
import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.bus.system.service.DataRuleService;
import com.snbc.bbpf.bus.system.service.PermissionService;
import com.snbc.bbpf.bus.system.utils.ResultUtil;
import com.snbc.bbpf.buslog.annotations.Buslog;
import com.snbc.bbpf.buslog.annotations.OperationType;
import com.snbc.bbpf.system.db.common.entity.Permission;
import com.snbc.bbpf.system.db.common.entity.PermissionNode;
import com.snbc.bbpf.system.db.common.entity.PermissionScope;
import com.snbc.bbpf.system.db.common.vo.PermissionSortVo;
import com.snbc.bbpf.system.db.common.vo.PermissionStatusVo;
import com.snbc.bbpf.system.db.common.vo.PermissionVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: PermissionController
 * 权限定义
 * @module: bbpf-bus-system
 * @Author: yangweipeng
 * @date: 2021/5/18
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 */
@RestController
@RequestMapping("/console/permission")
@Slf4j
public class PermissionController {

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private DataRuleService dataRuleService;
    /**
     * 获取权限树列表
     *
     * @param level
     * @return
     * @throws Exception
     */
    @GetMapping("/getPermissionTree")
    public CommonResp<List<PermissionNode>> getPermissionTree(@RequestParam(value = "level") String level) throws Exception {
        CommonResp<List<PermissionNode>> commonResp = new CommonResp<>();
        List<PermissionNode> permissionNodes=new ArrayList<>();
        PermissionNode node=permissionService.getPermissionTree(Integer.parseInt(level));
        permissionNodes.add(node);
        commonResp.setHead(ResultUtil.success());
        commonResp.setBody(permissionNodes);
        return commonResp;
    }

    /**
     * 拖拽排序
     *
     * @param permissionSortVo
     * @return
     * @throws Exception
     */
    @PostMapping("/dragOrderBy")
    @Buslog(opration = "permissionSort", zh = "权限排序：已更新",en = "Permission order: updated",
            target = "permissionManage")
    public CommonResp<String> dragOrderBy(@RequestBody PermissionSortVo permissionSortVo) throws Exception {
        CommonResp<String> commonResp = new CommonResp<>();
        if(permissionService.permissionSort(permissionSortVo)){
            commonResp.setHead(ResultUtil.success());
            commonResp.setBody("true");
        }else {
            commonResp.setHead(ResultUtil.error("拖拽排序失败"));
        }
        return commonResp;
    }
    /**
     *
     * @param url
     * @return
     */
    @GetMapping(value = "/getUserDataRule")
    public CommonResp<List<PermissionScope>> getUserDataRule(@RequestParam(value = "url") String url)  {
        CommonResp<List<PermissionScope>> commonResp = new CommonResp<>();
        List<PermissionScope> list = dataRuleService.getUserDataRule(url);
        commonResp.setHead(ResultUtil.success());
        commonResp.setBody(list);
        return commonResp;
    }


    /**
     *根据当前登录人组织机构获取
     * @return
     */
    @GetMapping(value = "/getUserListByCurrentUserOrg")
    public CommonResp<List<String>> getUserListByCurrentUserOrg(String orgType) throws IOException {
        CommonResp<List<String>> commonResp = new CommonResp<>();
        List<String> userList= dataRuleService.getUserListByCurrentUserOrg(orgType);
        commonResp.setHead(ResultUtil.success());
        commonResp.setBody(userList);
        return commonResp;
    }
    /**
     *根据当前登录人查询其下级组织机构
     * @return
     */
    @GetMapping(value = "/getOrgIdListByDataRuleType")
    public CommonResp<List<String>> getOrgIdListByDataRuleType(String orgType) {
        CommonResp<List<String>> commonResp = new CommonResp<>();
        List<String> userList= dataRuleService.getOrgIdListByDataRuleType(orgType);
        commonResp.setHead(ResultUtil.success());
        commonResp.setBody(userList);
        return commonResp;
    }
    /**
     *
     *  获取权限值
     * @param roleIds
     * @param sysType
     * @return CommonResp<List<String>>
     */
    @GetMapping(value = "/getResourcePermission")
    public CommonResp<List<String>> getResourcePermission(@RequestParam(value = "roleIds") String roleIds,
                                                          @RequestParam(value = "sysType") String sysType) throws IOException {
        CommonResp<List<String>> commonResp = new CommonResp<>();
        List<String> list = permissionService.getResourcePermissionsByRoleIds(roleIds, sysType);
        commonResp.setHead(ResultUtil.success());
        commonResp.setBody(list);
        return commonResp;
    }

    /**
     * 更新权限定义状态
     *
     * @param permissionId
     * @return
     * @throws Exception
     */
    @GetMapping("/getList")
    public CommonResp<List<PermissionVo>> getList(@Param("permissionId") String permissionId) throws Exception {
        CommonResp<List<PermissionVo>> commonResp = new CommonResp<>();
        List<Permission> list=null;
        if(permissionId==null||"".equals(permissionId)) {
            list = permissionService.getAllPermission();
        }else {
            list = permissionService.getAllPermissionById(permissionId);
        }
        if(list!=null){
            List<PermissionVo> permissionVoList=new ArrayList<>();
            list.forEach(item->{
                PermissionVo permissionVo = getPermissionVo(item);
                permissionVoList.add(permissionVo);
            });
            commonResp.setHead(ResultUtil.success());
            commonResp.setBody(permissionVoList);
        }else {
            commonResp.setHead(ResultUtil.error("获取列表数据出错"));
        }
        return commonResp;
    }

    private PermissionVo getPermissionVo(Permission permission) {
        PermissionVo permissionVo=new PermissionVo();
        permissionVo.setHasEnable(permission.getHasEnable());
        permissionVo.setPermissionIcon(permission.getPermissionImage());
        permissionVo.setOrderBy(permission.getOrderBy());
        permissionVo.setParentId(permission.getParentId());
        permissionVo.setRemarks(permission.getPermissionDesc());
        permissionVo.setRoutingUrl(permission.getRoutingUrl());
        permissionVo.setPermissionName(permission.getPermissionName());
        permissionVo.setPermissionCode(permission.getPermissionCode());
        permissionVo.setPermissionType(permission.getPermissionType());
        permissionVo.setSysType(permission.getSysType());
        permissionVo.setPermissionLevel(permission.getPermissionLevel());
        permissionVo.setParentName(permission.getParentName());
        permissionVo.setPermissionId(permission.getPermissionId());
        return permissionVo;
    }

    /**
     * 更新权限定义状态
     *
     * @param permissionStatusVo
     * @return
     * @throws Exception
     */
    @PostMapping("/changeStatus")
    @Buslog(opration = "changPermissionStatus", target = "permissionManage", type = PermissionStatusVo.class,
            zh = "${hasEnable}【${permissionName}】",
            en = "${hasEnable}[${permissionName}]")
    public CommonResp<String> changeStatus(@RequestBody PermissionStatusVo permissionStatusVo) throws Exception {
        CommonResp<String> commonResp = new CommonResp<>();
        Permission permission= ClassConvertorMapper.INSTANCE.convertPermission(permissionStatusVo);
        permissionService.updatePermission(permission);
        commonResp.setHead(ResultUtil.success());
        return commonResp;
    }

    /**
     * 权限详情
     *
     * @param permissionId
     * @return
     * @throws Exception
     */
    @GetMapping("/detail")
    public CommonResp<PermissionVo> detail(@RequestParam(value = "permissionId") String permissionId) throws Exception {
        CommonResp<PermissionVo> commonResp = new CommonResp<>();
        Permission permission=permissionService.queryPermission(permissionId);
        PermissionVo permissionVo = ClassConvertorMapper.INSTANCE.convertPermissionVo(permission);
        commonResp.setHead(ResultUtil.success());
        commonResp.setBody(permissionVo);
        return commonResp;
    }
    /**
     * 删除权限定义
     *
     * @param permission
     * @return
     * @throws Exception
     */
    @DeleteMapping("/delPermission")
    @Buslog(opration = "delPermission", zh = "删除权限【权限名称：${permissionNames}，权限编码：${permissionCodes}】",
            en = "Delete permission [permission name:${permissionNames}, permission code:${permissionCodes}]",
            target = "permissionManage")
    public CommonResp delPermission(@RequestBody  String permission) throws Exception {
        //获取json中roleId的值
        JSONObject jsonObject = JSONObject.parseObject(permission);
        permissionService.deletePermissions(jsonObject.getJSONArray("permissionIds").toArray(new String[0]));
        return CommonResp.builder().head(ResultUtil.success()).build();
    }
    /**
     * 判断权限代码是否存在
     * @param permissionCode
     * @return
     * @throws Exception
     */
    @PostMapping("/isExist")
    public CommonResp<String>  isExist(@RequestBody String permissionCode) throws Exception{
        CommonResp<String> commonResp = new CommonResp<>();
        ObjectMapper objectMapper = new ObjectMapper();
        log.info("The received permission encoding parameter：{}",permissionCode);
        String strCode=objectMapper.readTree(permissionCode).get("permissionCode").asText();
        log.info("Parameter after json deserialization：{}",strCode);
        Permission item= permissionService.selectByPermissionCode(strCode);

        commonResp.setHead(ResultUtil.success());
        if (null == item) {
            commonResp.setBody("false");
        } else {
            commonResp.setBody("true");
        }
        return commonResp;
    }
    /**
     * 更新权限数据
     * 权限名称唯一
     *
     * @param permissionVo
     * @return
     * @throws Exception
     */
    @PutMapping("/editPermission")
    @Buslog(oprationType = OperationType.MODIFY,opration = "modifyPermission", target = "permissionManage",type = PermissionVo.class )
    public CommonResp<String> updatePermission(@RequestBody PermissionVo permissionVo) throws Exception {
        CommonResp<String> commonResp = new CommonResp<>();
        Permission permission = ClassConvertorMapper.INSTANCE.convertPermission(permissionVo);
        Permission item= permissionService.selectByPermissionCode(permission.getPermissionCode());
        if(item!=null&&!item.getPermissionId().equals(permission.getPermissionId())){
            commonResp.setHead(ResultUtil.error("权限编码已存在"));
        }else {
            int num = permissionService.updatePermission(permission);
            if (1 == num) {
                commonResp.setHead(ResultUtil.success());
            } else {
                commonResp.setHead(ResultUtil.error("权限定义更新失败"));
            }
        }
        return commonResp;
    }
    /**
     * 新增权限代码
     * @param permissionVo
     * @return
     * @throws Exception
     */
    @PostMapping("/addPermission")
    @Buslog(opration = "addPermission", zh = "新增权限【权限名称：${permissionName}，权限编码：${permissionCode}】",
            en = "New permission [permission name:${permissionName}, permission code:${permissionCode}]", target = "permissionManage")
    public CommonResp<String> addPermission(@Valid @RequestBody PermissionVo permissionVo) throws Exception {
        CommonResp<String> commonResp = new CommonResp<>();
        Permission permission = ClassConvertorMapper.INSTANCE.convertPermission(permissionVo);
        Permission item = permissionService.selectByPermissionCode(permission.getPermissionCode());
        if (null == item) {
            permissionService.insertPermission(permission);
            commonResp.setHead(ResultUtil.success());
        } else {
            commonResp.setHead(ResultUtil.error("权限编码已存在"));
        }
        return commonResp;
    }
}
