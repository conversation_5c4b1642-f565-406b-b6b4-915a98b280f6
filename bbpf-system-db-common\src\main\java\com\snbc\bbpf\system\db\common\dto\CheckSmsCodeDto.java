/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.dto;

import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotBlank;


/**
 * @ClassName: CheckSmsCodeDto
 * 请求二维码请求
 * @module: si-bbpf-system
 * @Author: liangjb
 * @date: 2021/5/19 14:07
 */
@Data
@Builder
public class CheckSmsCodeDto {


    /**
     * 短信验证码
     * */
    @NotBlank( message = "短信验证码不能为空" )
    private String smsCode;




    /**
     * 41：绑定微信三方,42：绑定钉钉三方
     * */
    @NotBlank(message = "绑定三方类型不能为空")
    private String msgType;


}
