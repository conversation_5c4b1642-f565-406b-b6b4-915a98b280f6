/*
 * 版权所有 2009-2024山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.system.db.common.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 权限信息
 *
 * @ClassName: PermissionInfo
 * @module: SI-bbpf-system
 * @Author: wangsong
 * @date: 2024/9/23
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PermissionInfo{
    private String permissionId;
    private String permissionName;
    private Integer permissionType;
    private String parentId;

    private Boolean checked = false;
    private List<PermissionInfo> childrenPermissions;
}
