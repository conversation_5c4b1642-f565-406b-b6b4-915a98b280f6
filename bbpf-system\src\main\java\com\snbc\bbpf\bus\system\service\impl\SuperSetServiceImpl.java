/*
 * 版权所有 2009-2024山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.bus.system.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.snbc.bbpf.bus.system.config.BBPFSecurityConfig;
import com.snbc.bbpf.bus.system.service.SuperSetService;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.system.db.common.entity.login.RedisUserStatus;
import com.snbc.bbpf.system.db.common.mapper.RoleMapper;
import com.snbc.bbpf.system.db.common.vo.SuperSetUserVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * superset实现
 *
 * @ClassName: SuperSetServiceImpl
 * @module: SI-bbpf-system
 * @Author: wangsong
 * @date: 2024/3/16
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Slf4j
@Service
public class SuperSetServiceImpl implements SuperSetService {
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private RoleMapper roleMapper;
    @Autowired
    private BBPFSecurityConfig bbpfSecurityConfig;

    /**
     * 获取当前用户的信息，包括用户名、角色ID列表、用户ID、token过期时间。
     * 这个方法不接受任何参数，返回一个包含用户详细信息的SuperSetUserVo对象。
     *
     * @return SuperSetUserVo 包含用户姓名、角色ID列表、用户ID和token过期时间的对象。
     */
    @Override
    public SuperSetUserVo getUser() {
        // 获取当前用户ID
        String userId = CurrentUser.getUserId();
        // 获取当前系统类型
        String sysType = CurrentUser.getSysType();
        // 从Redis中根据用户ID和系统类型获取用户状态信息
        RedisUserStatus redisUser = getRedisUserStatus(sysType + ":" + userId);
        // 检查redisUser是否为空
        if (redisUser != null) {
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"); // 定义日期格式化器
            // 防止optTime为null，进行非空判断
            String optTime = redisUser.getOptTime();
            List<String> roleIds = new ArrayList<>();
            LocalDateTime expireTime = null;
            if (optTime != null) {
                // 根据用户状态中的操作时间计算token的过期时间
                expireTime = LocalDateTime.parse(redisUser.getOptTime(), df).plusHours(bbpfSecurityConfig.getExpiration());
                roleIds = roleMapper.getRoleIdsByUserId(userId); // 根据用户ID获取角色ID列表
            }
            // 构建并返回用户信息Vo对象
            return SuperSetUserVo.builder().userName(CurrentUser.getUserName()).roleIds(roleIds).userId(userId).expireTime(expireTime).build();
        } else {
            // 如果redisUser为空，你可以选择抛出异常、设置默认值或进行其他逻辑处理
            throw new RuntimeException("未能获取到用户的Redis登录信息");
        }
    }

    @Override
    public SuperSetUserVo getDataPermission(String userId) {
        // 获取当前系统类型
        String sysType = CurrentUser.getSysType();
        // 从Redis中根据用户ID和系统类型获取用户状态信息
        RedisUserStatus redisUser = getRedisUserStatus(sysType + ":" + userId);
        // 检查redisUser是否为空
        if (redisUser != null) {
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"); // 定义日期格式化器
            // 防止optTime为null，进行非空判断
            String optTime = redisUser.getOptTime();
            List<String> roleIds = new ArrayList<>();
            LocalDateTime expireTime = null;
            if (optTime != null) {
                // 根据用户状态中的操作时间计算token的过期时间
                expireTime = LocalDateTime.parse(redisUser.getOptTime(), df).plusHours(bbpfSecurityConfig.getExpiration());
                roleIds = roleMapper.getRoleIdsByUserId(userId); // 根据用户ID获取角色ID列表
            }
            // 构建并返回用户信息Vo对象
            return SuperSetUserVo.builder().userName(CurrentUser.getUserName()).roleIds(roleIds).userId(userId).expireTime(expireTime).build();
        } else {
            // 如果redisUser为空，你可以选择抛出异常、设置默认值或进行其他逻辑处理
            throw new RuntimeException("未能获取到用户的Redis登录信息");
        }
    }

    /**
     * 从Redis获取用户的缓存状态
     *
     * @param userKey 用户的唯一标识，用于在Redis中定位用户的状态信息
     * @return 返回从Redis中读取到的用户状态信息，如果未找到或发生异常则返回null
     */
    private RedisUserStatus getRedisUserStatus(String userKey) {
        // 从Redis的哈希表中获取指定用户的状态信息
        Object userInfo = redisTemplate.opsForHash().get("user-login-status", userKey);
        RedisUserStatus redisUser = null;

        if (null != userInfo) {
            // 尝试将获取到的信息转换为RedisUserStatus对象
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                redisUser = objectMapper.readValue(userInfo.toString(), RedisUserStatus.class);
            } catch (IOException e) {
                // 记录转换过程中发生的异常
                log.error("get userstatus info from redis error,userkey={}",userKey, e);
            }
        }

        return redisUser;
    }



}
