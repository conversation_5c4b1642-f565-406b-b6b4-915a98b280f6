/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.mapper;

import com.snbc.bbpf.system.db.common.entity.WeakPassword;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * 弱密码管理Mapper接口
 *
 * <AUTHOR>
 * @date 2023-01-16
 */
@Mapper
public interface WeakPasswordMapper {
    /**
     * 查询弱密码管理
     * 
     * @param id 弱密码管理主键
     * @return 弱密码管理
     */
    WeakPassword selectWeakPasswordById(String id);

    /**
     * 查询弱密码管理列表
     * 
     * @param weakPassword 弱密码管理
     * @return 弱密码管理集合
     */
    List<WeakPassword> selectWeakPasswordList(WeakPassword weakPassword);

    /**
     * 新增弱密码管理
     * 
     * @param weakPassword 弱密码管理
     * @return 受影响行数
     */
    int insertWeakPassword(WeakPassword weakPassword);

    /**
     * 修改弱密码管理
     * 
     * @param weakPassword 弱密码管理
     * @return 受影响行数
     */
    int updateWeakPassword(WeakPassword weakPassword);

    /**
     * 删除弱密码管理
     * 
     * @param id 弱密码管理主键
     * @return 受影响行数
     */
    int deleteWeakPasswordById(String id);

    /**
     * 批量删除弱密码管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 受影响行数
     */
    int deleteWeakPasswordByIds(String[] ids);
}
