package com.snbc.bbpf.bus.system.enums;

import java.util.regex.Pattern;

/**
 * @ClassName: PwdStrengthEnum
 * @Description: 密码强度校验
 * @module: SI-bbpf-system
 * @Author: wangsong
 * @date: 2023/5/22
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
public enum PwdStrengthEnum {

    //弱，表示该密码长度设置只需要>=8位即可<=20
    LOW("low",Pattern.compile("^(?=\\S+$).{8,20}$")),
    //中，表示该密码长度>=8,并且包含字母和数字<=20
    MIDDLE("middle", Pattern.compile("^(?=.*[A-Za-z])(?=.*\\d|[\\W_])(?=\\S+$).{8,20}$")),
    //强，表示该密码长度>=8,并且包含大小写字母和数字同时，要包含特殊字符.<=20（默认值）
    HIGH("high", Pattern.compile("^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?\\d)(?=.*[`~!@#$%^&*()_\\-+=" +
            "<>?:\"{}|,.\\/;'\\[\\]·~！@#￥%……&*（）——\\-+={}|《》？：“”【】、；‘'，。、])(?=\\S+$).{8,20}$"));

    private String pwdStrength;
    private Pattern regular;

    PwdStrengthEnum(String pwdStrength, Pattern regular) {
        this.pwdStrength = pwdStrength;
        this.regular = regular;
    }

    public static Pattern getRegular(String pwdStrength){
        for (PwdStrengthEnum pwdStrengthEnum : PwdStrengthEnum.values()){
            if (pwdStrengthEnum.pwdStrength.equalsIgnoreCase(pwdStrength)){
                return pwdStrengthEnum.regular;
            }
        }
        return null;
    }
}
