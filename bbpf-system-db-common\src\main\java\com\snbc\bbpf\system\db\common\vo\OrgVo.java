/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.snbc.bbpf.buslog.annotations.DiffKey;
import com.snbc.bbpf.buslog.annotations.UnionDisplayKey;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName: UserVo
 * @Description: 返回前端的用户信息
 * @module: si-bbpf-system
 * @Author: wangsong
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrgVo {

    /**
     * 组织机构编号
     */
    private String orgId;
    /**
     * 组织名称
     */
    @DiffKey(name = "组织机构名称",enName = "Org Name")
    @UnionDisplayKey
    private String orgName;
    /**
     * 组织编码
     */
    private String orgCode;
    /**
     * 父级组织编号
     */
    private String parentId;
    /**
     * 父级组织名称
     */
    private String parentName;
    /**
     * 顺序
     */
    private Integer sequence;
    /**
     * 组织层级 从1开始为顶级
     */
    private Integer orgLevel;
    /**
     * 组织路径
     */
    private String orgPath;
    /**
     * 组织状态  0：禁用 1：启用
     */
    private Integer orgStatus;
    /**
     * 组织描述
     */
    @DiffKey(name = "组织机构描述",enName = "Org Desc")
    private String orgDesc;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    /**
     * 部门主管id
     */
    private String supervisor;
    /**
     * 部门主管名称
     */
    @DiffKey(name = "部门主管",enName = "supervisor")
    private String supervisorName;
    /**
     * 主管手机号
     */
    @DiffKey(name = "部门主管手机号",enName = "Supervisor Phone")
    private String supervisorPhone;
    /**
     * 创建人id
     */
    private String createUserId;
    /**
     * 创建人所属组织机构
     */
    private String createOrgId;

    /**
     * 子组织机构
     */
    private List<OrgVo> children;

    // 保留
    private boolean toKeep;
    // 是否禁用： true能点击即禁用
    private boolean disabled;
}
