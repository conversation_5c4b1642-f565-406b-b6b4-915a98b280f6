package com.snbc.bbpf.bus.system.enums;

/**
 * @ClassName: BusTemplateCodeEnum
 * @Description: 业务模板编码
 * @module: bbpf-bus-system
 * @Author: wjc
 * @date: 2023/3/14
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
public enum BusTemplateCodeEnum {
    NO1("0", "USER_LOGIN","下发短信"),
    NO2("1", "CHANGE_SECRET","下发短信"),
    NO3("2", "CHANGE_SECRET","下发短信"),
    NO4("3","UPDATE_USER_PHONE","下发短信"),
    // 默认的短信验证码
    DEFAULT_CODE("SMS_CODE","SMS_CODE","下发短信"),
    // 用户禁用
    DISABLE_USER("DISABLE_USER","DISABLE_USER","账号禁用提醒"),
    // 用户启用
    ENABLE_USER("ENABLE_USER","ENABLE_USER","账号启用提醒"),
    //调整部门通知被调整的用户
    ADJUST_DEPARTMENT1("ADJUST_DEPARTMENT1","ADJUST_DEPARTMENT1","部门变更提醒"),
    //调整部门通知用户前部门负责人
    ADJUST_DEPARTMENT2("ADJUST_DEPARTMENT2","ADJUST_DEPARTMENT2","部门变更提醒"),
    //角色绑定用户
    ROLE_BIND_USER("ROLE_BIND_USER","ROLE_BIND_USER","角色添加提醒"),
    //角色解绑用户
    ROLE_UNBIND_USER("ROLE_UNBIND_USER","ROLE_UNBIND_USER","角色移除提醒"),
    //用户随机密码
    RANDOM_PWD("RANDOM_TEMP","RANDOM_TEMP","boss端下发用户密码"),
    // 用户绑定第三方平台
    USER_BIND_MSG("USER_BIND_MSG","USER_BIND_MSG","用户绑定"),
    // 用户解绑第三方平台
    USER_UNBIND_MSG("USER_UNBIND_MSG","USER_UNBIND_MSG","用户解绑"),
    // 用户导入成功
    USER_IMPORT_MSG("USER_IMPORT_MSG","USER_IMPORT_MSG","用户导入成功通知"),
    //用户导入失败
    USER_IMPORT_FAIL_MSG("USER_IMPORT_FAIL_MSG","USER_IMPORT_FAIL_MSG","用户导入失败通知"),
    //数据导出成功
    DATA_EXPORT_MSG("DATA_EXPORT_MSG","DATA_EXPORT_MSG","数据导出完成通知"),
    /**LDAP用户同步失败通知*/
    SYNCHD_USER("SYNCHD_USER","SYNCHD_USER","LDAP用户同步失败通知")
    ;

    //code 和name一样
    private String code;
    // name 用于消息中心业务编码
    private String name;
    private String title;

    BusTemplateCodeEnum(String code , String name,String title) {
        this.code = code;
        this.name = name;
        this.title=title;
    }

    /**
     * @return java.lang.String
     * <AUTHOR>
     * @Description 通过状态获取名称
     * @Date 16:54 2021/5/20
     * @Param [status]
     **/
    public static String getNameByCode(String code) {
        for (BusTemplateCodeEnum value : BusTemplateCodeEnum.values()) {
            if (code != null && code.equals(value.code)) {
                return value.name;
            }
        }
        return null;
    }

    public static String getTitleByCode(String code) {
        for (BusTemplateCodeEnum value : BusTemplateCodeEnum.values()) {
            if (code != null && code.equals(value.code)) {
                return value.title;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getTitle() {
        return title;
    }
}
