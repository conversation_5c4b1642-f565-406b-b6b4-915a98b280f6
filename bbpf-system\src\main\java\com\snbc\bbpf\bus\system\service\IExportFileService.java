/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service;

import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.system.db.common.dto.ExportFileDto;
import com.snbc.bbpf.system.db.common.vo.ExportFileQuery;
import com.snbc.bbpf.system.db.common.vo.ExportFileVo;

/**
 * 导出文件管理Service接口
 *
 * <AUTHOR>
 * @date 2022-11-29
 */
public interface IExportFileService {
    /**
     * 查询导出文件管理
     *
     * @param exportId 导出文件管理主键
     * @return 导出文件管理
     */
    ExportFileVo selectExportFileByExportId(String exportId);

    /**
     * 分页查询导出文件管理列表
     *
     * @param exportFileDto 导出文件管理
     * @return 导出文件管理集合
     */
    PageInfo<ExportFileVo> selectExportFileList(ExportFileQuery exportFileDto);

    /**
     * 新增导出文件管理
     *
     * @param exportFileDto 导出文件管理
     */
    void insertExportFile(ExportFileDto exportFileDto);

    /**
     * 修改导出文件管理
     *
     * @param exportFileDto 导出文件管理
     */
    void updateExportFile(ExportFileDto exportFileDto);

    /**
     * 批量删除导出文件管理
     *
     * @param exportIds 需要删除的导出文件管理主键集合
     * @return 受影响结果数
     */
    int deleteExportFileByExportIds(String[] exportIds);

    /**
     * 删除导出文件管理信息
     *
     * @param exportId 导出文件管理主键
     * @return 受影响结果数
     */
    int deleteExportFileByExportId(String exportId);
}
