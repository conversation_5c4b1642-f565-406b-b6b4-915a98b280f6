/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service.impl;

import com.snbc.bbpf.bus.system.config.LDAPConfig;
import com.snbc.bbpf.bus.system.config.LoginConfig;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.handler.UserLoginFailHandler;
import com.snbc.bbpf.bus.system.service.DictService;
import com.snbc.bbpf.bus.system.service.LdapService;
import com.snbc.bbpf.bus.system.strategy.LoginContext;
import com.snbc.bbpf.bus.system.strategy.LoginStrategy;
import com.snbc.bbpf.bus.system.strategy.impl.AccountLoginStrategy;
import com.snbc.bbpf.bus.system.utils.ContainerUtil;
import com.snbc.bbpf.bus.system.utils.JwtTokenUtil;
import com.snbc.bbpf.bus.system.utils.RedisCheckCodeLoginFlag;
import com.snbc.bbpf.commons.crypt.DesUtils;
import com.snbc.bbpf.commons.crypt.RSAUtils;
import com.snbc.bbpf.component.captcha.resp.CallBaseResponse;
import com.snbc.bbpf.component.captcha.service.CaptchaService;
import com.snbc.bbpf.component.captcha.service.PwdErrorCheckService;
import com.snbc.bbpf.system.db.common.entity.DictValue;
import com.snbc.bbpf.system.db.common.entity.Permission;
import com.snbc.bbpf.system.db.common.entity.PermissionNode;
import com.snbc.bbpf.system.db.common.entity.User;
import com.snbc.bbpf.system.db.common.entity.login.LoginUser;
import com.snbc.bbpf.system.db.common.mapper.LogMapper;
import com.snbc.bbpf.system.db.common.mapper.UserLoginMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

/**
 * @ClassName: UserLoginServiceImplTest
 * @Description: 用户登录业务具体处理单元测试 extends PowerMockTestCase
 * 1，登录
 * 2，登出
 * 3，获取权限数据
 * @module: si-bbpf-system
 * @Author: liangjb
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
class UserLoginServiceImplTest   {

    ///模拟测试的数据单个转换的数据
    @InjectMocks
    UserLoginServiceImpl userLoginService;
    @InjectMocks
    AccountLoginStrategy accountLoginStrategy;
    //用户MAPP打桩
    @Mock
    UserLoginMapper userLoginMapper;
    @Mock
    DictService dictService;
    @Mock
    LoginContext loginContext;
    @Mock
    LoginStrategy loginStrategy;

    @Mock
    PwdErrorCheckService pwdErrorCheckService;
    @Mock
    LDAPConfig ldapConfig;
    @Mock
    LdapService ldapService;

    //验证码打桩
    @Mock
    CaptchaService captchaService;
    @Mock
    LogMapper logMapper;
    //REDIS
    @Mock
    RedisCheckCodeLoginFlag redisCheckCodeLoginFlag;
    @Mock
    RedisTemplate redisTemplate;
    @Mock
    private LoginConfig loginConfig;
    //JWT 生成TOKEN工具类
    @Mock
    private JwtTokenUtil jwtUtil;
    //类转换工具
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        loginConfig.setType("account");
        when(ldapService.adLogin(anyString(),anyString())).thenReturn(true);
        when(ldapConfig.getType()).thenReturn("open");
        ReflectionTestUtils.setField(loginContext,"loginStrategy",new AccountLoginStrategy());
    }


    @Test
    @DisplayName("用户名不存在提示")
    @Tag("@id:23627")
    @Tag("@author:liangJb")
    @Tag("@date:2021/6/2")
    void testVerifyUserPwd_username_error() throws Exception {
        LoginUser user=LoginUser.builder().userName("test01").userPwd("11").sysType("1")
                .loginType("0").captchaType("11").build();
        assertEquals(ErrorMessage.USERNAME_OR_PASSWORD_ERROR.getCode(),
                userLoginService.verifyUserPwd(user,"","").getHead().getCode());
    }
    @Test
    @DisplayName("验证码失败")
    @Tag("@id:23627")
    @Tag("@author:liangJb")
    @Tag("@date:2021/6/2")
    void testVerifyUserPwd_captcha_error() throws ClassNotFoundException, IllegalAccessException, NoSuchFieldException, NoSuchMethodException, InvocationTargetException, InstantiationException, IOException {
        try(MockedStatic<UserLoginFailHandler> userLogin= mockStatic(UserLoginFailHandler.class);
            MockedStatic<ContainerUtil> mc = mockStatic(ContainerUtil.class)) {
            LoginUser user = LoginUser.builder().userName("test01").userPwd("11").sysType("1")
                    .loginType("0").captchaType("11").build();
            User auser = User.builder().userName("测试").isLdap(0).build();
            doNothing().when(pwdErrorCheckService).dealPwdError(anyString());
            doNothing().when(pwdErrorCheckService).dealPwdPass(anyString());
            when(userLoginMapper.selectUserByUserName(any())).thenReturn(auser);

            userLoginService.verifyUserPwd(user, "", "").getHead().getCode();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    @Test
    @DisplayName("haslock为0的时候用户被解冻")
    @Tag("@id:23627")
    @Tag("@author:liangJb")
    @Tag("@date:2021/6/2")
    void testVerifyUserPwd_user_freeze() throws IOException {
        try(MockedStatic<UserLoginFailHandler> userLogin= mockStatic(UserLoginFailHandler.class);
            MockedStatic<ContainerUtil> mc = mockStatic(ContainerUtil.class)) {

            //生成一个被冻结的登录账户
            LoginUser user = LoginUser.builder().
                    userName("test01").userPwd("11").
                    sysType("1").captchaType("11").loginType("0").build();
            User auser = User.builder().isLdap(0).userName("测试").hasLock(0).build();
            when(userLoginMapper.selectUserByUserName(any())).thenReturn(auser);
            when(ContainerUtil.getBean(anyString())).thenReturn(accountLoginStrategy);
            // 生成一个正常调用对象
            CallBaseResponse callBaseResponse = CallBaseResponse.builder().code("000000").build();
            com.snbc.bbpf.component.captcha.resp.CommonResp result = com.snbc.bbpf.component.captcha.resp.CommonResp.builder().head(callBaseResponse).build();
            //模拟验证码通过
            when(captchaService.verification(any())).thenReturn(result);
            doNothing().when(pwdErrorCheckService).dealPwdError(anyString());
            doNothing().when(pwdErrorCheckService).dealPwdPass(anyString());
            //注册为私有方法的的单元测试
            // when(ReflectionTestUtils.invokeMethod(userLoginService,"verifyCaptcha",user)).thenReturn(auser);
            assertEquals(ErrorMessage.USE_FREEZE.getCode(),
                    userLoginService.verifyUserPwd(user, "", "").getHead().getCode());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    @Test
    @DisplayName("用户不允许多端登录，用户已经存在")
    @Tag("@id:23627")
    @Tag("@author:liangJb")
    @Tag("@date:2021/6/2")
    void testVerifyUserPwd_user_multiportLogin() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException, IOException {
        //生成一个被冻结的登录账户
        try(MockedStatic<UserLoginFailHandler> userLogin= mockStatic(UserLoginFailHandler.class);
            MockedStatic<ContainerUtil> mc = mockStatic(ContainerUtil.class)) {

            LoginUser user = LoginUser.builder().
                    userName("amble").userPwd("D2XtKm7SKVApk0bCrPeoxuaDfGQl/mR8/ene08V6qPXwwHatPvTmyI1YCxzIZ9IZX4/SRjEbv32bOnRYpkLeWjH9/0sZozAbq/i+O73fkT4AazVl24B5JwQkt3tVxr2XIXc4EAIqbg+Lih2a0jwabTI/dhj7H4a1ApPiwJp42h3ILK5GsYXyjya132jOFsVoinf7G275sjrsi98Kd/WtnjTzZ1xTHRtsjNvMJRdFzHEFo0TfdaN0gK5vjEgsmLW0UQuUXl/PIlahpcfYZpY2Vrea3cqSR2vxZmZcFpOnYG5ICPq3nWSJbuCfXQ6XTnPPWD9xDhm/obrmZ3+ewdJusQ==").
                    sysType("1").captchaType("11").loginType("0").build();
            User auser = User.builder().userName("测试").isLdap(0).hasLock(1).userPwd("xrRE7jnor3s0KNqJ/mncSb1E3XSNivTL").build();
            when(userLoginMapper.selectUserByUserName(any())).thenReturn(auser);
            // 生成一个正常调用对象
            // 生成一个正常调用对象
            CallBaseResponse callBaseResponse = CallBaseResponse.builder().code("000000").build();
            com.snbc.bbpf.component.captcha.resp.CommonResp result = com.snbc.bbpf.component.captcha.resp.CommonResp.builder().head(callBaseResponse).build();
            //模拟验证码通过
            when(captchaService.verification(any())).thenReturn(result);
            when(ContainerUtil.getBean(anyString())).thenReturn(accountLoginStrategy);
            doNothing().when(pwdErrorCheckService).dealPwdError(anyString());
            doNothing().when(pwdErrorCheckService).dealPwdPass(anyString());
            when(redisCheckCodeLoginFlag.getUserStatus(any(), any())).thenReturn(true);
            when(loginConfig.getType()).thenReturn("account");
            //注册为私有方法的的单元测试
            assertEquals(ErrorMessage.USERNAME_OR_PASSWORD_ERROR.getCode(),
                    userLoginService.verifyUserPwd(user, "", "").getHead().getCode());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    @Test
    @DisplayName("用户密码模拟错误会提示用户密码错误")
    @Tag("@id:23627")
    @Tag("@author:liangJb")
    @Tag("@date:2021/6/2")
   // @PrepareForTest(RSAUtil.class)
    void testVerifyUserPwd_user_pwderror() {
        try(MockedStatic<UserLoginFailHandler> userLogin= mockStatic(UserLoginFailHandler.class);
            MockedStatic<ContainerUtil> mc = mockStatic(ContainerUtil.class)) {
            //生成一个被冻结的登录账户
            LoginUser user = LoginUser.builder().userName("test01").userPwd("11").sysType("1")
                    .loginType("0").captchaType("11").build();
            User auser = User.builder().userName("测试").isLdap(0).hasLock(1).build();
            //测试私有变量
            when(userLoginMapper.selectUserByUserName(any())).thenReturn(auser);
            when(ContainerUtil.getBean(anyString())).thenReturn(accountLoginStrategy);
            doNothing().when(pwdErrorCheckService).dealPwdError(anyString());
            doNothing().when(pwdErrorCheckService).dealPwdPass(anyString());
            // 生成一个正常调用对象
            CallBaseResponse callBaseResponse = CallBaseResponse.builder().code("000000").build();
            com.snbc.bbpf.component.captcha.resp.CommonResp result = com.snbc.bbpf.component.captcha.resp.CommonResp.builder().head(callBaseResponse).build();
            //模拟验证码通过
            when(captchaService.verification(any())).thenReturn(result);
            ;
            when(redisCheckCodeLoginFlag.getUserStatus(any(), any())).thenReturn(false);
            //mock静态方法
            //   Mockito.mockStatic(RSAUtil.class);
            //默认加密密码为123456
            when(loginConfig.getType()).thenReturn("account");
            assertEquals(ErrorMessage.USERNAME_OR_PASSWORD_ERROR.getCode(), userLoginService.verifyUserPwd(user, "", "").getHead().getCode());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    @Test
    @DisplayName("用户密码输入正确流程")
    @Tag("@id:23627")
    @Tag("@author:liangJb")
    @Tag("@date:2021/6/2")
    void testVerifyUserPwd_user_pwdright() {
        try(MockedStatic<UserLoginFailHandler> userLogin= mockStatic(UserLoginFailHandler.class);
            MockedStatic<ContainerUtil> mc = mockStatic(ContainerUtil.class);
            MockedStatic<RSAUtils> rs = mockStatic(RSAUtils.class);
            MockedStatic<DesUtils> de = mockStatic(DesUtils.class)) {
            LoginUser user = LoginUser.builder().
                    userName("test01").userPwd("2o3dDOmXuAY=").loginType("0").sysType("1").captchaType("11").build();
            User auser = User.builder().userId("1").isLdap(0).userName("测试").userPwd("FcFYhsO/9FbYeZ+1hHw3tg==")
                    .hasLock(1).loginTime(LocalDateTime.now()).build();
            when(userLoginMapper.selectUserByUserName(any())).thenReturn(auser);
            when(ContainerUtil.getBean(anyString())).thenReturn(accountLoginStrategy);
            // 生成一个正常调用对象
            CallBaseResponse callBaseResponse = CallBaseResponse.builder().code("000000").build();
            com.snbc.bbpf.component.captcha.resp.CommonResp result = com.snbc.bbpf.component.captcha.resp.CommonResp.builder().head(callBaseResponse).build();
            //模拟验证码通过
            when(captchaService.verification(any())).thenReturn(result);
            when(redisCheckCodeLoginFlag.getUserStatus(any(), any())).thenReturn(false);
            //mock静态方法
            //默认加密密码为123456
            when(RSAUtils.rsaDecrypt(any(), any())).thenReturn("1+z1kQXI4JQ=DES3Util");
//            when(ClassConvertorMapper.INSTANCE.userConvertReturnUser(any())).thenReturn(ruser);
            //用户获取角色ID
            List<String> roleIds = new ArrayList<>();
            roleIds.add("1");
            roleIds.add("2");
            List<DictValue> dictValues = new ArrayList<>();
            DictValue dictValue = new DictValue();
            dictValue.setValueId("dddd");
            dictValue.setValueName("YmfnyJZW4iqSsyV5aFxmf6H2QvdRWx96");
            dictValues.add(dictValue);
            doNothing().when(pwdErrorCheckService).dealPwdError(anyString());
            doNothing().when(pwdErrorCheckService).dealPwdPass(anyString());
            when(dictService.selectByTypeCodeAndValueCode(anyString(), anyString())).thenReturn(dictValues);
            when(userLoginMapper.selectUserRoleIdsByUserId(any())).thenReturn(roleIds);
            when(jwtUtil.generateToken(any(), any(), any(), any(), any(), any())).thenReturn("**********");
            when(logMapper.insert(any())).thenReturn(1);
            when(loginConfig.getType()).thenReturn("account");
            when(loginContext.executeVerify(Mockito.any(),Mockito.any())).thenReturn(true);
            when(loginStrategy.verify(Mockito.any(),Mockito.any())).thenReturn(true);
            when(RSAUtils.rsaDecrypt(Mockito.any(),Mockito.any())).thenReturn("gEg6ulLHq5A=");
            when(DesUtils.decryptCbc(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn("123456".getBytes());
            assertEquals(ErrorMessage.SUCCESS.getCode(),
                    userLoginService.verifyUserPwd(user, "", "").getHead().getCode());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    @DisplayName("获取用户菜单权限树，当等级为父节点ID时，表示获取该父节点底下的所有树但不包含该父节点")
    @Tag("@id:23627")
    @Tag("@author:liangJb")
    @Tag("@date:2021/6/2")
    void  testGetOrderLstLevel() {
        //定义要获取所有权限的数据
        List<Permission> items=new ArrayList<>();
        //全部节点，主要体现parent ,order,level
        items.add(Permission.builder().parentId("T-1").permissionId("T0").permissionName("T0").orderBy(1).permissionLevel(1).permissionPath("/T0").build());
        //第一层
        items.add(Permission.builder().parentId("T0").permissionId("T1").permissionName("T1").orderBy(1).permissionLevel(2).permissionPath("/T0/T1").build());
        items.add(Permission.builder().parentId("T0").permissionId("T2").permissionName("T2").orderBy(2).permissionLevel(2).permissionPath("/T0/T2").build());
        items.add(Permission.builder().parentId("T0").permissionId("T3").permissionName("T3").orderBy(3).permissionLevel(2).permissionPath("/T0/T3").build());
        //第二层
        items.add(Permission.builder().parentId("T1").permissionId("T11").permissionName("T11").orderBy(1).permissionLevel(3).permissionPath("/T0/T1/T11").build());
        items.add(Permission.builder().parentId("T1").permissionId("T12").permissionName("T12").orderBy(2).permissionLevel(3).permissionPath("/T0/T1/T12").build());
        items.add(Permission.builder().parentId("T2").permissionId("T21").permissionName("T21").orderBy(3).permissionLevel(3).permissionPath("/T0/T2/T21").build());
        //第三层
        items.add(Permission.builder().parentId("T11").permissionId("T111").permissionName("T111").orderBy(1).permissionLevel(3).permissionPath("/T0/T1/T11/T111").build());
        items.add(Permission.builder().parentId("T11").permissionId("T112").permissionName("T112").orderBy(2).permissionLevel(4).permissionPath("/T0/T1/T11/T112").build());
        items.add(Permission.builder().parentId("T21").permissionId("T221").permissionName("T221").orderBy(3).permissionLevel(4).permissionPath("/T0/T2/T21/T221").build());
        //第四层
        items.add(Permission.builder().parentId("T111").permissionId("T1111").permissionName("T1111").orderBy(1).permissionLevel(5).permissionPath("/T0/T1/T11/T111/T1111").build());
        //目标的权限层
        List<Permission> curitems=new ArrayList<>();
        curitems.add(Permission.builder().parentId("T111").permissionId("T1111").permissionName("T1111").orderBy(1).permissionLevel(5).permissionPath("/T0/T1/T11/T111/T1111").build());
        curitems.add(Permission.builder().parentId("T21").permissionId("T221").permissionName("T221").orderBy(3).permissionLevel(4).permissionPath("/T0/T2/T21/T221").build());
        PermissionNode curb = PermissionNode.builder().orderBy(1).permissionId("T0").build();
        List<PermissionNode> listP=userLoginService.getOrderLstLevel(items,curitems,"T0");
        assertEquals(2,listP.size());
    }

}
