<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2025-01-15T00:00:00.000Z" agent="5.0" etag="" version="" type="device">
  <diagram name="BBPF-Grafana逻辑视图" id="bbpf-grafana-logic">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 标题 -->
        <mxCell id="title" value="BBPF-Grafana代理模块逻辑视图" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;fontColor=#2F5597;" vertex="1" parent="1">
          <mxGeometry x="600" y="30" width="400" height="40" as="geometry" />
        </mxCell>
        
        <!-- 用户层 -->
        <mxCell id="user-layer" value="用户层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1F5FE;strokeColor=#01579B;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="100" y="120" width="200" height="60" as="geometry" />
        </mxCell>
        
        <!-- BBPF代理层 -->
        <mxCell id="proxy-layer" value="BBPF代理层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F3E5F5;strokeColor=#7B1FA2;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="100" y="250" width="1200" height="300" as="geometry" />
        </mxCell>
        
        <!-- Token验证模块 -->
        <mxCell id="token-module" value="Token验证模块\n• 配置驱动认证\n• 微服务/单体模式\n• 外部认证集成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F5E8;strokeColor=#2E7D32;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="150" y="300" width="180" height="80" as="geometry" />
        </mxCell>
        
        <!-- 接口代理模块 -->
        <mxCell id="interface-module" value="接口代理模块\n• HTTP请求转发\n• 响应处理\n• 性能监控" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF3E0;strokeColor=#F57C00;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="380" y="300" width="180" height="80" as="geometry" />
        </mxCell>
        
        <!-- SQL拦截模块 -->
        <mxCell id="sql-module" value="SQL拦截模块\n• 查询拦截\n• 权限注入\n• 数据过滤" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#1976D2;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="610" y="300" width="180" height="80" as="geometry" />
        </mxCell>
        
        <!-- 变量注入模块 -->
        <mxCell id="variable-module" value="变量注入模块\n• 动态变量生成\n• 权限变量管理\n• 缓存优化" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FCE4EC;strokeColor=#C2185B;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="840" y="300" width="180" height="80" as="geometry" />
        </mxCell>
        
        <!-- 分享控制模块 -->
        <mxCell id="share-module" value="分享控制模块\n• 功能开关\n• 页面修改\n• 导出控制" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F1F8E9;strokeColor=#689F38;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="1070" y="300" width="180" height="80" as="geometry" />
        </mxCell>
        
        <!-- 数据处理流程 -->
        <mxCell id="data-flow" value="数据处理流程" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF8E1;strokeColor=#F57F17;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="400" y="430" width="600" height="80" as="geometry" />
        </mxCell>
        
        <!-- Grafana层 -->
        <mxCell id="grafana-layer" value="Grafana实例" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFEBEE;strokeColor=#D32F2F;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="100" y="600" width="400" height="80" as="geometry" />
        </mxCell>
        
        <!-- 数据源层 -->
        <mxCell id="datasource-layer" value="数据源" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F5E8;strokeColor=#2E7D32;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="600" y="600" width="400" height="80" as="geometry" />
        </mxCell>
        
        <!-- 外部系统 -->
        <mxCell id="external-systems" value="外部系统\n• BBPF权限系统\n• 认证服务\n• Redis缓存" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF9C4;strokeColor=#F9A825;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="1350" y="300" width="200" height="200" as="geometry" />
        </mxCell>
        
        <!-- 连接线 -->
        <!-- 用户到代理 -->
        <mxCell id="user-to-proxy" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#666666;strokeWidth=2;" edge="1" parent="1" source="user-layer" target="token-module">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="200" as="sourcePoint" />
            <mxPoint x="250" y="150" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 模块间连接 -->
        <mxCell id="token-to-interface" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#666666;strokeWidth=2;" edge="1" parent="1" source="token-module" target="interface-module">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="330" y="340" as="sourcePoint" />
            <mxPoint x="380" y="340" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="interface-to-sql" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#666666;strokeWidth=2;" edge="1" parent="1" source="interface-module" target="sql-module">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="340" as="sourcePoint" />
            <mxPoint x="610" y="340" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="sql-to-variable" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#666666;strokeWidth=2;" edge="1" parent="1" source="sql-module" target="variable-module">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="790" y="340" as="sourcePoint" />
            <mxPoint x="840" y="340" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="variable-to-share" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#666666;strokeWidth=2;" edge="1" parent="1" source="variable-module" target="share-module">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1020" y="340" as="sourcePoint" />
            <mxPoint x="1070" y="340" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 到外部系统 -->
        <mxCell id="to-external" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#666666;strokeWidth=1;dashed=1;" edge="1" parent="1" source="token-module" target="external-systems">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="330" y="320" as="sourcePoint" />
            <mxPoint x="1350" y="380" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 到Grafana -->
        <mxCell id="to-grafana" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#666666;strokeWidth=2;" edge="1" parent="1" source="interface-module" target="grafana-layer">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="470" y="380" as="sourcePoint" />
            <mxPoint x="300" y="600" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 到数据源 -->
        <mxCell id="to-datasource" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#666666;strokeWidth=2;" edge="1" parent="1" source="sql-module" target="datasource-layer">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="700" y="380" as="sourcePoint" />
            <mxPoint x="800" y="600" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 数据流标注 -->
        <mxCell id="flow-1" value="认证" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="350" y="320" width="30" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="flow-2" value="代理" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="580" y="320" width="30" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="flow-3" value="拦截" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="810" y="320" width="30" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="flow-4" value="注入" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="1040" y="320" width="30" height="20" as="geometry" />
        </mxCell>
        
        <!-- 图例 -->
        <mxCell id="legend" value="图例" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F5F5F5;strokeColor=#666666;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1350" y="550" width="200" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-sync" value="——— 同步处理" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="1360" y="590" width="100" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-async" value="- - - - 异步调用" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="1360" y="620" width="100" height="20" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>