package com.snbc.bbpf.system.db.common.dto;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 獲取用戶微信的基礎數據
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-04
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TUserwxInfo {

    /**
     * 三方登录ID
     */
    private String thirdId;

    /**
     * 微信OPENID
     */
    private String userOpenid;

    /**
     * 微信unionid
     */
    private String userUnionid;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 用户ID
     */
    private String userId;


}
