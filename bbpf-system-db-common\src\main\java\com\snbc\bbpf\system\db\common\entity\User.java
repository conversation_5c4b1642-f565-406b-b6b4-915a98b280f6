/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * @ClassName: User
 * @Description: 用户实体类
 * @module: si-bbpf-system
 * @Author: wangsong
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class User {
    private String userId;

    @NotBlank
    private String userName;

    @NotBlank
    private String jobNumber;

    private String userPwd;

    private Integer hasLock;

    private Integer hasOnline;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime loginTime;
    /**
    创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    /**
     手机号
     */
    @NotBlank
    private String phone;

    private String userDesc;

    private String avatar;

    private String email;

    private Integer userStatus;
    private String userStatusName;
    private Integer hasResign;

    private String createUserId;

    private String createOrgId;

    private Integer isLdap;

    private String userDn;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatePwdTime;

    /**
     * 登录锁定之前的用户状态
     */
    private Integer beforeLockStatus;
    /**
     * 登录密码错误超过一定次数后的锁定时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime loginLockTime;
}
