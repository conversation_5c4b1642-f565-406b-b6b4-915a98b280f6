package com.snbc.bbpf.grafana.proxy.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.snbc.bbpf.grafana.proxy.config.GrafanaProxyConfig;
import com.snbc.bbpf.grafana.proxy.dto.UserPermissionDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 动态模板变量注入服务
 * 
 * 实现动态注入模板变量的核心功能：
 * 1. 拦截Grafana仪表盘的JSON响应
 * 2. 动态向templating列表添加用户权限变量
 * 3. 支持多种变量类型（constant、custom、query等）
 * 4. 提供安全的变量值处理和验证
 * 
 * 这种方案的优势：
 * - 高通用性：不关心底层数据源类型
 * - 低侵入性：对Grafana和数据源完全透明
 * - 高安全性：利用Grafana自身的变量机制
 * - 易维护性：权限逻辑集中在代理服务中
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2024-12-13
 */
@Service
public class DynamicTemplateVariableService {

    private static final Logger logger = LoggerFactory.getLogger(DynamicTemplateVariableService.class);

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private GrafanaProxyConfig config;

    // 需要拦截的仪表盘API路径模式
    private static final Pattern DASHBOARD_API_PATTERN = Pattern.compile("/api/dashboards/uid/.*");
    private static final Pattern DASHBOARD_DB_PATTERN = Pattern.compile("/api/dashboards/db/.*");
    
    // 权限变量前缀，用于标识动态注入的变量
    private static final String PERMISSION_VAR_PREFIX = "_bbpf_";
    
    // 常用的权限变量名称
    private static final String USER_ID_VAR = PERMISSION_VAR_PREFIX + "user_id";
    private static final String ORG_ID_VAR = PERMISSION_VAR_PREFIX + "org_id";
    private static final String DEPT_ID_VAR = PERMISSION_VAR_PREFIX + "dept_id";
    private static final String TENANT_ID_VAR = PERMISSION_VAR_PREFIX + "tenant_id";
    private static final String ROLE_VAR = PERMISSION_VAR_PREFIX + "role";
    private static final String DATA_FILTER_VAR = PERMISSION_VAR_PREFIX + "data_filter";

    /**
     * 检查是否需要拦截此API响应
     * 
     * @param apiPath API路径
     * @return true如果需要拦截
     */
    public boolean shouldInterceptResponse(String apiPath) {
        if (apiPath == null || apiPath.isEmpty()) {
            return false;
        }
        
        return DASHBOARD_API_PATTERN.matcher(apiPath).matches() || 
               DASHBOARD_DB_PATTERN.matcher(apiPath).matches();
    }

    /**
     * 拦截并修改仪表盘JSON响应，注入动态模板变量
     * 
     * @param responseBody 原始响应体
     * @param userId 用户ID
     * @param apiPath API路径
     * @return 修改后的响应体
     */
    public String interceptAndInjectVariables(String responseBody, String userId, String apiPath) {
        if (responseBody == null || responseBody.trim().isEmpty()) {
            return responseBody;
        }

        try {
            // 检查是否启用动态变量功能
            if (!config.isEnableDynamicVariables()) {
                if (config.isEnableVariableInjectionLogging()) {
                    logger.debug("[动态模板变量] 动态变量功能已禁用");
                }
                return responseBody;
            }
            
            if (config.isEnableVariableInjectionLogging()) {
                logger.info("[动态模板变量] 开始为用户 {} 注入模板变量，API路径: {}", userId, apiPath);
            }
            
            // 获取用户权限信息
            UserPermissionDto userPermission = permissionService.getUserPermissions(userId);
            if (userPermission == null) {
                if (config.isEnableVariableInjectionLogging()) {
                    logger.warn("[动态模板变量] 用户 {} 权限信息获取失败，使用默认权限", userId);
                }
                userPermission = createDefaultPermission(userId);
            }

            // 解析响应体JSON
            JsonNode rootNode = objectMapper.readTree(responseBody);
            
            if (!rootNode.isObject()) {
                if (config.isEnableVariableInjectionLogging()) {
                    logger.debug("[动态模板变量] 响应体不是JSON对象，跳过处理");
                }
                return responseBody;
            }

            ObjectNode responseObject = (ObjectNode) rootNode;
            
            // 检查是否为仪表盘响应
            if (!isDashboardResponse(responseObject)) {
                if (config.isEnableVariableInjectionLogging()) {
                    logger.debug("[动态模板变量] 不是仪表盘响应，跳过处理");
                }
                return responseBody;
            }

            // 注入动态模板变量
            injectTemplateVariables(responseObject, userPermission);
            
            String modifiedResponse = objectMapper.writeValueAsString(responseObject);
            if (config.isEnableVariableInjectionLogging()) {
                logger.info("[动态模板变量] 成功为用户 {} 注入模板变量", userId);
            }
            
            return modifiedResponse;
            
        } catch (Exception e) {
            logger.error("[动态模板变量] 处理用户 {} 的响应时发生错误: {}", userId, e.getMessage(), e);
            return responseBody; // 出错时返回原始响应
        }
    }

    /**
     * 检查是否为仪表盘响应
     */
    private boolean isDashboardResponse(ObjectNode responseObject) {
        // 检查是否包含仪表盘结构
        JsonNode dashboardNode = responseObject.get("dashboard");
        if (dashboardNode != null && dashboardNode.isObject()) {
            return true;
        }
        
        // 检查是否直接是仪表盘对象
        JsonNode idNode = responseObject.get("id");
        JsonNode titleNode = responseObject.get("title");
        JsonNode panelsNode = responseObject.get("panels");
        
        return idNode != null && titleNode != null && panelsNode != null;
    }

    /**
     * 向仪表盘JSON中注入动态模板变量
     */
    private void injectTemplateVariables(ObjectNode responseObject, UserPermissionDto userPermission) {
        // 获取或创建dashboard节点
        ObjectNode dashboardNode;
        if (responseObject.has("dashboard")) {
            dashboardNode = (ObjectNode) responseObject.get("dashboard");
        } else {
            dashboardNode = responseObject; // 直接是仪表盘对象
        }

        // 获取或创建templating节点
        ObjectNode templatingNode;
        if (dashboardNode.has("templating")) {
            templatingNode = (ObjectNode) dashboardNode.get("templating");
        } else {
            templatingNode = objectMapper.createObjectNode();
            dashboardNode.set("templating", templatingNode);
        }

        // 获取或创建list数组
        ArrayNode listArray;
        if (templatingNode.has("list")) {
            listArray = (ArrayNode) templatingNode.get("list");
        } else {
            listArray = objectMapper.createArrayNode();
            templatingNode.set("list", listArray);
        }

        // 移除已存在的权限变量（避免重复）
        removeExistingPermissionVariables(listArray);

        // 生成并添加权限变量
        Map<String, Object> permissionVariables = generatePermissionVariables(userPermission);
        for (Map.Entry<String, Object> entry : permissionVariables.entrySet()) {
            ObjectNode variableNode = createTemplateVariable(
                entry.getKey(), 
                entry.getValue().toString(),
                "权限变量: " + entry.getKey()
            );
            listArray.add(variableNode);
        }

        if (config.isEnableVariableInjectionLogging()) {
            logger.info("[动态模板变量] 成功注入 {} 个权限变量", permissionVariables.size());
        }
    }

    /**
     * 移除已存在的权限变量
     */
    private void removeExistingPermissionVariables(ArrayNode listArray) {
        for (int i = listArray.size() - 1; i >= 0; i--) {
            JsonNode variableNode = listArray.get(i);
            if (variableNode.isObject()) {
                JsonNode nameNode = variableNode.get("name");
                if (nameNode != null && nameNode.isTextual()) {
                    String varName = nameNode.asText();
                    if (varName.startsWith(PERMISSION_VAR_PREFIX)) {
                        listArray.remove(i);
                        if (config.isEnableVariableInjectionLogging()) {
                            logger.debug("[动态模板变量] 移除已存在的权限变量: {}", varName);
                        }
                    }
                }
            }
        }
    }

    /**
     * 生成权限变量映射
     */
    private Map<String, Object> generatePermissionVariables(UserPermissionDto userPermission) {
        Map<String, Object> variables = new HashMap<>();
        
        // 基础用户信息变量
        variables.put(USER_ID_VAR, userPermission.getUserId());
        
        if (userPermission.getUsername() != null) {
            variables.put(PERMISSION_VAR_PREFIX + "username", userPermission.getUsername());
        }
        
        // 组织相关变量
        Object orgId = userPermission.getRawPermissionValue("orgId");
        if (orgId != null) {
            variables.put(ORG_ID_VAR, orgId.toString());
        }
        
        Object deptId = userPermission.getRawPermissionValue("deptId");
        if (deptId != null) {
            variables.put(DEPT_ID_VAR, deptId.toString());
        }
        
        Object tenantId = userPermission.getRawPermissionValue("tenantId");
        if (tenantId != null) {
            variables.put(TENANT_ID_VAR, tenantId.toString());
        }
        
        // 角色变量
        Object role = userPermission.getRawPermissionValue("role");
        if (role != null) {
            variables.put(ROLE_VAR, role.toString());
        }
        
        // 数据过滤变量
        String dataFilter = userPermission.getDataFilter();
        if (dataFilter != null && !dataFilter.trim().isEmpty()) {
            variables.put(DATA_FILTER_VAR, dataFilter);
        }
        
        // 权限标志变量
        if (userPermission.getPermissions() != null) {
            variables.put(PERMISSION_VAR_PREFIX + "can_export", 
                userPermission.hasPermission("export") ? "true" : "false");
            variables.put(PERMISSION_VAR_PREFIX + "can_admin", 
                userPermission.hasPermission("admin") ? "true" : "false");
            variables.put(PERMISSION_VAR_PREFIX + "can_write", 
                userPermission.hasPermission("write") ? "true" : "false");
        }
        
        // 可访问资源变量
        if (userPermission.getAccessibleDashboardIds() != null && !userPermission.getAccessibleDashboardIds().isEmpty()) {
            variables.put(PERMISSION_VAR_PREFIX + "accessible_dashboards", 
                String.join(",", userPermission.getAccessibleDashboardIds()));
        }
        
        if (userPermission.getAccessibleDataSourceIds() != null && !userPermission.getAccessibleDataSourceIds().isEmpty()) {
            variables.put(PERMISSION_VAR_PREFIX + "accessible_datasources", 
                String.join(",", userPermission.getAccessibleDataSourceIds()));
        }
        
        // 过滤安全的变量值
        Map<String, Object> safeVariables = new HashMap<>();
        for (Map.Entry<String, Object> entry : variables.entrySet()) {
            String value = entry.getValue().toString();
            if (isVariableValueSafe(entry.getKey(), value)) {
                safeVariables.put(entry.getKey(), value);
            } else {
                logger.warn("[动态模板变量] 变量值不安全，已跳过: {} = {}", entry.getKey(), value);
            }
        }
        
        return safeVariables;
    }

    /**
     * 创建Grafana模板变量节点
     */
    private ObjectNode createTemplateVariable(String name, String value, String description) {
        ObjectNode variableNode = objectMapper.createObjectNode();
        
        // 基本属性
        variableNode.put("name", name);
        variableNode.put("type", "constant");
        variableNode.put("label", description);
        variableNode.put("description", description);
        variableNode.put("hide", 2); // 隐藏变量，用户不可见
        
        // 当前值
        ObjectNode currentNode = objectMapper.createObjectNode();
        currentNode.put("value", value);
        currentNode.put("text", value);
        currentNode.put("selected", true);
        variableNode.set("current", currentNode);
        
        // 选项列表（对于constant类型，只有一个选项）
        ArrayNode optionsArray = objectMapper.createArrayNode();
        ObjectNode optionNode = objectMapper.createObjectNode();
        optionNode.put("value", value);
        optionNode.put("text", value);
        optionNode.put("selected", true);
        optionsArray.add(optionNode);
        variableNode.set("options", optionsArray);
        
        // 查询配置（对于constant类型为空）
        variableNode.put("query", value);
        variableNode.put("skipUrlSync", true);
        
        return variableNode;
    }

    /**
     * 验证变量值的安全性
     */
    private boolean isVariableValueSafe(String variableName, String variableValue) {
        if (variableValue == null) {
            return false;
        }
        
        // 检查长度限制
        if (variableValue.length() > 1000) {
            logger.warn("[动态模板变量] 变量值过长: {} = {} (长度: {})", 
                variableName, variableValue.substring(0, 50) + "...", variableValue.length());
            return false;
        }
        
        // 检查SQL注入风险
        String lowerValue = variableValue.toLowerCase();
        String[] dangerousKeywords = {
            "drop", "delete", "update", "insert", "exec", "execute", 
            "script", "--", "/*", "*/", "xp_", "sp_", "union", "select"
        };
        
        for (String keyword : dangerousKeywords) {
            if (lowerValue.contains(keyword)) {
                logger.warn("[动态模板变量] 检测到潜在危险的变量值: {} = {}", variableName, variableValue);
                return false;
            }
        }
        
        // 检查特殊字符
        if (variableValue.contains("<script") || variableValue.contains("javascript:") || 
            variableValue.contains("vbscript:") || variableValue.contains("onload=")) {
            logger.warn("[动态模板变量] 检测到脚本注入风险: {} = {}", variableName, variableValue);
            return false;
        }
        
        return true;
    }

    /**
     * 创建默认权限对象
     */
    private UserPermissionDto createDefaultPermission(String userId) {
        UserPermissionDto defaultPermission = new UserPermissionDto();
        defaultPermission.setUserId(userId);
        defaultPermission.setUsername("unknown");
        defaultPermission.setDataFilter("1=0"); // 默认无数据访问权限
        return defaultPermission;
    }

    /**
     * 为指定用户生成权限变量映射（公共接口）
     * 
     * @param userId 用户ID
     * @return 权限变量映射
     */
    public Map<String, Object> generateUserPermissionVariables(String userId) {
        try {
            if (userId == null || userId.trim().isEmpty()) {
                logger.warn("[动态模板变量] 用户ID为空，返回空变量映射");
                return new HashMap<>();
            }
            
            // 获取用户权限信息
            UserPermissionDto userPermission = permissionService.getUserPermissions(userId);
            if (userPermission == null) {
                logger.warn("[动态模板变量] 用户 {} 权限信息获取失败，使用默认权限", userId);
                userPermission = createDefaultPermission(userId);
            }
            
            // 生成权限变量
            Map<String, Object> variables = generatePermissionVariables(userPermission);
            
            if (config.isEnableVariableInjectionLogging()) {
                logger.info("[动态模板变量] 为用户 {} 生成了 {} 个权限变量", userId, variables.size());
            }
            
            return variables;
            
        } catch (Exception e) {
            logger.error("[动态模板变量] 为用户 {} 生成权限变量时发生错误: {}", userId, e.getMessage(), e);
            return new HashMap<>();
        }
    }

    /**
     * 获取权限变量前缀
     */
    public static String getPermissionVariablePrefix() {
        return PERMISSION_VAR_PREFIX;
    }

    /**
     * 检查是否为权限变量
     */
    public static boolean isPermissionVariable(String variableName) {
        return variableName != null && variableName.startsWith(PERMISSION_VAR_PREFIX);
    }

    /**
     * 清理变量值，移除潜在的危险字符
     */
    public String sanitizeVariableValue(String value) {
        if (value == null) {
            return "";
        }
        
        // 移除HTML标签
        String sanitized = value.replaceAll("<[^>]*>", "");
        
        // 移除SQL注释
        sanitized = sanitized.replaceAll("--.*", "");
        sanitized = sanitized.replaceAll("/\\*.*?\\*/", "");
        
        // 移除多余的空格
        sanitized = sanitized.replaceAll("\\s+", " ").trim();
        
        // 限制长度
        if (sanitized.length() > 500) {
            sanitized = sanitized.substring(0, 500);
        }
        
        return sanitized;
    }
}