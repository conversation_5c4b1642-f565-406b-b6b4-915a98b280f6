/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.bus.system.service.IExportFileService;
import com.snbc.bbpf.bus.system.utils.ResultUtil;
import com.snbc.bbpf.buslog.annotations.Buslog;
import com.snbc.bbpf.system.db.common.dto.ExportFileDto;
import com.snbc.bbpf.system.db.common.vo.ExportFileQuery;
import com.snbc.bbpf.system.db.common.vo.ExportFileVo;
import feign.Headers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 导出文件管理Controller
 *
 * <AUTHOR>
 * @date 2022-11-29
 *
 */
@RestController
@RequestMapping("/console/file")
public class ExportFileController {
    @Autowired
    private IExportFileService exportFileService;

    /**
     * 查询导出文件管理列表
     *
     */
    @GetMapping("/exportFileList")
    public CommonResp<PageInfo<ExportFileVo>> list(ExportFileQuery exportFileDto) {
        CommonResp<PageInfo<ExportFileVo>> commonResp = new CommonResp<>();
        commonResp.setHead(ResultUtil.success());
        commonResp.setBody(exportFileService.selectExportFileList(exportFileDto));
        return  commonResp;
    }


    /**
     * 删除导出文件管理
     *
     */
    @Buslog(opration = "deleteFile", zh="删除文件【${fileName}】",en = "delete file [${fileName}]",target = "exportFile")
	@DeleteMapping("/deleteFile")
    public CommonResp<Object> deleteFile(@RequestBody String deleteString) throws JsonProcessingException {
        //反序列化参数
        ObjectMapper objectMapper = new ObjectMapper();
        String exportId = objectMapper.readTree(deleteString).get("exportId").asText();
        //删除文件
        exportFileService.deleteExportFileByExportId(exportId);
        return CommonResp.builder().head(ResultUtil.success()).build();
    }

    /**
     * 导出文件 新增【消息中心 导入消息模板时用】
     * wjc
     * 2023-05-18 16:34:00
     */
    @PostMapping("/addfile")
    @Headers("Content-Type:application/json")
    public CommonResp<Object> addFile(@RequestBody ExportFileDto dto) {
        exportFileService.insertExportFile(dto);
        return CommonResp.builder().head(ResultUtil.success()).build();
    }
    /**
     * 导出文件 更新状态【消息中心 导入消息模板时用】
     * wjc
     * 2023-05-18 16:34:00
     */
    @PostMapping("/editfile")
    @Headers("Content-Type:application/json")
    public CommonResp<Object> editFile(@RequestBody ExportFileDto dto) {
        exportFileService.updateExportFile(dto);
        return CommonResp.builder().head(ResultUtil.success()).build();
    }
}
