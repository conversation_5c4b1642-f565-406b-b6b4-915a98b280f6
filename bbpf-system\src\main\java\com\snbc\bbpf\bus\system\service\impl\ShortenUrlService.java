package com.snbc.bbpf.bus.system.service.impl;

import com.snbc.bbpf.bus.system.constans.NumberConstant;
import com.snbc.bbpf.bus.system.utils.RedisCheckCodeLoginFlag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
@Component
@Slf4j
public class ShortenUrlService {
    private static final String BASE62_CHARS = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private static final int BASE62_LENGTH = BASE62_CHARS.length();
    private static final String REDIS_KEY_PREFIX = "shortUrl:";
    @Autowired
    private RedisCheckCodeLoginFlag redisCheckCodeLoginFlag;

    @Value("${bbpf.system.baseUrl}")
    private String baseUrl;
    public String shortenUrl(String longUrl) {
        String hash = getMD5Hash(longUrl);
        String shortCode = generateShortCode(hash);
        String shortUrl = baseUrl + shortCode;
        redisCheckCodeLoginFlag.insertRedis(REDIS_KEY_PREFIX + shortCode, longUrl);
        return shortUrl;
    }
    public String getLongUrl(String shortCode) {
        return redisCheckCodeLoginFlag.getRedis(REDIS_KEY_PREFIX + shortCode);
    }

    public void delShortUrl(String shortCode) {
        redisCheckCodeLoginFlag.delRedis(REDIS_KEY_PREFIX + shortCode);
    }

    /**
     * 生成MD5哈希值
     *
     * @param value 要哈希的值
     * @return MD5哈希值
     * <AUTHOR>
     * @version 1.0
     * @date 2023年6月29日
     * @copyright 山东新北洋信息技术股份有限公司. All rights reserved.
     **/
    private static String getMD5Hash(String value) {
        StringBuilder hash = new StringBuilder();
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte[] md5Hash = md5.digest(value.getBytes(StandardCharsets.UTF_8));
            for (byte b : md5Hash) {
                String hex = Integer.toHexString(0xFF & b);
                if (hex.length() == 1) {
                    hash.append('0');
                }
                hash.append(hex);
            }
        } catch (NoSuchAlgorithmException e) {
            log.error("ShortenUrlService getMD5Hash error ",e);
        }
        return hash.toString();
    }
    /**
     * 生成短码
     *
     * @param hash 哈希值
     * @return 短码
     * <AUTHOR>
     * @version 1.0
     * @date 2023年6月29日
     * @copyright 山东新北洋信息技术股份有限公司. All rights reserved.
     */
    private static String generateShortCode(String hash) {
        StringBuilder shortCode = new StringBuilder();

        BigInteger hashValue = new BigInteger(hash, NumberConstant.NO_SIXTEEN);
        // 将哈希值转换为以 base62 编码的短码
        while (hashValue.compareTo(BigInteger.ZERO) > 0) {
            BigInteger[] quotientAndRemainder = hashValue.divideAndRemainder(BigInteger.valueOf(BASE62_LENGTH));
            int index = quotientAndRemainder[1].intValue();
            shortCode.insert(0, BASE62_CHARS.charAt(index));
            hashValue = quotientAndRemainder[0];
        }

        while (shortCode.length() < NumberConstant.NO_SIX) {
            shortCode.insert(0, BASE62_CHARS.charAt(0));
        }
        return shortCode.toString();
    }
}
