package com.snbc.bbpf.system.db.common.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @ClassName: Area
 * @Description: area实体类
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/12/30
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Area {
    private String areaId;
    private String areaCode;
    private String areaName;
    private Integer areaLevel;
    private String parentCode;
    private LocalDateTime createTime;
}
