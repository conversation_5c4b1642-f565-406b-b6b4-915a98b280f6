# BPF集成Grafana概要设计文档

## 1. 概述

### 1.1 项目背景

基于现有的bbpf-grafana-proxy代理服务架构，实现BPF框架与Grafana的深度集成，提供统一认证、权限控制、数据隔离等企业级监控解决方案。

### 1.2 设计目标

- 实现用户单点登录访问Grafana
- 支持iframe方式集成Grafana图表
- 提供精细化的权限控制机制
- 实现多租户数据隔离
- 确保系统安全性和高可用性

### 1.3 技术架构原则

- **安全第一**：多层次安全防护，数据权限精确控制
- **高可用性**：集群部署，故障自动切换
- **高性能**：缓存优化，连接池管理
- **可扩展性**：模块化设计，支持水平扩展
- **可维护性**：标准化部署，自动化监控

## 2. 系统架构设计

### 2.1 总体架构图

```mermaid
graph TB
    subgraph "用户接入层"
        A[Web浏览器]
        B[移动端App]
        C[业务系统iframe]
    end
  
    subgraph "负载均衡层"
        D[Nginx负载均衡器]
        E[SSL终端]
    end
  
    subgraph "应用服务层"
        F[BBPF前端应用]
        G1[bbpf-grafana-proxy-1]
        G2[bbpf-grafana-proxy-2]
        H[Grafana Enterprise]
    end
  
    subgraph "业务服务层"
        I[BBPF后端服务]
        J[权限服务]
        K[认证服务]
    end
  
    subgraph "数据存储层"
        L1[Redis Master]
        L2[Redis Slave]
        M1[MySQL Master]
        M2[MySQL Slave]
    end
  
    subgraph "监控数据源"
        N1[Prometheus]
        N2[MySQL监控数据]
        N3[业务数据库]
    end
  
    A --> D
    B --> D
    C --> D
    D --> E
    E --> F
    F --> G1
    F --> G2
    G1 --> H
    G2 --> H
    G1 --> I
    G2 --> I
    G1 --> J
    G2 --> J
    G1 --> K
    G2 --> K
    G1 --> L1
    G2 --> L1
    L1 --> L2
    I --> M1
    J --> M1
    K --> M1
    M1 --> M2
    H --> N1
    H --> N2
    H --> N3
  
    classDef userLayer fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef lbLayer fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef appLayer fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef serviceLayer fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef dataLayer fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef sourceLayer fill:#f1f8e9,stroke:#689f38,stroke-width:2px
  
    class A,B,C userLayer
    class D,E lbLayer
    class F,G1,G2,H appLayer
    class I,J,K serviceLayer
    class L1,L2,M1,M2 dataLayer
    class N1,N2,N3 sourceLayer
```

### 2.2 核心组件架构图

```mermaid
graph LR
    subgraph "bbpf-grafana-proxy核心组件"
        A[JwtAuthenticationFilter<br/>JWT认证过滤器]
        B[PermissionService<br/>权限服务]
        C[SqlInterceptorService<br/>SQL拦截服务]
        D[GrafanaProxyService<br/>代理服务]
    end
  
    subgraph "外部依赖"
        F[BPF认证服务]
        G[BPF权限API]
        H[Grafana实例]
        I[Redis缓存]
        J[MySQL数据库]
    end
  
    A --> F
    B --> G
    B --> I
    C --> B
    D --> H
    D --> C
  
    classDef coreComponent fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef externalDep fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
  
    class A,B,C,D coreComponent
    class F,G,H,I,J externalDep
```

## 3. 业务流程设计

### 3.1 用户认证流程图

```mermaid
sequenceDiagram
    participant U as 用户
    participant BF as BPF前端
    participant GP as Grafana代理服务
    participant BA as BPF认证服务
    participant R as Redis缓存
    participant G as Grafana实例

    Note over U,G: 用户登录流程
    U->>BF: 1. 访问BBPF系统
    BF->>BA: 2. 用户认证请求
    BA-->>BF: 3. 返回JWT Token
    BF-->>U: 4. 登录成功，获得Token

    Note over U,G: 访问Grafana流程
    U->>GP: 5. 访问Grafana(携带JWT Token)
    GP->>GP: 6. JwtAuthenticationFilter拦截
    GP->>BA: 7. 调用BPF后台验证Token
    BA-->>GP: 8. Token验证结果

    alt Token有效
        GP->>R: 9. 查询用户权限缓存
        alt 缓存命中
            R-->>GP: 10. 返回缓存的权限信息
        else 缓存未命中
            GP->>BA: 11. 调用权限API获取权限
            BA-->>GP: 12. 返回用户权限信息
            GP->>R: 13. 缓存权限信息
        end
        GP->>G: 14. 转发请求(设置X-WEBAUTH-USER头)
        G-->>GP: 15. 返回Grafana响应
        GP-->>U: 16. 返回最终响应
    else Token无效
        GP-->>U: 17. 返回401未授权
    end
```

### 3.2 SQL拦截与权限控制流程图

```mermaid
sequenceDiagram
    participant U as 用户
    participant GP as Grafana代理服务
    participant SI as SQL拦截器
    participant PS as 权限服务
    participant R as Redis缓存
    participant G as Grafana实例
    participant DB as 数据库

    U->>GP: 1. 发起数据查询请求
    GP->>GP: 2. 检查API路径是否需要拦截

    alt 需要SQL拦截
        GP->>SI: 3. 调用SQL拦截器
        SI->>PS: 4. 获取用户权限信息
        PS->>R: 5. 查询权限缓存
        R-->>PS: 6. 返回权限信息
        PS-->>SI: 7. 返回用户权限

        SI->>SI: 8. 解析请求体中的SQL
        SI->>SI: 9. 提取SQL中的表名
        SI->>SI: 10. 根据表级权限配置构建过滤条件

        Note over SI: 权限过滤条件构建
        Note over SI: - 租户Schema隔离: tenant_a.table_name
        Note over SI: - 用户字段过滤: user_id = 'xxx'
        Note over SI: - 组织字段过滤: org_id = 'xxx'
        Note over SI: - 部门字段过滤: dept_id IN ('xxx','yyy')

        SI->>SI: 11. 修改SQL添加WHERE条件
        SI-->>GP: 12. 返回修改后的请求体
        GP->>G: 13. 转发修改后的请求
    else 无需拦截
        GP->>G: 13. 直接转发原始请求
    end

    G->>DB: 14. 执行SQL查询
    DB-->>G: 15. 返回查询结果
    G-->>GP: 16. 返回Grafana响应
    GP-->>U: 17. 返回最终响应
```

### 3.3 iframe集成流程图

```mermaid
sequenceDiagram
    participant BS as 业务系统
    participant IF as iframe
    participant GP as Grafana代理服务
    participant G as Grafana实例

    Note over BS,G: iframe嵌入初始化
    BS->>IF: 1. 创建iframe元素
    BS->>IF: 2. 设置Grafana URL(包含Token)
    IF->>GP: 3. 加载Grafana页面
    GP->>GP: 4. 验证Token
    GP->>G: 5. 转发请求
    G-->>GP: 6. 返回页面内容
    GP-->>IF: 7. 返回处理后的页面
    IF-->>BS: 8. 显示Grafana图表

    Note over BS,G: 跨域通信
    IF->>BS: 9. postMessage(认证请求)
    BS->>IF: 10. postMessage(Token信息)
    IF->>GP: 11. 使用Token访问API
    GP->>G: 12. 转发API请求
    G-->>GP: 13. 返回数据
    GP-->>IF: 14. 返回处理后的数据
    IF-->>BS: 15. 更新图表显示
```

## 4. 数据模型设计

### 4.1 用户权限数据模型

```mermaid
erDiagram
    USER ||--o{ USER_ROLE : has
    ROLE ||--o{ ROLE_PERMISSION : has
    PERMISSION ||--o{ ROLE_PERMISSION : belongs_to
    USER ||--o{ USER_PERMISSION_CACHE : cached_in
    TENANT ||--o{ USER : belongs_to
    ORGANIZATION ||--o{ USER : belongs_to
    DEPARTMENT ||--o{ USER : belongs_to

    USER {
        string user_id PK
        string username
        string email
        string tenant_id FK
        string org_id FK
        string dept_id FK
        datetime created_at
        datetime updated_at
    }

    ROLE {
        string role_id PK
        string role_name
        string description
        datetime created_at
    }

    PERMISSION {
        string permission_id PK
        string resource_type
        string resource_id
        string action
        string description
    }

    USER_PERMISSION_CACHE {
        string user_id PK
        json permissions_data
        datetime cached_at
        datetime expires_at
    }

    TENANT {
        string tenant_id PK
        string tenant_name
        string schema_name
        boolean active
    }

    ORGANIZATION {
        string org_id PK
        string org_name
        string tenant_id FK
    }

    DEPARTMENT {
        string dept_id PK
        string dept_name
        string org_id FK
    }
```

### 4.2 SQL拦截配置数据模型

```mermaid
erDiagram
    TABLE_PERMISSION_CONFIG ||--o{ FIELD_MAPPING : has
    TENANT_SCHEMA_MAPPING ||--|| TENANT : maps_to
    SQL_INTERCEPT_LOG ||--|| USER : logged_by

    TABLE_PERMISSION_CONFIG {
        string table_name PK
        json user_fields
        string org_field
        string dept_field
        string tenant_field
        json custom_fields
        boolean enabled
    }

    FIELD_MAPPING {
        string mapping_id PK
        string table_name FK
        string field_name
        string permission_type
        string mapping_rule
    }

    TENANT_SCHEMA_MAPPING {
        string tenant_id PK
        string schema_name
        boolean isolation_enabled
        datetime created_at
    }

    SQL_INTERCEPT_LOG {
        string log_id PK
        string user_id FK
        string original_sql
        string modified_sql
        string api_path
        datetime intercept_time
        int processing_time_ms
    }
```

## 5. 部署架构设计

### 5.1 生产环境部署图

```mermaid
graph TB
    subgraph "DMZ区域"
        LB1[Nginx主负载均衡器]
        LB2[Nginx备负载均衡器]
    end

    subgraph "应用服务区域"
        subgraph "代理服务集群"
            GP1[bbpf-grafana-proxy-1<br/>8核16G]
            GP2[bbpf-grafana-proxy-2<br/>8核16G]
        end

        subgraph "Grafana服务"
            GF[Grafana Enterprise<br/>4核8G]
        end

        subgraph "业务服务"
            BS[BBPF后端服务<br/>8核16G]
        end
    end

    subgraph "数据服务区域"
        subgraph "缓存集群"
            RM[Redis Master<br/>8核16G]
            RS[Redis Slave<br/>8核16G]
        end

        subgraph "数据库集群"
            MM[MySQL Master<br/>16核32G]
            MS[MySQL Slave<br/>16核32G]
        end
    end

    subgraph "监控区域"
        PM[Prometheus<br/>4核8G]
        ELK[ELK Stack<br/>8核16G]
        AM[AlertManager<br/>2核4G]
    end

    Internet --> LB1
    Internet --> LB2
    LB1 --> GP1
    LB1 --> GP2
    LB2 --> GP1
    LB2 --> GP2
    GP1 --> GF
    GP2 --> GF
    GP1 --> BS
    GP2 --> BS
    GP1 --> RM
    GP2 --> RM
    RM --> RS
    BS --> MM
    MM --> MS

    GP1 --> PM
    GP2 --> PM
    GF --> PM
    BS --> PM
    PM --> AM

    GP1 --> ELK
    GP2 --> ELK
    GF --> ELK
    BS --> ELK

    classDef dmz fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef app fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef data fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef monitor fill:#fff3e0,stroke:#f57c00,stroke-width:2px

    class LB1,LB2 dmz
    class GP1,GP2,GF,BS app
    class RM,RS,MM,MS data
    class PM,ELK,AM monitor
```

### 5.2 网络拓扑图

```mermaid
graph LR
    subgraph "外网区域"
        Internet[互联网]
    end

    subgraph "DMZ区域 - 192.168.1.0/24"
        FW1[防火墙]
        LB[负载均衡器<br/>192.168.1.10]
    end

    subgraph "应用区域 - 192.168.2.0/24"
        GP1[代理服务1<br/>192.168.2.10]
        GP2[代理服务2<br/>192.168.2.11]
        GF[Grafana<br/>192.168.2.20]
        BS[BBPF服务<br/>192.168.2.30]
    end

    subgraph "数据区域 - 192.168.3.0/24"
        RM[Redis主<br/>192.168.3.10]
        RS[Redis从<br/>192.168.3.11]
        MM[MySQL主<br/>192.168.3.20]
        MS[MySQL从<br/>192.168.3.21]
    end

    subgraph "管理区域 - ***********/24"
        PM[Prometheus<br/>************]
        ELK[ELK<br/>************]
    end

    Internet --> FW1
    FW1 --> LB
    LB --> GP1
    LB --> GP2
    GP1 --> GF
    GP2 --> GF
    GP1 --> BS
    GP2 --> BS
    GP1 --> RM
    GP2 --> RM
    RM -.-> RS
    BS --> MM
    MM -.-> MS

    GP1 --> PM
    GP2 --> PM
    GF --> PM
    BS --> PM

    GP1 --> ELK
    GP2 --> ELK
    GF --> ELK
    BS --> ELK
```

### 5.3 容器化部署图

```mermaid
graph TB
    subgraph "Docker Host 1"
        subgraph "Nginx容器"
            N1[nginx:1.20-alpine]
        end
        subgraph "代理服务容器"
            P1[bbpf-grafana-proxy:latest]
        end
        subgraph "Redis容器"
            R1[redis:6.2-alpine<br/>Master]
        end
    end

    subgraph "Docker Host 2"
        subgraph "代理服务容器"
            P2[bbpf-grafana-proxy:latest]
        end
        subgraph "Grafana容器"
            G1[grafana/grafana-enterprise:9.5.0]
        end
        subgraph "Redis容器"
            R2[redis:6.2-alpine<br/>Slave]
        end
    end

    subgraph "Docker Host 3"
        subgraph "MySQL容器"
            M1[mysql:8.0<br/>Master]
        end
        subgraph "MySQL容器"
            M2[mysql:8.0<br/>Slave]
        end
    end

    subgraph "共享存储"
        V1[grafana-data]
        V2[mysql-master-data]
        V3[mysql-slave-data]
        V4[redis-data]
    end

    N1 --> P1
    N1 --> P2
    P1 --> G1
    P2 --> G1
    P1 --> R1
    P2 --> R1
    R1 -.-> R2
    P1 --> M1
    P2 --> M1
    M1 -.-> M2

    G1 --> V1
    M1 --> V2
    M2 --> V3
    R1 --> V4
    R2 --> V4
```

## 6. 安全架构设计

### 6.1 安全防护层次图

```mermaid
graph TB
    subgraph "网络安全层"
        A1[防火墙]
        A2[DDoS防护]
        A3[WAF应用防火墙]
    end

    subgraph "接入安全层"
        B1[SSL/TLS加密]
        B2[负载均衡器]
        B3[IP白名单]
    end

    subgraph "应用安全层"
        C1[JWT Token认证]
        C2[权限验证]
        C3[SQL注入防护]
        C4[XSS防护]
    end

    subgraph "数据安全层"
        D1[数据加密存储]
        D2[传输加密]
        D3[权限隔离]
        D4[审计日志]
    end

    subgraph "监控安全层"
        E1[异常行为检测]
        E2[安全事件告警]
        E3[访问日志分析]
    end

    A1 --> B1
    A2 --> B2
    A3 --> B3
    B1 --> C1
    B2 --> C2
    B3 --> C3
    C1 --> D1
    C2 --> D2
    C3 --> D3
    C4 --> D4
    D1 --> E1
    D2 --> E2
    D3 --> E3

    classDef network fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef access fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef app fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef data fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef monitor fill:#fff3e0,stroke:#f57c00,stroke-width:2px

    class A1,A2,A3 network
    class B1,B2,B3 access
    class C1,C2,C3,C4 app
    class D1,D2,D3,D4 data
    class E1,E2,E3 monitor
```

### 6.2 权限控制模型图

```mermaid
graph LR
    subgraph "用户层"
        U1[用户A<br/>租户1]
        U2[用户B<br/>租户2]
        U3[管理员<br/>超级权限]
    end

    subgraph "权限层"
        R1[角色1<br/>运维人员]
        R2[角色2<br/>业务人员]
        R3[角色3<br/>系统管理员]
    end

    subgraph "资源层"
        D1[仪表盘1<br/>租户1数据]
        D2[仪表盘2<br/>租户2数据]
        DS1[数据源1<br/>MySQL]
        DS2[数据源2<br/>Prometheus]
    end

    subgraph "数据层"
        T1[租户1Schema<br/>tenant1.tables]
        T2[租户2Schema<br/>tenant2.tables]
        T3[公共数据<br/>public.tables]
    end

    U1 --> R1
    U1 --> R2
    U2 --> R2
    U3 --> R3

    R1 --> D1
    R1 --> DS1
    R2 --> D1
    R2 --> D2
    R2 --> DS1
    R2 --> DS2
    R3 --> D1
    R3 --> D2
    R3 --> DS1
    R3 --> DS2

    D1 --> T1
    D2 --> T2
    DS1 --> T1
    DS1 --> T2
    DS1 --> T3
    DS2 --> T3

    classDef user fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef role fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef resource fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef data fill:#fff3e0,stroke:#f57c00,stroke-width:2px

    class U1,U2,U3 user
    class R1,R2,R3 role
    class D1,D2,DS1,DS2 resource
    class T1,T2,T3 data
```

## 7. 性能架构设计

### 7.1 缓存架构图

```mermaid
graph TB
    subgraph "应用层缓存"
        A1[本地缓存<br/>Caffeine]
        A2[JVM堆内缓存<br/>权限信息]
    end

    subgraph "分布式缓存层"
        B1[Redis Master<br/>主缓存节点]
        B2[Redis Slave<br/>从缓存节点]
        B3[Redis Sentinel<br/>哨兵节点]
    end

    subgraph "数据库层"
        C1[MySQL Master<br/>主数据库]
        C2[MySQL Slave<br/>从数据库]
    end

    subgraph "缓存策略"
        D1[用户权限缓存<br/>TTL: 30分钟]
        D2[SQL结果缓存<br/>TTL: 5分钟]
        D3[配置信息缓存<br/>TTL: 2小时]
    end

    A1 --> B1
    A2 --> B1
    B1 --> B2
    B1 --> B3
    B2 --> B3
    B1 --> C1
    B2 --> C2

    D1 --> B1
    D2 --> B1
    D3 --> B1

    classDef appCache fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef distCache fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef database fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef strategy fill:#fff3e0,stroke:#f57c00,stroke-width:2px

    class A1,A2 appCache
    class B1,B2,B3 distCache
    class C1,C2 database
    class D1,D2,D3 strategy
```

### 7.2 负载均衡架构图

```mermaid
graph TB
    subgraph "客户端"
        C1[Web浏览器]
        C2[移动端App]
        C3[API客户端]
    end

    subgraph "负载均衡层"
        LB[Nginx负载均衡器<br/>least_conn算法]
    end

    subgraph "应用服务集群"
        AS1[bbpf-grafana-proxy-1<br/>权重: 1]
        AS2[bbpf-grafana-proxy-2<br/>权重: 1]
        AS3[bbpf-grafana-proxy-3<br/>权重: 1]
    end

    subgraph "健康检查"
        HC[健康检查端点<br/>/actuator/health]
    end

    subgraph "会话保持"
        SP[Session Sticky<br/>基于IP Hash]
    end

    C1 --> LB
    C2 --> LB
    C3 --> LB

    LB --> AS1
    LB --> AS2
    LB --> AS3

    LB --> HC
    LB --> SP

    HC --> AS1
    HC --> AS2
    HC --> AS3

    classDef client fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef lb fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef app fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef health fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef session fill:#fce4ec,stroke:#c2185b,stroke-width:2px

    class C1,C2,C3 client
    class LB lb
    class AS1,AS2,AS3 app
    class HC health
    class SP session
```

## 8. 监控架构设计

### 8.1 监控体系架构图

```mermaid
graph TB
    subgraph "数据采集层"
        A1[应用指标<br/>Micrometer]
        A2[系统指标<br/>Node Exporter]
        A3[业务指标<br/>Custom Metrics]
        A4[日志数据<br/>Logback]
    end

    subgraph "数据存储层"
        B1[Prometheus<br/>时序数据库]
        B2[Elasticsearch<br/>日志存储]
        B3[InfluxDB<br/>业务指标]
    end

    subgraph "数据处理层"
        C1[Prometheus<br/>数据聚合]
        C2[Logstash<br/>日志处理]
        C3[Grafana<br/>数据查询]
    end

    subgraph "展示层"
        D1[Grafana Dashboard<br/>监控看板]
        D2[Kibana<br/>日志分析]
        D3[AlertManager<br/>告警管理]
    end

    subgraph "告警层"
        E1[邮件告警]
        E2[短信告警]
        E3[企业微信告警]
        E4[钉钉告警]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B3
    A4 --> B2

    B1 --> C1
    B2 --> C2
    B3 --> C3

    C1 --> D1
    C2 --> D2
    C1 --> D3

    D3 --> E1
    D3 --> E2
    D3 --> E3
    D3 --> E4

    classDef collect fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef store fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef process fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef display fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef alert fill:#fce4ec,stroke:#c2185b,stroke-width:2px

    class A1,A2,A3,A4 collect
    class B1,B2,B3 store
    class C1,C2,C3 process
    class D1,D2,D3 display
    class E1,E2,E3,E4 alert
```

### 8.2 关键指标监控图

```mermaid
graph LR
    subgraph "应用性能指标"
        A1[响应时间<br/>P95 < 100ms]
        A2[吞吐量<br/>QPS > 1000]
        A3[错误率<br/>< 0.1%]
        A4[可用性<br/>> 99.9%]
    end

    subgraph "系统资源指标"
        B1[CPU使用率<br/>< 80%]
        B2[内存使用率<br/>< 85%]
        B3[磁盘使用率<br/>< 90%]
        B4[网络带宽<br/>< 80%]
    end

    subgraph "业务指标"
        C1[用户登录数<br/>实时统计]
        C2[权限查询次数<br/>实时统计]
        C3[SQL拦截次数<br/>实时统计]
        C4[缓存命中率<br/>> 80%]
    end

    subgraph "安全指标"
        D1[异常登录<br/>实时监控]
        D2[权限异常<br/>实时监控]
        D3[SQL注入尝试<br/>实时监控]
        D4[访问频率异常<br/>实时监控]
    end

    A1 --> A2
    A2 --> A3
    A3 --> A4

    B1 --> B2
    B2 --> B3
    B3 --> B4

    C1 --> C2
    C2 --> C3
    C3 --> C4

    D1 --> D2
    D2 --> D3
    D3 --> D4

    classDef app fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef system fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef business fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef security fill:#ffebee,stroke:#c62828,stroke-width:2px

    class A1,A2,A3,A4 app
    class B1,B2,B3,B4 system
    class C1,C2,C3,C4 business
    class D1,D2,D3,D4 security
```

## 9. 接口设计

### 9.1 核心API接口图

```mermaid
graph TB
    subgraph "认证相关接口"
        A1[POST /api/auth/login<br/>用户登录]
        A2[POST /api/auth/refresh<br/>Token刷新]
        A3[POST /api/auth/logout<br/>用户登出]
        A4[GET /api/auth/validate<br/>Token验证]
    end

    subgraph "权限相关接口"
        B1[GET /api/permission/user/{userId}<br/>获取用户权限]
        B2[GET /api/permission/dashboard/{dashboardId}<br/>获取仪表盘权限]
        B3[GET /api/permission/datasource/{datasourceId}<br/>获取数据源权限]
        B4[POST /api/permission/validate<br/>权限验证]
    end

    subgraph "代理相关接口"
        C1[ALL /api/grafana/**<br/>Grafana代理]
        C2[GET /api/proxy/health<br/>健康检查]
        C3[GET /api/proxy/metrics<br/>监控指标]
        C4[POST /api/proxy/sql/intercept<br/>SQL拦截]
    end

    subgraph "管理相关接口"
        D1[GET /api/admin/users<br/>用户管理]
        D2[GET /api/admin/permissions<br/>权限管理]
        D3[GET /api/admin/config<br/>配置管理]
        D4[GET /api/admin/logs<br/>日志查询]
    end

    A1 --> B1
    A4 --> B4
    B1 --> C1
    B4 --> C4
    C2 --> D3
    C3 --> D4

    classDef auth fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef permission fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef proxy fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef admin fill:#fff3e0,stroke:#f57c00,stroke-width:2px

    class A1,A2,A3,A4 auth
    class B1,B2,B3,B4 permission
    class C1,C2,C3,C4 proxy
    class D1,D2,D3,D4 admin
```

### 9.2 数据流向图

```mermaid
graph LR
    subgraph "前端应用"
        F1[BBPF前端]
        F2[业务系统iframe]
        F3[移动端App]
    end

    subgraph "代理服务"
        P1[认证过滤器]
        P2[权限验证]
        P3[SQL拦截器]
        P4[请求转发]
    end

    subgraph "后端服务"
        B1[BBPF认证服务]
        B2[BBPF权限服务]
        B3[Grafana服务]
        B4[监控数据源]
    end

    F1 --> P1
    F2 --> P1
    F3 --> P1

    P1 --> B1
    P1 --> P2
    P2 --> B2
    P2 --> P3
    P3 --> P4
    P4 --> B3
    B3 --> B4

    classDef frontend fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef proxy fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef backend fill:#e8f5e8,stroke:#388e3c,stroke-width:2px

    class F1,F2,F3 frontend
    class P1,P2,P3,P4 proxy
    class B1,B2,B3,B4 backend
```

## 10. 总结

### 10.1 设计特点

本概要设计文档基于现有的bbpf-grafana-proxy架构，具有以下特点：

1. **完整的架构体系**：从总体架构到具体实现，涵盖了系统的各个层面
2. **详细的流程设计**：通过时序图和流程图清晰展示了业务流程
3. **全面的安全考虑**：多层次安全防护，确保系统安全可靠
4. **高性能架构**：缓存策略和负载均衡保证系统高性能
5. **可视化设计**：通过各种图表直观展示系统设计

### 10.2 核心价值

- **统一认证**：基于JWT Token的单点登录机制
- **精细权限**：多租户、多层级的权限控制体系
- **数据隔离**：Schema级别的租户数据隔离
- **高可用性**：集群部署和故障自动切换
- **可扩展性**：模块化设计支持功能扩展

### 10.3 实施建议

1. **分阶段实施**：按照设计文档分阶段推进项目实施
2. **重点关注安全**：确保权限控制和数据隔离的正确实现
3. **性能优化**：重点关注缓存策略和SQL拦截性能
4. **监控完善**：建立完整的监控和告警体系
5. **文档维护**：及时更新设计文档和技术文档

通过本概要设计文档，为BPF集成Grafana项目提供了完整的技术架构指导，确保项目能够按照既定目标顺利实施。
