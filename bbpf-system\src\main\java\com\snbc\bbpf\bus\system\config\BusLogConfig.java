package com.snbc.bbpf.bus.system.config;

import com.snbc.bbpf.bus.system.properties.BusLogTaskPropertie;
import com.snbc.bbpf.bus.system.service.impl.BusLogSaveServiceImpl;
import com.snbc.bbpf.system.db.common.mapper.LogMapper;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName: BusLogConfig
 * @Description: 注册日志实现类
 * @module: SI-bbpf-tenant-config
 * @Author: wangsong
 * @date: 2022/11/7
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Configuration
@EnableConfigurationProperties(BusLogTaskPropertie.class)
public class BusLogConfig {
    @Bean
    public BusLogSaveServiceImpl busLogSaveServiceImpl(LogMapper busLogMapper, BusLogTaskPropertie busLogTaskPropertie) {
        BusLogSaveServiceImpl busLogSaveServiceImpl = new BusLogSaveServiceImpl(busLogMapper,busLogTaskPropertie);
        busLogSaveServiceImpl.startTask();
        return busLogSaveServiceImpl;
    }
}
