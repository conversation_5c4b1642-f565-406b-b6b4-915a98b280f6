package com.snbc.bbpf.bus.system.handler;

import com.snbc.bbpf.bus.system.constans.ThirdHandlerType;
import com.snbc.bbpf.bus.system.enums.ThirdBindEnum;
import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.service.ThirdLoginBind;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description ThirdBindServiceFactory 策略统一加载类
 * @createTime 2022年01月21日 10:51
 */
@Component
@Slf4j
public class ThirdBindServiceFactory implements ApplicationContextAware {

    /** 存所有 ElectronicProductService 实现类 */
    private static Map<ThirdBindEnum, ThirdLoginBind> enumThirdLoginBindMap=new HashMap<>();


    /**
     * 自动加载所有因子组实现类
     * @param applicationContext
     * @throws BeansException
     */
    @Override
    public  void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        // 获取 ThirdLoginBind 接口的所有实现类
        Map<String,ThirdLoginBind> electronicProductServiceMap=applicationContext.getBeansOfType(ThirdLoginBind.class);
        // 将实现类放入map中，key为实现类对应的枚举，value为实现类
        for (ThirdLoginBind item: electronicProductServiceMap.values()) {
            ThirdHandlerType annotation = item.getClass().getAnnotation(ThirdHandlerType.class);
            if (null != annotation ){
                enumThirdLoginBindMap.put(annotation.value(),item);
            }

        }
    }


    /**
     * 获取实现类
     * @param type
     * @return electronicProductService
     * @throws BusinessException
     */
    public static ThirdLoginBind getThirdLoginBindServiceImpl(String type) throws BusinessException {
        return enumThirdLoginBindMap.get(getElectronicEnum(type));
    }

    public static ThirdBindEnum getElectronicEnum(String type){
        for (ThirdBindEnum item: ThirdBindEnum.values()) {
            if (item.getType().equals(type)){
                return item;
            }
        }
        throw new BusinessException(ErrorMessage.USER_LOGIN_NOT_HANDLE);
    }
}
