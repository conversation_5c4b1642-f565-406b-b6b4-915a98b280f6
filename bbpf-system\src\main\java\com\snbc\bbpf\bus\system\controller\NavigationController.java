/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.controller;


import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.bus.system.service.INavigationService;
import com.snbc.bbpf.bus.system.utils.ResultUtil;
import com.snbc.bbpf.system.db.common.dto.NavigationDto;
import com.snbc.bbpf.system.db.common.dto.NavigationPermissionDto;
import com.snbc.bbpf.system.db.common.vo.NavigationVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
/**
 * 顶部导航功能Controller
 *
 * <AUTHOR>
 * @date 2022-12-27
 */
@RestController
@RequestMapping("/console/navigation")
public class NavigationController {
    @Autowired
    private INavigationService navigationService;

    /**
     * 查询顶部导航功能列表
     * @Description: 查询顶部导航功能列表
     * @Author: wangsong
     * @param :         enterprise
     * @CreateDate: 2021/12/30 17:17
     * @UpdateDate: 2021/12/30 17:17
     * @return :        com.snbc.bbpf.component.config.CommonResp
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @GetMapping("/list")
    public CommonResp<Object> list() {
        CommonResp<Object> commonResp = new CommonResp<>();
        commonResp.setHead(ResultUtil.success());
        //获取导航数据
        commonResp.setBody(navigationService.getNavigationList());
        return  commonResp;
    }

    /**
     * 获取顶部导航功能详细信息
     *
     */
    @GetMapping(value = "/detail")
    public CommonResp<NavigationVo> getInfo(@RequestParam("navigationId") String navigationId) {
        CommonResp<NavigationVo> commonResp = new CommonResp<>();
        commonResp.setHead(ResultUtil.success());
        commonResp.setBody(navigationService.selectNavigationByNavigationId(navigationId));
        return  commonResp;
    }

    /**
     *保存关系
     *
     *
     * @return
     */
    @PostMapping(value = "/saveRelationship")
    public CommonResp<Object> saveNavigationPermission(@RequestBody NavigationPermissionDto navigationPermissionDto){
        CommonResp<Object> commonResp = new CommonResp<>();
        commonResp.setHead(ResultUtil.success());
        navigationService.saveNavigationPermission(navigationPermissionDto);
        return  commonResp;
    }

    /**
     * 获取顶部导航功能详细信息
     *
     */
    @GetMapping(value = "/getRelationship")
    public CommonResp<Object> getRelationship(@RequestParam("navigationId") String navigationId) {
        CommonResp<Object> commonResp = new CommonResp<>();
        commonResp.setHead(ResultUtil.success());
        commonResp.setBody(navigationService.getRelationship(navigationId));
        return  commonResp;
    }

    /**
     *保存关系
     * @return
     *
     */
    @PostMapping(value = "/updateRelationship")
    public CommonResp<Object> updateRelationship(NavigationPermissionDto navigationPermissionDto){
        CommonResp<Object> commonResp = new CommonResp<>();
        commonResp.setHead(ResultUtil.success());
        navigationService.saveNavigationPermission(navigationPermissionDto);
        return  commonResp;
    }
    /**
     * 新增顶部导航功能
     *
     */
    @PostMapping(value = "/add")
    public CommonResp<Object> add(@Validated @RequestBody NavigationDto navigationDto) {
        CommonResp<Object> commonResp = new CommonResp<>();
        commonResp.setHead(ResultUtil.success());
        commonResp.setBody(navigationService.insertNavigation(navigationDto));
        return  commonResp;
    }

    /**
     * 修改顶部导航功能
     */
    @PutMapping(value = "/edit")
    public CommonResp<Object> edit(@Validated @RequestBody NavigationDto navigationDto) {
        CommonResp<Object> commonResp = new CommonResp<>();
        commonResp.setHead(ResultUtil.success());
        commonResp.setBody(navigationService.updateNavigation(navigationDto));
        return  commonResp;
    }

    /**
     * 删除顶部导航功能
     */
	@DeleteMapping("/delete")
    public CommonResp<Object> remove(@RequestParam("navigationIds") String navigationIds) {
        CommonResp<Object> commonResp = new CommonResp<>();
        commonResp.setHead(ResultUtil.success());
        String[] ids=navigationIds.split(",");
        commonResp.setBody(navigationService.deleteNavigationByNavigationIds(ids));
        return  commonResp;
    }
}
