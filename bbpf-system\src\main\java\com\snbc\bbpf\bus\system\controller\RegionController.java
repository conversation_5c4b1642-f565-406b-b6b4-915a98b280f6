/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.bus.system.service.RegionService;
import com.snbc.bbpf.bus.system.utils.ExcelReadListener;
import com.snbc.bbpf.bus.system.utils.ResultUtil;
import com.snbc.bbpf.buslog.annotations.Buslog;
import com.snbc.bbpf.buslog.annotations.OperationType;
import com.snbc.bbpf.system.db.common.entity.Region;
import com.snbc.bbpf.system.db.common.vo.RegionVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: RegionController
 * 地域控制层
 * @module: bbpf-bus-system
 * @Author: liuyi
 * @date: 2021/5/20
 * copyright 2020 barm Inc. All rights reserver
 */
@RestController
@RequestMapping("/console/region")
public class RegionController {

    @Autowired
    private RegionService regionService;

    /**
     * @param region 地域json体
     * @description: 新增地域
     * @return: com.snbc.bbpf.component.config.CommonResp
     * @author: liuyi
     * @time: 2021/5/20 10:19
     */
    @PostMapping(value = "/addRegion")
    @Buslog(opration = "addRegion", zh = "新增地域【地域名称：${regionName}，地域编号：${regionCode}】",
            en = "New region [region name:${regionName}, region number:${regionCode}]", target = "regionManage")
    public CommonResp<String> addRegion(@RequestBody @Valid Region region) {
        CommonResp<String> commonResp = new CommonResp<>();
        regionService.addRegion(region, commonResp);
        return commonResp;
    }

    /**
     * @param region 地域json体
     * @description:修改地域
     * @return: com.snbc.bbpf.component.config.CommonResp
     * @author: liuyi
     * @time: 2021/5/20 10:19
     */
    @PostMapping(value = "/updateRegion")
    @Buslog(oprationType = OperationType.MODIFY,opration = "modifyRegion", target = "regionManage",type = Region.class)
    public CommonResp<String> updateRegion(@RequestBody @Valid Region region) {
        CommonResp<String> commonResp = new CommonResp<>();
        if(StringUtils.isEmpty(region.getParentCode())){
            region.setParentCode("0");
        }
        regionService.updateRegion(region, commonResp);
        return commonResp;
    }

    @DeleteMapping(value = "/delRegion")
    @Buslog(opration = "delRegion", zh = "删除地域【地域名称：${regionName}，地域编号：${regionCode}】",
            en = "Delete region [region name:${regionName}, region number:${regionCode}]", target = "regionManage")
    public CommonResp<String> delRegion(@RequestParam("regionCode") @Valid @Size(min = 1, max = 15) String regionCode) {
        CommonResp<String> commonResp = new CommonResp<>();
        regionService.deleteRegion(regionCode, commonResp);
        return commonResp;
    }

    @GetMapping(value = "/getRegionList")
    public CommonResp getRegionList(@RequestParam("regionCode") String regionCode) {
        List<RegionVo> regionList = regionService.getRegionList(regionCode);
        return CommonResp.builder().head(ResultUtil.success()).body(regionList).build();
    }

    @PostMapping(value = "/importRegion")
    public CommonResp<String> importRegion(HttpServletRequest request) throws IOException {
        Map<String, MultipartFile> files = ((MultipartHttpServletRequest) request).getFileMap();
        CommonResp<String> commonResp = new CommonResp<>();
        if (files.size() != 1) {
            commonResp.setHead(ResultUtil.error("excel 文件过多"));
            return commonResp;
        }
        for (Map.Entry<String, MultipartFile> key : files.entrySet()) {
            ExcelReadListener<Region> excelReadListener = new ExcelReadListener<>();
            EasyExcelFactory.read(files.get(key.getKey()).getInputStream(), Region.class, excelReadListener).sheet().doRead();
            List<Region> list = excelReadListener.getList();
            regionService.importRegion(list, commonResp);
        }
        return commonResp;
    }
}
