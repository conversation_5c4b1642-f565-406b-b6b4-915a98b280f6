<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BBPF Grafana代理服务 - JWT认证测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007bff;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
        }
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 10px 0;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .warning {
            color: #ffc107;
            font-weight: bold;
        }
        .info {
            color: #17a2b8;
            font-weight: bold;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px 0;
        }
        .test-result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
        .status-unknown { background-color: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 BBPF Grafana代理服务 - JWT认证测试</h1>
        
        <div class="section">
            <h2>📊 服务状态检查</h2>
            <p>当前访问返回的错误信息表明代理服务正常运行，但需要JWT认证。</p>
            <div id="serviceStatus">
                <p><span class="status-indicator status-online"></span><span class="success">代理服务运行正常</span></p>
                <p><span class="status-indicator status-unknown"></span>需要JWT Token进行认证</p>
            </div>
        </div>

        <div class="section">
            <h2>🔑 JWT Token测试</h2>
            <p>请输入有效的JWT Token来测试认证：</p>
            <input type="text" id="jwtToken" placeholder="请输入JWT Token (Bearer token)">
            <br>
            <button onclick="testWithToken()">测试JWT认证</button>
            <button onclick="testWithoutToken()">测试无Token访问</button>
            <button onclick="testHealthCheck()">健康检查</button>
            
            <div id="testResult" class="test-result" style="display: none;"></div>
        </div>

        <div class="section">
            <h2>📝 正确的访问方式</h2>
            
            <h3>1. 使用Authorization Header</h3>
            <div class="code">
fetch('http://g.xinbeiyang.info/grafana/api/dashboards/home', {
    method: 'GET',
    headers: {
        'Authorization': 'Bearer YOUR_JWT_TOKEN_HERE',
        'Content-Type': 'application/json'
    }
})
.then(response => response.json())
.then(data => console.log(data));
            </div>

            <h3>2. 使用Query参数</h3>
            <div class="code">
http://g.xinbeiyang.info/grafana/api/dashboards/home?token=YOUR_JWT_TOKEN_HERE
            </div>

            <h3>3. cURL命令示例</h3>
            <div class="code">
curl -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
     -H "Content-Type: application/json" \
     http://g.xinbeiyang.info/grafana/api/dashboards/home
            </div>
        </div>

        <div class="section">
            <h2>🚫 白名单路径（无需认证）</h2>
            <p>以下路径可以直接访问，无需JWT Token：</p>
            <ul>
                <li><code>/health</code> - 健康检查</li>
                <li><code>/actuator/**</code> - 监控端点</li>
                <li><code>/public/**</code> - 公共资源</li>
                <li><code>/static/**</code> - 静态资源</li>
                <li><code>/css/**</code> - CSS文件</li>
                <li><code>/js/**</code> - JavaScript文件</li>
                <li><code>/images/**</code> - 图片资源</li>
                <li><code>/fonts/**</code> - 字体文件</li>
                <li><code>/assets/**</code> - 资源文件</li>
            </ul>
        </div>

        <div class="section">
            <h2>🔧 故障排除</h2>
            
            <h3>常见错误及解决方案：</h3>
            
            <h4 class="error">错误: {"error":"Missing JWT token","code":401}</h4>
            <p><strong>原因：</strong>请求中没有提供JWT Token</p>
            <p><strong>解决：</strong>在请求头中添加 <code>Authorization: Bearer YOUR_TOKEN</code> 或在URL中添加 <code>?token=YOUR_TOKEN</code></p>
            
            <h4 class="error">错误: {"error":"Invalid JWT token","code":401}</h4>
            <p><strong>原因：</strong>JWT Token格式错误或签名验证失败</p>
            <p><strong>解决：</strong>检查Token格式，确保使用正确的密钥签名</p>
            
            <h4 class="error">错误: {"error":"JWT token has expired","code":401}</h4>
            <p><strong>原因：</strong>JWT Token已过期</p>
            <p><strong>解决：</strong>获取新的有效Token</p>
        </div>

        <div class="section">
            <h2>📋 配置信息</h2>
            <p>当前代理服务配置：</p>
            <ul>
                <li><strong>代理服务地址：</strong> http://g.xinbeiyang.info</li>
                <li><strong>Grafana后端：</strong> http://localhost:3000</li>
                <li><strong>认证方式：</strong> JWT Token</li>
                <li><strong>Token验证：</strong> 暂时禁用（开发模式）</li>
                <li><strong>详细日志：</strong> 已启用</li>
            </ul>
        </div>
    </div>

    <script>
        function showResult(message, type = 'info') {
            const resultDiv = document.getElementById('testResult');
            resultDiv.style.display = 'block';
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = message;
        }

        async function testWithToken() {
            const token = document.getElementById('jwtToken').value.trim();
            if (!token) {
                showResult('请先输入JWT Token', 'error');
                return;
            }

            try {
                const response = await fetch('/grafana/api/health', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.text();
                if (response.ok) {
                    showResult(`✅ 认证成功！<br>响应: ${data}`, 'success');
                } else {
                    showResult(`❌ 认证失败 (${response.status})<br>响应: ${data}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 请求失败: ${error.message}`, 'error');
            }
        }

        async function testWithoutToken() {
            try {
                const response = await fetch('/grafana/api/health', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.text();
                showResult(`❌ 无Token访问结果 (${response.status})<br>响应: ${data}`, 'warning');
            } catch (error) {
                showResult(`❌ 请求失败: ${error.message}`, 'error');
            }
        }

        async function testHealthCheck() {
            try {
                const response = await fetch('/health', {
                    method: 'GET'
                });

                const data = await response.text();
                if (response.ok) {
                    showResult(`✅ 健康检查成功！<br>响应: ${data}`, 'success');
                } else {
                    showResult(`⚠️ 健康检查异常 (${response.status})<br>响应: ${data}`, 'warning');
                }
            } catch (error) {
                showResult(`❌ 健康检查失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动检查服务状态
        window.onload = function() {
            console.log('BBPF Grafana代理服务测试页面已加载');
        };
    </script>
</body>
</html>