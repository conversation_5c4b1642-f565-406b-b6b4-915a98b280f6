package com.snbc.bbpf.system.db.common.dto;

import com.snbc.bbpf.system.db.common.vo.ExportLogVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Worksheet {
    //sheet 名称
    private String sheet;
    //导出页面名称
    private String title;
    //具体的行数
    private List<ExportLogVo> logs;
}
