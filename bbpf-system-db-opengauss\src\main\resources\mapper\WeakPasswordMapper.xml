<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snbc.bbpf.system.db.common.mapper.WeakPasswordMapper">
    
    <resultMap type="com.snbc.bbpf.system.db.common.entity.WeakPassword" id="WeakPasswordResult">
        <result property="id"    column="id"    />
        <result property="pwd"    column="pwd"    />
        <result property="createUserId"    column="create_user_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateUserId"    column="update_user_id"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWeakPasswordVo">
        select id, pwd, create_user_id, create_time, update_user_id, update_time from t_weak_password
    </sql>

    <select id="selectWeakPasswordList" parameterType="com.snbc.bbpf.system.db.common.entity.WeakPassword" resultMap="WeakPasswordResult">
        <include refid="selectWeakPasswordVo"/>
        <where>  
            <if test="pwd != null  and pwd != ''"> and pwd =#{pwd}</if>
            <if test="createUserId != null  and createUserId != ''"> and create_user_id like concat('%', #{createUserId}, '%')</if>
        </where>
    </select>
    
    <select id="selectWeakPasswordById" parameterType="String" resultMap="WeakPasswordResult">
        <include refid="selectWeakPasswordVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertWeakPassword" parameterType="com.snbc.bbpf.system.db.common.entity.WeakPassword">
        insert into t_weak_password
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="pwd != null">pwd,</if>
            <if test="createUserId != null">create_user_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateUserId != null">update_user_id,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="pwd != null">#{pwd},</if>
            <if test="createUserId != null">#{createUserId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateUserId != null">#{updateUserId},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWeakPassword" parameterType="com.snbc.bbpf.system.db.common.entity.WeakPassword">
        update t_weak_password
        <trim prefix="SET" suffixOverrides=",">
            <if test="pwd != null">pwd = #{pwd},</if>
            <if test="createUserId != null">create_user_id = #{createUserId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateUserId != null">update_user_id = #{updateUserId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWeakPasswordById" parameterType="String">
        delete from t_weak_password where id = #{id}
    </delete>

    <delete id="deleteWeakPasswordByIds" parameterType="String">
        delete from t_weak_password where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>