package com.snbc.bbpf.bus.system.enums;

/**
 * @ClassName: SmsKeyEnum
 * @Description: 短信验证码key
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2022/2/14
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
public enum SmsKeyEnum {
    NO1(0, "smsLogin:%s"),
    NO2(1, "smsForgotPwd:%s"),
    NO3(2, "smsUpdatePwd:%s"),
    NO4(3,"smsUpdatePhone:%s"),
    NO41(41,"bindWeiXin:%s"),
    NO42(42,"bindDingding:%s"),
    ;

    private Integer type;
    private String smsKeyName;

    SmsKeyEnum(Integer type, String smsKeyName) {
        this.type = type;
        this.smsKeyName = smsKeyName;
    }

    /**
     * @return java.lang.String
     * <AUTHOR>
     * @Description 通过状态获取名称
     * @Date 16:54 2021/5/20
     * @Param [status]
     **/
    public static String getStatusName(Integer type) {
        for (SmsKeyEnum value : SmsKeyEnum.values()) {
            if (type != null && type.equals(value.type)) {
                return value.smsKeyName;
            }
        }
        return null;
    }
}
