package com.snbc.bbpf.grafana.proxy.controller;

import com.snbc.bbpf.grafana.proxy.filter.JwtAuthenticationFilter;
import com.snbc.bbpf.grafana.proxy.service.GrafanaProxyService;
import com.snbc.bbpf.grafana.proxy.service.PermissionService;
import com.snbc.bbpf.grafana.proxy.service.GrafanaVariableService;
import com.snbc.bbpf.grafana.proxy.dto.UserPermissionDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Grafana代理控制器
 * 
 * 处理所有到Grafana的代理请求：
 * 1. 通用代理请求处理
 * 2. 仪表盘访问
 * 3. API请求转发
 * 4. 数据导出
 * 5. 健康检查和统计信息
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2024-12-13
 */
@RestController
@RequestMapping("/")
public class GrafanaProxyController {

	private static final Logger logger = LoggerFactory.getLogger(GrafanaProxyController.class);

	@Autowired
	private GrafanaProxyService grafanaProxyService;

	@Autowired
	private PermissionService permissionService;

	@Autowired
	private GrafanaVariableService grafanaVariableService;

	/**
	 * 仪表盘访问
	 * 专门处理仪表盘访问请求，提供更精确的权限控制
	 */
	@GetMapping("/d/{dashboardId}/")
	@Order(1)
	public void accessDashboard(@PathVariable String dashboardId,
			HttpServletRequest request,
			HttpServletResponse response) {

		String userId = JwtAuthenticationFilter.getUserId(request);
		if (userId == null) {
			logger.error("User ID not found for dashboard access");
			try {
				response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
				response.getWriter().write("{\"error\":\"Authentication required\"}");
			} catch (Exception e) {
				logger.error("Error writing authentication error response: {}", e.getMessage());
			}
			return;
		}

		logger.info("Dashboard access request: {} for user: {}", dashboardId, userId);

		// 处理仪表盘访问
		boolean success = grafanaProxyService.handleDashboardAccess(request, response, dashboardId, userId);

		if (!success) {
			logger.warn("Dashboard access failed: {} for user: {}", dashboardId, userId);
		}
	}

	/**
	 * API请求处理
	 * 处理Grafana API请求
	 */
	@RequestMapping(value = "/api/**", method = { RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT,
			RequestMethod.DELETE })
	@Order(2)
	public void handleApiRequest(HttpServletRequest request, HttpServletResponse response) {

		String userId = JwtAuthenticationFilter.getUserId(request);
		if (userId == null) {
			logger.error("User ID not found for API request");
			try {
				response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
				response.getWriter().write("{\"error\":\"Authentication required\"}");
			} catch (Exception e) {
				logger.error("Error writing authentication error response: {}", e.getMessage());
			}
			return;
		}

		String apiPath = request.getRequestURI().replaceFirst("^/grafana", "");

		logger.debug("API request: {} {} for user: {}", request.getMethod(), apiPath, userId);

		// 处理API请求
		boolean success = grafanaProxyService.handleApiRequest(request, response, apiPath, userId);

		if (!success) {
			logger.warn("API request failed: {} {} for user: {}", request.getMethod(), apiPath, userId);
		}
	}

	/**
	 * 数据导出处理
	 * 处理数据导出请求，需要特殊的导出权限
	 */
	@PostMapping("/export/{resourceId}")
	@Order(3)
	public void handleDataExport(@PathVariable String resourceId,
			HttpServletRequest request,
			HttpServletResponse response) {
		String userId = JwtAuthenticationFilter.getUserId(request);
		if (userId == null) {
			logger.error("User ID not found for data export");
			try {
				response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
				response.getWriter().write("{\"error\":\"Authentication required\"}");
			} catch (Exception e) {
				logger.error("Error writing authentication error response: {}", e.getMessage());
			}
			return;
		}
		logger.info("Data export request: {} for user: {}", resourceId, userId);
		// 处理数据导出
		boolean success = grafanaProxyService.handleDataExport(request, response, resourceId, userId);
		if (!success) {
			logger.warn("Data export failed: {} for user: {}", resourceId, userId);
		}
	}

	/**
	 * 通用代理请求处理
	 * 处理所有到Grafana的请求，包括页面访问、API调用等
	 */
	@RequestMapping(value = "/**", method = { RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT,
			RequestMethod.DELETE })
	@Order(4)
	public void proxyToGrafana(HttpServletRequest request, HttpServletResponse response) {
		String userId = JwtAuthenticationFilter.getUserId(request);

		// 提取目标路径（去除/grafana前缀）
		String requestURI = request.getRequestURI();
		String targetPath = requestURI.replaceFirst("^/grafana", "");

		if (targetPath.isEmpty()) {
			targetPath = "/";
		}

		logger.debug("Proxying request: {} {} for user: {}", request.getMethod(), targetPath, userId);

		// 执行代理请求
		boolean success = grafanaProxyService.proxyRequest(request, response, targetPath, userId);

		if (!success) {
			logger.warn("Failed to proxy request: {} {} for user: {}", request.getMethod(), targetPath, userId);
		}
	}

	/**
	 * 导出图表为PNG图片
	 * 
	 * @param dashboardUid 仪表盘UID
	 * @param panelId      面板ID
	 * @param from         时间范围起始
	 * @param to           时间范围结束
	 * @param width        图片宽度
	 * @param height       图片高度
	 * @param request      HTTP请求
	 * @param response     HTTP响应
	 */
	@GetMapping("/api/export/panel")
	public void exportPanel(
			@RequestParam("dashboardUid") String dashboardUid,
			@RequestParam("panelId") int panelId,
			@RequestParam("from") long from,
			@RequestParam("to") long to,
			@RequestParam(value = "width", defaultValue = "1000") int width,
			@RequestParam(value = "height", defaultValue = "500") int height,
			HttpServletRequest request,
			HttpServletResponse response) {

		String userId = JwtAuthenticationFilter.getUserId(request);
		if (userId == null) {
			try {
				response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
				response.getWriter().write("{\"error\":\"Authentication required\"}");
			} catch (Exception e) {
				logger.error("Error writing authentication error response: {}", e.getMessage());
			}
			return;
		}

		try {
			byte[] imageBytes = grafanaProxyService.getPanelAsPng(userId, dashboardUid, panelId, from, to, width,
					height, request);

			if (imageBytes != null && imageBytes.length > 0) {
				response.setContentType("image/png");
				response.setHeader("Content-Disposition", "attachment; filename=\"grafana_panel_" + panelId + ".png\"");
				response.getOutputStream().write(imageBytes);
			} else {
				response.setStatus(HttpServletResponse.SC_NOT_FOUND);
				response.getWriter().write("{\"error\":\"Failed to render panel or panel not found\"}");
			}
		} catch (Exception e) {
			logger.error("Failed to export panel: {}", e.getMessage(), e);
			try {
				response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
				response.getWriter().write("{\"error\":\"Failed to export panel\"}");
			} catch (IOException ioException) {
				logger.error("Error writing error response: {}", ioException.getMessage());
			}
		}
	}

	/**
	 * 健康检查
	 * 检查代理服务和相关组件的健康状态
	 */
	@GetMapping("/health")
	public ResponseEntity<Map<String, Object>> healthCheck() {
		Map<String, Object> health = new HashMap<>();

		try {
			// 检查Grafana可用性
			boolean grafanaAvailable = grafanaProxyService.isGrafanaAvailable();
			health.put("grafanaAvailable", grafanaAvailable);

			// 检查权限服务可用性
			boolean permissionServiceAvailable = permissionService.isServiceAvailable();
			health.put("permissionServiceAvailable", permissionServiceAvailable);

			// 整体健康状态
			boolean overallHealth = grafanaAvailable && permissionServiceAvailable;
			health.put("status", overallHealth ? "UP" : "DOWN");
			health.put("timestamp", System.currentTimeMillis());

			// 添加详细信息
			Map<String, Object> details = new HashMap<>();
			details.put("grafana", grafanaAvailable ? "UP" : "DOWN");
			details.put("permissionService", permissionServiceAvailable ? "UP" : "DOWN");
			health.put("details", details);

			return ResponseEntity.ok(health);

		} catch (Exception e) {
			logger.error("Health check failed: {}", e.getMessage());
			health.put("status", "DOWN");
			health.put("error", e.getMessage());
			health.put("timestamp", System.currentTimeMillis());

			return ResponseEntity.status(500).body(health);
		}
	}

	/**
	 * 获取代理统计信息
	 * 返回代理服务的运行统计数据
	 */
	@GetMapping("/stats")
	public ResponseEntity<Map<String, Object>> getStatistics(HttpServletRequest request) {

		String userId = JwtAuthenticationFilter.getUserId(request);
		if (userId == null) {
			Map<String, Object> errorResponse = new HashMap<>();
			errorResponse.put("error", "Authentication required");
			return ResponseEntity.status(HttpServletResponse.SC_UNAUTHORIZED)
					.body(errorResponse);
		}

		try {
			Map<String, Object> stats = grafanaProxyService.getProxyStatistics();
			stats.put("timestamp", System.currentTimeMillis());
			stats.put("requestedBy", userId);

			return ResponseEntity.ok(stats);

		} catch (Exception e) {
			logger.error("Failed to get statistics: {}", e.getMessage());
			Map<String, Object> errorResponse = new HashMap<>();
			errorResponse.put("error", "Failed to get statistics");
			return ResponseEntity.status(500)
					.body(errorResponse);
		}
	}

	/**
	 * 清除用户权限缓存
	 * 允许管理员清除特定用户的权限缓存
	 */
	@DeleteMapping("/cache/permission/{targetUserId}")
	public ResponseEntity<Map<String, Object>> clearUserPermissionCache(
			@PathVariable String targetUserId,
			HttpServletRequest request) {

		String userId = JwtAuthenticationFilter.getUserId(request);
		if (userId == null) {
			Map<String, Object> errorResponse = new HashMap<>();
			errorResponse.put("error", "Authentication required");
			return ResponseEntity.status(HttpServletResponse.SC_UNAUTHORIZED)
					.body(errorResponse);
		}

		// 检查是否有管理权限（简化实现，实际应该检查具体权限）
		if (!permissionService.hasPermission(userId, "admin")) {
			Map<String, Object> errorResponse = new HashMap<>();
			errorResponse.put("error", "Admin permission required");
			return ResponseEntity.status(HttpServletResponse.SC_FORBIDDEN)
					.body(errorResponse);
		}

		try {
			permissionService.clearUserPermissionCache(targetUserId);

			logger.info("User {} cleared permission cache for user {}", userId, targetUserId);

			Map<String, Object> successResponse = new HashMap<>();
			successResponse.put("message", "Permission cache cleared successfully");
			successResponse.put("targetUserId", targetUserId);
			successResponse.put("clearedBy", userId);
			successResponse.put("timestamp", System.currentTimeMillis());
			return ResponseEntity.ok(successResponse);

		} catch (Exception e) {
			logger.error("Failed to clear permission cache for user {}: {}", targetUserId, e.getMessage());
			Map<String, Object> errorResponse = new HashMap<>();
			errorResponse.put("error", "Failed to clear permission cache");
			return ResponseEntity.status(500)
					.body(errorResponse);
		}
	}

	/**
	 * 刷新用户权限
	 * 强制重新从BBPF系统获取用户权限
	 */
	@PostMapping("/permission/refresh")
	public ResponseEntity<Map<String, Object>> refreshUserPermission(HttpServletRequest request) {

		String userId = JwtAuthenticationFilter.getUserId(request);
		if (userId == null) {
			Map<String, Object> errorResponse = new HashMap<>();
			errorResponse.put("error", "Authentication required");
			return ResponseEntity.status(HttpServletResponse.SC_UNAUTHORIZED)
					.body(errorResponse);
		}

		try {
			permissionService.refreshUserPermissions(userId);

			logger.info("User {} refreshed their permissions", userId);

			Map<String, Object> successResponse = new HashMap<>();
			successResponse.put("message", "Permissions refreshed successfully");
			successResponse.put("userId", userId);
			successResponse.put("timestamp", System.currentTimeMillis());
			return ResponseEntity.ok(successResponse);

		} catch (Exception e) {
			logger.error("Failed to refresh permissions for user {}: {}", userId, e.getMessage());
			Map<String, Object> errorResponse = new HashMap<>();
			errorResponse.put("error", "Failed to refresh permissions");
			return ResponseEntity.status(500)
					.body(errorResponse);
		}
	}

	/**
	 * 获取用户权限变量
	 * 为Grafana仪表盘提供基于用户权限的动态变量
	 */
	@GetMapping("/api/variables/user-permissions")
	public ResponseEntity<Map<String, Object>> getUserPermissionVariables(HttpServletRequest request) {

		String userId = JwtAuthenticationFilter.getUserId(request);
		if (userId == null) {
			Map<String, Object> errorResponse = new HashMap<>();
			errorResponse.put("error", "Authentication required");
			return ResponseEntity.status(HttpServletResponse.SC_UNAUTHORIZED)
					.body(errorResponse);
		}

		try {
			// 获取用户权限
			UserPermissionDto userPermission = permissionService.getUserPermissions(userId);
			if (userPermission == null) {
				Map<String, Object> errorResponse = new HashMap<>();
				errorResponse.put("error", "User permissions not found");
				return ResponseEntity.status(HttpServletResponse.SC_FORBIDDEN)
						.body(errorResponse);
			}

			// 生成权限变量
			Map<String, Object> variables = new HashMap<>();
			// 从用户权限中获取基本信息
			variables.put("userId", userPermission.getUserId());
			variables.put("permissions", userPermission.getPermissions());
			logger.debug("Generated permission variables for user {}: {}", userId, variables.keySet());
			Map<String, Object> response = new HashMap<>();
			response.put("variables", variables);
			response.put("userId", userId);
			response.put("timestamp", System.currentTimeMillis());
			return ResponseEntity.ok(response);

		} catch (Exception e) {
			logger.error("Failed to get permission variables for user {}: {}", userId, e.getMessage());
			Map<String, Object> errorResponse = new HashMap<>();
			errorResponse.put("error", "Failed to get permission variables");
			return ResponseEntity.status(500)
					.body(errorResponse);
		}
	}

	/**
	 * 设置仪表盘变量
	 * 为特定仪表盘设置基于用户权限的变量
	 */
	@PostMapping("/api/dashboards/{dashboardId}/variables")
	public ResponseEntity<Map<String, Object>> setDashboardVariables(
			@PathVariable String dashboardId,
			HttpServletRequest request) {

		String userId = JwtAuthenticationFilter.getUserId(request);
		if (userId == null) {
			Map<String, Object> errorResponse = new HashMap<>();
			errorResponse.put("error", "Authentication required");
			return ResponseEntity.status(HttpServletResponse.SC_UNAUTHORIZED)
					.body(errorResponse);
		}

		try {
			// 获取用户权限
			UserPermissionDto userPermission = permissionService.getUserPermissions(userId);
			if (userPermission == null) {
				Map<String, Object> errorResponse = new HashMap<>();
				errorResponse.put("error", "User permissions not found");
				return ResponseEntity.status(HttpServletResponse.SC_FORBIDDEN)
						.body(errorResponse);
			}

			// 检查仪表盘访问权限
			if (!userPermission.canAccessDashboard(dashboardId)) {
				Map<String, Object> errorResponse = new HashMap<>();
				errorResponse.put("error", "Dashboard access denied");
				return ResponseEntity.status(HttpServletResponse.SC_FORBIDDEN)
						.body(errorResponse);
			}

			// 设置仪表盘变量
			boolean success = grafanaVariableService.setDashboardVariables(dashboardId, userPermission);

			if (success) {
				logger.info("Successfully set variables for dashboard {} for user {}", dashboardId, userId);

				Map<String, Object> response = new HashMap<>();
				response.put("message", "Dashboard variables set successfully");
				response.put("dashboardId", dashboardId);
				response.put("userId", userId);
				response.put("timestamp", System.currentTimeMillis());
				return ResponseEntity.ok(response);
			} else {
				logger.warn("Failed to set variables for dashboard {} for user {}", dashboardId, userId);
				Map<String, Object> errorResponse = new HashMap<>();
				errorResponse.put("error", "Failed to set dashboard variables");
				return ResponseEntity.status(500)
						.body(errorResponse);
			}

		} catch (Exception e) {
			logger.error("Failed to set dashboard variables for dashboard {} and user {}: {}",
					dashboardId, userId, e.getMessage());
			Map<String, Object> errorResponse = new HashMap<>();
			errorResponse.put("error", "Failed to set dashboard variables");
			return ResponseEntity.status(500)
					.body(errorResponse);
		}
	}
}