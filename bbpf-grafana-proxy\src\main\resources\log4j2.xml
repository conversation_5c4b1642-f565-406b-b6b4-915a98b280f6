<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="INFO" name="BBPFGrafanaProxyConfig" packages="org.apache.logging.log4j.test" monitorInterval="10" >
    <Properties>
        <Property name="pattern">%d{yyyy-MM-dd HH:mm:ss SSS} [%-5level] [${MODULE_NAME}] [%-40t] [%-36X{userId}] [%-40.40c{39}] [%-40M] [%-5L] --- %m%n</Property>
        <property name="MODULE_NAME">bbpf-grafana-proxy</property>
        <property name="LOG_HOME">/app/data/snbclogs/bbpf/bbpf-grafana-proxy</property>
    </Properties>

    <Appenders>
        <Console name="STDOUT">
            <PatternLayout>
                <pattern>${pattern}</pattern>
            </PatternLayout>
         </Console>
        <RollingFile name="RF" fileName="${LOG_HOME}/${MODULE_NAME}.log"
                     filePattern="${LOG_HOME}/%d{yyyy-MM}/${MODULE_NAME}_%d{yyyy-MM-dd-HH}_%i.log.gz">
            <PatternLayout
                    pattern="${pattern}"/>
            <Policies>
                <TimeBasedTriggeringPolicy modulate="true"
                                           interval="1" />
                <SizeBasedTriggeringPolicy size="100MB"/>
                <CronTriggeringPolicy schedule="0 0 * * * ? *"/> <!-- 这里是每小时监测一次 -->
            </Policies>
            <DefaultRolloverStrategy max="24">
                <Delete basePath="${LOG_HOME}" maxDepth="3">
                    <IfFileName glob="*/${MODULE_NAME}-*.log.gz"/>
                    <IfAny>
                        <IfAccumulatedFileSize exceeds="2GB" /><!-- 文件总大小超过2G -->
                        <IfAccumulatedFileCount exceeds="5000" /><!-- 总文件超过5000个 -->
                    </IfAny>
                    <IfLastModified age="P30D"/><!-- 这里保留30天 -->
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>
    </Appenders>
    
    <Loggers>
        <!-- BBPF Grafana Proxy 核心日志 -->
        <Logger name="com.snbc.bbpf.grafana.proxy" level="debug" additivity="false">
            <AppenderRef ref="RF"/>
            <AppenderRef ref="STDOUT"/>
        </Logger>
        
        <!-- BBPF 通用日志 -->
        <Logger name="com.snbc.bbpf" level="info" additivity="false">
            <AppenderRef ref="RF"/>
            <AppenderRef ref="STDOUT"/>
        </Logger>
        
        <!-- HTTP 客户端日志 -->
        <Logger name="org.apache.http" level="warn" additivity="false">
            <AppenderRef ref="RF"/>
            <AppenderRef ref="STDOUT"/>
        </Logger>
        
        <!-- Redis 连接日志 -->
        <Logger name="io.lettuce.core.protocol" level="warn" additivity="false">
            <AppenderRef ref="RF"/>
            <AppenderRef ref="STDOUT"/>
        </Logger>
        
        <!-- Spring Security 日志 -->
        <Logger name="org.springframework.security" level="warn" additivity="false">
            <AppenderRef ref="RF"/>
            <AppenderRef ref="STDOUT"/>
        </Logger>
        
        <!-- Spring Web 日志 -->
        <Logger name="org.springframework.web" level="warn" additivity="false">
            <AppenderRef ref="RF"/>
            <AppenderRef ref="STDOUT"/>
        </Logger>
        
        <!-- Spring 框架日志 -->
        <Logger name="org.springframework" level="WARN" additivity="false" includeLocation="false">
            <AppenderRef ref="RF"/>
            <AppenderRef ref="STDOUT"/>
        </Logger>
        
        <!-- 连接池日志 -->
        <Logger name="com.zaxxer.hikari" level="warn" additivity="false">
            <AppenderRef ref="RF"/>
            <AppenderRef ref="STDOUT"/>
        </Logger>
        
        <!-- Root Logger -->
        <Root level="info">
            <AppenderRef ref="STDOUT"/>
            <AppenderRef ref="RF"/>
        </Root>
    </Loggers>

</Configuration>