package com.snbc.bbpf.bus.system.strategy.impl;

import com.snbc.bbpf.bus.system.constans.CommonConstant;
import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.handler.UserLoginFailHandler;
import com.snbc.bbpf.bus.system.strategy.LoginStrategy;
import com.snbc.bbpf.bus.system.utils.BossDES3Util;
import com.snbc.bbpf.bus.system.utils.SendMsgUtil;
import com.snbc.bbpf.commons.crypt.Base64Utils;
import com.snbc.bbpf.commons.crypt.DesUtils;
import com.snbc.bbpf.commons.crypt.Padding;
import com.snbc.bbpf.commons.crypt.RSAUtils;
import com.snbc.bbpf.component.captcha.service.PwdErrorCheckService;
import com.snbc.bbpf.system.db.common.entity.User;
import com.snbc.bbpf.system.db.common.entity.login.LoginUser;
import com.snbc.bbpf.system.db.common.mapper.UserLoginMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * @ClassName: SmsLoginStrategy
 * @Description: 短信登陆
 * @module: SI-bbpf-system
 * @Author: wangsong
 * @date: 2023/5/17
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Component
public class SmsLoginStrategy implements LoginStrategy {
    /**
     * 私钥
     */
    @Value("${bbpf.system.RSA.privateKey}")
    private String privateKey;
    /**
     * 支持的登录方式
     */
    @Value("${bbpf.boss.login.type:ldap,sms,account,qrcode}")
    private String loginType;
    @Autowired
    private UserLoginMapper userLoginMapper;
    @Autowired
    private PwdErrorCheckService pwdErrorCheckService;

    /***
     * @Description: 验证手机验证码
     * @Author: WangSong
     * @param :         loginUser
     * @param :         user
     * @return: boolean
     * @CreateDate: 2023/5/17 17:25
     * @UpdateDate: 2023/5/17 17:25
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public boolean verify(LoginUser loginUser, User user) throws Exception {
        // 先进行RSA解密 测试阶段可放开
        String desCipher = RSAUtils.rsaDecrypt(loginUser.getUserPwd(), privateKey);
        if (StringUtils.isBlank(desCipher) || !loginType.contains(CommonConstant.SMS_LOGIN_TYPE)) {
            throw new BusinessException(ErrorMessage.USERNAME_OR_PASSWORD_ERROR.getMessage(), ErrorMessage.USERNAME_OR_PASSWORD_ERROR.getCode());
        }
        //手机号加密
        String encryptPhone = BossDES3Util.encrypt(loginUser.getUserName());
        User dbUser = userLoginMapper.selectUserByPhone(encryptPhone);
        // 验证用户不能为空，为空抛异常
        ErrorMessage.PHONE_UNREGISTERED.assertNotNull(dbUser);

        UserLoginFailHandler.queryUserLoginStatus(dbUser);
        // 进行3DES解密
        String smsCode = new String(DesUtils.decryptCbc(CommonConstant.KEY.getBytes(),
                CommonConstant.DES_KEY_IV.getBytes(), Base64Utils.decode(desCipher), Padding.PKCS5PADDING), StandardCharsets.UTF_8);
        // 校验短信验证码
        checkSmsCode(dbUser.getUserId(), smsCode, user);
        return true;
    }

    /**
     * 解密短信验证码并校验
     *
     * @param userId  用户ID
     * @param smsCode 短信验证码
     */
    private void checkSmsCode(String userId, String smsCode, User user) {
        String smsCodeKey = String.format(CommonConstant.SMS_LOGIN, userId);
        //校验短信验证码
        try {
            SendMsgUtil.checkSmsCode(smsCodeKey, smsCode, true);
        } catch (BusinessException e) {
            //登陆失败后 计算失败次数
            pwdErrorCheckService.dealPwdError(user.getUserName());
            UserLoginFailHandler.modifyLoginFailCount(user, 0);
            //抛出验证码错误异常
            throw e;
        }
    }
}
