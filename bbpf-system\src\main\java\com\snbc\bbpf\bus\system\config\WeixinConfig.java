package com.snbc.bbpf.bus.system.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 微信配置类
 */
@Component
@Getter
public class WeixinConfig {

    /**测试号ID*/
    @Value("${wx.appid}")
    private String appid;
    /**测试号密钥*/
    @Value("${wx.appsecret}")
    private String appsecret;
    /**微信登录回调地址*/
    @Value("${wx.redirectUrl}")
    private String redirectUrl;
    /**用户授权地址*/
    @Value("${wx.userAuthUrl}")
    private String userAuthUrl;
    /**通过code换取网页授权access_token地址*/
    @Value("${wx.getAccessTokenUrl}")
    private String getAccessTokenUrl;
    /**拉取用户信息地址*/
    @Value("${wx.getUserInfoUrl}")
    private String getUserInfoUrl;

    /**授权成功的回调地址*/
    @Value("${wx.getSuccesUrl}")
    private String getSuccesUrl;

    /**授权失败的回调地址*/
    @Value("${wx.getErrorUrl}")
    private String getErrorUrl;
}
