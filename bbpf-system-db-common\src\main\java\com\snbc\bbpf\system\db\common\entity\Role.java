/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
/**
 * @ClassName: Role
 * 角色实体类
 * @module: si-bbpf-system
 * @Author: wjc1 增加的注释
 * @date:   2021/5/25 16:56
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Role {
    private String roleId;

    private String roleName;

    private String roleCode;

    private String roleDesc;

    private String createUserId;

    private String createOrgId;

    private Integer roleType;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

}
