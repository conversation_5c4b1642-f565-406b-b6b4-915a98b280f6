/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.entity;

import com.snbc.bbpf.buslog.annotations.DiffKey;
import com.snbc.bbpf.buslog.annotations.UnionDisplayKey;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * @ClassName: Param
 * @Description: 参数配置实体类
 * @module: si-bbpf-system
 * @Author: wjc1
 * @date: 2021/5/17 14:23:00
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Param {
    private String paramId;

    @NotBlank(message = "配置名称不能为空")
    @Length(max=45, message="配置名称最大长度为45")
    @DiffKey(name = "参数名称",enName = "Param Name")
    @UnionDisplayKey
    private String paramName;

    @NotBlank(message = "配置编码不能为空")
    @Length(max=45, message="配置编码最大长度为45")
    private String paramCode;

    @NotBlank(message = "配置值不能为空")
    @Length(max=200, message="配置值最大长度为200")
    @DiffKey(name = "参数值",enName = "Param Value")
    private String paramValue;

    private String paramTypeCode;

    @NotBlank(message = "配置类型值不能为空")
    @Length(max=45, message="配置类型名称最大长度为45")
    @DiffKey(name = "参数类型值",enName = "paramTypeName")
    private String paramTypeName;

    @DiffKey(name = "参数描述",enName = "Param Desc")
    private String paramDesc;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 创建组织机构id
     * */
    private String createOrgId;
    /**
     * 创建用户id
     * */
    private String createUserId;
}
