/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service;

import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.system.db.common.dto.AdjustDepartmentDto;
import com.snbc.bbpf.system.db.common.vo.OrgUserPageVo;

/**
 * @ClassName: UserService
 * @Description: 用户业务处理
 * @module: si-bbpf-system
 * @Author: wangsong
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
public interface UserOptService {
    /**
     * 调整部门
     */
    void adjustDepartment(AdjustDepartmentDto adjustDepartmentDto);
    /**
     * 查到用户列表
     */
    PageInfo<OrgUserPageVo> queryUserListPage(String orgId, Integer pageNum, Integer pageSize, String queryParam);

}
