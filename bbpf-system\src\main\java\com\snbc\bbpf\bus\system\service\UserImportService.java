/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service;

import com.snbc.bbpf.system.db.common.dto.UserDto;

import java.util.Map;

/**
 * @ClassName: UserImportService
 * @Description: 用户类过复杂将用户导入和新增业务处理
 * @module: si-bbpf-system
 * @Author: wangsong
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
public interface UserImportService {
    /**
     * @Description: 添加/更新用户
     * @Author:  wjc1
     * @param    userDto 用户信息
     * @CreateDate: 2021/5/19 11:10
     * @UpdateDate: 2021/5/19 11:10
     */
    String saveUser(UserDto userDto);
    /**
     * @Description: 添加/更新用户
     * @Author:  wjc1
     * @param    userDto 用户信息
     * @CreateDate: 2021/5/19 11:10
     * @UpdateDate: 2021/5/19 11:10
     */
    void updateUser(UserDto userDto);

    Map<String,String> initAllOrgLst();
}
