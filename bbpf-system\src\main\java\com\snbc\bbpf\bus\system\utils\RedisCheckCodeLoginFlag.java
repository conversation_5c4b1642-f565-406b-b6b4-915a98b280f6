/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.utils;


import com.snbc.bbpf.bus.system.constans.CommonConstant;
import com.snbc.bbpf.system.db.common.entity.User;
import com.snbc.bbpf.system.db.common.entity.login.RedisUserStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020-05-21 17:38
 */
@Service
@Slf4j
public class RedisCheckCodeLoginFlag {

    @Autowired
    private RedisTemplate<String,String> redisTemplate;

    @Value("${bbpf.system.redis.cache.ttl}")
    private int cacheTtl;
    @Value("${bbpf.system.sms.outTime:300}")
    private int smsoutTime;
    private ObjectMapper objectMapper = new ObjectMapper();
    /**
     * 根据key删除redis
     * 目前用于在了禁用用户删除用户登录信息
     * @param hKey key值
     * */
    public void delKey(String hashKey, String hKey){
        if(redisTemplate.opsForHash().hasKey(hashKey,hKey)) {
            redisTemplate.opsForHash().delete(hashKey , hKey);
        }
    }

    public String pushCode(String result,String sign){
        String roudom = UUID.randomUUID().toString();
        String randomSign = roudom+sign;
        redisTemplate.opsForValue().set(randomSign,result,cacheTtl, TimeUnit.SECONDS);
        return randomSign;

    }

    public boolean hasCode(String checkResult){
        return redisTemplate.hasKey(checkResult);
    }

    public boolean checkCode(String checkCode,String checkResult) {
        if(StringUtils.isNotBlank(checkCode)&& StringUtils.isNotBlank(checkResult)){
            String codeString = redisTemplate.opsForValue().get(checkResult);
            if (StringUtils.isNotBlank(codeString)) {
                redisTemplate.delete(checkResult);
                return checkCode.equals(codeString);
            }
        }
        return false;
    }

    /**
     * 用户登录状态保存
     * @param user 用户信息
     * @param ip 用户IP
     * @param sysType 系统类型
     * @param token 用户token
     * @param sessionId 会话ID，从网关服务传递过来
     * @throws IOException
     */
    public void userLoginStatus(User user, String ip, String sysType, String token, String sessionId) throws IOException {
        //redis中保存登录状态
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        Map<String, String> userStatusMap = new HashMap<>();
        String userId = user.getUserId();
        // 使用sessionId作为缓存key的一部分
        String redisKey = sysType + CommonConstant.JOINER + userId;
        if (StringUtils.isNotBlank(sessionId)) {
            redisKey = redisKey + CommonConstant.JOINER + sessionId;
        }
        userStatusMap.put(redisKey,
                objectMapper.writeValueAsString(RedisUserStatus.builder().userStatus(CommonConstant.ONE.toString())
                        .optTime(dateTimeFormatter.format(LocalDateTime.now())).sysType(sysType)
                        .phone(user.getPhone()).ip(ip).userId(userId)
                        .loginTime(dateTimeFormatter.format(LocalDateTime.now()))
                        .userName(user.getUserName()).token(token).sessionId(sessionId).build()));
        //插入REDIS 记录当前用户登录状态
        redisTemplate.opsForHash().putAll(CommonConstant.USER_LOGIN_STATUS, userStatusMap);
    }
    
    public Boolean getUserStatus(String sysType, String userId) {
        //模糊查询用户状态信息
        try (Cursor<Map.Entry<Object, Object>> allUserStatus = redisTemplate.opsForHash().scan(
                CommonConstant.USER_LOGIN_STATUS,
                ScanOptions.scanOptions().match("*" + userId+"*").build())) {
            while (allUserStatus.hasNext()) {
                RedisUserStatus redisUserStatus = objectMapper.readValue((String) allUserStatus.next().getValue(),
                        RedisUserStatus.class);
                if (redisUserStatus.getUserStatus().equals(CommonConstant.ONE.toString()) &&
                        !redisUserStatus.getSysType().equals(sysType)) {
                    return true;
                }
            }
        } catch (IOException e) {
            log.warn("redis user status fail sysType={},userId={}",sysType,userId,e);
        }
        return false;
    }

    /***
     * @Description:    redis中更新用户登出时间状态
     * @Author:         liangJb
     * @param :         hashKey
     * @param :         hKey
     * @CreateDate:     2020/6/15 17:01
     * @UpdateUser:     liangjb
     * @UpdateDate:     2021/5/27 17:01
     * @return :        void
     */
    public Boolean userLogOut(String hashKey, String hKey) {
        //修改缓存中用户登出时间
        //先取出缓存内容
        String userStatus = String.valueOf(redisTemplate.opsForHash().get(hashKey, hKey));
        if(null == userStatus||"null".equals(userStatus)  ){
            return false;
        }
        try {
            redisTemplate.opsForHash().delete(hashKey,hKey);
        } catch (Exception e) {
            log.warn("redis user status fail hashKey={},hKey={}",hashKey,hKey,e);
        }
        return true;
    }
    /***
     * @Description: 根据系统类型、用户ID和租户ID删除模糊匹配的缓存
     * @Author: liangjunbin
     * @param : hashKey
     * @param : sysType
     * @param : userId
     * @CreateDate: 2024/5/30 10:30
     * @return : Boolean
     *         Copyright© Shandong New Beiyang Information Technology Co., Ltd. .
     *         all rights reserved.
     */
    public Boolean deleteUserCache(String hashKey, String sysType, String userId) {
        // 模糊查询用户状态信息
        try (Cursor<Map.Entry<Object, Object>> allUserStatus = redisTemplate.opsForHash().scan(
                hashKey,
                ScanOptions.scanOptions().match("*" + userId+"*").build())) {
            while (allUserStatus.hasNext()) {
                Map.Entry<Object, Object> entry = allUserStatus.next();
                String key = entry.getKey().toString();
                try {
                    RedisUserStatus redisUserStatus = objectMapper.readValue(entry.getValue().toString(),
                            RedisUserStatus.class);
                    if (sysType.equals(redisUserStatus.getSysType())) {
                        redisUserStatus.setSessionId("");
                        redisUserStatus.setOptTime(
                                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(LocalDateTime.now()));
                        redisTemplate.opsForHash().put(hashKey,
                                key,
                                objectMapper.writeValueAsString(redisUserStatus));
                        log.info("update user cache. hashKey={}, key={}, sysType={}, userId={}",
                                hashKey, key, sysType, userId);
                    }
                } catch (IOException e) {
                    log.warn(
                            "Failed to process user cache during deletion. hashKey={}, key={}, sysType={}, userId={}",
                            hashKey, key, sysType, userId, e);
                }
            }
            return true;
        } catch (Exception e) {
            log.warn("Failed to delete user cache. hashKey={}, sysType={}, userId={}",
                    hashKey, sysType, userId, e);
            return false;
        }
    }


    /***
     * @Description: 写入缓存数据
     * @Author: liangjunbin
     * @CreateDate: 2021/6/17 16:30
     * @UpdateDate: 2021/6/17 16:30
     * @return : boolean
     *         Copyright© Shandong New Beiyang Information Technology Co., Ltd. .
     *         all rights reserved.
     */
    public void insertRedis(String redisKey, String redisValue) {
        redisTemplate.opsForValue().set(redisKey, redisValue, smsoutTime, TimeUnit.SECONDS);
    }
    /***
     * @Description: 读取缓存数据
     * @Author: liangjunbin
     * @CreateDate: 2021/6/17 16:30
     * @UpdateDate: 2021/6/17 16:30
     * @return : boolean
     *         Copyright© Shandong New Beiyang Information Technology Co., Ltd. .
     *         all rights reserved.
     */
    public String getRedis(String redisKey) {
        return (redisTemplate.hasKey(redisKey))?redisTemplate.opsForValue().get(redisKey):"";
    }
    /***
     * @Description: 读取缓存数据
     * @Author: liangjunbin
     * @CreateDate: 2021/6/17 16:30
     * @UpdateDate: 2021/6/17 16:30
     * @return : boolean
     *         Copyright© Shandong New Beiyang Information Technology Co., Ltd. .
     *         all rights reserved.
     */
    public void delRedis(String redisKey) {
        redisTemplate.delete(redisKey);
    }
    
}
