/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.controller;


import com.alibaba.excel.EasyExcelFactory;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.bus.system.service.DictService;
import com.snbc.bbpf.bus.system.utils.ResultUtil;
import com.snbc.bbpf.buslog.annotations.Buslog;
import com.snbc.bbpf.buslog.annotations.OperationType;
import com.snbc.bbpf.system.db.common.dto.DictValueDto;
import com.snbc.bbpf.system.db.common.entity.DictType;
import com.snbc.bbpf.system.db.common.vo.DictTypeVo;
import com.snbc.bbpf.system.db.common.vo.DictValueVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: DictTypeController
 * @Description: 字典管理
 * @module: si-bbpf-system
 * @Author: liuyi
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Slf4j
@RestController
@RequestMapping("/console/dict")
public class DictTypeController {

    @Autowired
    private DictService dictService;


    /**
     * @param dictType 字典类型json体
     * @description: 修改字典类型
     * @return: com.snbc.bbpf.component.config.CommonResp
     * @author: liuyi
     * @time: 2021/5/19 16:41
     */
    @Buslog(oprationType = OperationType.MODIFY,opration = "modifyDictType", target = "dictManage",type = DictType.class )
    @PostMapping(value = "/updateDictType")
    public CommonResp<String> updateDictType(@RequestBody @Valid DictType dictType) {
        CommonResp<String> commonResp = new CommonResp<>();
        dictService.updateDictType(dictType, commonResp);
        return commonResp;
    }


    /**
     * @param pageNum      第几页
     * @param dictTypeName 字典类型名称
     * @param pageSize     每页大小
     * @description:
     * @return: com.snbc.bbpf.component.config.CommonResp
     * @author: liuyi
     * @time: 2021/5/19 16:41
     */
    @GetMapping(value = "/getDictTypeList")
    public CommonResp<PageInfo<DictType>> getDictTypeList(@Valid @RequestParam(value = "pageNum",defaultValue = "1") String pageNum, String dictTypeName,
                                                          @Valid @RequestParam(value = "pageSize",defaultValue = "10") String pageSize) {
        CommonResp<PageInfo<DictType>> commonResp = new CommonResp<>();
        try {
            PageMethod.startPage(Integer.valueOf(pageNum), Integer.valueOf(pageSize));
            List<DictType> dictTypeList = dictService.getDictTypeByName(dictTypeName);
            PageInfo<DictType> page = new PageInfo<>(dictTypeList);
            commonResp.setHead(ResultUtil.success());
            commonResp.setBody(page);
        } catch (Exception e) {
            commonResp.setHead(ResultUtil.error(ErrorMessage.FAILED.getCode(), ErrorMessage.FAILED.getMessage()));
            log.error(e.getMessage(), e);
        }
        return commonResp;
    }


    /**
     * @param dictTypeVo 字典类型json体
     * @description:
     * @return: com.snbc.bbpf.component.config.CommonResp
     * @author: liuyi
     * @time: 2021/5/19 16:43
     */
    @Buslog(opration = "addDictType", zh = "新增字典类型【${typeName}】",en = "New dictionary type [${typeName}]",target = "dictManage")
    @PostMapping(value = "/addDictType")
    public CommonResp<String> addDictType(@RequestBody @Valid DictTypeVo dictTypeVo) {
        CommonResp<String> commonResp = new CommonResp<>();
        dictService.addDictType(dictTypeVo, commonResp);
        return commonResp;
    }

    /**
     * @param dictValueVo 字典值json体
     * @description:
     * @return: com.snbc.bbpf.component.config.CommonResp
     * @author: liuyi
     * @time: 2021/5/19 16:43
     */
    @Buslog(opration = "addDictValue", zh = "在字典类型【${typeName}】中新增字典值【${valueName}】",
            en = "Newly add dictionary value [${typeName}] in dictionary type [${valueName}]", target = "dictManage")
    @PostMapping(value = "/addDictValue")
    public CommonResp<String> addDictValue(@RequestBody @Valid DictValueVo dictValueVo) {

        CommonResp<String> commonResp = new CommonResp<>();

        dictService.addDictValue(dictValueVo, commonResp);
        return commonResp;
    }

    /**
     * @param dictValueVo 字典值json体
     * @description:
     * @return: com.snbc.bbpf.component.config.CommonResp
     * @author: liuyi
     * @time: 2021/5/19 16:44
     */
    @Buslog(oprationType = OperationType.MODIFY,opration = "modifyDictValue", target = "dictManage",type =DictValueVo.class )
    @PostMapping(value = "/updateDictValue")
    public CommonResp<String> updateDictValue(@RequestBody @Valid DictValueVo dictValueVo) {
        CommonResp<String> commonResp = new CommonResp<>();
        dictService.updateDictValue(dictValueVo, commonResp);
        return commonResp;
    }

    /**
     * @param typeCode 字典类型编码
     * @description:
     * @return: com.snbc.bbpf.component.config.CommonResp
     * @author: liuyi
     * @time: 2021/5/19 16:44
     */
    @DeleteMapping(value = "/delDictType")
    @Buslog(opration = "delDictType", zh = "删除字典类型【${typeName}】", en = "Deleting dictionary type [${typeName}]", target = "dictManage")
    public CommonResp<String> delDictType(@Valid @RequestParam("typeCode")  String typeCode) {
        CommonResp<String> commonResp = new CommonResp<>();
        // 如果参数为空 则返回
        if (StringUtils.isEmpty(typeCode)) {
            commonResp.setHead(ResultUtil.error("typeCode 为空"));
            return commonResp;
        }
        dictService.delDictType(typeCode, commonResp);
        return commonResp;
    }

    /**
     * @param valueId 字典值主键
     * @description:
     * @return: com.snbc.bbpf.component.config.CommonResp
     * @author: liuyi
     * @time: 2021/5/19 16:44
     */
    @DeleteMapping(value = "/delDictValue")
    @Buslog(opration = "delDictValue", zh = "在字典类型【${typeName}】中删除字典值【${valueName}】",
            en = "Delete the dictionary value [${typeName}] in dictionary type [${valueName}]", target = "dictManage")
    public CommonResp<String> delDictValue(@Valid @RequestParam("valueId") String valueId) {
        CommonResp<String> commonResp = new CommonResp<>();
        // 如果参数为空 则返回
        if (StringUtils.isBlank(valueId)) {
            commonResp.setHead(ResultUtil.error("valueId 为空"));
            return commonResp;
        }
        dictService.delDictValue(valueId, commonResp);
        return commonResp;
    }


    /**
     * @param typeCode 字典类型编号
     * @description:
     * @return: com.snbc.bbpf.component.config.CommonResp
     * @author: liuyi
     * @time: 2021/5/19 16:45
     */
    @GetMapping(value = "/getDictValueList")
    public CommonResp<List<DictValueVo>> getDictValueList(@Valid @RequestParam("typeCode") String typeCode,
                                                          @RequestParam(value = "parentId",required = false)String parentId) {
        CommonResp<List<DictValueVo>> commonResp = new CommonResp<>();
        try {
            List<DictValueVo> dictValueVoList = dictService.getDictValueByTypeCodeParentId(typeCode,parentId);
            commonResp.setHead(ResultUtil.success());
            commonResp.setBody(dictValueVoList);
        } catch (Exception e) {
            commonResp.setHead(ResultUtil.error(ErrorMessage.FAILED.getCode(), ErrorMessage.FAILED.getMessage()));
            log.error(e.getMessage(), e);
        }
        return commonResp;
    }

    /**
     * @param typeCode      字典类型编码
     * @param typeValueName 字典值名称
     * @description:
     * @return: com.snbc.bbpf.component.config.CommonResp
     * @author: liuyi
     * @time: 2021/5/19 16:45
     */
    @GetMapping(value = "/getAllDictValue")
    public CommonResp<List<DictValueVo>> getAllDictValue(@RequestParam("typeCode") String typeCode,
                                                             @Valid @RequestParam("typeValueName") String typeValueName) {
        CommonResp<List<DictValueVo>> commonResp = new CommonResp<>();
        try {
            commonResp.setHead(ResultUtil.success());
            commonResp.setBody(dictService.getByTypeCodeAndValueName(typeCode, typeValueName));
        } catch (Exception e) {
            commonResp.setHead(ResultUtil.error(ErrorMessage.FAILED.getCode(), ErrorMessage.FAILED.getMessage()));
            log.error(e.getMessage(), e);
        }
        return commonResp;
    }


    /**
     * @param dictTypeName 字典类型名称
     * @param response     http servlet response
     * @description:
     * @return: com.snbc.bbpf.component.config.CommonResp
     * @author: liuyi
     * @time: 2021/5/19 16:46
     */
    @Buslog(opration = "Export DictType", zh = "导出了字典类型", en = "The dictionary type is exported", target = "dictManage")
    @GetMapping(value = "exportAllDict")
    public CommonResp<String> exportAllDict(String dictTypeName, HttpServletResponse response) {
        List<List<String>> list = new ArrayList<>();
        List<String> head0 = new ArrayList<>();
        head0.add("字典名称");
        List<String> head1 = new ArrayList<>();
        head1.add("字典编码");
        list.add(head0);
        list.add(head1);
        List<DictType> listData = dictService.quertAllDictTypeCode();
        List<List<String>> excelData = new ArrayList<>();
        for (DictType listDatum : listData) {
            List<String> data = new ArrayList<>();
            data.add(listDatum.getTypeName());
            data.add(listDatum.getTypeCode());
            excelData.add(data);
        }
        response.setContentType("application/force-download");
        response.addHeader("Content-Disposition", "attachment;fileName=dictType.xls");
        try {
            EasyExcelFactory.write(response.getOutputStream()).head(list).sheet("字典类型").doWrite(excelData);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        CommonResp<String> commonResp = new CommonResp<>();
        commonResp.setHead(ResultUtil.success());
        return commonResp;
    }

    /**
     * 根据DictTypeCode获取dictValueList
     *
     * @param dictTypeCodes
     * @return
     */
    @RequestMapping(value = "/getMultipleDictValue", method = RequestMethod.GET)
    public CommonResp multipleDictValues(@RequestParam("dictTypeCodes") List<String> dictTypeCodes) {
        Map<String, List<DictValueDto>> multipleDictValues = dictService.getMultipleDictValues(dictTypeCodes);
        return CommonResp.builder().
                body(multipleDictValues)
                .head(ResultUtil.success())
                .build();
    }

}
