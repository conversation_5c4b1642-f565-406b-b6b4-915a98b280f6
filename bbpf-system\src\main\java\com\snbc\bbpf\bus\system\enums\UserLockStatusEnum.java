/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.enums;

/**
 * @ClassName: UserStatusEnum
 * 用户状态：启动，禁用
 * @module: si-bbpf-system
 * @Author: wjc1
 * @date: 2021/5/20 13:32
 */
public enum UserLockStatusEnum {
    NOTLOCK(1, "启用"), TLOCK(0, "禁用");
    private Integer status;
    private String statusName;

    UserLockStatusEnum(Integer status, String statusName) {
        this.status = status;
        this.statusName = statusName;
    }

    /**
     * @return java.lang.String
     * <AUTHOR>
     * @Description 通过状态获取名称
     * @Date 16:54 2021/5/20
     * @Param [status]
     **/
    public static String getStatusName(Integer status) {
        for (UserLockStatusEnum value : UserLockStatusEnum.values()) {
            if (status != null && status.equals(value.status)) {
                return value.statusName;
            }
        }
        return null;
    }
    /**
     * @return java.lang.String
     * <AUTHOR>
     * @Description 通过名称取状态
     * @Date 16:54 2021/5/20
     * @Param [status]
     **/
    public static Integer getStatus(String statusName) {
        for (UserLockStatusEnum value : UserLockStatusEnum.values()) {
            if (statusName != null && statusName.equals(value.statusName)) {
                return value.status;
            }
        }
        return null;
    }
    public Integer getStatus() {
        return status;
    }

    public String getStatusName() {
        return statusName;
    }
}
