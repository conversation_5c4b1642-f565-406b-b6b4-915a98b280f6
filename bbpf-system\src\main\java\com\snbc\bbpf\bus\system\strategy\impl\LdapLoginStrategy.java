package com.snbc.bbpf.bus.system.strategy.impl;

import com.snbc.bbpf.bus.system.constans.CommonConstant;
import com.snbc.bbpf.bus.system.service.LdapService;
import com.snbc.bbpf.bus.system.strategy.LoginStrategy;
import com.snbc.bbpf.commons.crypt.Base64Utils;
import com.snbc.bbpf.commons.crypt.DesUtils;
import com.snbc.bbpf.commons.crypt.Padding;
import com.snbc.bbpf.commons.crypt.RSAUtils;
import com.snbc.bbpf.system.db.common.entity.User;
import com.snbc.bbpf.system.db.common.entity.login.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @ClassName: LdapLoginStrategy
 * @Description: ldap登录策略实现
 * @module: SI-bbpf-system
 * @Author: wangsong
 * @date: 2023/5/17
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Slf4j
@Component
public class LdapLoginStrategy implements LoginStrategy {

    /**
     * 私钥
     */
    @Value("${bbpf.system.RSA.privateKey}")
    private String privateKey;

    @Autowired
    private LdapService ldapService;

    private String type;
    private String domain;

    @Override
    public boolean verify(LoginUser loginUser, User user) {
        String userName = loginUser.getUserName();
        if ("open".equals(type)) {
            String userDn = String.format("cn=%s,%s", loginUser.getUserName(), user.getUserDn());
            return checkLdapUserPwd(userDn, loginUser.getUserPwd());
        } else {
            return checkLdapUserPwd(userName + "@" + domain, loginUser.getUserPwd());
        }
    }

    /***
     * @Description:    验证Ldap用户密码是否正确
     * @Author:         yangweipeng
     * @param :         userName 用户名
     * @param :         userPwd   用户密码
     * @CreateDate:     2023/5/4 10:30
     * @return :        登录是否成功
     */
    private Boolean checkLdapUserPwd(String userName,String userPwd) {
        Boolean result=false;
        try {
            // 先进行RSA解密 测试阶段可放开
            String desPwd = RSAUtils.rsaDecrypt(userPwd, privateKey);
            // 进行3DES解密
            if (StringUtils.isNotBlank(desPwd)) {
                byte[] bytes = DesUtils.decryptCbc(CommonConstant.KEY.getBytes(), CommonConstant.DES_KEY_IV.getBytes(),
                        Base64Utils.decode(desPwd), Padding.PKCS5PADDING);
                //将密码进行加盐解密
                String password=new String(bytes, CommonConstant.CHARSET_UTF8);
                return ldapService.adLogin(userName,password);
            }
        }catch(Exception ex){
            log.error("The password verification is abnormal. Procedure：",ex);
            result=false;
        }
        return result;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }
}
