/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.vo;

import com.snbc.bbpf.buslog.annotations.DiffKey;
import com.snbc.bbpf.buslog.annotations.UnionDisplayKey;
import com.snbc.bbpf.buslog.annotations.convert.DictFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * @ClassName: BasePermissonVo
 * 权限类基础信息
 * @module: bbpf-bus-system
 * @Author: yangweipeng
 * @date: 2021/5/21
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BasePermissonVo  {
    /**
     * 权限id
     */
    private String permissionId;
    @NotBlank(message = "权限类型不能为空")

    private String permissionType;

    @NotBlank(message = "权限名称不能为空")
    @DiffKey(name = "权限名称",enName = "Permission Name")
    @UnionDisplayKey
    private String permissionName;

    @NotBlank(message = "权限编码不可以为空")
    @DiffKey(name = "权限编码",enName = "Permission Code")
    private String permissionCode;

    @NotBlank(message = "父节点id不可以为空")
    private String parentId;

    private Integer orderBy;

    private String permissionIcon;
    @DiffKey(name = "是否启用",enName = "Enabled")
    @DictFormat(dictJson = "[{'enName':'enabled','zhName':'启用','value':'1'}," +
            "{'enName':'disabled','zhName':'禁用','value':'0'}]")
    private Integer hasEnable;

    @DiffKey(name = "路由地址",enName = "Routing Url")
    private String routingUrl;

}
