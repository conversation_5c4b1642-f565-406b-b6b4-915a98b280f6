package com.snbc.bbpf.bus.system.utils;

import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * @ClassName: IpUtil
 * @Description: 获取ip
 * @module: SI-bbpf-system
 * @Author: wangsong
 * @date: 2022/11/10
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
public class IpUtil {
    private static final String UNKNOWN = "unknown";
    public static final int MAX_LENGTH = 15;

    /**
     * 获取客户端真实IP
     *
     * @return
     */
    public static String getIpAddress() {
        RequestAttributes req = RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = ((ServletRequestAttributes) req).getRequest();
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        // 对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
        if (ip != null && ip.length() > MAX_LENGTH && ip.indexOf(',') > -1) { // "***.***.***.***".length()
            // = 15
            ip = ip.substring(0, ip.indexOf(','));
        }
        //IPV6的情况下本机ip是0:0:0:0:0:0:0:1，做一下转化
        if ("0:0:0:0:0:0:0:1".equals(ip)) {
            ip = "127.0.0.1";
        }
        return ip;
    }
}
