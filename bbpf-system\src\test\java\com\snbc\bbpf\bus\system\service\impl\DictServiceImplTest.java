package com.snbc.bbpf.bus.system.service.impl;

import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.bus.system.utils.ResultUtil;
import com.snbc.bbpf.system.db.common.entity.DictType;
import com.snbc.bbpf.system.db.common.entity.DictValue;
import com.snbc.bbpf.system.db.common.mapper.DictTypeMapper;
import com.snbc.bbpf.system.db.common.mapper.DictValueMapper;
import com.snbc.bbpf.system.db.common.vo.DictTypeVo;
import com.snbc.bbpf.system.db.common.vo.DictValueVo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatcher;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.argThat;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.when;

class DictServiceImplTest {
    @Mock
    DictTypeMapper dictTypeMapper;
    @Mock
    DictValueMapper dictValueMapper;
    @Mock
    Logger log;
    @InjectMocks
    DictServiceImpl dictServiceImpl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("修改字典类型")
    @Tag("@id:23573")
    @Tag("@author:jiafei")
    @Tag("@date:2021/7/2")
    void testUpdateDictType() {
        DictType update = new DictType("123456789","1234","5678");
        DictType updateFail = new DictType("123456789","1234","56789");
        when(dictTypeMapper.selectByPrimaryKey(update.getId())).thenReturn(new DictType("123456789", "123", "456"));
        when(dictTypeMapper.updateByPrimaryKeySelective(update)).thenReturn(1);
        CommonResp<String> commonResp = new CommonResp<>();
        dictServiceImpl.updateDictType(update, commonResp);
        CommonResp<String> success = new CommonResp<>();
        success.setHead(ResultUtil.success());
        Assertions.assertEquals(commonResp,success);
        DictType noExist = new DictType("123","123","123");
        dictServiceImpl.updateDictType(noExist,commonResp);
        success.setHead(ResultUtil.error(ErrorMessage.DICT_CODE_IS_NONE,noExist.getId()));
        Assertions.assertEquals(commonResp,success);
        dictServiceImpl.updateDictType(updateFail,commonResp);
        success.setHead(ResultUtil.error(ErrorMessage.SQL_EXECUTE_FAILED));
        Assertions.assertEquals(commonResp,success);
    }

    @Test
    @DisplayName("新增字典类型")
    @Tag("@id:23569")
    @Tag("@author:jiafei")
    @Tag("@date:2021/7/2")
    void testAddDictType() {
        DictType newObject = new DictType("new123","new123","new123");
        DictType exist = new DictType("exist123","exist123","123");
        DictType inserError = new DictType("new123","new123","new1234");
        when(dictTypeMapper.selectByExample(exist.getTypeCode())).thenReturn(Arrays.<DictType>asList(new DictType("123456789", "123", "typeName")));
        when(dictTypeMapper.insertSelective(argThat((ArgumentMatcher<DictType>) dictType -> dictType.getTypeCode().equals("new123")&&dictType.getTypeName().equals("new123")))).thenReturn(1);
        CommonResp<String> commonResp = new CommonResp<>();
        commonResp.setHead(ResultUtil.success());

        CommonResp<String> success = new CommonResp<>();
        success.setHead(ResultUtil.success());
        dictServiceImpl.addDictType(new DictTypeVo(newObject.getTypeCode(), newObject.getTypeName()), commonResp);
        Assertions.assertEquals(commonResp, success);
        success.setHead(ResultUtil.error(ErrorMessage.DICT_CODE_EXIST,exist.getTypeCode()));
        dictServiceImpl.addDictType(new DictTypeVo(exist.getTypeCode(), exist.getTypeName()), commonResp);
        Assertions.assertEquals(commonResp, success);
        dictServiceImpl.addDictType(new DictTypeVo(inserError.getTypeCode(), inserError.getTypeName()), commonResp);
        success.setHead(ResultUtil.error(ErrorMessage.SQL_EXECUTE_FAILED));
        Assertions.assertEquals(commonResp, success);
    }


    @Test
    @DisplayName("新增字典值")
    @Tag("@id:23570")
    @Tag("@author:jiafei")
    @Tag("@date:2021/7/2")
    void testAddDictValue() {
        DictValueVo newValue = new DictValueVo();
        newValue.setValueCode("123");
        newValue.setValueName("345");
        newValue.setTypeCode("exist");

        DictValueVo noExistType = new DictValueVo();
        noExistType.setValueCode("123");
        noExistType.setValueName("345");
        noExistType.setTypeCode("no_exist");

        DictValueVo exist = new DictValueVo();
        exist.setValueCode("exist");
        exist.setValueName("exist");
        exist.setTypeCode("exist");

        when(dictTypeMapper.selectByExample(eq("exist"))).thenReturn(Arrays.<DictType>asList(new DictType("id", "typeCode", "typeName")));
        when(dictValueMapper.insertSelective(any())).thenReturn(1);
        when(dictValueMapper.selectByExample(eq("exist"), eq("exist"), eq(null))).thenReturn(Arrays.<DictValue>asList(new DictValue("valueId", "typeCode", "valueName", "valueCode", "parentId", "valueDesc")));

        CommonResp<String> commonResp = new CommonResp<>();
        commonResp.setHead(ResultUtil.success());

        CommonResp<String> success = new CommonResp<>();
        success.setHead(ResultUtil.success());
        dictServiceImpl.addDictValue(newValue, commonResp);
        Assertions.assertEquals(commonResp, success);
        dictServiceImpl.addDictValue(noExistType, commonResp);
        success.setHead(ResultUtil.error(ErrorMessage.DICT_CODE_IS_NONE,noExistType.getTypeCode()));
        Assertions.assertEquals(commonResp, success);
        //dictServiceImpl.addDictValue(exist, commonResp);
        //success.setHead(ResultUtil.error(ErrorMessage.DICT_VALUE_EXIST,exist.getValueCode()));
        //Assertions.assertEquals(commonResp, success);
    }

    @Test
    @DisplayName("修改字典值")
    @Tag("@id:23574")
    @Tag("@author:jiafei")
    @Tag("@date:2021/7/2")
    void testUpdateDictValue() {
        DictValueVo noExist = new DictValueVo();
        noExist.setValueCode("123");
        noExist.setValueName("345");
        noExist.setTypeCode("123");

        DictValue exist = new DictValue();
        exist.setValueCode("exist");
        exist.setValueName("exist");
        exist.setTypeCode("exist");

        DictValueVo existVo = new DictValueVo();
        existVo.setValueCode("exist");
        existVo.setValueName("exist");
        existVo.setTypeCode("exist");

        when(dictTypeMapper.selectByExample(eq("exist"))).thenReturn(Arrays.<DictType>asList(new DictType("id", "typeCode", "typeName")));
        when(dictValueMapper.updateByPrimaryKeySelective(argThat(
                exist1 -> exist.getValueCode().equals(exist1.getValueCode())&&exist.getTypeCode().equals(exist1.getTypeCode())
        &&exist.getValueName().equals(exist1.getValueName())))).thenReturn(1);
        when(dictValueMapper.selectByExample(eq(exist.getTypeCode()), eq(exist.getValueCode()), eq(null))).thenReturn(Arrays.<DictValue>asList(new DictValue("valueId", "typeCode", "valueName", "valueCode", "parentId", "valueDesc")));

        CommonResp<String> commonResp = new CommonResp<>();

        CommonResp<String> success = new CommonResp<>();
        success.setHead(ResultUtil.success());
        dictServiceImpl.updateDictValue(existVo, commonResp);
        Assertions.assertEquals(commonResp, success);
        dictServiceImpl.updateDictValue(noExist, commonResp);
        success.setHead(ResultUtil.error(ErrorMessage.DICT_CODE_IS_NONE,noExist.getTypeCode()));
        Assertions.assertEquals(commonResp, success);
    }

    @Test
    @DisplayName("删除字典类型")
    @Tag("@id:23575")
    @Tag("@author:jiafei")
    @Tag("@date:2021/7/2")
    void testDelDictType() {
        String typeCode = "123";
        when(dictTypeMapper.deleteByTypeCode(eq(typeCode))).thenReturn(1);
        when(dictValueMapper.deleteByPrimaryKey(argThat(id -> "123,456,789".contains(id)))).thenReturn(1);
        DictValue dictValue1 = new DictValue("123", "123", "123", "123", "123", "123");
        DictValue dictValue2 = new DictValue("456", "123", "456", "456", "456", "456");
        DictValue dictValue3 = new DictValue("789", "123", "789", "789", "789", "789");
        List<DictValue> list = new ArrayList<>();
        list.add(dictValue1);
        list.add(dictValue2);
        list.add(dictValue3);
        when(dictValueMapper.selectByExample(eq("123"), eq(null), eq(null))).thenReturn(list);
        CommonResp<String> commonResp = new CommonResp<>();

        CommonResp<String> success = new CommonResp<>();
        success.setHead(ResultUtil.success());
        success.setBody("删除1条数据");
        dictServiceImpl.delDictType(typeCode, commonResp);
        Assertions.assertEquals(commonResp, success);
    }

    @Test
    @DisplayName("删除字典值")
    @Tag("@id:23576")
    @Tag("@author:jiafei")
    @Tag("@date:2021/7/2")
    void testDelDictValue() {
        String valueId = "123";
        when(dictValueMapper.deleteByPrimaryKey(eq("123"))).thenReturn(1);
        CommonResp<String> commonResp = new CommonResp<>();

        CommonResp<String> success = new CommonResp<>();
        success.setHead(ResultUtil.success());
        success.setBody("删除1条数据");
        dictServiceImpl.delDictValue(valueId, commonResp);
        Assertions.assertEquals(commonResp, success);
    }

    @Test
    @DisplayName("获取字典值列表")
    @Tag("@id:23572")
    @Tag("@author:jiafei")
    @Tag("@date:2021/7/2")
    void testGetDictValueList() {
        DictValue dictValue1 = new DictValue("123", "123", "123", "123", "abc", "123");
        DictValue dictValue2 = new DictValue("456", "123", "456", "456", "def", "456");
        DictValue dictValue3 = new DictValue("789", "123", "789", "789", "ghi", "789");
        List<DictValue> list = new ArrayList<>();
        list.add(dictValue1);
        list.add(dictValue2);
        list.add(dictValue3);

        DictValue parent1 = new DictValue("abc", "123", "XI,XII,XIII", "XI,XII,XIII", null, "123");
        DictValue parent2 = new DictValue("def", "123", "XIV,XV,XVI", "XIV,XV,XVI", null, "456");
        DictValue parent3 = new DictValue("ghi", "123", "XVII,XVIII,XIX", "XVII,XVIII,XIX", null, "789");

        DictValueVo dictValueVo1 = new DictValueVo();
        dictValueVo1.setValueId(dictValue1.getValueId());
        dictValueVo1.setTypeCode(dictValue1.getTypeCode());
        dictValueVo1.setValueCode(dictValue1.getValueCode());
        dictValueVo1.setValueName(dictValue1.getValueName());
        dictValueVo1.setValueDesc(dictValue1.getValueDesc());
        dictValueVo1.setParentValueCode(parent1.getValueCode());
        dictValueVo1.setParentId("abc");
        DictValueVo dictValueVo2 = new DictValueVo();
        dictValueVo2.setValueId(dictValue2.getValueId());
        dictValueVo2.setTypeCode(dictValue2.getTypeCode());
        dictValueVo2.setValueCode(dictValue2.getValueCode());
        dictValueVo2.setValueName(dictValue2.getValueName());
        dictValueVo2.setValueDesc(dictValue2.getValueDesc());
        dictValueVo2.setParentValueCode(parent2.getValueCode());
        dictValueVo2.setParentId("def");
        DictValueVo dictValueVo3 = new DictValueVo();
        dictValueVo3.setValueId(dictValue3.getValueId());
        dictValueVo3.setTypeCode(dictValue3.getTypeCode());
        dictValueVo3.setValueCode(dictValue3.getValueCode());
        dictValueVo3.setValueName(dictValue3.getValueName());
        dictValueVo3.setValueDesc(dictValue3.getValueDesc());
        dictValueVo3.setParentValueCode(parent3.getValueCode());
        dictValueVo3.setParentId("ghi");
        List<DictValueVo> valueVos = new ArrayList<>();
        valueVos.add(dictValueVo1);
        valueVos.add(dictValueVo2);
        valueVos.add(dictValueVo3);

        when(dictValueMapper.selectByTypeLike(eq("123"), Mockito.any())).thenReturn(list);
        when(dictValueMapper.selectByPrimaryKey(eq("abc"))).thenReturn(parent1);
        when(dictValueMapper.selectByPrimaryKey(eq("def"))).thenReturn(parent2);
        when(dictValueMapper.selectByPrimaryKey(eq("ghi"))).thenReturn(parent3);
        List<DictValueVo> result = dictServiceImpl.getDictValueList("123");
        Assertions.assertEquals(valueVos, result);
    }

    @Test
    @DisplayName("获取字典值列表--通过code和name")
    @Tag("@id:23572")
    @Tag("@author:jiafei")
    @Tag("@date:2021/7/2")
    void testGetByTypeCodeAndValueName() {
        DictValue dictValue1 = new DictValue("123", "123", "123", "123", "abc", "123");
        DictValue dictValue2 = new DictValue("456", "123", "156", "456", "def", "456");
        DictValue dictValue3 = new DictValue("789", "123", "189", "789", "ghi", "789");
        List<DictValue> list = new ArrayList<>();
        list.add(dictValue1);
        list.add(dictValue2);
        list.add(dictValue3);

        DictValue parent1 = new DictValue("abc", "123", "XI,XII,XIII", "XI,XII,XIII", null, "123");
        DictValue parent2 = new DictValue("def", "123", "XIV,XV,XVI", "XIV,XV,XVI", null, "456");
        DictValue parent3 = new DictValue("ghi", "123", "XVII,XVIII,XIX", "XVII,XVIII,XIX", null, "789");

        DictValueVo dictValueVo1 = new DictValueVo();
        dictValueVo1.setValueId(dictValue1.getValueId());
        dictValueVo1.setTypeCode(dictValue1.getTypeCode());
        dictValueVo1.setValueCode(dictValue1.getValueCode());
        dictValueVo1.setValueName(dictValue1.getValueName());
        dictValueVo1.setValueDesc(dictValue1.getValueDesc());
        dictValueVo1.setParentValueCode(parent1.getValueCode());
        dictValueVo1.setParentId("abc");
        DictValueVo dictValueVo2 = new DictValueVo();
        dictValueVo2.setValueId(dictValue2.getValueId());
        dictValueVo2.setTypeCode(dictValue2.getTypeCode());
        dictValueVo2.setValueCode(dictValue2.getValueCode());
        dictValueVo2.setValueName(dictValue2.getValueName());
        dictValueVo2.setValueDesc(dictValue2.getValueDesc());
        dictValueVo2.setParentValueCode(parent2.getValueCode());
        dictValueVo2.setParentId("def");
        DictValueVo dictValueVo3 = new DictValueVo();
        dictValueVo3.setValueId(dictValue3.getValueId());
        dictValueVo3.setTypeCode(dictValue3.getTypeCode());
        dictValueVo3.setValueCode(dictValue3.getValueCode());
        dictValueVo3.setValueName(dictValue3.getValueName());
        dictValueVo3.setValueDesc(dictValue3.getValueDesc());
        dictValueVo3.setParentValueCode(parent3.getValueCode());
        dictValueVo3.setParentId("ghi");

        List<DictValueVo> valueVos = new ArrayList<>();
        valueVos.add(dictValueVo1);
        valueVos.add(dictValueVo2);
        valueVos.add(dictValueVo3);

        when(dictValueMapper.selectByExample(eq("123"), eq(null), eq("1"))).thenReturn(list);
        when(dictValueMapper.selectByPrimaryKey(eq("abc"))).thenReturn(parent1);
        when(dictValueMapper.selectByPrimaryKey(eq("def"))).thenReturn(parent2);
        when(dictValueMapper.selectByPrimaryKey(eq("ghi"))).thenReturn(parent3);

        List<DictValueVo> result= dictServiceImpl.getByTypeCodeAndValueName("123", "1");
        Assertions.assertEquals(valueVos, result);
    }

    @Test
    @DisplayName("获取字典类型列表")
    @Tag("@id:23571")
    @Tag("@author:jiafei")
    @Tag("@date:2021/7/2")
    void testQuertAllDictTypeCode() {
        when(dictTypeMapper.quertAllDictType()).thenReturn(Arrays.<DictType>asList(new DictType("id", "typeCode", "typeName")));

        List<DictType> result = dictServiceImpl.quertAllDictTypeCode();
        Assertions.assertEquals(Arrays.<DictType>asList(new DictType("id", "typeCode", "typeName")), result);
    }

    @Test
    @DisplayName("获取字典类型")
    @Tag("@id:23571")
    @Tag("@author:jiafei")
    @Tag("@date:2021/7/2")
    void testGetDictTypeByName() {
        when(dictTypeMapper.queryDictTypeByName(anyString())).thenReturn(Arrays.<DictType>asList(new DictType("id", "typeCode", "typeName")));

        List<DictType> result = dictServiceImpl.getDictTypeByName("typeName");
        Assertions.assertEquals(Arrays.<DictType>asList(new DictType("id", "typeCode", "typeName")), result);
    }

    @Test
    @DisplayName("获取字典类型列表")
    @Tag("@id:23571")
    @Tag("@author:jiafei")
    @Tag("@date:2021/7/2")
    void testSelectByTypeCodeAndValueCode() {
        when(dictValueMapper.selectByExample(eq("typeCode"), eq("valueCode"), eq(null))).thenReturn(Arrays.<DictValue>asList(new DictValue("valueId", "typeCode", "valueName", "valueCode", "parentId", "valueDesc")));
        List<DictValue> result = dictServiceImpl.selectByTypeCodeAndValueCode("typeCode", "valueCode");
        Assertions.assertEquals(Arrays.<DictValue>asList(new DictValue("valueId", "typeCode", "valueName", "valueCode", "parentId", "valueDesc")), result);
    }

    @Test
    @DisplayName("获取字典值列表")
    @Tag("@id:23572")
    @Tag("@author:jiafei")
    @Tag("@date:2021/7/2")
    void testSelectByValueIds() {
        DictValue dictValue1 = new DictValue("123", "123", "123", "123", "abc", "123");
        DictValue dictValue2 = new DictValue("456", "123", "156", "456", "def", "456");
        DictValue dictValue3 = new DictValue("789", "123", "189", "789", "ghi", "789");
        List<DictValue> list = new ArrayList<>();
        list.add(dictValue1);
        list.add(dictValue2);
        list.add(dictValue3);
        List<String> ids = Arrays.<String>asList("123","456","789");
        when(dictValueMapper.selectByValueIds(eq(ids))).thenReturn(list);

        List<DictValue> result = dictServiceImpl.selectByValueIds(ids);
        Assertions.assertEquals(list, result);
    }

    @Test
    @DisplayName("获取字典值")
    @Tag("@id:23572")
    @Tag("@author:jiafei")
    @Tag("@date:2021/7/2")
    void testGetDictByTypeValueCode() {
        when(dictValueMapper.selectByExample(eq("typeCode"), eq("valueCode"), eq(null))).thenReturn(Arrays.<DictValue>asList(new DictValue("valueId", "typeCode", "valueName", "valueCode", "parentId", "valueDesc")));
        DictValueVo result = dictServiceImpl.getDictByTypeValueCode("typeCode", "valueCode");
        Assertions.assertEquals(result.getTypeCode(),"typeCode");
    }
}
