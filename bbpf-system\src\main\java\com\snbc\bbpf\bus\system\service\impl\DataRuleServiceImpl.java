/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.snbc.bbpf.bus.system.constans.Constant;
import com.snbc.bbpf.bus.system.constans.NumberConstant;
import com.snbc.bbpf.bus.system.service.DataRuleService;
import com.snbc.bbpf.bus.system.service.OrgService;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.system.db.common.entity.Org;
import com.snbc.bbpf.system.db.common.entity.PermissionScope;
import com.snbc.bbpf.system.db.common.mapper.OrgMapper;
import com.snbc.bbpf.system.db.common.mapper.PermissionScopeMapper;
import com.snbc.bbpf.system.db.common.mapper.UserMapper;
import com.snbc.bbpf.system.db.common.vo.OrgVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName: DataRuleServiceImpl
 * 数据权限规则实现类
 * @module: bbpf-bus-system
 * @Author: yangweipeng
 * @date: 2021/5/28
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 */
@Service
@Transactional
@Slf4j
public class DataRuleServiceImpl implements DataRuleService {

    @Autowired
    private PermissionScopeMapper permissionScopeMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private OrgMapper orgMapper;
    @Autowired
    private OrgService orgService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Value("${bbpf.system.security.jwt.expiration}")
    private Integer expireTime;

    public Integer getExpireTime() {
        if (null == expireTime) {
            expireTime = NumberConstant.NO_TWENTY_FOUR;
        }
        return expireTime;
    }

    /**
     * 获取当前用户的url的数据权限
     *
     * @param url
     * @return
     */
    @Override
    @Cacheable(value = Constant.PERMISSION_CACHE_CATEGORY, keyGenerator = Constant.CACHE_KEY_GENERATOR_NAME)
    public List<PermissionScope> getUserDataRule(String url) {
        String userId = CurrentUser.getUserId();
        return permissionScopeMapper.getDataScopeByUserId(url, userId);
    }
    /**
     * 根据向组织机构获取对应的组织机构的用户列表
     *
     * @param orgType
     * @return
     */
    @Override
    public List<String> getUserListByCurrentUserOrg(String orgType) throws IOException {
        List<String> userList = new ArrayList<>();
        ObjectMapper objectMapper=new ObjectMapper();
        List<String> orgIds=userMapper.selectOrgIdListByUserId(CurrentUser.getUserId());
        try {
            // 本组织机构及下级
            if ("1".equals(orgType)) {
                Object strUserList =redisTemplate.opsForHash().get(Constant.USER_DATA_AUTH_ORG_AND_SUB_ORG, CurrentUser.getUserId());
                if (strUserList != null) {
                    userList=objectMapper.readValue(strUserList.toString(),ArrayList.class);
                    return userList;
                }
                List<String> orgList = new ArrayList<>();
                orgIds.forEach(orgId -> {
                    List<OrgVo> list = orgService.getOrgListById(orgId, "0");
                    if (!CollectionUtils.isEmpty(list)) {
                        orgList.addAll(list.stream().map(OrgVo::getOrgId).collect(Collectors.toList()));
                    }
                });
                userList = userMapper.getUserListByOrg(orgList);
                //将结果存储到redis,hash类型的
                redisTemplate.opsForHash().put(Constant.USER_DATA_AUTH_ORG_AND_SUB_ORG, CurrentUser.getUserId(),objectMapper.writeValueAsString(userList));
            } else {
                Object strUserList =redisTemplate.opsForHash().get(Constant.USER_DATA_AUTH_ORG, CurrentUser.getUserId());
                if (strUserList != null) {
                    userList=objectMapper.readValue(strUserList.toString(),ArrayList.class);
                    return userList;
                }
                userList = userMapper.getUserListByOrg(orgIds);
                //将结果存储到redis,hash类型的
                redisTemplate.opsForHash().put(Constant.USER_DATA_AUTH_ORG, CurrentUser.getUserId(), objectMapper.writeValueAsString(userList));
            }
        } catch (JsonProcessingException e) {
            log.error("Failed to obtain the current login organization", e);
        }
        return userList;
    }

    /**
     * 获取数据权限下的 组织机构id：1：获取本人组织机构及本人创建的 2、本人组织机构 3 本人及下级组织机构
     * @param orgType
     * @return
     */
    @Override
    public List<String> getOrgIdListByDataRuleType(String orgType) {
        List<String> userOwenOrgIdList = Lists.newArrayList();
        try {
            ObjectMapper objectMapper=new ObjectMapper();
            // 本人的组织机构
            Object strUserOwenList =redisTemplate.opsForHash().get(Constant.ORG_DATA_AUTH_ORG_ORG, CurrentUser.getUserId());
            if (strUserOwenList != null) {
                userOwenOrgIdList = objectMapper.readValue(strUserOwenList.toString(),ArrayList.class);
            }else {
                // 本人组织机构
                userOwenOrgIdList = userMapper.selectOrgIdListByUserId(CurrentUser.getUserId());
                //将结果存储到redis,hash类型的
                redisTemplate.opsForHash().put(Constant.ORG_DATA_AUTH_ORG_ORG, CurrentUser.getUserId(),objectMapper.writeValueAsString(userOwenOrgIdList));
            }

            //获取本人组织机构及本人创建的组织机构，
            if("1".equals(orgType)){
                // 本人的组织机构
                Object strUserList =redisTemplate.opsForHash().get(Constant.ORG_DATA_AUTH_ORG_AND_CREATE_ORG, CurrentUser.getUserId());
                if (strUserList != null) {
                    return objectMapper.readValue(strUserList.toString(),ArrayList.class);
                }
                List<String> createOrgId = orgMapper.getOrgIdByCreateUserId(CurrentUser.getUserId());
                if (!CollectionUtils.isEmpty(createOrgId)) {
                    userOwenOrgIdList.addAll(createOrgId);
                    redisTemplate.opsForHash().put(Constant.ORG_DATA_AUTH_ORG_AND_CREATE_ORG, CurrentUser.getUserId(),
                            objectMapper.writeValueAsString(userOwenOrgIdList));
                }
            }
            //获取本人组织机构及下级
            if("3".equals(orgType)){
                // 本组织机构的下级
                Object strUserList =redisTemplate.opsForHash().get(Constant.ORG_DATA_AUTH_ORG_AND_SUB_ORG, CurrentUser.getUserId());
                if (strUserList != null) {
                    return objectMapper.readValue(strUserList.toString(),ArrayList.class);
                }
                // 查询userId的orgPath
                List<Org> list = orgMapper.getOrgsByUserId(CurrentUser.getUserId());
                List<String> pathList= list.stream().filter(org -> !"0".equals(org.getOrgId())).map(Org::getOrgPath).collect(Collectors.toList());
                // 查询本身及下级组织机构
                List<String> subOrgIdList = orgMapper.selectAllOrgIdByOrgPath(pathList);
                //将结果存储到redis,hash类型的
                redisTemplate.opsForHash().put(Constant.ORG_DATA_AUTH_ORG_AND_SUB_ORG, CurrentUser.getUserId(),objectMapper.writeValueAsString(subOrgIdList));
                return subOrgIdList;
            }
        } catch (Exception e) {
            log.error("Failed to getOrgIdListByDataRuleType", e);
        }
        return userOwenOrgIdList;
    }
}
