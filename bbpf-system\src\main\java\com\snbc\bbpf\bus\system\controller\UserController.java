/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.controller;

import com.alibaba.fastjson.JSON;
import com.snbc.bbpf.bus.system.constans.CommonConstant;
import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.queue.BusLogQueue;
import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.bus.system.service.UserLoginService;
import com.snbc.bbpf.bus.system.service.UserService;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.bus.system.utils.IpUtil;
import com.snbc.bbpf.bus.system.utils.ResultUtil;
import com.snbc.bbpf.buslog.annotations.Buslog;
import com.snbc.bbpf.buslog.entity.BusLog;
import com.snbc.bbpf.buslog.util.RequestLogInfoUtil;
import com.snbc.bbpf.commons.strings.security.parse.MobilePhoneParse;
import com.snbc.bbpf.system.db.common.dto.UpdatePhoneDto;
import com.snbc.bbpf.system.db.common.entity.PermissionNode;
import com.snbc.bbpf.system.db.common.entity.User;
import com.snbc.bbpf.system.db.common.vo.UpdatePwdVo;
import com.snbc.bbpf.system.db.common.vo.UpdateUserPwdVo;
import com.snbc.bbpf.system.db.common.vo.UserVo;
import com.snbc.component.filestorage.args.UploadArg;
import com.snbc.component.filestorage.args.UploadResult;
import com.snbc.component.filestorage.fileutil.FileUpAndDownload;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.CharEncoding;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.net.URLDecoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * @ClassName: UserController
 * @Description: 用户控制器
 * @module: si-bbpf-system
 * @Author: wangsong
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@RestController
@RequestMapping("/console/user")
@Slf4j
public class UserController {

    @Autowired
    private UserService userService;
    @Autowired
    private UserLoginService userLoginService;
    //文件上传下载
    @Autowired
    private FileUpAndDownload fileUpAndDownload;
    @Value("${bbpf.oss.config.uploadPath}")
    private String userAvatarPath;
    @Value("${bbpf.boss.login.pwdStrength:high}")
    private String pwdStrength;
    /***
      * @Description:    用户详情查询
      * @Author:         wangsong
      * @param :         userId
      * @CreateDate:     2021/5/15 17:31
      * @UpdateDate:     2021/5/15 17:31
      * @return :        org.springframework.http.ResponseEntity<com.snbc.bbpf.system.db.common.dto.UserDto>
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @GetMapping("/getUserDetail")
    public CommonResp<UserVo> getUser() {
        log.debug("User details enter the parameter userId：{}",CurrentUser.getUserId());
        UserVo detail = userService.getDetail(CurrentUser.getUserId());
        return CommonResp.<UserVo>builder().head(ResultUtil.success()).body(detail).build();
    }

    /**
     * 编辑用户时，反查用户基本信息及角色和组织机构信息
     * @param userId
     * @return
     */
    @GetMapping("/getUserById")
    public CommonResp<UserVo> getUserById(String userId) {
        UserVo detail = userService.getUserById(userId);
        return CommonResp.<UserVo>builder().head(ResultUtil.success()).body(detail).build();
    }

    /**
     * 新增角色时，反显当前用户默认的组织机构
     * @return
     */
    @GetMapping("/getUserOrgInfo4Role")
    public CommonResp<List<Map<String,String>>> getUserOrgInfo4Role() {
        return CommonResp.<List<Map<String,String>>>builder().head(ResultUtil.success())
                .body(userService.getUserOrgInfo4Role()).build();
    }

    /***
      * @Description:    修改用户状态
      * @Author:         wangsong
      * @param :         userStatus
      * @CreateDate:     2021/7/19 15:10
      * @UpdateDate:     2021/7/19 15:10
      * @return :        com.snbc.bbpf.component.config.CommonResp
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @PutMapping("/updateUserCondition")
    public CommonResp updateUserCondition(@RequestParam String userStatus) {
        ErrorMessage.USER_STATUS_INVALID.assertNotNull(userStatus);
        if (!StringUtils.isNumeric(userStatus)){
            throw new BusinessException(ErrorMessage.USER_STATUS_INVALID.getMessage(),
                    ErrorMessage.USER_STATUS_INVALID.getCode());
        }
        userService.updateUserCondition(CurrentUser.getUserId(),Integer.parseInt(userStatus));
        return CommonResp.builder().head(ResultUtil.success()).build();
    }

    /**
     * @Description: 根据工号查询用户信息：用于验证 工号是否重复
     * @Author:  wjc1
     * @param :         jobNumber
     * @CreateDate: 2021/5/23 9:14
     * @UpdateDate: 2021/5/23 9:14
     * @return :        CommonResp
     */
    @GetMapping("/getUserByJobNumber")
    public CommonResp<User> getUserByJobNumber(String jobNumber){
        return CommonResp.<User>builder().head(ResultUtil.success()).body(userService.queryByJobNumber(jobNumber)).build();
    }
    /***
      * @Description:    修改密码
      * @Author:         wangsong
      * @param :            user
      * @CreateDate:     2021/5/17 16:10
      * @UpdateDate:     2021/5/17 16:10
      * @return :        void
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @PutMapping("updatePwd")
    @Buslog(opration = "changPwd", zh = "修改密码成功", en = "New password reset succeeded", target = "userManage")
    public CommonResp<Object> updatePwd(@Validated @RequestBody UpdatePwdVo userVo) throws Exception {
        log.debug("The user changes the password entry parameter userId：{}",CurrentUser.getUserId());
        userVo.setUserId(CurrentUser.getUserId());
        userService.updatePwd(userVo,CommonConstant.USER_SMS_UPDATE_CIPHER);
        return CommonResp.builder().body(null).head(ResultUtil.success()).build();
    }
        /***
      * @Description:    忘记密码
      * @Author:         wangsong
      * @param :            user
      * @CreateDate:     2021/5/17 16:10
      * @UpdateDate:     2021/5/17 16:10
      * @return :        void
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @PutMapping("updateUserPwd")
    @Buslog(opration = "forgetPwd", zh = "新密码修改成功", en = "New password reset succeeded", target = "userManage")
    public CommonResp<Object> updateUserPwd(@Validated @RequestBody UpdateUserPwdVo userVo) throws Exception {
        log.debug("The user changes the password entry parameter userId={}",CurrentUser.getUserId());
        userService.updateUserPwd(userVo);
        return CommonResp.builder().body(null).head(ResultUtil.success()).build();
    }

    /***
      * @Description:    获取密码强度
      * @Author:         WangSong
      * @return:         com.snbc.bbpf.component.config.CommonResp
      * @CreateDate:     2023/5/22 15:31
      * @UpdateDate:     2023/5/22 15:31
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
    */
    @GetMapping("/getcipherstrength")
    public CommonResp getCipherStrength() {
        Map resultMap = new HashMap<>();
        resultMap.put("cipherStrength",pwdStrength);
        return CommonResp.builder().head(ResultUtil.success()).body(resultMap).build();
    }

    /***
     * @Description:    发送短信验证码
     * @Author:         wangsong
     * @CreateDate:     2021/6/9 16:52
     * @UpdateDate:     2021/6/9 16:52
     * @return :        com.snbc.bbpf.component.config.CommonResp<java.lang.Object>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @GetMapping("/smsVerifCode")
    public CommonResp<Object> smsVerifCode() throws Exception {
        return userService.smsVerifCode(CurrentUser.getUserId());

    }
    /**
     * @Description: 启用/禁用
     * @Author:  wjc1
     * @param :  userDto 修改的用户信息
     * @CreateDate: 2021/5/19 11:08
     * @UpdateDate: 2021/5/19 11:08
     * @return :        CommonResp
     */
    @PutMapping("changeUserStatus")
    @Buslog(target = "userManage",opration = "modifyUser", zh = "【操作类型：${status}（0：禁用用户 1：启用用户），姓名：${userName}】",
            en="Operation type: ${status} (0: disable user 1: Enable user), name: ${userName}")
    public CommonResp<Object> changeUserStatus(@RequestParam String userId, @RequestParam Integer status){
        userService.updateUserStatus(userId,status);
        return CommonResp.builder().head(ResultUtil.success()).build();
    }
    /***
     * @Description:    更新头像
     * @Author:         liangjunbin
     * @param :            user
     * @CreateDate:     2021/5/17 16:10
     * @UpdateDate:     2021/5/17 16:10
     * @return :        void
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @PutMapping("updateAvatar")
    @Buslog(opration = "modifyUser", zh = "头像已更新",en="Profile picture has been updated", target = "userManage")
    public CommonResp<Object> updateAvatar(@RequestParam String userAvatar)  {
        String userId = CurrentUser.getUserId();
        log.debug("updateAvatar userId={}",userId);
        userService.updateAvatar(userAvatar,userId);
        return CommonResp.builder().head(ResultUtil.success()).build();
    }

    /***
     * @Description:    校验是否管理员
     * @Author:         oywp
     * @CreateDate:     2021/7/5 16:52
     * @UpdateDate:     2021/7/5 16:52
     * @return :        com.snbc.bbpf.component.config.CommonResp<java.lang.Object>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @GetMapping("isAdminUser")
    public CommonResp<Object> isAdminUser() {
        return CommonResp.builder().head(ResultUtil.success())
                .body(userService.isAdmin(CurrentUser.getUserId()))
                .build();
    }
    /***
     * @Description:    获取用户菜单权限
     * @Author:         liangjunbin
     * @param :            user
     * @CreateDate:     2021/5/17 16:10
     * @UpdateDate:     2021/5/17 16:10
     * @return :        void
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @GetMapping("getNavPermissionTree")
    public CommonResp<Object> getUserNavPermission(@RequestParam String sysType,@RequestParam String langKey,
                                                @RequestParam String permissionType)  {
        String userId = CurrentUser.getUserId();
        log.debug("getPermissionTree userId：{}",userId);
        return CommonResp.builder().head(ResultUtil.success())
                .body(userLoginService.getUserPermissionsByUserId(userId,sysType,langKey,permissionType)).build();
    }
    /***
     * @Description:    获取用户菜单权限
     * @Author:         liangjunbin
     * @param :            user
     * @CreateDate:     2021/5/17 16:10
     * @UpdateDate:     2021/5/17 16:10
     * @return :        void
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @GetMapping("getPermissionTree")
    public CommonResp<Object> getUserPermission(@RequestParam String sysType,
                                                @RequestParam String langKey,
                                                @RequestParam String permissionType,
                                                @RequestParam(required = false) String navId,
                                                @RequestParam(required = false) List<String> roIds)  {
        String userId = CurrentUser.getUserId();
        log.debug("getPermissionTree userId：{}",userId);
        List<PermissionNode> userPermissions = userLoginService.getUserNavPermissionsByUserId(userId, sysType, langKey, permissionType, navId, roIds);
        return CommonResp.builder().head(ResultUtil.success())
                .body(userPermissions).build();
    }
    /***
     * @Description:    用户修改文件头像
     * @Author:         liangjunbin
     * @param :            file 文件
     * @CreateDate:     2021/5/17 16:10
     * @UpdateDate:     2021/5/17 16:10
     * @return :        void
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @PostMapping(value="/updateAvatarFile")
    @Buslog(opration = "modifyUser", zh = "头像已更新",en="Profile picture has been updated", target = "userManage")
    public CommonResp<Object> fileUpload(@RequestParam("file") MultipartFile file)  throws Exception {
        String userId =CurrentUser.getUserId();
        log.debug("updateAvatarFile userId：{}",userId);
        DateTimeFormatter dtf2 = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        UploadArg fileArg = (new UploadArg.UploadArgBuilder(userAvatarPath+userId +
                CommonConstant.PERMISSION_FILTER_CHAR +  dtf2.format(LocalDateTime.now())
                + file.getOriginalFilename(),
                file.getInputStream(), userId,file.getSize())).override(true).build();
        UploadResult result = fileUpAndDownload.upload(fileArg);
        if (ErrorMessage.SUCCESS.getCode().equals( result.getCode())) {
            userService.updateAvatar(result.getFileUrl(),userId);
            return CommonResp.builder().head(ResultUtil.success()).body(result.getFileUrl()).build();
        }else  {
            return CommonResp.builder().head(ResultUtil.error(result.getCode(),result.getMessage()))
                    .body(result.getMessage()).build();
        }
    }

    /***
     * @Description:    验证修改用户手机号时用户手机号是否已经被绑定
     * @Author:         wangsong
     * @param :         null
     * @CreateDate:     2022/7/4 16:36
     * @UpdateDate:     2022/7/4 16:36
     * @return :        null
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @GetMapping("/getSmsCodeAndVerifPhoneBind")
    public CommonResp getSmsCodeAndVerifPhoneBind(@RequestParam String phone) throws Exception {
        userService.getSmsCodeAndVerifPhoneBind(phone);
        return CommonResp.builder().head(ResultUtil.success()).build();
    }

    /***
      * @Description:    修改用户手机号
      * @Author:         wangsong
      * @param :         phone
      * @param :         smsVerifCode
      * @CreateDate:     2022/7/4 15:45
      * @UpdateDate:     2022/7/4 15:45
      * @return :        com.snbc.bbpf.component.config.CommonResp
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @PutMapping("/updatePhone")
    public CommonResp updateUserPhone(@RequestBody UpdatePhoneDto updateInfo) throws Exception {
        userService.updateUserPhone(updateInfo.getPhone(),updateInfo.getSmsVerifCode());
        BusLog busLog = new BusLog();
        try {
            //记录日志 因为手机号要脱敏记录
            MobilePhoneParse parsePhone = new MobilePhoneParse();
            String logPhone = parsePhone.parseString(updateInfo.getPhone());
            busLog = BusLog.builder().logType("modifyUser").logTarget("userManage")
                    .createTime(LocalDateTime.now()).logId(UUID.randomUUID().toString()).userId(CurrentUser.getUserId())
                    .requestSource(RequestLogInfoUtil.getRequestSource())
                    .userName(URLDecoder.decode(CurrentUser.getUserName(), CharEncoding.UTF_8))
                    .enContent("Change the mobile number from [" + updateInfo.getOldPhone() + "] to [" + logPhone + "]")
                    .zhContent("手机号[" + updateInfo.getOldPhone() + "]更换为[" + logPhone + "]")
                    .ip(IpUtil.getIpAddress()).build();
            BusLogQueue.getInstance().addLog(busLog);
        } catch (Exception e) {
            log.error("Logging failure,log:{}", JSON.toJSONString(busLog),e);
        }
        return CommonResp.builder().head(ResultUtil.success()).build();
    }
}
