<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snbc.bbpf.system.db.common.mapper.UserProfileMapper">

    <resultMap type="com.snbc.bbpf.system.db.common.entity.UserProfile" id="UserProfileResult">
        <result property="profileId"    column="profile_id"    />
        <result property="userId"    column="user_id"    />
        <result property="profileCode"    column="profile_code"    />
        <result property="tenantCode"    column="tenant_code"    />
        <result property="moduleType"    column="module_type"    />
        <result property="profileName"    column="profile_name"    />
        <result property="profileContent"    column="profile_content"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectUserProfileVo">
        select profile_id, user_id, profile_code, profile_content, create_time from t_user_profile
    </sql>

    <select id="selectUserProfileList" parameterType="com.snbc.bbpf.system.db.common.entity.UserProfile" resultMap="UserProfileResult">
        <include refid="selectUserProfileVo"/>
        <where>
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="profileCode != null  and profileCode != ''"> and profile_code = #{profileCode}</if>
            <if test="profileContent != null  and profileContent != ''"> and profile_content = #{profileContent}</if>
        </where>
    </select>
    <select id="selectUserProfileByProfileCode" parameterType="String" resultMap="UserProfileResult">
        <include refid="selectUserProfileVo"/>
        where profile_code = #{profileCode} and user_id=#{userId}
    </select>
    <insert id="insertUserProfile" parameterType="com.snbc.bbpf.system.db.common.entity.UserProfile">
        insert into t_user_profile
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="profileId != null">profile_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="tenantCode != null">tenant_code,</if>
            <if test="moduleType != null">module_type,</if>
            <if test="profileCode != null">profile_code,</if>
            <if test="profileName != null">profile_name,</if>
            <if test="profileContent != null">profile_content,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="profileId != null">#{profileId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="tenantCode != null">#{tenantCode},</if>
            <if test="moduleType != null">#{moduleType},</if>
            <if test="profileCode != null">#{profileCode},</if>
            <if test="profileName != null">#{profileName},</if>
            <if test="profileContent != null">#{profileContent},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateUserProfile" parameterType="com.snbc.bbpf.system.db.common.entity.UserProfile">
        update t_user_profile
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="moduleType != null">module_type = #{moduleType},</if>
            <if test="tenantCode != null">tenant_code = #{tenantCode},</if>
            <if test="profileCode != null">profile_code = #{profileCode},</if>
            <if test="profileName != null">profile_name = #{profileName},</if>
            <if test="profileContent != null">profile_content = #{profileContent},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where profile_id = #{profileId}
    </update>

    <delete id="deleteUserProfileByprofileId" parameterType="String">
        delete from t_user_profile where profile_id = #{profileId}
    </delete>

    <select id="getMyView" resultType="Map">
        SELECT
        profile_id as id,
        profile_name as name,
        profile_content as groups,
        profile_code as code,
        user_id as userId,
        module_type as moduleType,
        tenant_code as tenantCode
        FROM
        t_user_profile
        where (user_id is null or user_id = #{userId})
        <if test="moduleType != null">
            and module_type = #{moduleType}
        </if>
        <if test="tenantCode != null">
            and tenant_code = #{tenantCode}
        </if>
        order by create_time desc;
    </select>

    <delete id="deleteUserProfileByprofileIds" parameterType="String">
        delete from t_user_profile where profile_id in
        <foreach item="profileId" collection="array" open="(" separator="," close=")">
            #{profileId}
        </foreach>
    </delete>

    <select id="selectCountByProfileName" resultType="int">
        SELECT
        count(0)
        FROM
        t_user_profile
        where profile_name = #{profileName}
        <choose>
            <when test="userId != null and userId !=''">
                and user_id = #{userId}
            </when>
            <otherwise>
                and user_id IS NULL
            </otherwise>
        </choose>
        <if test="id != null and id != ''">
            and profile_id != #{id}
        </if>
    </select>
</mapper>