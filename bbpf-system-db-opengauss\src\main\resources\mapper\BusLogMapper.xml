<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snbc.bbpf.system.db.common.mapper.BusLogMapper" >
  <resultMap id="BaseResultMap" type="com.snbc.bbpf.system.db.common.entity.BusLog" >
    <constructor >
      <idArg column="log_id" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="create_time" jdbcType="TIMESTAMP" javaType="java.time.LocalDateTime" />
      <arg column="user_id" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="user_name" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="org_id" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="org_name" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="ip" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="log_target" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="log_type" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="create_org_id" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="log_content" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="remarks" jdbcType="VARCHAR" javaType="java.lang.String" />
    </constructor>
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    log_id, create_time, user_id, user_name, org_id, org_name, ip, log_target, log_type,
    create_org_id, log_content, remarks
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="java.util.List" >
    select
    <include refid="Base_Column_List" />
    from t_log  where log_id in
    <foreach collection="logIdList" index="index" item="logId" open="(" separator="," close=")">
      #{logId,jdbcType=VARCHAR}
    </foreach>
    order by create_time desc
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from t_log
    where log_id = #{logId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from t_log
    where log_id = #{logId,jdbcType=VARCHAR}
  </delete>

  <insert id="insert" parameterType="com.snbc.bbpf.system.db.common.entity.BusLog" >
    insert into t_log (log_id, create_time, user_id,
      user_name, org_id, org_name,
      ip, log_target, log_type,
      create_org_id, log_content, remarks)
    values (#{logId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{userId,jdbcType=VARCHAR},
      #{userName,jdbcType=VARCHAR}, #{orgId,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR},
      #{ip,jdbcType=VARCHAR}, #{logTarget,jdbcType=VARCHAR}, #{logType,jdbcType=VARCHAR},
      #{createOrgId,jdbcType=VARCHAR}, #{logContent,jdbcType=VARCHAR}, #{remarks,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.snbc.bbpf.system.db.common.entity.BusLog" >
    insert into t_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="logId != null" >
        log_id,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="userId != null" >
        user_id,
      </if>
      <if test="userName != null" >
        user_name,
      </if>
      <if test="orgId != null" >
        org_id,
      </if>
      <if test="orgName != null" >
        org_name,
      </if>
      <if test="ip != null" >
        ip,
      </if>
      <if test="logTarget != null" >
        log_target,
      </if>
      <if test="logType != null" >
        log_type,
      </if>
      <if test="createOrgId != null" >
        create_org_id,
      </if>
      <if test="logContent != null" >
        log_content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="logId != null" >
        #{logId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="userName != null" >
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null" >
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null" >
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="ip != null" >
        #{ip,jdbcType=VARCHAR},
      </if>
      <if test="logTarget != null" >
        #{logTarget,jdbcType=VARCHAR},
      </if>
      <if test="logType != null" >
        #{logType,jdbcType=VARCHAR},
      </if>
      <if test="createOrgId != null" >
        #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="logContent != null" >
        #{logContent,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByExampleSelective" parameterType="map" >
    update t_log
    <set >
      <if test="record.logId != null" >
        log_id = #{record.logId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.userId != null" >
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.userName != null" >
        user_name = #{record.userName,jdbcType=VARCHAR},
      </if>
      <if test="record.orgId != null" >
        org_id = #{record.orgId,jdbcType=VARCHAR},
      </if>
      <if test="record.orgName != null" >
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.ip != null" >
        ip = #{record.ip,jdbcType=VARCHAR},
      </if>
      <if test="record.logTarget != null" >
        log_target = #{record.logTarget,jdbcType=VARCHAR},
      </if>
      <if test="record.logType != null" >
        log_type = #{record.logType,jdbcType=VARCHAR},
      </if>
      <if test="record.createOrgId != null" >
        create_org_id = #{record.createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="record.logContent != null" >
        log_content = #{record.logContent,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update t_log
    set log_id = #{record.logId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      user_id = #{record.userId,jdbcType=VARCHAR},
      user_name = #{record.userName,jdbcType=VARCHAR},
      org_id = #{record.orgId,jdbcType=VARCHAR},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      ip = #{record.ip,jdbcType=VARCHAR},
      log_target = #{record.logTarget,jdbcType=VARCHAR},
      log_type = #{record.logType,jdbcType=VARCHAR},
      create_org_id = #{record.createOrgId,jdbcType=VARCHAR},
      log_content = #{record.logContent,jdbcType=VARCHAR},
      remarks #{remarks,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.snbc.bbpf.system.db.common.entity.BusLog" >
    update t_log
    <set >
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userId != null" >
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="userName != null" >
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null" >
        org_id = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null" >
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="ip != null" >
        ip = #{ip,jdbcType=VARCHAR},
      </if>
      <if test="logTarget != null" >
        log_target = #{logTarget,jdbcType=VARCHAR},
      </if>
      <if test="logType != null" >
        log_type = #{logType,jdbcType=VARCHAR},
      </if>
      <if test="createOrgId != null" >
        create_org_id = #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="logContent != null" >
        log_content = #{logContent,jdbcType=VARCHAR},
      </if>
    </set>
    where log_id = #{logId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.snbc.bbpf.system.db.common.entity.BusLog" >
    update t_log
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      user_id = #{userId,jdbcType=VARCHAR},
      user_name = #{userName,jdbcType=VARCHAR},
      org_id = #{orgId,jdbcType=VARCHAR},
      org_name = #{orgName,jdbcType=VARCHAR},
      ip = #{ip,jdbcType=VARCHAR},
      log_target = #{logTarget,jdbcType=VARCHAR},
      log_type = #{logType,jdbcType=VARCHAR},
      create_org_id = #{createOrgId,jdbcType=VARCHAR},
      log_content = #{logContent,jdbcType=VARCHAR},
      remarks =  #{remarks,jdbcType=VARCHAR},
    where log_id = #{logId,jdbcType=VARCHAR}
  </update>
  <select id="selectExportLogs" resultType="com.snbc.bbpf.system.db.common.vo.ExportLogVo" parameterType="com.snbc.bbpf.system.db.common.vo.BusLogQuery" >
    select
    create_time  createTime,user_name  userName,ip,log_target  logTarget,
    log_type  logType, log_content  logContent,remarks
    from t_log where 1=1
    <if test="depId != null and depId !=''" >
      AND org_id like concat('%',  #{depId,jdbcType=VARCHAR},'%')
    </if>
    <if test="endTime != null and endTime !=''" >
      AND create_time &lt;= #{endTime,jdbcType=VARCHAR}
    </if>
    <if test="startTime != null and startTime !=''" >
      AND create_time &gt;= #{startTime,jdbcType=VARCHAR}
    </if>
    <if test="logTarget != null and logTarget !=''" >
      AND log_target like concat('%', #{logTarget,jdbcType=VARCHAR},'%')
    </if>
    <if test="logType != null and logType !=''" >
      AND log_type like concat('%', #{logType,jdbcType=VARCHAR},'%')
    </if>
    <if test="userName != null and userName !=''" >
      AND user_name like concat('%', #{userName,jdbcType=VARCHAR},'%')
    </if>
    order by create_time desc
  </select>
  <select id="selelctBusLogIds" resultType="java.lang.String">
    select
    log_Id
    from t_log where 1=1
    <if test="depId != null and depId !=''" >
      AND org_id like concat('%',  #{depId,jdbcType=VARCHAR},'%')
    </if>
    <if test="endTime != null and endTime !=''" >
      AND create_time &lt;= #{endTime,jdbcType=VARCHAR}
    </if>
    <if test="startTime != null and startTime !=''" >
      AND create_time &gt;= #{startTime,jdbcType=VARCHAR}
    </if>
    <if test="logTarget != null and logTarget !=''" >
      AND log_target like concat('%', #{logTarget,jdbcType=VARCHAR},'%')
    </if>
    <if test="logType != null and logType !=''" >
      AND log_type like concat('%', #{logType,jdbcType=VARCHAR},'%')
    </if>
    <if test="userName != null and userName !=''" >
      AND user_name like concat('%', #{userName,jdbcType=VARCHAR},'%')
    </if>
    order by create_time desc
  </select>

</mapper>
