/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.job;

import com.google.gson.Gson;
import com.snbc.bbpf.bus.system.constans.CommonConstant;
import com.snbc.bbpf.bus.system.constans.NumberConstant;
import com.snbc.bbpf.bus.system.constans.OrgConstant;
import com.snbc.bbpf.bus.system.enums.BusTemplateCodeEnum;
import com.snbc.bbpf.bus.system.service.LdapService;
import com.snbc.bbpf.bus.system.utils.BossDES3Util;
import com.snbc.bbpf.bus.system.utils.ChineseCharacterUtil;
import com.snbc.bbpf.bus.system.utils.JasyptEncryptUtil;
import com.snbc.bbpf.bus.system.utils.SendMsgUtil;
import com.snbc.bbpf.system.db.common.dto.LdapUserDto;
import com.snbc.bbpf.system.db.common.entity.Message;
import com.snbc.bbpf.system.db.common.entity.Org;
import com.snbc.bbpf.system.db.common.entity.User;
import com.snbc.bbpf.system.db.common.entity.UserOrg;
import com.snbc.bbpf.system.db.common.entity.UserRole;
import com.snbc.bbpf.system.db.common.mapper.OrgMapper;
import com.snbc.bbpf.system.db.common.mapper.RoleMapper;
import com.snbc.bbpf.system.db.common.mapper.UserMapper;
import com.snbc.bbpf.system.db.common.mapper.UserOptMapper;
import com.snbc.bbpf.system.db.common.mapper.UserOrgMapper;
import com.snbc.bbpf.system.db.common.mapper.UserRoleMapper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @return BCD编码字节数组
 * @date 2023/4/25
 * @since 1.0.0
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */

@Component
@Slf4j
public class LDAPUserSynchJob {
    @Autowired
    private LdapService ldapService;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private OrgMapper orgMapper;
    @Autowired
    private UserOrgMapper userOrgMapper;
    @Autowired
    private UserOptMapper userOptMapper;
    @Autowired
    private UserRoleMapper userRoleMapper;
    @Autowired
    private RoleMapper roleMapper;
    /**
     * 默认密码
     */
    @Value("${bbpf.system.default.pwd}")
    private String defaultPassword;
    @Value("${bbpf.system.default.rolecode:}")
    private List<String> defaultRoles;
    /**
     * ldap 用户同步
     */
    @XxlJob("ldapuserSynchHandler")
    public void ldapuserSynchHandler() {
        log.info("begin user sync job");
        List<LdapUserDto> userList = ldapService.getUserList();
        if (!CollectionUtils.isEmpty(userList)) {
            userList.forEach(entity -> {
                try {
                    //处理用户
                    String userId = hanlderUser(entity);
                    //处理组织机构
                    if (null != userId) {
                        handlerOrg(entity, userId);
                    }
                } catch (Exception e) {
                    log.error("sync ldap user error={},{}",entity, e);
                }
            });
        }
    }

    /**
     * 该方法用于将一个用户分配到指定的机构中。
     *
     * @param entity 需要分配机构的用户
     * @param userId 用户ID
     */
    private void handlerOrg(LdapUserDto entity, String userId) {
        String[] orgNames = entity.getOrgNames().split("/");
        // 设置父节点初始值为0
        String parentId = "0";
        String orgId = "";
        for (int i = 0; i < orgNames.length; i++) {
            // 根据组织机构名称和父节点ID查询数据库中是否已经存在该机构
            Org org = orgMapper.getOrgByName(orgNames[i], parentId);
            // 如果已存在该机构，则更新当前机构ID和父节点ID值
            if (org != null) {
                orgId = org.getOrgId();
                parentId = org.getOrgId();
            } else {
                // 如果不存在该机构，则新插入该机构，并更新当前机构ID和父节点ID值
                Org parentOrg = orgMapper.selectByPrimaryKey(parentId);
                org = new Org();
                org.setOrgName(orgNames[i]);
                int maxOrderBy = orgMapper.queryMaxOrderByParentId(parentId);
                // 组织机构名称首字母作为机构编号
                String orgCode = ChineseCharacterUtil.getUpperCase(org.getOrgName(), false);
                orgId = UUID.randomUUID().toString();
                org.setOrgId(orgId);
                org.setOrgCode(getOrgCode(orgCode));
                org.setCreateTime(LocalDateTime.now());
                org.setSequence(maxOrderBy + NumberConstant.NO_ONE);
                org.setOrgStatus(OrgConstant.ORG_STATUS_ENABLE);
                org.setOrgPath(parentOrg.getOrgPath() + "/" + orgId);
                org.setParentId(parentId);
                // 判断父ID是否为根节点
                if (OrgConstant.ORG_PARNENT_ID.equalsIgnoreCase(parentId)) {
                    // 如果是根节点，设置机构层级为1
                    org.setOrgLevel(NumberConstant.NO_ONE);
                } else {
                    // 如果不是根节点，设置机构层级为父节点层级值加1
                    org.setOrgLevel(parentOrg.getOrgLevel() + NumberConstant.NO_ONE);
                }
                orgMapper.insert(org);
                // 更新父节点ID
                parentId = orgId;
            }
            if (i == orgNames.length - 1) {
                // 当遍历到最后一个组织机构名称时，将用户和机构的关联关系写入用户机构关联表中
                userOrgMapper.deleteByUserId(userId);
                UserOrg userOrg = new UserOrg();
                userOrg.setUserId(userId);
                userOrg.setOrgId(orgId);
                userOrg.setId(UUID.randomUUID().toString());
                userOrgMapper.insert(userOrg);
            }
        }
    }

    /**
     * 组织机构编码唯一,生成规则：按组织机构首字母拼接，如果重复追加数字;
     *
     * @return
     */
    private String getOrgCode(String code) {
        int seq = 0;
        Org orgReq = new Org();
        orgReq.setOrgCode(code);
        List<Org> orgList = orgMapper.getOrgs(orgReq);
        if (!CollectionUtils.isEmpty(orgList)) {
            code = getOrgCode(code + seq + 1);
        }
        return code;
    }

    /**
     * 处理用户
     *
     * @param entity
     */
    private String hanlderUser(LdapUserDto entity) {
        //解密手机号
        String strPhone = BossDES3Util.encrypt(entity.getPhone());
        User user = userMapper.selectByPhone(strPhone);
        //更新用户
        if (user != null) {
            //判断工号是否存在
            int count = userOptMapper.selectOnlyJobNumber(entity.getJobNumber(), user.getUserId());
            if (count > 0) {
                sendMessage(entity.getJobNumber());
                return null;
            }
            user.setUserDn(entity.getUserDN());
            user.setUserName(entity.getUserName());
            user.setIsLdap(NumberConstant.NO_ONE);
            user.setUpdateTime(LocalDateTime.now());
            user.setJobNumber(entity.getJobNumber());
            user.setPhone(strPhone);
            user.setEmail(entity.getEmail());
            userMapper.updateByPrimaryKeySelective(user);
        } else {
            //新用户
            int jobCount = userOptMapper.selectOnlyJobNumber(entity.getJobNumber(), null);
            //判断工号是否存在
            if (jobCount > 0) {
                sendMessage(entity.getJobNumber());
                return null;
            }
            String password = JasyptEncryptUtil.desencrypt(defaultPassword, CommonConstant.KEY_IV);
            password = JasyptEncryptUtil.encrypt(password, CommonConstant.KEY_IV);
            user = User.builder().userStatus(NumberConstant.NO_ONE).userId(UUID.randomUUID().toString())
                    .userDn(entity.getUserDN()).userPwd(password).userName(entity.getUserName()).email(entity.getEmail())
                    .isLdap(NumberConstant.NO_ONE).jobNumber(entity.getJobNumber()).updateTime(LocalDateTime.now())
                    .createTime(LocalDateTime.now()).hasLock(NumberConstant.NO_ONE).phone(strPhone).build();
            int count = userOptMapper.selectOnlyUserName(entity.getUserName(), user.getUserId());
            if (count > 0) {
                user.setUserName(entity.getUserName() + (count + NumberConstant.NO_ONE));
            }
            user.setPhone(strPhone);
            userMapper.insertSelective(user);
            //ldap用户绑定初始角色
            userBindRole(user.getUserId());
        }
        return user.getUserId();
    }

    /**
     * 绑定初始角色
     * @param 	userId
     * @return void
     * @throws //
     * @since 1.0.0
     * <AUTHOR>
     * @date 2024/6/17
     */
    private void userBindRole(String userId) {
        //通过过来的用户绑定固定角色
        List<UserRole> userRoleList = new ArrayList<>();
        for (String roleCode : defaultRoles) {
            String roleId = roleMapper.getRoleIdByRoleCode(roleCode);
            if (StringUtils.isNotBlank(roleId)) {
                userRoleList.add(UserRole.builder().id(UUID.randomUUID().toString()).userId(userId).roleId(roleId).build());
            }
        }
        if (!userRoleList.isEmpty()) {
            userRoleMapper.insertRelation(userRoleList);
        }
    }
    /**
     * 给管理员发送短信
     *
     * @param jobNumber 工号
     */
    private void sendMessage(String jobNumber) {
        List<UserRole> userList = userRoleMapper.queryRoleListByRoleId("-1");
        String[] userIdArr = userList.stream().map(UserRole::getUserId).toArray(String[]::new);
        Map<String, String> templateParamMap = new HashMap<>();
        templateParamMap.put("jobNumber", jobNumber);
        Message message = Message.builder().receiveNos(userIdArr)
                .templateCode(BusTemplateCodeEnum.SYNCHD_USER.getName())
                .templateParamJsonArr(new String[]{new Gson().toJson(templateParamMap)})
                .msgTitle(BusTemplateCodeEnum.SYNCHD_USER.getTitle()).build();
        SendMsgUtil.sendSysMessage(message);
    }
}
