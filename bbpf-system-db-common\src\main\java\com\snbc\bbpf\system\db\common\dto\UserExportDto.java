package com.snbc.bbpf.system.db.common.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: UserExportDto
 * @Description: 导出用户dto
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/9/14
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ContentRowHeight(17)
@HeadRowHeight(21)
@ColumnWidth(20)
public class UserExportDto {
    @ExcelIgnore
    private String userId;

    @ExcelProperty({"用户名"})
    private String userName;

    @ExcelProperty({"工号"})
    private String jobNumber;

    @ExcelProperty({"手机号码"})
    private String phone;

    @ExcelProperty({"邮箱"})
    private String email;

    @ExcelProperty({"部门"})
    private String orgNames;

    @ExcelProperty({"角色"})
    private String roleNames;

    @ExcelProperty({"用户状态"})
    private String hasLock;
}
