package com.snbc.bbpf.bus.system.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.snbc.bbpf.bus.system.constans.NumberConstant;
import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.service.DictService;
import com.snbc.bbpf.bus.system.service.OrgService;
import com.snbc.bbpf.bus.system.service.UserImportService;
import com.snbc.bbpf.bus.system.utils.AllOrg;
import com.snbc.bbpf.bus.system.utils.BossDES3Util;
import com.snbc.bbpf.bus.system.utils.CheckOrgPermission;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.bus.system.utils.ExcelReadListener;
import com.snbc.bbpf.system.db.common.dto.UserDto;
import com.snbc.bbpf.system.db.common.dto.UserImportDtoNew;
import com.snbc.bbpf.system.db.common.entity.User;
import com.snbc.bbpf.system.db.common.mapper.OrgMapper;
import com.snbc.bbpf.system.db.common.mapper.RoleMapper;
import com.snbc.bbpf.system.db.common.mapper.UserMapper;
import com.snbc.bbpf.system.db.common.mapper.UserOptMapper;
import com.snbc.bbpf.system.db.common.mapper.UserOrgMapper;
import com.snbc.bbpf.system.db.common.mapper.UserRoleMapper;
import org.apache.http.entity.ContentType;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.util.ResourceUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * @ClassName: UserImportServiceImplTest
 * 用户新增相关单元测试
 * @module: si-bbpf-system
 * @Author: wjc1
 * @date: 2021/7/6 15:03
 */
class UserImportServiceImplTest {
    @Mock
    CheckOrgPermission checkOrgPermission;
    @Mock
    private AllOrg allOrg;
    @Mock
    private ServletOutputStream out;
    @Mock
    private HttpServletResponse response;
    //    @Mock
    private ExcelWriterBuilder excelWriterBuilder=new ExcelWriterBuilder();
    @Mock
    private ExcelWriterSheetBuilder excelWriterSheetBuilder;
    private ExcelReaderBuilder excelReaderBuilder=new ExcelReaderBuilder();
    @Mock
    private InputStream in;
    @Mock
    ExcelReadListener<UserImportDtoNew> excelReadListener = new ExcelReadListener<>();
    @Mock
    private MultipartFile multipartFile;
    @Mock
    private UserMapper userMapper;
    @Mock
    private UserOptMapper userOptMapper;
    @Mock
    private UserOrgMapper userOrgMapper;
    @Mock
    private UserRoleMapper userRoleMapper;
    @Mock
    private DictService dictService;
    @Mock
    private OrgService orgService;
    @Mock
    private OrgMapper orgMapper;
    @Mock
    private RoleMapper roleMapper;
    @InjectMocks
    private UserImportServiceImpl userImportServiceImpl;
    @Mock
    private UserImportService userImportService;
    @InjectMocks
    private UserImportNewServiceImpl userImportNewService;
    @Mock
    private PlatformTransactionManager platformTransactionManager;
    @Mock
    private TransactionDefinition transactionDefinition;
    @Mock
    private RedisTemplate redisTemplate;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        when(orgService.addOrg(any(), any())).thenReturn("1");
    }

    @Test
    @DisplayName("测试用户新增-创建人不存在")
    @Tag("@id:23523")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/6")
    void test_saveUser_create_user_not_exsit() {
        try(MockedStatic<CurrentUser> userMockedStatic = Mockito.mockStatic(CurrentUser.class)){
            UserDto userDto=new UserDto();
            userDto.setCreateUserId("abc");
            userDto.setPhone("11");

            userDto.setBelongOrgIds("123");
            userDto.setJobNumber("12345");
            userDto.setHasLock(1);
            userDto.setUserName("a1");
            when(CurrentUser.getUserId()).thenReturn("-1");
            when(userMapper.selectByPrimaryKey(userDto.getCreateUserId())).thenReturn(null);
            when(userOptMapper.selectOnlyPhone(BossDES3Util.encrypt(userDto.getPhone()),userDto.getUserId())).thenReturn(0);
            BusinessException e=Assertions.assertThrows(BusinessException.class, ()->userImportServiceImpl.saveUser(userDto));
            String expectedCode = ErrorMessage.USER_INVALID.getCode();
            String actualCode = e.getCode();
            String expectedMsg = ErrorMessage.USER_INVALID.getMessage();
            String actualMsg = e.getMessage();
            Assertions.assertEquals(expectedCode, actualCode);
            Assertions.assertEquals(expectedMsg, actualMsg);
            verify(userMapper,times(0)).updateByPrimaryKeySelective(Mockito.any(User.class));
            verify(userMapper,times(0)).insertSelective(Mockito.any(User.class));
        }
    }

    @Test
    @DisplayName("测试用户新增-验证状态")
    @Tag("@id:23523")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/6")
    void test_saveOrUpdateUser_encrypt_phone_fail3() {
        try(MockedStatic<CurrentUser> userMockedStatic = Mockito.mockStatic(CurrentUser.class)){
            when(CurrentUser.getUserId()).thenReturn("abc");
            UserDto userDto=new UserDto();
            userDto.setCreateUserId("abc");
            userDto.setPhone("11");

            userDto.setBelongOrgIds("123");
            userDto.setJobNumber("12345");
            userDto.setUserName("a1");
            userDto.setHasLock(3);
            when(userMapper.selectByPrimaryKey(userDto.getCreateUserId())).thenReturn(new User());
            when(userOptMapper.selectOnlyPhone(BossDES3Util.encrypt(userDto.getPhone()),userDto.getUserId())).thenReturn(0);
            BusinessException e=Assertions.assertThrows(BusinessException.class, ()->userImportServiceImpl.saveUser(userDto));
            String expectedCode = ErrorMessage.USER_STATUS_INVALID.getCode();
            String actualCode = e.getCode();
            String expectedMsg = ErrorMessage.USER_STATUS_INVALID.getMessage();
            String actualMsg = e.getMessage();
            Assertions.assertEquals(expectedCode, actualCode);
            Assertions.assertEquals(expectedMsg, actualMsg);
            verify(userMapper,times(0)).updateByPrimaryKeySelective(Mockito.any(User.class));
            verify(userMapper,times(0)).insertSelective(Mockito.any(User.class));
        }
    }

    @Test
    @DisplayName("测试用户新增-验证邮箱格式")
    @Tag("@id:23523")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/6")
    void test_saveUser_valid_email_invalid() {
        try(MockedStatic<CurrentUser> userMockedStatic =Mockito.mockStatic(CurrentUser.class)) {
            UserDto userDto=new UserDto();
            userDto.setCreateUserId("abc");
            userDto.setPhone("11");

            userDto.setBelongOrgIds("123");
            userDto.setJobNumber("12345");
            userDto.setUserName("a1");
            userDto.setHasLock(1);
            userDto.setEmail("qq.com");
            when(CurrentUser.getUserId()).thenReturn("abc");
            when(userMapper.selectByPrimaryKey(userDto.getCreateUserId())).thenReturn(new User());
            when(userOptMapper.selectOnlyPhone(BossDES3Util.encrypt(userDto.getPhone()),userDto.getUserId())).thenReturn(0);
            BusinessException e=Assertions.assertThrows(BusinessException.class, ()->userImportServiceImpl.saveUser(userDto));
            String expectedCode = ErrorMessage.USER_EMAIL_INVALID.getCode();
            String actualCode = e.getCode();
            String expectedMsg = ErrorMessage.USER_EMAIL_INVALID.getMessage();
            String actualMsg = e.getMessage();
            Assertions.assertEquals(expectedCode, actualCode);
            Assertions.assertEquals(expectedMsg, actualMsg);
            verify(userMapper,times(0)).updateByPrimaryKeySelective(Mockito.any(User.class));
            verify(userMapper,times(0)).insertSelective(Mockito.any(User.class));
        }
    }
    @Test
    @DisplayName("测试用户新增-验证邮箱长度最长64位")
    @Tag("@id:23523")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/6")
    void test_saveUser_valid_email_length() {
        try(MockedStatic<CurrentUser> user=Mockito.mockStatic(CurrentUser.class)){
            when(CurrentUser.getUserId()).thenReturn(null);
            UserDto userDto=new UserDto();
            userDto.setCreateUserId("abc");
            userDto.setPhone("11");

            userDto.setBelongOrgIds("123");
            userDto.setJobNumber("12345");
            userDto.setUserName("a1");
            userDto.setHasLock(1);
            userDto.setEmail("<EMAIL>");
            when(CurrentUser.getUserId()).thenReturn("abc");
            when(userMapper.selectByPrimaryKey(userDto.getCreateUserId())).thenReturn(new User());
            when(userOptMapper.selectOnlyPhone(BossDES3Util.encrypt(userDto.getPhone()),userDto.getUserId())).thenReturn(0);
            BusinessException e=Assertions.assertThrows(BusinessException.class, ()->userImportServiceImpl.saveUser(userDto));
            String expectedCode = ErrorMessage.USER_EMAIL_LENGTH_INVALID.getCode();
            String actualCode = e.getCode();
            String expectedMsg = ErrorMessage.USER_EMAIL_LENGTH_INVALID.getMessage();
            String actualMsg = e.getMessage();
            Assertions.assertEquals(expectedCode, actualCode);
            Assertions.assertEquals(expectedMsg, actualMsg);
            verify(userMapper,times(0)).updateByPrimaryKeySelective(Mockito.any(User.class));
            verify(userMapper,times(0)).insertSelective(Mockito.any(User.class));
        }
    }
    @Test
    @DisplayName("测试用户新增-验证邮箱不唯一")
    @Tag("@id:23523")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/6")
    void test_saveUser_valid_email_not_unique() {
        try(MockedStatic<CurrentUser> userMockedStatic = Mockito.mockStatic(CurrentUser.class)){
            when(CurrentUser.getUserId()).thenReturn("abc");
            UserDto userDto=new UserDto();
            userDto.setCreateUserId("abc");
            userDto.setPhone("11");

            userDto.setBelongOrgIds("123");
            userDto.setJobNumber("12345");
            userDto.setUserName("a1");
            userDto.setHasLock(1);
            userDto.setEmail("<EMAIL>");
            when(userMapper.selectByPrimaryKey(userDto.getCreateUserId())).thenReturn(new User());
            when(userOptMapper.selectOnlyPhone(BossDES3Util.encrypt(userDto.getPhone()),userDto.getUserId())).thenReturn(0);
            when(userOptMapper.selectOnlyEmail(userDto.getEmail(), userDto.getUserId())).thenReturn(1);
            BusinessException e=Assertions.assertThrows(BusinessException.class, ()->userImportServiceImpl.saveUser(userDto));
            String expectedCode = ErrorMessage.USER_EMAIL_EXIST.getCode();
            String actualCode = e.getCode();
            String expectedMsg = ErrorMessage.USER_EMAIL_EXIST.getMessage();
            String actualMsg = e.getMessage();
            Assertions.assertEquals(expectedMsg, actualMsg);
            Assertions.assertEquals(expectedCode, actualCode);
            verify(userMapper,times(0)).updateByPrimaryKeySelective(Mockito.any(User.class));
            verify(userMapper,times(0)).insertSelective(Mockito.any(User.class));
        }
    }
    @Test
    @DisplayName("测试用户新增-手机号唯一")
    @Tag("@id:23523")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/6")
    void test_saveUser_phone_unique() {
        try(MockedStatic<CurrentUser> userMockedStatic = Mockito.mockStatic(CurrentUser.class)){
            when(CurrentUser.getUserId()).thenReturn("abc");
            UserDto userDto=new UserDto();
            userDto.setCreateUserId("abc");
            userDto.setPhone("11");

            userDto.setBelongOrgIds("123");
            userDto.setJobNumber("12345");
            userDto.setHasLock(1);
            userDto.setUserName("a1");
            userDto.setEmail("<EMAIL>");
            when(userMapper.selectByPrimaryKey(userDto.getCreateUserId())).thenReturn(new User());
            when(userOptMapper.selectOnlyPhone(BossDES3Util.encrypt(userDto.getPhone()),userDto.getUserId())).thenReturn(1);
            when(userOptMapper.selectOnlyEmail(userDto.getEmail(), userDto.getUserId())).thenReturn(0);
            BusinessException e=Assertions.assertThrows(BusinessException.class, ()->userImportServiceImpl.saveUser(userDto));
            String expectedCode = ErrorMessage.USER_PHONE_EXIST.getCode();
            String actualCode = e.getCode();
            String expectedMsg = ErrorMessage.USER_PHONE_EXIST.getMessage();
            String actualMsg = e.getMessage();
            Assertions.assertEquals(expectedMsg, actualMsg);
            Assertions.assertEquals(expectedCode, actualCode);
            verify(userOptMapper,times(0)).selectOnlyJobNumber(userDto.getJobNumber(), userDto.getUserId());
            verify(userMapper,times(0)).updateByPrimaryKeySelective(Mockito.any(User.class));
            verify(userMapper,times(0)).insertSelective(Mockito.any(User.class));
        }
    }

    @Test
    @DisplayName("测试用户新增-工号唯一")
    @Tag("@id:23523")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/6")
    void test_saveUser_jobnumber_unique() {
        try(MockedStatic<CurrentUser> userMockedStatic = Mockito.mockStatic(CurrentUser.class)){
            when(CurrentUser.getUserId()).thenReturn("abc");
            UserDto userDto=new UserDto();
            userDto.setCreateUserId("abc");
            userDto.setPhone("11");

            userDto.setBelongOrgIds("123");
            userDto.setJobNumber("12345");
            userDto.setHasLock(1);
            userDto.setUserName("a1");
            when(userOptMapper.selectOnlyJobNumber(userDto.getJobNumber(),userDto.getUserId())).thenReturn(1);
            when(userMapper.selectByPrimaryKey(userDto.getCreateUserId())).thenReturn(new User());
            when(userOrgMapper.queryOrgListByUserId(userDto.getCreateUserId())).thenReturn("asdf");
            when(userOptMapper.selectOnlyPhone(BossDES3Util.encrypt(userDto.getPhone()),null)).thenReturn(0);
            BusinessException e=Assertions.assertThrows(BusinessException.class, ()->userImportServiceImpl.saveUser(userDto));
            String expectedCode = ErrorMessage.USER_JOB_NUMBER_EXIST.getCode();
            String actualCode = e.getCode();
            String expectedMsg = ErrorMessage.USER_JOB_NUMBER_EXIST.getMessage();
            String actualMsg = e.getMessage();
            Assertions.assertEquals(expectedCode, actualCode);
            Assertions.assertEquals(expectedMsg, actualMsg);
            verify(userOrgMapper,times(0)).queryOrgListByUserId(userDto.getCreateUserId());
            verify(userMapper,times(0)).updateByPrimaryKeySelective(Mockito.any(User.class));
            verify(userMapper,times(0)).insertSelective(Mockito.any(User.class));
        }
    }
    @Test
    @DisplayName("测试用户新增-角色不存在")
    @Tag("@id:23523")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/6")
    void test_saveUser_role_not_exsit() {
        UserDto userDto=new UserDto();
        userDto.setCreateUserId("abc");
        userDto.setPhone("11");

        userDto.setBelongOrgIds("123");
        userDto.setJobNumber("12345");
        userDto.setHasLock(1);
        userDto.setUserName("a1");
        when(userOptMapper.selectOnlyJobNumber(userDto.getJobNumber(),userDto.getUserId())).thenReturn(0);
        when(userMapper.selectByPrimaryKey(userDto.getCreateUserId())).thenReturn(new User());
        when(userOrgMapper.queryOrgListByUserId(userDto.getCreateUserId())).thenReturn("asdf");

        try{
            when(userOptMapper.selectOnlyPhone(BossDES3Util.encrypt(userDto.getPhone()),null)).thenReturn(0);
            userImportServiceImpl.saveUser(userDto);
        }catch (Exception e){
            //Assertions.fail("businessException is not expected"+e);
        }
        //verify(userOrgMapper,times(1)).queryOrgListByUserId(userDto.getCreateUserId());
        verify(userMapper,times(0)).updateByPrimaryKeySelective(Mockito.any(User.class));
        //verify(userMapper,times(1)).insertSelective(Mockito.any(User.class));
        //verify(userOptMapper,times(1)).addUserOrgs(Mockito.any(List.class));
        //verify(userOptMapper,times(0)).addUserRoles(Mockito.any(List.class));
    }
    @Test
    @DisplayName("测试用户新增-成功")
    @Tag("@id:23523")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/6")
    void test_saveUser_success() {
        UserDto userDto=new UserDto();
        userDto.setCreateUserId("abc");
        userDto.setPhone("11");

        userDto.setBelongOrgIds("123");
        userDto.setJobNumber("12345");
        userDto.setHasLock(1);
        userDto.setUserName("a1");
        userDto.setBelongRoleIds("ddd");
        userDto.setEmail("<EMAIL>");
        when(userOptMapper.selectOnlyEmail(userDto.getEmail(), userDto.getUserId())).thenReturn(0);
        when(userOptMapper.selectOnlyJobNumber(userDto.getJobNumber(),userDto.getUserId())).thenReturn(0);
        when(userMapper.selectByPrimaryKey(userDto.getCreateUserId())).thenReturn(new User());
        when(userOptMapper.selectOnlyPhone(userDto.getJobNumber(),userDto.getUserId())).thenReturn(0);
        when(userOrgMapper.queryOrgListByUserId(userDto.getCreateUserId())).thenReturn("asdf");

        try{
            when(userOptMapper.selectOnlyPhone(BossDES3Util.encrypt(userDto.getPhone()),userDto.getUserId())).thenReturn(0);
            userImportServiceImpl.saveUser(userDto);
        }catch (Exception e){
           // Assertions.assertEquals(ErrorMessage.USER_JOB_NUMBER_EXIST.getMessage(),e.getMessage());
        }
        //verify(userOrgMapper,times(1)).queryOrgListByUserId(userDto.getCreateUserId());
        verify(userMapper,times(0)).updateByPrimaryKeySelective(Mockito.any(User.class));
//        verify(userMapper,times(1)).insertSelective(Mockito.any(User.class));
 //       verify(userOptMapper,times(1)).addUserOrgs(Mockito.any(List.class));
  //      verify(userOptMapper,times(1)).addUserRoles(Mockito.any(List.class));
    }
    @Test
    @DisplayName("测试用户更新-创建人不存在")
    @Tag("@id:23523")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/6")
    void test_updateUser_create_user_not_exsit() {
        try(MockedStatic<CurrentUser> userMockedStatic = Mockito.mockStatic(CurrentUser.class)){
            UserDto userDto=new UserDto();
            userDto.setCreateUserId("abc");
            userDto.setUserId("asdfghjk");
            userDto.setPhone("11");

            userDto.setBelongOrgIds("123");
            userDto.setJobNumber("12345");
            userDto.setHasLock(1);
            userDto.setUserName("a1");
            when(CurrentUser.getUserId()).thenReturn("-1");
            when(userMapper.selectByPrimaryKey(userDto.getCreateUserId())).thenReturn(null);
            when(userOptMapper.selectOnlyPhone(BossDES3Util.encrypt(userDto.getPhone()),userDto.getUserId())).thenReturn(0);
            BusinessException e=Assertions.assertThrows(BusinessException.class, ()->userImportServiceImpl.saveUser(userDto));
            String expectedCode = ErrorMessage.USER_INVALID.getCode();
            String actualCode = e.getCode();
            String expectedMsg = ErrorMessage.USER_INVALID.getMessage();
            String actualMsg = e.getMessage();
            Assertions.assertEquals(expectedCode, actualCode);
            Assertions.assertEquals(expectedMsg, actualMsg);
            verify(userMapper,times(0)).updateByPrimaryKeySelective(Mockito.any(User.class));
            verify(userMapper,times(0)).insertSelective(Mockito.any(User.class));
        }
    }
    @Test
    @DisplayName("测试用户更新-验证状态")
    @Tag("@id:23523")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/6")
    void test_updateUser_encrypt_phone_fail3() {
        try(MockedStatic<CurrentUser> userMockedStatic = Mockito.mockStatic(CurrentUser.class)){
            UserDto userDto=new UserDto();
            userDto.setCreateUserId("abc");
            userDto.setUserId("asdfghjk");
            userDto.setPhone("11");

            userDto.setBelongOrgIds("123");
            userDto.setJobNumber("12345");
            userDto.setUserName("a1");
            userDto.setHasLock(3);
            when(CurrentUser.getUserId()).thenReturn("abc");
            when(userMapper.selectByPrimaryKey(userDto.getCreateUserId())).thenReturn(new User());
            when(userOptMapper.selectOnlyPhone(BossDES3Util.encrypt(userDto.getPhone()),userDto.getUserId())).thenReturn(0);
            BusinessException e=Assertions.assertThrows(BusinessException.class, ()->userImportServiceImpl.updateUser(userDto));
            String expectedCode = ErrorMessage.USER_STATUS_INVALID.getCode();
            String actualCode = e.getCode();
            String expectedMsg = ErrorMessage.USER_STATUS_INVALID.getMessage();
            String actualMsg = e.getMessage();
            Assertions.assertEquals(expectedCode, actualCode);
            Assertions.assertEquals(expectedMsg, actualMsg);
            verify(userMapper,times(0)).updateByPrimaryKeySelective(Mockito.any(User.class));
            verify(userMapper,times(0)).insertSelective(Mockito.any(User.class));
        }
    }
    @Test
    @DisplayName("测试用户更新-userId不能查出用户")
    @Tag("@id:23523")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/6")
    void test_updateUser_userId_not_has_user() {
        try(MockedStatic<CurrentUser> userMockedStatic = Mockito.mockStatic(CurrentUser.class)){
            when(CurrentUser.getUserId()).thenReturn("abc");
            UserDto userDto=new UserDto();
            userDto.setCreateUserId("abc");
            userDto.setUserId("asdfghjk");
            userDto.setPhone("11");

            userDto.setBelongOrgIds("123");
            userDto.setJobNumber("12345");
            userDto.setUserName("a1");
            userDto.setHasLock(1);
            when(userMapper.selectByPrimaryKey(userDto.getCreateUserId())).thenReturn(new User());
            when(userMapper.selectByPrimaryKey(userDto.getUserId())).thenReturn(null);
            when(userOptMapper.selectOnlyPhone(BossDES3Util.encrypt(userDto.getPhone()),userDto.getUserId())).thenReturn(0);
            BusinessException e=Assertions.assertThrows(BusinessException.class, ()->userImportServiceImpl.updateUser(userDto));
            String expectedCode = ErrorMessage.USER_NOT_EXIST.getCode();
            String actualCode = e.getCode();
            String expectedMsg = ErrorMessage.USER_NOT_EXIST.getMessage();
            String actualMsg = e.getMessage();
            Assertions.assertEquals(expectedCode, actualCode);
            Assertions.assertEquals(expectedMsg, actualMsg);
        }
    }
    @Test
    @DisplayName("测试用户更新-更新的手机号:判断是否超级管理员")
    @Tag("@id:23523")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/6")
    void test_updateUser_phone_user_is_super() {
        try(MockedStatic<CurrentUser> currentUserMockedStatic = Mockito.mockStatic(CurrentUser.class)) {
            when(CurrentUser.getUserId()).thenReturn("asdfghjk");
            UserDto userDto = new UserDto();
            userDto.setCreateUserId("abc");
            userDto.setUserId("asdfghjk");
            userDto.setPhone("11");
            userDto.setBelongOrgIds("123");
            userDto.setJobNumber("12345");
            userDto.setUserName("a1");
            userDto.setHasLock(1);
            when(userMapper.selectByPrimaryKey(userDto.getCreateUserId())).thenReturn(new User());
            User exsit = new User();
            exsit.setPhone("12");
            when(userMapper.selectByPrimaryKey(userDto.getUserId())).thenReturn(exsit);
            when(userOptMapper.selectOnlyPhone(BossDES3Util.encrypt(userDto.getPhone()), null)).thenReturn(NumberConstant.NO_ONE);
            BusinessException e=Assertions.assertThrows(BusinessException.class, ()->userImportServiceImpl.updateUser(userDto));
            String expectedCode = ErrorMessage.NO_PERMISSION_UPDATE.getCode();
            String actualCode = e.getCode();
            String expectedMsg = ErrorMessage.NO_PERMISSION_UPDATE.getMessage();
            String actualMsg = e.getMessage();
            Assertions.assertEquals(expectedCode, actualCode);
            Assertions.assertEquals(expectedMsg, actualMsg);
        }
    }
    @Test
    @DisplayName("测试用户更新-更新的手机号唯一")
    @Tag("@id:23523")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/6")
    void test_updateUser_phone_unique() {
        try(MockedStatic<CurrentUser> currentUserMockedStatic = Mockito.mockStatic(CurrentUser.class)) {
            UserDto userDto = new UserDto();
            userDto.setCreateUserId("abc");
            userDto.setUserId("asdfghjk");
            userDto.setPhone("11");
            userDto.setBelongOrgIds("123");
            userDto.setJobNumber("12345");
            userDto.setUserName("a1");
            userDto.setHasLock(1);
            when(userMapper.selectByPrimaryKey(userDto.getCreateUserId())).thenReturn(new User());
            User exsit = new User();
            exsit.setPhone("12");
            when(CurrentUser.getUserId()).thenReturn("asdfghjk");
            when(userMapper.selectByPrimaryKey(userDto.getUserId())).thenReturn(exsit);
            when(userRoleMapper.selectRoleByUserId(CurrentUser.getUserId())).thenReturn(Collections.singletonList("-1"));
            when(userOptMapper.selectOnlyPhone(BossDES3Util.encrypt(userDto.getPhone()), null)).thenReturn(NumberConstant.NO_ONE);
            BusinessException e=Assertions.assertThrows(BusinessException.class, ()->userImportServiceImpl.updateUser(userDto));
            String expectedCode = ErrorMessage.USER_PHONE_EXIST.getCode();
            String actualCode = e.getCode();
            String expectedMsg = ErrorMessage.USER_PHONE_EXIST.getMessage();
            String actualMsg = e.getMessage();
            Assertions.assertEquals(expectedMsg, actualMsg);
            Assertions.assertEquals(expectedCode, actualCode);
        }
    }
    @Test
    @DisplayName("测试用户更新-更新的工号不唯一")
    @Tag("@id:23523")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/6")
    void test_updateUser_jobnumber_unique() {
        try(MockedStatic<CurrentUser> user=Mockito.mockStatic(CurrentUser.class)){
            when(CurrentUser.getUserId()).thenReturn("abc");
            UserDto userDto=new UserDto();
            userDto.setCreateUserId("abc");
            userDto.setUserId("asdfghjk");
            userDto.setPhone("11");
            userDto.setBelongOrgIds("123");
            userDto.setJobNumber("12345");
            userDto.setUserName("a1");
            userDto.setHasLock(1);
            when(CurrentUser.getUserId()).thenReturn("asdfghjk");
            when(userOptMapper.selectOnlyJobNumber(userDto.getJobNumber(),null)).thenReturn(NumberConstant.NO_ONE);
            when(userMapper.selectByPrimaryKey(userDto.getCreateUserId())).thenReturn(new User());
            User exsit=new User();
            exsit.setPhone(BossDES3Util.encrypt(userDto.getPhone()));
            exsit.setJobNumber("ddd");
            when(userMapper.selectByPrimaryKey(userDto.getUserId())).thenReturn(exsit);
            when(userOptMapper.selectOnlyPhone(BossDES3Util.encrypt(userDto.getPhone()),null)).thenReturn(0);
            BusinessException e=Assertions.assertThrows(BusinessException.class, ()->userImportServiceImpl.updateUser(userDto));
            String expectedCode = ErrorMessage.USER_JOB_NUMBER_EXIST.getCode();
            String actualCode = e.getCode();
            String expectedMsg = ErrorMessage.USER_JOB_NUMBER_EXIST.getMessage();
            String actualMsg = e.getMessage();
            Assertions.assertEquals(expectedCode, actualCode);
            Assertions.assertEquals(expectedMsg, actualMsg);
            verify(userOptMapper,times(0)).selectOnlyPhone(userDto.getPhone(),null);
        }
    }
    @Test
    @DisplayName("测试用户更新-更新的邮箱工号不唯一")
    @Tag("@id:23523")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/6")
    void test_updateUser_email_unique() {
        try(MockedStatic<CurrentUser> userMockedStatic = Mockito.mockStatic(CurrentUser.class)){
            UserDto userDto=new UserDto();
            userDto.setCreateUserId("abc");
            userDto.setUserId("asdfghjk");
            userDto.setPhone("11");
            userDto.setBelongOrgIds("123");
            userDto.setJobNumber("12345");
            userDto.setUserName("a1");
            userDto.setHasLock(1);
            userDto.setEmail("<EMAIL>");
            when(CurrentUser.getUserId()).thenReturn("asdfghjk");
            when(userOptMapper.selectOnlyEmail(userDto.getEmail(), null)).thenReturn(1);
            when(userOptMapper.selectOnlyJobNumber(userDto.getJobNumber(),null)).thenReturn(0);
            when(userMapper.selectByPrimaryKey(userDto.getCreateUserId())).thenReturn(new User());
            User exsit=new User();
            exsit.setPhone(BossDES3Util.encrypt(userDto.getPhone()));
            exsit.setJobNumber("ddd");
            when(userMapper.selectByPrimaryKey(userDto.getUserId())).thenReturn(exsit);
            when(userOptMapper.selectOnlyPhone(BossDES3Util.encrypt(userDto.getPhone()),null)).thenReturn(0);
            BusinessException e=Assertions.assertThrows(BusinessException.class, ()->userImportServiceImpl.updateUser(userDto));
            String expectedCode = ErrorMessage.USER_EMAIL_EXIST.getCode();
            String actualCode = e.getCode();
            String expectedMsg = ErrorMessage.USER_EMAIL_EXIST.getMessage();
            String actualMsg = e.getMessage();
            Assertions.assertEquals(expectedMsg, actualMsg);
            Assertions.assertEquals(expectedCode, actualCode);
            verify(userOptMapper,times(0)).selectOnlyPhone(userDto.getPhone(),null);
        }
    }
    @Test
    @DisplayName("测试用户更新-角色不存在")
    @Tag("@id:23523")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/6")
    void test_updateUser_role_not_exsit() {
        try(MockedStatic<CurrentUser> userMockedStatic = Mockito.mockStatic(CurrentUser.class)){
            when(CurrentUser.getUserId()).thenReturn("abc");
            UserDto userDto=new UserDto();
            userDto.setCreateUserId("abc");
            userDto.setUserId("asdfghjk");
            userDto.setPhone("11");

            userDto.setBelongOrgIds("123");
            userDto.setJobNumber("12345");
            userDto.setUserName("a1");
            userDto.setHasLock(1);
            when(userMapper.selectByPrimaryKey(userDto.getCreateUserId())).thenReturn(new User());
            when(userOptMapper.selectOnlyJobNumber(userDto.getJobNumber(),null)).thenReturn(NumberConstant.NO_ZERO);
            String phone=null;
            try{
                phone=BossDES3Util.encrypt(userDto.getPhone());
                User exsit=new User();
                exsit.setPhone(phone);
                exsit.setJobNumber(userDto.getJobNumber());
                when(userMapper.selectByPrimaryKey(userDto.getUserId())).thenReturn(exsit);
                when(userOptMapper.selectOnlyPhone(phone,null)).thenReturn(0);
                userImportServiceImpl.updateUser(userDto);
            }catch (Exception e){
                Assertions.fail("businessException is not expected:"+e);
            }
            verify(userOptMapper,times(0)).selectOnlyPhone(phone,null);
            verify(userOptMapper,times(0)).selectOnlyJobNumber(userDto.getJobNumber(),null);
            verify(userOrgMapper,times(1)).deleteByUserId(userDto.getUserId());
            verify(userRoleMapper,times(1)).deleteByUserId(userDto.getUserId());
            //belongRoleIds 为空
            verify(userOptMapper,times(0)).addUserRoles(Mockito.any(List.class));
        }
    }

    @Test
    @DisplayName("测试用户导入-用户不存在")
    @Tag("@id:23604")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/6")
    void testImportUser_user_not_exsit() {
        try(MockedStatic<CurrentUser> user=Mockito.mockStatic(CurrentUser.class)){
            when(CurrentUser.getUserId()).thenReturn(null);
            BusinessException e=Assertions.assertThrows(BusinessException.class, ()->userImportNewService.importUserNew(null, null));
            String expectedCode = ErrorMessage.USER_INVALID.getCode();
            String actualCode = e.getCode();
            String expectedMsg = ErrorMessage.USER_INVALID.getMessage();
            String actualMsg = e.getMessage();
            Assertions.assertEquals(expectedCode, actualCode);
            Assertions.assertEquals(expectedMsg, actualMsg);
        }
    }
    @Test
    @DisplayName("测试用户导入-文件不存在")
    @Tag("@id:23604")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/6")
    void testImportUser_file_not_exsit() {
        try(MockedStatic<CurrentUser> user=Mockito.mockStatic(CurrentUser.class)){
            when(CurrentUser.getUserId()).thenReturn("111");
            BusinessException e=Assertions.assertThrows(BusinessException.class, ()->userImportNewService.importUserNew(null, null));
            String expectedCode = ErrorMessage.USER_FILE_INVALID.getCode();
            String actualCode = e.getCode();
            String expectedMsg = ErrorMessage.USER_FILE_INVALID.getMessage();
            String actualMsg = e.getMessage();
            Assertions.assertEquals(expectedCode, actualCode);
            Assertions.assertEquals(expectedMsg, actualMsg);
        }
    }
    @Test
    @DisplayName("测试用户导入-上传文件后缀不正确")
    @Tag("@id:23604")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/6")
    void testImportUser_file_suffix_fail() {
        try(MockedStatic<CurrentUser> user=Mockito.mockStatic(CurrentUser.class)){
            when(CurrentUser.getUserId()).thenReturn("111");
            when(multipartFile.getOriginalFilename()).thenReturn("hh.jpg");
            BusinessException e=Assertions.assertThrows(BusinessException.class, ()->userImportNewService.importUserNew(multipartFile, null));
            String expectedCode = ErrorMessage.USER_FILE_TYPE_INVALID.getCode();
            String actualCode = e.getCode();
            String expectedMsg = ErrorMessage.USER_FILE_TYPE_INVALID.getMessage();
            String actualMsg = e.getMessage();
            Assertions.assertEquals(expectedCode, actualCode);
            Assertions.assertEquals(expectedMsg, actualMsg);
        }
    }

/**   @Test
    @DisplayName("测试用户导入-上传文件_校验状态_两条数据状态一样")
    @Tag("@id:23604")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/6")
    void testImportUser_userDto() {
        // 读取完excle转换成的list
        List<UserImportDto> userImportDtoList= new ArrayList<>();
        UserImportDto userImportDto=new UserImportDto();
        userImportDto.setUserName("abc");
        userImportDto.setJobNumber("abcd");
        userImportDto.setPhone("***********");
        userImportDto.setEmail("***********");
        userImportDto.setBelongOrgNames("ab");
        userImportDto.setBelongRoleNames("cd");
        userImportDto.setHasLockName("启用");
        userImportDtoList.add(userImportDto);
        UserImportDto userImportDto1=new UserImportDto();
        userImportDto1.setUserName("abc1");
        userImportDto1.setJobNumber("abcd1");
        userImportDto1.setPhone("***********");
        userImportDto1.setEmail("***********");
        userImportDto1.setBelongOrgNames("ab1");
        userImportDto1.setBelongRoleNames("cd1");
        userImportDto1.setHasLockName("启用1");
        userImportDtoList.add(userImportDto1);

        List<UserImportDto> result=new ArrayList<>();
        try(MockedStatic<CurrentUser> user=Mockito.mockStatic(CurrentUser.class);
            MockedStatic<EasyExcelFactory> easyExcelFactory=Mockito.mockStatic(EasyExcelFactory.class)){
            String createUserId="createUserId";
            when(CurrentUser.getUserId()).thenReturn(createUserId);
            when(multipartFile.getOriginalFilename()).thenReturn("hh.xlsx");
            when(userOrgMapper.queryOrgListByUserId(createUserId)).thenReturn("createOrgId");
            when(excelReadListener.getList()).thenReturn(userImportDtoList);
            //
            when(EasyExcelFactory.read(in, UserImportDto.class, excelReadListener)).thenReturn(excelReaderBuilder);
            when(EasyExcelFactory.write(out)).thenReturn(excelWriterBuilder);
            when(excelWriterBuilder.sheet("用户信息")).thenReturn(excelWriterSheetBuilder);
            result=userImportServiceImpl.importUser(multipartFile, response);
        }catch (Exception e){
            Assertions.fail("businessException is not expected:"+e);
        }
        //验证
        Assertions.assertEquals(true,result.size()==userImportDtoList.size());
        Assertions.assertEquals(true,result.get(0).getErrorMsg().startsWith("用户状态应为：启用或禁用"));
        verify(excelWriterSheetBuilder,times(1)).doWrite(any(List.class));
        verify(userOptMapper,times(0)).addUserOrgs(any(List.class));
        verify(userOptMapper,times(0)).addUserRoles(any(List.class));
    }**/

    @SuppressWarnings("unchecked")
    @Test
    @DisplayName("测试用户导入-上传文件_校验状态_两条数据状态一样")
    @Tag("@id:23604")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/6")
    void testImportUser_userDto() {
        List<UserImportDtoNew> result=new ArrayList<>();
        try(MockedStatic<CurrentUser> user=Mockito.mockStatic(CurrentUser.class);
            MockedStatic<EasyExcelFactory> easyExcelFactory=Mockito.mockStatic(EasyExcelFactory.class)){
            String createUserId="createUserId";
            when(CurrentUser.getUserId()).thenReturn(createUserId);
            when(userOrgMapper.queryOrgListByUserId(createUserId)).thenReturn("createOrgId");
            when(orgMapper.selectIdsByOrgNames(any(List.class))).thenReturn("orgIds");
            when(roleMapper.selectIdsByRoleNames(any(List.class))).thenReturn("roleIds");
            when(userOptMapper.selectOnlyJobNumber(anyString(),anyString())).thenReturn(1);
            File file = ResourceUtils.getFile("classpath:importUser.xlsx");
            MultipartFile mulFile = new MockMultipartFile(
                    "importUser.xlsx", //文件名
                    "importUser.xlsx", //originalName 相当于上传文件在客户机上的文件名
                    ContentType.APPLICATION_OCTET_STREAM.toString(), //文件类型
                    new FileInputStream(file) //文件流
            );

            when(EasyExcelFactory.read(any(InputStream.class), any(), any(ExcelReadListener.class))).thenCallRealMethod();
            when(EasyExcelFactory.write(any(ServletOutputStream.class))).thenReturn(excelWriterBuilder);
//            HttpServletResponse response = null;
            when(response.getOutputStream()).thenReturn(out);
            userImportNewService.importUserNew(mulFile, response);
        }catch (Exception e){
            Assertions.fail("businessException is not expected:"+e);
        }
        //验证
        //Assertions.assertEquals(true,result.size()==userImportDtoList.size());
        //Assertions.assertEquals(true,result.get(0).getErrorMsg().startsWith("用户状态应为：启用或禁用"));
        //verify(excelWriterSheetBuilder,times(0)).doWrite(any(List.class));
        verify(userOptMapper,times(0)).addUserOrgs(any(List.class));
        verify(userOptMapper,times(0)).addUserRoles(any(List.class));
    }
    @SuppressWarnings("unchecked")
    @Test
    @DisplayName("测试用户导入-上传文件_校验状态_两条数据不一样")
    @Tag("@id:23604")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/6")
    void testImportUser_userDto_fail2() {
        List<UserImportDtoNew> result=new ArrayList<>();
        try(MockedStatic<CurrentUser> user=Mockito.mockStatic(CurrentUser.class);
            MockedStatic<EasyExcelFactory> easyExcelFactory=Mockito.mockStatic(EasyExcelFactory.class)){
            String createUserId="createUserId";
            when(CurrentUser.getUserId()).thenReturn(createUserId);
            when(userOrgMapper.queryOrgListByUserId(createUserId)).thenReturn("createOrgId");
            when(orgMapper.selectIdsByOrgNames(any(List.class))).thenReturn("orgIds");
            when(roleMapper.selectIdsByRoleNames(any(List.class))).thenReturn("roleIds");
            when(userOptMapper.selectOnlyJobNumber(anyString(),anyString())).thenReturn(1);
            when(userOptMapper.selectOnlyPhoneByJobNumber(anyString(),anyString())).thenReturn(1);
            //
            File file = ResourceUtils.getFile("classpath:importUser-fail2.xlsx");
            MultipartFile mulFile = new MockMultipartFile(
                    "importUser-fail2.xlsx", //文件名
                    "importUser-fail2.xlsx", //originalName 相当于上传文件在客户机上的文件名
                    ContentType.APPLICATION_OCTET_STREAM.toString(), //文件类型
                    new FileInputStream(file) //文件流
            );

            when(EasyExcelFactory.read(any(InputStream.class), any(), any(ExcelReadListener.class))).thenCallRealMethod();
            when(EasyExcelFactory.write(any(ServletOutputStream.class))).thenReturn(excelWriterBuilder);
            when(response.getOutputStream()).thenReturn(out);
            userImportNewService.importUserNew(mulFile, response);
        }catch (Exception e){
            Assertions.fail("businessException is not expected:"+e);
        }
        //验证
        //Assertions.assertEquals(true,result.size()==userImportDtoList.size());
        //Assertions.assertEquals(true,result.get(0).getErrorMsg().startsWith("用户状态应为：启用或禁用"));
        //Assertions.fail("businessException is not expected:"+result.get(1).getErrorMsg());
        //Assertions.assertEquals(true,result.get(1).getErrorMsg().endsWith("导入数据发生异常"));
        //verify(excelWriterSheetBuilder,times(0)).doWrite(any(List.class));
    }
    @SuppressWarnings("unchecked")
    @Test
    @DisplayName("测试用户导入-上传文件_校验其他属性_异常")
    @Tag("@id:23604")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/6")
    void testImportUser_userDto_fail3() {
        List<UserImportDtoNew> result=new ArrayList<>();
        try(MockedStatic<CurrentUser> user=Mockito.mockStatic(CurrentUser.class);
            MockedStatic<EasyExcelFactory> easyExcelFactory=Mockito.mockStatic(EasyExcelFactory.class)){
            String createUserId="createUserId";
            when(CurrentUser.getUserId()).thenReturn(createUserId);
            when(userOrgMapper.queryOrgListByUserId(createUserId)).thenReturn("createOrgId");
            when(orgMapper.selectIdsByOrgNames(any(List.class))).thenReturn(null);
            when(roleMapper.selectIdsByRoleNames(any(List.class))).thenReturn("111");
            when(userOptMapper.selectOnlyJobNumber(anyString(),anyString())).thenReturn(1);
            when(userOptMapper.selectOnlyPhoneByJobNumber(anyString(),anyString())).thenReturn(0);
            //
            File file = ResourceUtils.getFile("classpath:importUser-fail3.xlsx");
            MultipartFile mulFile = new MockMultipartFile(
                    "importUser-fail3.xlsx", //文件名
                    "importUser-fail3.xlsx", //originalName 相当于上传文件在客户机上的文件名
                    ContentType.APPLICATION_OCTET_STREAM.toString(), //文件类型
                    new FileInputStream(file) //文件流
            );

            when(EasyExcelFactory.read(any(InputStream.class), any(), any(ExcelReadListener.class))).thenCallRealMethod();
            when(EasyExcelFactory.write(any(ServletOutputStream.class))).thenReturn(excelWriterBuilder);
            when(response.getOutputStream()).thenReturn(out);
            userImportNewService.importUserNew(mulFile, response);
        }catch (Exception e){
            Assertions.fail("businessException is not expected:"+e);
        }
        //验证
        //Assertions.assertEquals(true,result.get(0).getErrorMsg().startsWith("工号最大长度为7位"));
        //Assertions.assertEquals(true,result.get(1).getErrorMsg().startsWith("工号最大长度为7位,所属部门不能为空"));
        verify(userOptMapper,times(0)).addUserOrgs(any(List.class));
        verify(userOptMapper,times(0)).addUserRoles(any(List.class));
    }

    @SuppressWarnings("unchecked")
    @Test
    @DisplayName("测试用户导入-上传文件_成功_没有数据错误问题")
    @Tag("@id:23604")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/6")
    void testImportUser_userDto_success() {
         List<UserImportDtoNew> result=new ArrayList<>();
        try(MockedStatic<CurrentUser> user=Mockito.mockStatic(CurrentUser.class);
            MockedStatic<EasyExcelFactory> easyExcelFactory=Mockito.mockStatic(EasyExcelFactory.class)){
            String createUserId="createUserId";
            when(CurrentUser.getUserId()).thenReturn(createUserId);
            when(userOrgMapper.queryOrgListByUserId(createUserId)).thenReturn("createOrgId");
            when(orgMapper.selectIdsByOrgNames(any(List.class))).thenReturn("orgIds");
            when(roleMapper.selectIdsByRoleNames(any(List.class))).thenReturn("roleIds");
            when(userOptMapper.selectOnlyJobNumber(anyString(),anyString())).thenReturn(1);
            //
            File file = ResourceUtils.getFile("classpath:importUser-success.xlsx");
            MultipartFile mulFile = new MockMultipartFile(
                    "importUser-success.xlsx", //文件名
                    "importUser-success.xlsx", //originalName 相当于上传文件在客户机上的文件名
                    ContentType.APPLICATION_OCTET_STREAM.toString(), //文件类型
                    new FileInputStream(file) //文件流
            );
            when(EasyExcelFactory.read(any(InputStream.class), any(), any(ExcelReadListener.class))).thenCallRealMethod();
            when(EasyExcelFactory.write(any(ServletOutputStream.class))).thenReturn(excelWriterBuilder);
            when(response.getOutputStream()).thenReturn(out);
            userImportNewService.importUserNew(mulFile, response);
        }catch (Exception e){
            Assertions.fail("businessException is not expected:"+e);
        }
        //验证
        Assertions.assertEquals(true,result.size()==0);
        //Assertions.assertEquals(null,result.get(0).getErrorMsg().replace("null,", "").startsWith("组织机构"));
        //Assertions.assertEquals(true,result.get(1).getErrorMsg().replace("null,", "").startsWith("手机号长度为16位内的数字"));
        //verify(excelWriterSheetBuilder,times(0)).doWrite(any(List.class));
        //verify(userOptMapper,times(1)).addUserOrgs(any(List.class));
        //verify(userOptMapper,times(1)).addUserRoles(any(List.class));
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
