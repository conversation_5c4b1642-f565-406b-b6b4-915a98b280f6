# BBPF-Grafana代理模块技术架构图

## 系统整体架构图

```mermaid
graph TB
    subgraph "客户端层"
        A[Web浏览器]
        B[移动端App]
        C[第三方系统]
    end

    subgraph "负载均衡层"
        D[Nginx/HAProxy]
    end

    subgraph "BBPF-Grafana代理模块"
        E["🔐 单点登录模块<br/>JWT认证 | 用户验证"]
        F["🌐 Grafana接口代理模块<br/>HTTP代理 | 请求转发"]
        G["🛡️ SQL拦截模块<br/>查询拦截 | 权限过滤"]
        H["📝 动态模板变量注入模块<br/>变量注入 | 权限变量"]
        I["🚫 分享禁止模块<br/>分享控制 | 导出限制"]
    end

    subgraph "外部系统"
        J[BBPF权限系统]
        K[Redis缓存]
        L[MySQL数据库]
    end

    subgraph "目标系统"
        M[Grafana服务器]
        N[数据源<br/>MySQL/InfluxDB/Prometheus]
    end

    %% 连接关系
    A --> D
    B --> D
    C --> D
    D --> E
    
    E --> F
    F --> G
    G --> H
    H --> I
    I --> M
    
    E <--> J
    E <--> K
    G <--> L
    H <--> K
    I <--> L
    
    M --> N

    %% 样式定义
    classDef clientStyle fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef proxyStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef externalStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef targetStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px
    
    class A,B,C clientStyle
    class E,F,G,H,I proxyStyle
    class J,K,L externalStyle
    class M,N targetStyle
```

## 核心模块详细架构图

```mermaid
graph LR
    subgraph "🔐 单点登录模块"
        A1[JwtAuthenticationFilter]
        A2[BbpfAuthService]
        A3[GrafanaAuthService]
        A4[JwtUtil]
        A5[SecurityConfig]
        
        A1 --> A2
        A1 --> A4
        A2 --> A3
    end

    subgraph "🌐 Grafana接口代理模块"
        B1[GrafanaProxyController]
        B2[GrafanaProxyService]
        B3[StaticResourceController]
        B4[GrafanaApiClient]
        B5[ProxyStatistics]
        
        B1 --> B2
        B1 --> B5
        B2 --> B4
        B1 --> B3
    end

    subgraph "🛡️ SQL拦截模块"
        C1[SqlInterceptorFilter]
        C2[SqlParserService]
        C3[PermissionValidatorService]
        C4[QueryRewriteService]
        C5[DataPermissionService]
        
        C1 --> C2
        C2 --> C3
        C3 --> C4
        C4 --> C5
    end

    subgraph "📝 动态模板变量注入模块"
        D1[VariableInjectionFilter]
        D2[TemplateVariableService]
        D3[PermissionVariableGenerator]
        D4[VariableCache]
        
        D1 --> D2
        D2 --> D3
        D3 --> D4
    end

    subgraph "🚫 分享禁止模块"
        E1[SharePermissionController]
        E2[SharePermissionService]
        E3[ExportPermissionService]
        E4[SharePolicyEngine]
        E5[AuditService]
        
        E1 --> E2
        E2 --> E3
        E2 --> E4
        E2 --> E5
    end

    %% 模块间交互
    A1 -.-> B1
    B2 -.-> C1
    C4 -.-> D1
    D2 -.-> E1
```

## 技术栈架构图

```mermaid
graph TB
    subgraph "前端技术栈"
        F1["HTML5/CSS3/JavaScript"]
        F2["React/Vue.js"]
        F3["Grafana Dashboard"]
    end

    subgraph "后端技术栈"
        B1["Spring Boot 2.7.x"]
        B2["Spring Security 5.7.x"]
        B3["Spring Web MVC"]
        B4["Apache HttpClient"]
    end

    subgraph "中间件技术栈"
        M1["Redis 6.x (缓存)"]
        M2["Nginx (负载均衡)"]
        M3["JWT (认证)"]
    end

    subgraph "数据库技术栈"
        D1["MySQL 8.0 (主数据库)"]
        D2["InfluxDB (时序数据)"]
        D3["Prometheus (监控数据)"]
    end

    subgraph "监控与运维"
        O1["Spring Boot Actuator"]
        O2["Micrometer Metrics"]
        O3["Logback (日志)"]
        O4["Docker (容器化)"]
    end

    subgraph "开发工具"
        T1["Maven (构建工具)"]
        T2["JUnit 5 (单元测试)"]
        T3["Mockito (Mock测试)"]
        T4["SonarQube (代码质量)"]
    end

    F1 --> B1
    F2 --> B1
    F3 --> B1
    
    B1 --> M1
    B2 --> M3
    B3 --> M2
    
    B1 --> D1
    B1 --> D2
    B1 --> D3
    
    B1 --> O1
    O1 --> O2
    B1 --> O3
    
    classDef frontend fill:#e3f2fd,stroke:#1976d2
    classDef backend fill:#f3e5f5,stroke:#7b1fa2
    classDef middleware fill:#e8f5e8,stroke:#388e3c
    classDef database fill:#fff3e0,stroke:#f57c00
    classDef ops fill:#fce4ec,stroke:#c2185b
    classDef tools fill:#f1f8e9,stroke:#689f38
    
    class F1,F2,F3 frontend
    class B1,B2,B3,B4 backend
    class M1,M2,M3 middleware
    class D1,D2,D3 database
    class O1,O2,O3,O4 ops
    class T1,T2,T3,T4 tools
```

## 数据流架构图

```mermaid
flowchart TD
    A["👤 用户请求"] --> B["🔐 JWT认证"]
    B --> C{"认证成功?"}
    C -->|否| D["❌ 返回401错误"]
    C -->|是| E["🌐 代理请求"]
    
    E --> F["🛡️ SQL拦截"]
    F --> G{"权限检查"}
    G -->|拒绝| H["❌ 返回403错误"]
    G -->|通过| I["📝 变量注入"]
    
    I --> J["🚫 分享控制"]
    J --> K{"分享权限"}
    K -->|拒绝| L["❌ 分享被禁止"]
    K -->|允许| M["📊 转发到Grafana"]
    
    M --> N["📈 Grafana处理"]
    N --> O["📋 返回结果"]
    O --> P["✅ 响应用户"]
    
    %% 缓存流程
    B -.-> Q["💾 Redis缓存"]
    I -.-> Q
    J -.-> R["🗄️ MySQL数据库"]
    
    %% 审计日志
    B -.-> S["📝 审计日志"]
    G -.-> S
    K -.-> S
    
    classDef success fill:#c8e6c9,stroke:#4caf50
    classDef error fill:#ffcdd2,stroke:#f44336
    classDef process fill:#e1f5fe,stroke:#2196f3
    classDef storage fill:#fff3e0,stroke:#ff9800
    
    class P,O success
    class D,H,L error
    class A,B,E,F,I,J,M,N process
    class Q,R,S storage
```

## 部署架构图

```mermaid
graph TB
    subgraph "生产环境"
        subgraph "Web层"
            W1["Nginx-1<br/>负载均衡"]
            W2["Nginx-2<br/>负载均衡"]
        end
        
        subgraph "应用层"
            A1["BBPF-Proxy-1<br/>8080端口"]
            A2["BBPF-Proxy-2<br/>8080端口"]
            A3["BBPF-Proxy-3<br/>8080端口"]
        end
        
        subgraph "服务层"
            S1["Grafana-1<br/>3000端口"]
            S2["Grafana-2<br/>3000端口"]
        end
        
        subgraph "数据层"
            D1["MySQL主库<br/>3306端口"]
            D2["MySQL从库<br/>3306端口"]
            D3["Redis集群<br/>6379端口"]
        end
    end
    
    subgraph "外部系统"
        E1["BBPF权限系统"]
        E2["监控数据源"]
    end
    
    %% 连接关系
    W1 --> A1
    W1 --> A2
    W2 --> A2
    W2 --> A3
    
    A1 --> S1
    A2 --> S1
    A2 --> S2
    A3 --> S2
    
    A1 --> D1
    A2 --> D1
    A3 --> D2
    
    A1 --> D3
    A2 --> D3
    A3 --> D3
    
    A1 <--> E1
    A2 <--> E1
    A3 <--> E1
    
    S1 --> E2
    S2 --> E2
    
    classDef web fill:#e3f2fd,stroke:#1976d2
    classDef app fill:#f3e5f5,stroke:#7b1fa2
    classDef service fill:#e8f5e8,stroke:#388e3c
    classDef data fill:#fff3e0,stroke:#f57c00
    classDef external fill:#fce4ec,stroke:#c2185b
    
    class W1,W2 web
    class A1,A2,A3 app
    class S1,S2 service
    class D1,D2,D3 data
    class E1,E2 external
```

## 安全架构图

```mermaid
graph TB
    subgraph "安全防护层"
        S1["🔥 防火墙"]
        S2["🛡️ WAF应用防火墙"]
        S3["🔒 SSL/TLS加密"]
    end
    
    subgraph "认证授权层"
        A1["🎫 JWT Token认证"]
        A2["👤 BBPF用户认证"]
        A3["🔑 权限验证"]
        A4["📋 角色管理"]
    end
    
    subgraph "数据安全层"
        D1["🔐 数据加密"]
        D2["🛡️ SQL注入防护"]
        D3["🚫 数据权限控制"]
        D4["📝 操作审计"]
    end
    
    subgraph "网络安全层"
        N1["🌐 VPC网络隔离"]
        N2["🔒 内网访问控制"]
        N3["📊 流量监控"]
    end
    
    subgraph "应用安全层"
        AP1["⚡ 限流防护"]
        AP2["🚫 CSRF防护"]
        AP3["🔒 XSS防护"]
        AP4["📋 输入验证"]
    end
    
    %% 安全流程
    S1 --> S2
    S2 --> S3
    S3 --> A1
    A1 --> A2
    A2 --> A3
    A3 --> A4
    A4 --> D1
    D1 --> D2
    D2 --> D3
    D3 --> D4
    
    %% 横向关联
    A1 -.-> AP1
    A3 -.-> N2
    D3 -.-> N1
    D4 -.-> N3
    
    classDef security fill:#ffebee,stroke:#d32f2f
    classDef auth fill:#e8f5e8,stroke:#4caf50
    classDef data fill:#e3f2fd,stroke:#2196f3
    classDef network fill:#fff3e0,stroke:#ff9800
    classDef app fill:#f3e5f5,stroke:#9c27b0
    
    class S1,S2,S3 security
    class A1,A2,A3,A4 auth
    class D1,D2,D3,D4 data
    class N1,N2,N3 network
    class AP1,AP2,AP3,AP4 app
```

## 性能监控架构图

```mermaid
graph LR
    subgraph "应用监控"
        M1["📊 Spring Boot Actuator"]
        M2["📈 Micrometer Metrics"]
        M3["⏱️ 响应时间监控"]
        M4["🔢 并发量监控"]
    end
    
    subgraph "系统监控"
        S1["💻 CPU使用率"]
        S2["💾 内存使用率"]
        S3["💿 磁盘I/O"]
        S4["🌐 网络流量"]
    end
    
    subgraph "业务监控"
        B1["👤 用户访问量"]
        B2["🔐 认证成功率"]
        B3["🛡️ SQL拦截统计"]
        B4["📝 变量注入统计"]
        B5["🚫 分享控制统计"]
    end
    
    subgraph "告警系统"
        A1["📧 邮件告警"]
        A2["📱 短信告警"]
        A3["🔔 钉钉告警"]
        A4["📋 告警规则引擎"]
    end
    
    subgraph "日志系统"
        L1["📝 应用日志"]
        L2["🔍 访问日志"]
        L3["⚠️ 错误日志"]
        L4["🔒 安全日志"]
    end
    
    %% 监控流程
    M1 --> A4
    M2 --> A4
    M3 --> A4
    M4 --> A4
    
    S1 --> A4
    S2 --> A4
    S3 --> A4
    S4 --> A4
    
    B1 --> A4
    B2 --> A4
    B3 --> A4
    B4 --> A4
    B5 --> A4
    
    A4 --> A1
    A4 --> A2
    A4 --> A3
    
    M1 -.-> L1
    B2 -.-> L2
    A4 -.-> L3
    B2 -.-> L4
    
    classDef monitor fill:#e8f5e8,stroke:#4caf50
    classDef system fill:#e3f2fd,stroke:#2196f3
    classDef business fill:#fff3e0,stroke:#ff9800
    classDef alert fill:#ffebee,stroke:#f44336
    classDef log fill:#f3e5f5,stroke:#9c27b0
    
    class M1,M2,M3,M4 monitor
    class S1,S2,S3,S4 system
    class B1,B2,B3,B4,B5 business
    class A1,A2,A3,A4 alert
    class L1,L2,L3,L4 log
```

---

## 架构说明

### 1. 系统整体架构
- **分层设计**: 采用经典的分层架构，从客户端到目标系统层层递进
- **模块化**: 五大核心模块独立设计，职责清晰
- **高可用**: 支持负载均衡和集群部署

### 2. 核心模块架构
- **单点登录模块**: 负责用户认证和JWT管理
- **代理模块**: 核心HTTP代理功能
- **SQL拦截模块**: 数据权限控制
- **变量注入模块**: 动态权限变量管理
- **分享控制模块**: 数据分享和导出控制

### 3. 技术栈选择
- **后端**: Spring Boot生态，成熟稳定
- **缓存**: Redis集群，高性能
- **数据库**: MySQL主从，保证数据一致性
- **监控**: 完整的监控体系

### 4. 安全架构
- **多层防护**: 从网络到应用的全方位安全
- **权限控制**: 细粒度的数据权限管理
- **审计追踪**: 完整的操作审计日志

### 5. 性能监控
- **全方位监控**: 应用、系统、业务三个维度
- **智能告警**: 基于规则引擎的告警系统
- **日志管理**: 分类管理各种日志

这个技术架构图展示了BBPF-Grafana代理模块的完整技术架构，包括系统架构、模块设计、技术栈、部署方案、安全设计和监控体系，为系统的设计、开发、部署和运维提供了全面的指导。