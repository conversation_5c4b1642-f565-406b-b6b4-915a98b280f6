package com.snbc.bbpf.bus.system.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.Gson;
import com.snbc.bbpf.bus.system.config.WeixinConfig;
import com.snbc.bbpf.bus.system.constans.ThirdHandlerType;
import com.snbc.bbpf.bus.system.enums.BusTemplateCodeEnum;
import com.snbc.bbpf.bus.system.enums.ThirdBindEnum;
import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.bus.system.service.ThirdLoginBind;
import com.snbc.bbpf.system.db.common.dto.TUserwxInfo;
import com.snbc.bbpf.system.db.common.dto.UserBindMsgDto;
import com.snbc.bbpf.system.db.common.entity.Message;
import com.snbc.bbpf.system.db.common.mapper.TUserwxInfoMapper;
import com.snbc.bbpf.system.db.common.mapper.UserLoginMapper;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 微信第三方相关活动
 */
@Service
@Getter
@Slf4j
@ThirdHandlerType(ThirdBindEnum.WEIXIN)
public class WeiXinServiceUtil extends ServiceImpl<TUserwxInfoMapper, TUserwxInfo> implements ThirdLoginBind {
    public static final int SUCCSS = 200;
    //dessKey配置
     /**
     * 微信配置类
     */
    @Autowired
    private WeixinConfig weixinConfig;
    /**
     * Redis 缓存
     */
    @Autowired
    private RedisCheckCodeLoginFlag redisCheckCodeLoginFlag;

    @Autowired
    private UserLoginMapper userLoginMapper;
    /**
     * 解除用户绑定
     * @param userId 对应的唯一ID
     * @return
     * @throws Exception
     */
    @Override
    public String delUserThirdBind(String userId) throws Exception{
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("user_id",userId);
        if(! remove(wrapper)){
            throw new BusinessException(ErrorMessage.USER_HAS_UNBIND);
        }
        //修改主表的用户绑定状态
        String bindState=userLoginMapper.selectUserBindByid(userId);
        if(StringUtils.isEmpty(bindState)){
            bindState="00000000";
        }else {
            bindState="0"+bindState.substring(1);
        }
        userLoginMapper.updateUserBindById(userId,bindState);
        sendMsg(true,userId,false);
        return "ok";
    }


    /**
     * 获得用户登录Code
     * 1,bindID 内容含bind 为绑定CODE ，对应的REDIS 为 用户ID
     * 2，bindID 内容含login 为登录CODE ，对应的REDIS 为 返回值
     * @param bindId 该State 唯一的编码替换state
     * @return
     * @throws Exception
     */
    @Override
    public String getUserLoginQRcode(String bindId) throws Exception {
        //给用户授权地址(用户扫的二维码)设置参数
        String qrCode=String.format(weixinConfig.getUserAuthUrl(),
                weixinConfig.getAppid(), URLEncoder.encode(weixinConfig.getRedirectUrl(), "GBK"), bindId);
        log.debug(qrCode);
        return qrCode;
    }

    /**
     * 处理 绑定成功发送系统通知
     * @param code code是作为换取access_token的票据
     */
    @Override
    public String dealCallback(String code, String state) throws Exception{
        //1.通过code换取网页授权access_token(访问令牌)
        String result=gethttpResult(String.format(weixinConfig.getGetAccessTokenUrl(),weixinConfig.getAppid(),
                weixinConfig.getAppsecret(),code));
        if(StringUtils.isEmpty(result)){
            log.warn("dealCallback code={},state={} output is null",code,state);
            throw new BusinessException(ErrorMessage.FAILED);
        }
        JSONObject json = JSON.parseObject(result);
        //判断微信用户是否已经被绑定
        String openid= json.getString("openid");

        log.info("dealCallback code={},state={} output ={}",code,state,result);
        //BIND绑定逻辑。2微信逻辑
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("user_openid",openid);
        String userId=redisCheckCodeLoginFlag.getRedis(state);
        if(StringUtils.isEmpty(userId)){
            log.warn("dealCallback code={},state={} qrcode timeout",code,state);
            throw new BusinessException(ErrorMessage.USER_BIND_TIMEOUT);
        }
        TUserwxInfo tUserwxInfo=getOne(wrapper);
        if(state.startsWith("BIND")){
            if(null!=tUserwxInfo){
                log.warn("dealCallback code={},state={} OPENID={} BINDUSERID={}",code,state,openid,tUserwxInfo);
                redisCheckCodeLoginFlag.insertRedis(state,ErrorMessage.USER_BIND_ENABLE_BIND.getCode());
                throw new BusinessException(ErrorMessage.USER_BIND_ENABLE_BIND);
            }
            tUserwxInfo=TUserwxInfo.builder().thirdId(UUID.randomUUID().toString()).createTime(LocalDateTime.now())
                    .userId(userId).userOpenid(openid).build();
            save(tUserwxInfo);
            //修改主表的用户绑定状态
            String bindState=userLoginMapper.selectUserBindByid(userId);
            if(StringUtils.isEmpty(bindState)){
                bindState="10000000";
            }else {
                bindState="1"+bindState.substring(1);
            }
            userLoginMapper.updateUserBindById(userId,bindState);
            updateUserImage(userId,openid,json.getString("access_token"));
            redisCheckCodeLoginFlag.insertRedis(state,ErrorMessage.SUCCESS.getCode());
            //发送系统消息通知
            sendMsg(false,userId,true);
            log.info("bind user ok code={},state={} OPENID={} userId={}",code,state,openid,userId);
        }else{
            if(null==tUserwxInfo){
                log.warn("dealCallback code={},state={} OPENID={} BINDUSERID={}",code,state,openid,tUserwxInfo);
                redisCheckCodeLoginFlag.insertRedis(state,ErrorMessage.USER_LOGIN_NOT_BIND.getCode());
                throw new BusinessException(ErrorMessage.USER_LOGIN_NOT_BIND);
            }
            redisCheckCodeLoginFlag.insertRedis(state,tUserwxInfo.getUserId());
            log.info("login user ok code={},state={} OPENID={} userId={}",code,state,openid);
        }
        return weixinConfig.getGetSuccesUrl();

    }

    /**
     * 修改用户頭像
     * @param userId
     * @param openId
     * @param accessToken
     */
    private void updateUserImage(String userId,String openId,String accessToken ){
        //修改主表的用户绑定状态
        String userImage=userLoginMapper.selectUserImageByid(userId);
        if(StringUtils.isEmpty(userImage)){
            //给拉取微信用户信息的请求地址设置参数
            String getUserInfoUrl = String.format(weixinConfig.getGetUserInfoUrl(),accessToken,openId);
            //发起get请求,并返回结果
            String result=gethttpResult(getUserInfoUrl);
            if(StringUtils.isEmpty(result)){
                log.warn("updateUserImage openId={},openId={} output is null",openId,openId);
                throw new BusinessException(ErrorMessage.FAILED);
            }
            JSONObject json = JSON.parseObject(result);
            //判断微信用户是否已经被绑定
            userLoginMapper.updateUserImageById(userId,json.getString("headimgurl"));
        }
    }
    /**
     * 发送用户消息【尊敬的 ×× 用户，您的微信账号已与 ×× 系统完成绑定，请知悉。】
     * @param sendShort 是否发送短信
     * @param userId 发送用户ID
     * @param flag true 绑定平台，false 解绑
     */
    private void sendMsg(boolean sendShort,String userId,boolean flag){
        //1 通过userID 获取当前用户的 名称，租户ID，然后通过租户ID 获取缓存的名字
        try {
            UserBindMsgDto userBindMsgDto= userLoginMapper.selectUserMsgbyUserId(userId);
            if (null==userBindMsgDto) {
                log.warn("sendMsg ERROR userId={} no user", userId);
                throw new BusinessException(ErrorMessage.FAILED);
            }
            String[] receiveUserIdArr = {userId};
            String templateCode= BusTemplateCodeEnum.USER_UNBIND_MSG.getName();
            if(flag) {
                //绑定
                templateCode= BusTemplateCodeEnum.USER_BIND_MSG.getName();
            }
            String msgTitle=BusTemplateCodeEnum.getTitleByCode(templateCode);
            Message message = Message.builder().receiveNos(receiveUserIdArr)
                    .templateCode(templateCode)
                    .templateParamJsonArr(convertTemplateParamJsonArr(userBindMsgDto.getUserName()))
                    .msgTitle(msgTitle).senderId(CurrentUser.getUserId()).build();
            //发送系统通知
            String[] userIdArr = {userId};
            message.setReceiveNos(userIdArr);
            SendMsgUtil.sendSysMessage(message);
            if (sendShort) {
                //解码用户手机
                String phoneKey = BossDES3Util.decrypt(userBindMsgDto.getUserPhone());
                String[] receivePhoneArr={phoneKey};
                message.setReceiverPhone(receivePhoneArr);
                CommonResp commonResp = SendMsgUtil.sendShortMessage(message);
                // 判断是否发送成功
                if (!ErrorMessage.SUCCESS.getCode().equals(commonResp.getHead().getCode())) {
                    log.warn("sendMsg ERROR userId={} msgTitle={} no user resp={}", userId, msgTitle,commonResp);
                }
            }
        }catch (Exception ex){
            log.warn("sendMsg ERROR userId={} no user ex={}", userId,ex);
            throw new BusinessException(ErrorMessage.FAILED);
        }
    }
    /**
     * 转换模板参数
     * @param userName 用户名
     * @return
     */
    private static String[] convertTemplateParamJsonArr(String userName){
        Map<String,String> templateParamMap=new HashMap<>();
        templateParamMap.put("userName", userName);
        templateParamMap.put("platform", "微信");
        templateParamMap.put("systemName", "BOSS管理平台");
        return new String[]{new Gson().toJson(templateParamMap)};
    }

    /**
     * 使用HTTP 访问
     * @param url
     * @return
     */
    public String gethttpResult(String url) {
        String resultData="";
        try {
            // 2.创建连接与设置连接参数
            CloseableHttpClient httpClient = HttpClientBuilder.create().build();
            HttpGet httpGet = new HttpGet(url);
            // 3.发起请求与接收返回值
            HttpResponse response = httpClient.execute(httpGet);
            if (response.getStatusLine().getStatusCode() != SUCCSS) {
                log.warn("gethttpResult end code={} get url={}",response.getStatusLine().getStatusCode(), url);
                throw new BusinessException(ErrorMessage.FAILED);
            }
            HttpEntity res = response.getEntity();
            resultData = EntityUtils.toString(res);
            log.info("gethttpResult end result={} get url={}",resultData, url);
            // 4.关闭连接
            httpClient.close();
        }catch (Exception ex){
            log.error("gethttpResult EX={} get url={}",ex, url);
        }
        return resultData;
    }
}
