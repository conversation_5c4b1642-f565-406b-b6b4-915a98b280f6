/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service.impl;

import com.snbc.bbpf.bus.system.config.LDAPConfig;
import com.snbc.bbpf.bus.system.config.LoginConfig;
import com.snbc.bbpf.bus.system.constans.CommonConstant;
import com.snbc.bbpf.bus.system.dto.TokenPayload;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.handler.UserLoginFailHandler;
import com.snbc.bbpf.bus.system.mapstruct.ClassConvertorMapper;
import com.snbc.bbpf.bus.system.queue.BusLogQueue;
import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.bus.system.service.UserLoginService;
import com.snbc.bbpf.bus.system.strategy.LoginContext;
import com.snbc.bbpf.bus.system.utils.BossDES3Util;
import com.snbc.bbpf.bus.system.utils.JasyptEncryptUtil;
import com.snbc.bbpf.bus.system.utils.JwtTokenUtil;
import com.snbc.bbpf.bus.system.utils.RedisCheckCodeLoginFlag;
import com.snbc.bbpf.bus.system.utils.ResultUtil;
import com.snbc.bbpf.buslog.entity.BusLog;
import com.snbc.bbpf.component.captcha.model.common.NumConstant;
import com.snbc.bbpf.component.captcha.model.vo.CaptchaVO;
import com.snbc.bbpf.component.captcha.service.CaptchaService;
import com.snbc.bbpf.component.captcha.service.PwdErrorCheckService;
import com.snbc.bbpf.system.db.common.entity.Permission;
import com.snbc.bbpf.system.db.common.entity.PermissionNode;
import com.snbc.bbpf.system.db.common.entity.User;
import com.snbc.bbpf.system.db.common.entity.login.LoginUser;
import com.snbc.bbpf.system.db.common.entity.login.ReturnUser;
import com.snbc.bbpf.system.db.common.mapper.NavigationMapper;
import com.snbc.bbpf.system.db.common.mapper.UserLoginMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * @ClassName: UserLoginService
 * @Description: 用户登录业务具体处理
 * @module: si-bbpf-system
 * @Author: liangjb
 * @date: 2021/5/15
 *        Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all
 *        rights reserved.
 */
@Service
@Slf4j
public class UserLoginServiceImpl implements UserLoginService {
	/**
	 * 常量判断条件
	 */
	private static final Integer ISLOCK = 1;

	private static final String SMSCODELOGIN = "1";

	/**
	 * Redis 缓存
	 */
	@Autowired
	private RedisCheckCodeLoginFlag redisCheckCodeLoginFlag;
	/**
	 * JWT token
	 */
	@Autowired
	private JwtTokenUtil jwtTokenUtil;

	/**
	 * 验证码工具类
	 */
	@Autowired
	private CaptchaService captchaService;

	@Autowired
	private PwdErrorCheckService pwdErrorCheckService;
	/**
	 * 用户注册登录数据
	 */
	@Autowired
	private UserLoginMapper userLoginMapper;

	@Autowired
	private NavigationMapper navigationMapper;

	@Autowired
	private LoginConfig loginConfig;

	@Autowired
	private LDAPConfig ldapConfig;

	/**
	 * 用户登录验证方法
	 *
	 * @param loginUser     登录用户信息
	 * @param requestSource 请求来源
	 * @param ip            用户IP
	 * @return CommonResp<ReturnUser> 返回用户信息及状态码
	 * @throws Exception 抛出异常
	 */
	@Override
	public CommonResp verifyUserPwd(LoginUser loginUser, String requestSource, String ip) throws Exception {
		if (StringUtils.isBlank(loginUser.getUserPwd()) || StringUtils.isBlank(loginUser.getUserName())) {
			// 当0为密码手机号登录时，1为验证码手机号登录
			ErrorMessage errorMsg = SMSCODELOGIN.equals(loginUser.getLoginType()) ? ErrorMessage.PHONE_OR_CODE_ERROR
					: ErrorMessage.USERNAME_OR_PASSWORD_ERROR;
			return CommonResp.builder().head(ResultUtil.error(errorMsg)).build();
		}
		// 验证验证码是否正确
		if (!verifyCaptcha(loginUser)) {
			return CommonResp.builder().head(ResultUtil.error(ErrorMessage.CHECK_CODE_ERROR)).build();
		}
		// 获取用户
		User user = getUserByLoginName(loginUser.getLoginType(), loginUser.getUserName());
		// 如果用户不存在则返回错误信息
		if (null == user) {
			// 当0为密码手机号登录时，1为验证码手机号登录
			ErrorMessage errorMsg = SMSCODELOGIN.equals(loginUser.getLoginType()) ? ErrorMessage.PHONE_OR_CODE_ERROR
					: ErrorMessage.USERNAME_OR_PASSWORD_ERROR;
			return CommonResp.builder().head(ResultUtil.error(errorMsg)).build();
		}
		// 查询该用户的登录状态
		UserLoginFailHandler.queryUserLoginStatus(user);
		// 判断用户是否已锁定
		if (!ISLOCK.equals(user.getHasLock())) {
			return CommonResp.builder().head(ResultUtil.error(ErrorMessage.USE_FREEZE)).build();
		}
		// 根据配置执行具体的登录验证操作，并将结果保存在loginResult变量中
		boolean loginResult = new LoginContext(user.getIsLdap(), loginConfig.getType(), loginUser.getLoginType(),
				ldapConfig).executeVerify(loginUser, user);
		// 如果验证成功
		if (loginResult) {
			// 记录用户id到MDC中
			MDC.put("userId", user.getUserId());
			// 处理密码正确的情况
			pwdErrorCheckService.dealPwdPass(loginUser.getUserName());
			// 处理登录成功后的操作并返回结果
			return loginSuccess(user, loginUser, ip, requestSource, Integer.parseInt(loginUser.getLoginType()));
		} else {
			// 如果验证失败，则处理密码错误的情况，并将该用户的登录失败次数加一，然后返回错误信息
			pwdErrorCheckService.dealPwdError(loginUser.getUserName());
			UserLoginFailHandler.modifyLoginFailCount(user, 0);
			return CommonResp.builder().head(ResultUtil.error(ErrorMessage.USERNAME_OR_PASSWORD_ERROR)).build();
		}
	}

	/***
	 * @Description: 根据登录页的用户名获取用户
	 * @Author: WangSong
	 * @param : loginType
	 * @param : userName
	 * @return: com.snbc.bbpf.system.db.common.entity.User
	 * @CreateDate: 2023/5/17 19:23
	 * @UpdateDate: 2023/5/17 19:23
	 *              Copyright© Shandong New Beiyang Information Technology Co., Ltd.
	 *              . all rights reserved.
	 */
	private User getUserByLoginName(String loginType, String userName) {
		if (loginType.equals(String.valueOf(NumConstant.ONE))) {
			// 手机号加密
			String encryptPhone = BossDES3Util.encrypt(userName);
			return userLoginMapper.selectUserByPhone(encryptPhone);
		} else {
			// 根据用户名查询对应用户
			return userLoginMapper.selectUserByUserName(userName);
		}
	}

	private CommonResp<ReturnUser> loginSuccess(User user, LoginUser loginUser, String ip, String requestSource,
			int loginType) throws IOException {
		// 密码正确就算登陆成功,清空登录失败次数
		UserLoginFailHandler.loginSuccessReset(user.getUserId());
		// 验证一个账号是否多人登录
		ifMultiportLogin(loginUser, user);
		// 进行类转换
		ReturnUser returnUser = ClassConvertorMapper.INSTANCE.userConvertReturnUser(user);
		// 获取角色ID列表
		List<String> roleIds = userLoginMapper.selectUserRoleIdsByUserId(user.getUserId());
		String sessionId = UUID.randomUUID().toString();
		// 获取TOKEN
		String token = jwtTokenUtil.generateToken(TokenPayload.builder()
				.userId(user.getUserId())
				.userName(user.getUserName())
				.password(user.getUserPwd())
				.roleIds(roleIds)
				.sysType(loginUser.getSysType())
				.sessionId(sessionId)
				.tenantId("")
				.build());
		returnUser.setToken(token);
		// 校验初始密码及密码时效
		checkOriginPwdAndAging(returnUser, user);
		CommonResp<ReturnUser> callResponse = CommonResp.<ReturnUser>builder().head(ResultUtil.success())
				.body(returnUser).build();
		// redis中保存登录状态
		redisCheckCodeLoginFlag.userLoginStatus(user, ip, loginUser.getSysType(), token, sessionId);
		BusLogQueue.getInstance().addLog(BusLog.builder().logId(UUID.randomUUID().toString())
				.logType("userLogin").logTarget("login").createTime(LocalDateTime.now())
				.enContent(enContent(loginType)).zhContent(zhContent(loginType)).ip(ip)
				.userId(user.getUserId()).userName(user.getUserName()).requestSource(requestSource).build());
		return callResponse;
	}

	private String zhContent(int loginType) {
		if (loginType == NumConstant.ZERO) {
			return "［账号密码］登录成功";
		}
		return "［手机验证码］登录成功";
	}

	private String enContent(int loginType) {
		if (loginType == NumConstant.ZERO) {
			return "[Account password] login succeeded";
		}
		return "[Mobile phone verification code] login succeeded";
	}

	private void checkOriginPwdAndAging(ReturnUser returnUser, User user) {
		// 验证是否初始化密码
		String defaultPwd = JasyptEncryptUtil.desencrypt(loginConfig.getDefaultPassword(), CommonConstant.KEY_IV);
		if (defaultPwd == null) {
			defaultPwd = CommonConstant.DEFAULT_P_NAME;
		}
		String password = JasyptEncryptUtil.desencrypt(user.getUserPwd(), CommonConstant.KEY_IV);
		if (defaultPwd.equals(password) && "default".equals(loginConfig.getGenerateType())
				&& user.getIsLdap().equals(NumConstant.ZERO)) {
			returnUser.setIsOriginPwd("true");
		} else {
			returnUser.setIsOriginPwd("false");
		}
		// 验证密码是否过期
		LocalDateTime now = LocalDateTime.now();
		LocalDateTime beforeDay = now.minusDays(loginConfig.getPwdExpireDay());
		returnUser.setIsPwdExpired(null != user.getUpdatePwdTime() && user.getUpdatePwdTime().isBefore(beforeDay));
	}

	/***
	 * @Description: 获取用户权限的树
	 * @Author: LiangJb
	 * @param : userId 用户ID
	 * @param : sysType 系统类型如前端或WEB，SYST
	 * @param : langKey 编号
	 * @param : permissionType 权限类型，比如MENU,FUN
	 * @CreateDate: 2020/6/15 15:53
	 * @UpdateUser: LiangJb
	 * @UpdateDate: 2020/6/15 15:53
	 * @return : com.snbc.bbpf.common.config.CallResponse
	 */
	@Override
	public List<PermissionNode> getUserPermissionsByUserId(String userId, String sysType, String langKey,
			String permissionType) {
		try {
			// 获取用户有权限的菜单
			List<Permission> perList = userLoginMapper.selectUserRolesByUserId(userId, sysType, permissionType, null);
			// 按钮权限返回LIST
			if (CommonConstant.THREE.equals(permissionType)) {
				List<PermissionNode> result = new ArrayList<>();
				if (!perList.isEmpty() && perList.size() > CommonConstant.ZERO) {
					perList.forEach(ej ->
					// 从缓存里获取值
					result.add(ClassConvertorMapper.INSTANCE.convertPermissionNode(ej)));
					return result;
				}
			} else {
				// 获取用户所有的菜单权限
				List<Permission> perListall = userLoginMapper.selectAllRolesByType(sysType, permissionType);
				return getOrderLstLevel(perListall, perList, loginConfig.getMenuParentid());
			}
		} catch (Exception ex) {
			log.error("Description Obtaining the user permission tree fails,userId={},permissionType={}，sysType={}",
					userId, permissionType, sysType, ex);
		}
		return new ArrayList<>();
	}

	@Override
	public List<PermissionNode> getUserNavPermissionsByUserId(String userId, String sysType, String langKey,
			String permissionType, String navId, List<String> roleIds) {
		try {
			// 获取用户有权限的菜单
			List<Permission> perList = userLoginMapper.selectUserRolesByUserId(userId, sysType, permissionType,
					roleIds);

			// 按钮权限返回LIST
			if (CommonConstant.THREE.equals(permissionType)) {
				if (!CollectionUtils.isEmpty(perList)) {
					// 从缓存里获取值
					return perList.stream().distinct()
							.map(ej -> ClassConvertorMapper.INSTANCE.convertPermissionNode(ej))
							.collect(Collectors.toList());
				}
			} else {
				// 获取顶部菜单权限
				if (StringUtils.isNotBlank(navId)) {
					List<String> permissionIds = navigationMapper.selectRelationship(navId);
					perList = perList.stream().filter(item -> permissionIds.contains(item.getPermissionId())).distinct()
							.collect(Collectors.toList());
				}
				// 获取所有的菜单权限
				List<Permission> perListAll = userLoginMapper.selectAllRolesByType(sysType, permissionType);
				return getOrderLstLevel(perListAll, perList, loginConfig.getMenuParentid());
			}
		} catch (Exception ex) {
			log.error("Description Obtaining the user permission tree fails,userId={},permissionType={}，sysType={}",
					userId, permissionType, sysType, ex);
		}
		return new ArrayList<>();
	}

	/***
	 * @Description: 根据参数将LIST 转成权限树 的LIST，
	 *               主要步骤，1，将LIST 赋值到MAP 里方便查找
	 *               2， 将要进行转换的权限追溯根节点的权限节点，本方案不采用递归直接用权限附带的路径
	 *               3，先根据总权限表进行对比 子权限所包含的节点进行遍历，如果满足父级节点就放入列表进行初始化 同时进行更新MAP
	 *               4，依次对各个节点进行判断依据是父节点是否是当前的节点，因为排序是从根目录往下排，
	 *               所以这个可以满足一次循环并能插入到其父级节点，并更新总的MAP值
	 * @Author: LiangJb
	 * @param : items 所有相关权限的集合
	 * @param : curitems 当前权限数据的集合，要通过这个集合去追随父级的ID
	 * @param : level 父级编号的ID，但不包含此父级ID
	 * @CreateDate: 2020/6/15 15:53
	 * @return : List<PermissionNode>
	 */
	public List<PermissionNode> getOrderLstLevel(Iterable<Permission> items, Iterable<Permission> curItems,
			String level) {
		List<PermissionNode> result = new ArrayList<>();
		// 全局的缓存的对象
		Map<String, PermissionNode> collect = new HashMap<>();
		// 快速获取权限对象集合
		Map<String, Permission> collectB = new HashMap<>();
		/// 初始化全局的对象集合
		items.forEach(e -> {
			if (!collectB.containsKey(e.getPermissionId())) {
				collectB.put(e.getPermissionId(), e);
			}
		});
		// 获取所有子节点相关父节点关系
		List<String> allParenList = new ArrayList<>();
		/// 存入map item，获取要排序的当前所关联的父级节点有两种方案，1采用递归获取，2根据每个节点自带的路径进行解析
		curItems.forEach(ei ->
		// allParenList.addAll(getAllParentIdList(collectB,e.getPermissionId(),level));去掉一个路径的分隔
		allParenList.addAll(Arrays.asList(
				ei.getPermissionPath().substring(CommonConstant.ONE).split(CommonConstant.PERMISSION_FILTER_CHAR))));
		/// 开始根据情况进行处理,如果父节点为为LEVEL 则进行初始化添加
		items.forEach(ej -> {
			// 去重跟ID判断是否在要排序的根节点下
			if (allParenList.contains(ej.getPermissionId())) {
				// 当其父节点等于，筛选值的时候，开始初始化如果要包含父节点，则把条件设置为当前节点
				if (level.equals(ej.getParentId()) && !collect.containsKey(ej.getPermissionId())) {
					PermissionNode resultNode = ClassConvertorMapper.INSTANCE.convertPermissionNode(ej);
					resultNode.setChildren(new ArrayList<>());
					// 给父节点进行赋值
					collect.put(ej.getPermissionId(), resultNode);
					result.add(resultNode);
				}
				if (!level.equals(ej.getParentId()) && collect.containsKey(ej.getParentId())) {
					// 当缓存里有数据才开始进行插入处理，否则认为条件不允许
					PermissionNode curp = collect.get(ej.getParentId());
					// 从缓存里获取值
					PermissionNode curb = ClassConvertorMapper.INSTANCE.convertPermissionNode(ej);
					curb.setChildren(new ArrayList<>());
					// 生成一个当前对象
					curp.getChildren().add(curb);
					collect.put(ej.getPermissionId(), curb);
				}
			}
		});
		return result;
	}

	/***
	 * @Description: 根据ID，获取所相关，父级ID，
	 * @Author: LiangJb
	 * @param : collectB 所有相关权限的集合
	 * @param : curId 要获取当前所有父级ID的编号
	 * @param : parentLevel 父级编号的ID，但不包含此父级ID
	 * @CreateDate: 2020/6/15 15:53
	 * @UpdateUser: LiangJb
	 * @UpdateDate: 2020/6/15 15:53
	 * @return : com.snbc.bbpf.common.config.CallResponse
	 */
	public List<String> getAllParentIdList(Map<String, Permission> collectB, String curId, String parentLevel) {
		List<String> result = new ArrayList<>();
		// 判断是否包含ID
		if (collectB.containsKey(curId)) {
			// 获取当前ID
			Permission curItem = collectB.get(curId);
			// 判断父节点是否满足条件
			if (!curItem.getParentId().isEmpty() && !parentLevel.equals(curItem.getParentId())
					&& !curItem.getParentId().equals(curId)) {
				result.addAll(getAllParentIdList(collectB, curItem.getParentId(), parentLevel));
			}
			result.add(curId);
		}
		return result;
	}

	/***
	 * @Description: 是否允许一个账号多人登录操作
	 * @Author: wangsong
	 * @param : loginUser
	 * @param : user
	 * @CreateDate: 2020/6/15 15:53
	 * @UpdateUser: wangsong
	 * @UpdateDate: 2020/6/15 15:53
	 */
	private void ifMultiportLogin(LoginUser loginUser, User user) {
		if (!loginConfig.getMultiportLogin()) {
			redisCheckCodeLoginFlag.deleteUserCache(CommonConstant.USER_LOGIN_STATUS,
					loginUser.getSysType(), user.getUserId());		// 获取redis中的用户状态
		}
	}

	/***
	 * @Description: 二次校验验证码
	 * @Author: wangsong
	 * @param : loginUser
	 * @CreateDate: 2020/6/12 14:33
	 * @UpdateUser: wangsong
	 * @UpdateDate: 2020/6/12 14:33
	 * @return : void
	 */
	private boolean verifyCaptcha(LoginUser loginUser) {
		// 判断登录是否校验验证码
		if (!Boolean.parseBoolean(loginConfig.getIsCheckCaptcha())
				|| !Boolean.parseBoolean(loginUser.getCaptchaVerification())) {
			return true;
		}
		// 组织用户状态数据
		CaptchaVO captchaVO = new CaptchaVO();
		captchaVO.setCaptchaType(loginUser.getCaptchaType());
		captchaVO.setCaptchaVerification(loginUser.getCaptchaVerification());
		captchaVO.setCheckCode(loginUser.getCheckCode());
		captchaVO.setCheckResult(loginUser.getCheckResult());
		// 二次验证验证码
		com.snbc.bbpf.component.captcha.resp.CommonResp result = captchaService.verification(captchaVO);
		return (null != result && null != result.getHead()
				&& ErrorMessage.SUCCESS.getCode().equals(result.getHead().getCode()));
	}

	/**
	 * @param userName 用户名
	 * @param tenantId 租户ID
	 * @Description: 验证用户
	 * @Author: liangjB
	 * @CreateDate: 2021/5/19 11:10
	 * @UpdateDate: 2021/5/19 11:10
	 */
	@Override
	public String getUserIdToken(String userName, String tenantId, String sysType, String ip) {
		String token = "";
		if (loginConfig.getTokenLogin()) {
			try {
				User user = userLoginMapper.selectUserByUserName(userName);
				if (null != user) {
					// 获取角色ID列表
					List<String> roleIds = userLoginMapper.selectUserRoleIdsByUserId(user.getUserId());
					String sessionId = UUID.randomUUID().toString();
					// 获取TOKEN
					token = jwtTokenUtil.generateToken(TokenPayload.builder()
							.userId(user.getUserId())
							.userName(user.getUserName())
							.password(user.getUserPwd())
							.roleIds(roleIds)
							.sysType(sysType)
							.sessionId(sessionId)
							.tenantId("")
							.build());

					// redis中保存登录状态
					redisCheckCodeLoginFlag.userLoginStatus(user, ip, sysType, token, sessionId);
				}
			} catch (Exception e) {
				log.error("getUserIdTokenErrorInfo", e);
			}
		}
		return token;
	}
}
