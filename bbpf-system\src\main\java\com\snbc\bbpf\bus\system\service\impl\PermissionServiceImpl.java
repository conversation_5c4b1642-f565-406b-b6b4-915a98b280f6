/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service.impl;

import com.snbc.bbpf.bus.system.constans.CommonConstant;
import com.snbc.bbpf.bus.system.constans.Constant;
import com.snbc.bbpf.bus.system.constans.NumberConstant;
import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.bus.system.service.PermissionScopeService;
import com.snbc.bbpf.bus.system.service.PermissionService;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.bus.system.utils.TreeUtil;
import com.snbc.bbpf.system.db.common.entity.Permission;
import com.snbc.bbpf.system.db.common.entity.PermissionNode;
import com.snbc.bbpf.system.db.common.entity.PermissionRole;
import com.snbc.bbpf.system.db.common.entity.PermissionScope;
import com.snbc.bbpf.system.db.common.entity.Role;
import com.snbc.bbpf.system.db.common.mapper.PermissionMapper;
import com.snbc.bbpf.system.db.common.mapper.PermissionRoleMapper;
import com.snbc.bbpf.system.db.common.mapper.RoleMapper;
import com.snbc.bbpf.system.db.common.mapper.UserMapper;
import com.snbc.bbpf.system.db.common.vo.PermissionInfo;
import com.snbc.bbpf.system.db.common.vo.PermissionSortVo;
import com.snbc.bbpf.system.db.common.vo.RolePermissionVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.snbc.bbpf.bus.system.constans.Constant.INNER;
import static com.snbc.bbpf.bus.system.constans.Constant.PREV;
import static com.snbc.bbpf.bus.system.exception.ErrorMessage.PERMISSION_CHILD;
import static com.snbc.bbpf.bus.system.exception.ErrorMessage.PERMISSION_UNBIND_ROLE_FIRST;

/**
 * @ClassName: PermissionServiceImpl
 * 权限定义实现类
 * @module: bbpf-bus-system
 * @Author: yangweipeng
 * @date: 2021/5/18
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 */
@Service
@Transactional
@Slf4j
public class PermissionServiceImpl implements PermissionService {
    @Autowired
    private PermissionMapper permissionMapper;
    @Autowired
    private PermissionRoleMapper permissionRoleMapper;
    @Autowired
    private RedisTemplate redisTemplate;
    @Value("${bbpf.system.security.jwt.expiration}")
    private Integer expireTime;
    @Autowired
    private PermissionScopeService permissionScopeService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private RoleMapper roleMapper;
    @Autowired
    private UserMapper userMapper;

    public Integer getExpireTime() {
        if(null==expireTime){
            expireTime= NumberConstant.NO_TWENTY_FOUR;
        }
        return expireTime;
    }
    /**
     * 查询所有权限树节点
     *
     * @return
     */
    @Override
    public List<Permission> getAllPermission() {
        return permissionMapper.getAllPermission();
    }

    /**
     * 根据父节点获取所有子节点以及下属节点
     *
     * @param parentId
     * @return
     */
    @Override
    public List<Permission> getAllPermissionByParentId(String parentId) {
        return permissionMapper.getPermissionByParentId(parentId);
    }

    /**
     * 获取所有子节点以及下属节点
     *
     * @param permissionId
     * @return
     */
    @Override
    public List<Permission> getAllPermissionById(String permissionId) {
        return permissionMapper.getAllPermissionById(permissionId);
    }

    /**
     * 插入资源权限
     */
    @Override
    @CacheEvict(value = Constant.PERMISSION_CACHE_CATEGORY, allEntries = true)
    public int insertPermission(Permission permission) throws Exception {
        Permission parent=permissionMapper.selectByPrimaryKey(permission.getParentId());
        String permissionId= UUID.randomUUID().toString();
        permission.setPermissionId(permissionId);
        String permissionPath=parent.getPermissionPath()+ CommonConstant.PERMISSION_FILTER_CHAR+permissionId;
        permission.setPermissionPath(permissionPath);
        permission.setSysType(0);
        permission.setPermissionLevel(parent.getPermissionLevel()+1);
        permission.setParentName(parent.getPermissionName());
        int maxOrderby = this.getMaxOrderByParentId(permission.getParentId());
        permission.setCreateTime(LocalDateTime.now());
        permission.setUpdateTime(LocalDateTime.now());
        permission.setCreateUserId(CurrentUser.getUserId());
        permission.setOrderBy(maxOrderby + 1);
        List<PermissionRole> permissionRoleList=new ArrayList<>();
        PermissionRole permissionRole= PermissionRole.builder()
                .id(UUID.randomUUID().toString())
                .permissionId(permissionId).roleId("-1").build();
        permissionRoleList.add(permissionRole);
        permissionRoleMapper.insertPermissionRole(permissionRoleList);
        return permissionMapper.insertSelective(permission);
    }
    /**
     * 更新资源权限代码
     */
    @Override
    @CacheEvict(value = Constant.PERMISSION_CACHE_CATEGORY, allEntries = true)
    public int updatePermission(Permission permission) {
        permission.setUpdateTime(LocalDateTime.now());
        return permissionMapper.updateByPrimaryKeySelective(permission);
    }
    /**
     * 权限树
     *
     * @return
     */
    @Override
    public PermissionNode getPermissionTree(int level) {
        List<Permission> permissions = permissionMapper.getAllPermissionWithRoot();
        Optional<Permission> list = permissions.stream().filter(item -> "-1".equals(item.getPermissionId())).findFirst();
        Permission root = null;
        if (list.isPresent()) {
            root = list.get();
        }
        PermissionNode node = new PermissionNode();
        if (null != root) {
            node = convertNode(root);
        }
        if (level == 0) {
            level = permissions.stream().mapToInt(Permission::getPermissionLevel).max().getAsInt();
        }
        buildTree(node, permissions, level);
        return node;
    }

    /**
     *
     * @param permission
     * @return
     */
    private static PermissionNode convertNode(Permission permission){
        PermissionNode node=new PermissionNode();
        node.setPermissionId(permission.getPermissionId());
        node.setLevel(permission.getPermissionLevel());
        node.setHasEnable(permission.getHasEnable());
        node.setPermissionIcon(permission.getPermissionImage());
        node.setOrderBy(permission.getOrderBy());
        node.setParentId(permission.getParentId());
        node.setRemarks(permission.getPermissionDesc());
        node.setRoutingUrl(permission.getRoutingUrl());
        node.setPermissionName(permission.getPermissionName());
        node.setParentName(permission.getParentName());
        node.setPermissionCode(permission.getPermissionCode());
        node.setPermissionType(permission.getPermissionType());
        return node;
    }
    /**
     *
     * @param parentNode
     * @param permissionList
     * @param level
     */
    private void  buildTree(PermissionNode parentNode,List<Permission> permissionList,int level) {
        List<PermissionNode> childen = permissionList.stream()
                .filter(item -> item.getParentId().equals(parentNode.getPermissionId()))
                .map(PermissionServiceImpl::convertNode).sorted(Comparator.comparing(PermissionNode::getOrderBy)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(childen)) {
            return;
        }
        parentNode.setChildren(childen);
        int currentLevel = childen.stream().mapToInt(PermissionNode::getLevel).max().getAsInt();
        if (currentLevel <= level) {
            childen.forEach(item -> buildTree(item, permissionList, level));
        }
    }
    @Override
    public Boolean permissionSort(PermissionSortVo perSort) throws Exception {
        try {
            if (INNER.equals(perSort.getType())) {
                int maxOrderByParentId = this.getMaxOrderByParentId(perSort.getTargetNodeId());
                Permission targetNode = this.queryPermission(perSort.getTargetNodeId());
                Permission permission = permissionMapper.selectByPrimaryKey(perSort.getCurrentNodeId());
                //根据父节点ID获取系统类型
                Permission parentPermission = permissionMapper.selectByPrimaryKey(perSort.getTargetNodeId());
                permission.setPermissionId(perSort.getCurrentNodeId());
                permission.setOrderBy(maxOrderByParentId + 1);
                permission.setParentId(perSort.getTargetNodeId());
                permission.setParentName(targetNode.getPermissionName());
                permission.setSysType(parentPermission.getSysType());
                permission.setPermissionLevel(targetNode.getPermissionLevel() + 1);
                String newPath=parentPermission.getPermissionPath()+ CommonConstant.PERMISSION_FILTER_CHAR +perSort.getCurrentNodeId();
                permission.setPermissionPath(newPath);
                permissionMapper.updatePermissionPath(permission.getPermissionPath(),newPath);
                this.updatePermission(permission);
                permissionMapper.updatePermissionLevel();
                return true;
            } else {
                // 根据父节点查询所有子节点
                List<Permission> permissionList = permissionMapper.getPermissionByParentId(perSort.getParentId());
                // 对子节点重新排序
                List<Permission> perSortList = sort(permissionList, perSort);
                permissionMapper.updatePermissionList(perSortList);
                permissionMapper.updatePermissionLevel();
                return true;
            }
        }catch (Exception ex){
            log.error("Drag sort exception",ex);
            throw  ex;
        }
    }
    /**
     * 权限树排序操作
     *
     * @param perList
     * @param permissionSortVo
     * @return
     */
    private List<Permission> sort(List<Permission> perList, PermissionSortVo permissionSortVo) {
        // 目标节点的下标
        int targetNodeSubscript = 0;
        Permission perSort = permissionMapper.selectByPrimaryKey(permissionSortVo.getCurrentNodeId());
        perSort.setPermissionId(permissionSortVo.getCurrentNodeId());
        perSort.setParentId(permissionSortVo.getParentId());
        int flag=-1;
        //
        for (int i = 0; i < perList.size(); i++) {
            if (perList.get(i).getPermissionId().equals(permissionSortVo.getCurrentNodeId())) {
                perSort=perList.get(i);
                flag=i;
            }
            if (perList.get(i).getPermissionId().equals(permissionSortVo.getTargetNodeId())) {
                if (permissionSortVo.getType().equals(PREV)) {
                    targetNodeSubscript = i;
                } else {
                    targetNodeSubscript = i + 1;
                }
            }
        }
        if(flag>-1){
            perList.remove(flag);
            if(flag<targetNodeSubscript){
                targetNodeSubscript=targetNodeSubscript-1;
            }
        }
        perList.add(targetNodeSubscript, perSort);
        String oldPath=perSort.getPermissionPath();
        Permission parentPermission=permissionMapper.selectByPrimaryKey(permissionSortVo.getParentId());
        String newPath=parentPermission.getPermissionPath()+ CommonConstant.PERMISSION_FILTER_CHAR +perSort.getPermissionId();
        for (int i = 0; i < perList.size(); i++) {
            Permission permission = perList.get(i);
            permission.setParentName(parentPermission.getPermissionName());
            permission.setPermissionPath(parentPermission.getPermissionPath()+ CommonConstant.PERMISSION_FILTER_CHAR +permission.getPermissionId());
            permission.setOrderBy(i);
        }
        permissionMapper.updatePermissionPath(oldPath,newPath);
        return perList;
    }

    /**
     * 根据父节点获取子节点最大的排序号
     *
     * @param parentId 父节点id
     * @return 返回最大序号值
     */
    private int getMaxOrderByParentId(String parentId) {
        return permissionMapper.queryMaxOrderByParentId(parentId);
    }

    /**
     * 获取资源权限
     *
     * @param roleIds
     * @param sysType
     * @return
     * @throws IOException
     */
    @Override
    public List<String> getResourcePermissionsByRoleIds(String roleIds, String sysType) throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();
        String cacheKey=Constant.ALL_RESOURCE_PERMISSION+sysType;
        //如果roleIds为空,查询所有资源权限
        if(StringUtils.isBlank(roleIds)){
            List<String> allResourcePermission = permissionMapper.selectResourcePermissionListByRoleId("",sysType);
            String paths = objectMapper.writeValueAsString(allResourcePermission);
            //获取资源权限之后存放到redis缓存中,全部资源权限的redis,key value 类型的
            redisTemplate.opsForValue().set(cacheKey,paths,getExpireTime(), TimeUnit.HOURS);
            return allResourcePermission;
        }else{
            //一个集合添加所有的资源权限路径
            Set<String> permissionPath = new HashSet<>();
            //先把roleIds 转换成数组
            String[] roleIdArray = StringUtils.split(roleIds,",");
            //遍历所有roleIdArray
            for(String roleId: roleIdArray){
                //查询数据库
                List<String> resourcePermissionByRoleId = permissionMapper.selectResourcePermissionListByRoleId(roleId,sysType);
                if(resourcePermissionByRoleId!=null&&!resourcePermissionByRoleId.isEmpty()){
                    String paths = objectMapper.writeValueAsString(resourcePermissionByRoleId);
                    for(String path:resourcePermissionByRoleId){
                        permissionPath.add(path);
                    }
                    //将结果存储到redis,hash类型的
                    redisTemplate.opsForHash().put(Constant.ROLE_RESOURCE_PERMISSION,roleId,paths);
                }
            }
            // 有效期
            redisTemplate.expire(Constant.ROLE_RESOURCE_PERMISSION,getExpireTime(),TimeUnit.HOURS);
            return new ArrayList<>(permissionPath);
        }
    }


    /**
     * 根据资源权限IDs删除资源权限
     *
     * @param permissionIds 资源权限ids
     */
    @Override
    @CacheEvict(value = Constant.PERMISSION_CACHE_CATEGORY, allEntries = true)
    public void deletePermissions(String[] permissionIds) {
        //先判断有没有缓存
        Set<String> keys =stringRedisTemplate.keys(Constant.ALL_RESOURCE_PERMISSION+"*");
        if(!CollectionUtils.isEmpty(keys)) {
            for (String key : keys) {
                stringRedisTemplate.delete(key);
            }
        }
        if(redisTemplate.hasKey(Constant.ROLE_RESOURCE_PERMISSION)){
            redisTemplate.delete(Constant.ROLE_RESOURCE_PERMISSION);
        }
        Arrays.stream(permissionIds).forEach(permissionId->{
            List<Permission> permissions=permissionMapper.getPermissionByParentId(permissionId);
            if(permissions!=null&&!permissions.isEmpty()){
                throw  new BusinessException(PERMISSION_CHILD.getMessage(),PERMISSION_CHILD.getCode());
            }
            PermissionScope permissionScope= new PermissionScope();
            permissionScope.setPermissionId(permissionId);
            List<PermissionScope> list = permissionScopeService.getDataScopeList(permissionScope);
            if(null!=list&&!list.isEmpty()){
                throw  new BusinessException(com.snbc.bbpf.bus.system.exception.ErrorMessage.PERMISSION_DATA_AUTH);
            }
            List<String> roleIds=permissionMapper.selectRoleListByPermissionId(permissionId);
            if(roleIds!=null&&!roleIds.isEmpty()){
                throw  new BusinessException(PERMISSION_UNBIND_ROLE_FIRST.getMessage(), PERMISSION_UNBIND_ROLE_FIRST.getCode());
            }

        });
        permissionMapper.deleteByPermissionIds(permissionIds);
        Arrays.stream(permissionIds).forEach(id->
            permissionRoleMapper.deletePermissionByPermissionId(id)
        );
    }

    /**
     * 获取用户的所有权限信息
     * 该方法通过角色ID和用户ID来获取用户的所有权限信息，包括角色信息、权限菜单、
     * 角色所属的组织机构以及数据权限的组织机构，并将这些信息封装到RolePermissionVo对象中返回
     *
     * @param sysType 系统类型，用于区分不同系统间的权限控制
     * @param userId 用户ID，标识需要获取权限信息的用户
     * @param roleId 角色ID，标识需要获取权限信息的角色
     * @return 返回封装了用户权限信息的RolePermissionVo对象
     *
     * @CreateDate 2021/5/21 13:54
     * @UpdateDate 2021/5/21 13:54
     *
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public RolePermissionVo getRoleInfo(String sysType, String userId, String roleId) {
        // 查询用户在系统中的权限菜单
        List<Permission> menuList = permissionMapper.selectUserPermission(sysType, userId);
        // 查询角色在系统中的权限ID列表
        List<String> roleMenuList = StringUtils.isNotBlank(roleId) ?
                permissionMapper.selectPermissionIdByRoleId(roleId) : new ArrayList<>();
        // 转换路径为权限ID列表
        Set<String> allParentIds = TreeUtil.PermissionIdConvert(menuList);
        // 构建权限菜单树
        List<PermissionInfo> treeList = allParentIds.isEmpty() ?
                new ArrayList<>() : TreeUtil.getTreeList(
                permissionMapper.selectPermissionIdPermissionIds(allParentIds), roleMenuList);
        // 角色所属的组织机构
        List<Map<String, String>> orgListNoDataAuth = roleMapper.selectOrgByRoleId(roleId);
        // 数据权限的组织机构
        List<Map<String, String>> orgList = userMapper.selectUserOrg4DataRule();
        // 组织机构数据转换
        convertMap(orgListNoDataAuth, orgList);
        // 封装角色权限信息到RolePermissionVo对象
        RolePermissionVo rolePermissionVo = new RolePermissionVo();
        if (StringUtils.isNotBlank(roleId)) {
            // 查询角色详细信息
            Role role = roleMapper.selectRoleDetail(roleId);
            if (null != role) {
                rolePermissionVo.setRoleId(role.getRoleId());
                rolePermissionVo.setRoleName(role.getRoleName());
                rolePermissionVo.setRoleDesc(role.getRoleDesc());
            }
        }
        rolePermissionVo.setOrgInfo(orgListNoDataAuth);
        rolePermissionVo.setPermissionInfo(treeList);
        return rolePermissionVo;
    }


    /**
     * 判断map中的值是否能在前端移除
     * @param ownedList 用户所属的
     * @param dateRuleList  用户数据权限的
     */
    private static void convertMap(List<Map<String, String>> ownedList,List<Map<String, String>> dateRuleList){
        log.debug("owned ={}, dataRule={}",ownedList,dateRuleList);
        // 转换： 判断orgList中角色是否有数据权限
        for (Map<String, String> owned : ownedList) {
            owned.put("canRemove", "false");
            // 如果不包含，设置成false
            if (judgeContains(dateRuleList,owned.get("orgId"))){
                owned.put("canRemove", "true");
            }
        }
    }
    /**
     * 判断是否  包含返回true,不包含返回false
     * @param mapList
     * @param value
     * @return
     */
    private static boolean judgeContains(List<Map<String, String>> mapList,String value){
        for (Map<String, String> map : mapList) {
            if (map.containsValue(value)){
                return true;
            }
        }
        return false;
    }
    /**
     * 根据资源权限Id获取资源权限信息
     */
    @Override
    public Permission queryPermission(String permissionId) {
        return permissionMapper.selectByPrimaryKey(permissionId);
    }

    /**
     * 查询系统资源信息
     *
     * @param permissionCode
     * @return
     */
    @Override
    public Permission selectByPermissionCode(String permissionCode) {
        return  permissionMapper.selectByPermissionCode(permissionCode);
    }
}
