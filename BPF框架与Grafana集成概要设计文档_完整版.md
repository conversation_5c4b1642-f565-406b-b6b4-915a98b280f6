# BPF框架与Grafana集成概要设计文档

## 1. 概述

### 1.1 项目背景

基于BPF框架的监控需求，设计一套全新的Grafana集成解决方案，通过代理模块实现统一认证、权限控制、数据隔离等企业级监控功能。该代理模块可以集成到任何服务中，通过配置即可实现Grafana集成功能。

### 1.2 设计目标

- **3.1 用户能够登录直接访问Grafana**：基于JWT Token的单点登录
- **3.2 支持iframe方式集成Grafana图表**：跨域配置和Token传递
- **3.3 仪表盘权限控制与导出权限控制**：基于BPF权限系统的精细化控制
- **3.4 提供Grafana图表查询和导出的能力**：API接口封装
- **3.5 与业务平台深度集成**：多租户数据隔离和业务数据联动
- **3.6 数据分享功能和用户使用**：利用Grafana原生分享功能

### 1.3 技术线路

```mermaid
timeline
    title BPF-Grafana集成技术线路图
    
    section 第一阶段：基础架构
        代理模块设计 : Spring Boot 2.7.x
                   : Spring Security 5.7.x
                   : JWT认证机制
        
        缓存架构    : Redis 6.2.x
                   : 多级缓存策略
                   : 权限信息缓存
        
        数据库设计  : MySQL 8.0.x
                   : 权限数据模型
                   : 配置数据模型
    
    section 第二阶段：核心功能
        权限控制    : RBAC权限模型
                   : 多租户数据隔离
                   : Schema级隔离
        
        SQL拦截     : SQL解析引擎
                   : 动态权限注入
                   : 表级权限控制
        
        代理转发    : HTTP代理
                   : WebSocket代理
                   : 请求响应处理
    
    section 第三阶段：高级特性
        iframe集成  : CORS跨域配置
                   : Token传递机制
                   : 响应式适配
        
        监控告警    : Prometheus集成
                   : 性能指标监控
                   : 异常告警机制
        
        安全加固    : 防SQL注入
                   : 访问审计日志
                   : 异常行为检测
    
    section 第四阶段：生产部署
        容器化      : Docker容器化
                   : K8s编排部署
                   : 自动化CI/CD
        
        高可用      : 集群部署
                   : 负载均衡
                   : 故障自动切换
        
        运维监控    : 日志收集分析
                   : 性能监控告警
                   : 运维自动化
```

## 2. 五视图架构设计

### 2.1 逻辑视图 (Logical View)

```mermaid
graph TB
    subgraph "表现层"
        A1[Web前端]
        A2[移动端]
        A3[iframe嵌入]
    end
    
    subgraph "应用层"
        B1[认证模块]
        B2[权限模块]
        B3[代理模块]
        B4[监控模块]
    end
    
    subgraph "业务层"
        C1[用户管理]
        C2[权限管理]
        C3[租户管理]
        C4[配置管理]
    end
    
    subgraph "数据层"
        D1[用户数据]
        D2[权限数据]
        D3[配置数据]
        D4[日志数据]
    end
    
    subgraph "外部系统"
        E1[Grafana]
        E2[监控数据源]
        E3[BPF后端服务]
    end
    
    A1 --> B1
    A2 --> B1
    A3 --> B1
    B1 --> B2
    B2 --> B3
    B3 --> B4
    B1 --> C1
    B2 --> C2
    B3 --> C3
    B4 --> C4
    C1 --> D1
    C2 --> D2
    C3 --> D3
    C4 --> D4
    B3 --> E1
    E1 --> E2
    B1 --> E3
```

### 2.2 开发视图 (Development View)

```mermaid
graph TB
    subgraph "bpf-grafana-proxy代理模块"
        A1[认证过滤器<br/>AuthenticationFilter]
        A2[权限服务<br/>PermissionService]
        A3[SQL拦截器<br/>SqlInterceptor]
        A4[代理控制器<br/>ProxyController]
        A5[配置管理<br/>ConfigManager]
    end
    
    subgraph "核心依赖"
        B1[Spring Boot Starter]
        B2[Spring Security]
        B3[Redis Template]
        B4[MySQL Driver]
        B5[HTTP Client]
    end
    
    subgraph "工具类库"
        C1[JWT工具类<br/>JwtUtils]
        C2[SQL解析器<br/>SqlParser]
        C3[缓存工具类<br/>CacheUtils]
        C4[HTTP工具类<br/>HttpUtils]
    end
    
    A1 --> B2
    A2 --> B3
    A3 --> C2
    A4 --> B5
    A5 --> B4
    A1 --> C1
    A2 --> C3
    A4 --> C4
```

### 2.3 进程视图 (Process View)

```mermaid
graph LR
    subgraph "用户请求处理流程"
        P1[用户请求] --> P2[认证过滤器]
        P2 --> P3[权限验证]
        P3 --> P4[SQL拦截]
        P4 --> P5[代理转发]
        P5 --> P6[响应处理]
        P6 --> P7[返回结果]
    end
    
    subgraph "缓存处理流程"
        C1[缓存查询] --> C2[缓存命中?]
        C2 -->|是| C3[返回缓存数据]
        C2 -->|否| C4[查询数据库]
        C4 --> C5[更新缓存]
        C5 --> C6[返回数据]
    end
    
    subgraph "权限处理流程"
        R1[权限请求] --> R2[用户识别]
        R2 --> R3[角色查询]
        R3 --> R4[权限计算]
        R4 --> R5[权限缓存]
        R5 --> R6[权限返回]
    end
```

### 2.4 物理视图 (Physical View)

```mermaid
graph TB
    subgraph "负载均衡层"
        LB1[Nginx-1<br/>192.168.1.10]
        LB2[Nginx-2<br/>192.168.1.11]
    end
    
    subgraph "应用服务层"
        APP1[BPF-Grafana-Proxy-1<br/>192.168.2.10<br/>8核16G]
        APP2[BPF-Grafana-Proxy-2<br/>192.168.2.11<br/>8核16G]
        GF1[Grafana<br/>192.168.2.20<br/>4核8G]
    end
    
    subgraph "数据服务层"
        REDIS1[Redis-Master<br/>192.168.3.10<br/>8核16G]
        REDIS2[Redis-Slave<br/>192.168.3.11<br/>8核16G]
        MYSQL1[MySQL-Master<br/>192.168.3.20<br/>16核32G]
        MYSQL2[MySQL-Slave<br/>192.168.3.21<br/>16核32G]
    end
    
    subgraph "监控服务层"
        PROM[Prometheus<br/>************<br/>4核8G]
        ELK[ELK Stack<br/>************<br/>8核16G]
    end
    
    LB1 --> APP1
    LB1 --> APP2
    LB2 --> APP1
    LB2 --> APP2
    APP1 --> GF1
    APP2 --> GF1
    APP1 --> REDIS1
    APP2 --> REDIS1
    REDIS1 --> REDIS2
    APP1 --> MYSQL1
    APP2 --> MYSQL1
    MYSQL1 --> MYSQL2
    APP1 --> PROM
    APP2 --> PROM
    GF1 --> PROM
    APP1 --> ELK
    APP2 --> ELK
    GF1 --> ELK
```

### 2.5 场景视图 (Scenario View)

```mermaid
sequenceDiagram
    participant U as 用户
    participant P as 代理模块
    participant A as 认证服务
    participant R as Redis缓存
    participant G as Grafana
    participant D as 数据库

    Note over U,D: 场景1：用户登录访问Grafana
    U->>P: 1. 访问Grafana(携带Token)
    P->>A: 2. 验证JWT Token
    A-->>P: 3. Token验证结果
    P->>R: 4. 查询用户权限缓存
    alt 缓存命中
        R-->>P: 5. 返回权限信息
    else 缓存未命中
        P->>D: 6. 查询用户权限
        D-->>P: 7. 返回权限信息
        P->>R: 8. 缓存权限信息
    end
    P->>G: 9. 代理转发请求
    G-->>P: 10. 返回Grafana响应
    P-->>U: 11. 返回最终响应

    Note over U,D: 场景2：SQL拦截与权限控制
    U->>P: 12. 发起数据查询
    P->>P: 13. SQL拦截器处理
    P->>P: 14. 注入权限过滤条件
    P->>G: 15. 转发修改后的SQL
    G->>D: 16. 执行权限过滤后的SQL
    D-->>G: 17. 返回过滤后的数据
    G-->>P: 18. 返回查询结果
    P-->>U: 19. 返回最终结果
```

## 3. 数据视图设计

### 3.1 用户权限数据模型

**用户表 (USER)**
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| user_id | VARCHAR(64) | 用户ID | PRIMARY KEY |
| username | VARCHAR(100) | 用户名 | NOT NULL |
| email | VARCHAR(200) | 邮箱 | |
| tenant_id | VARCHAR(64) | 租户ID | FOREIGN KEY |
| org_id | VARCHAR(64) | 组织ID | FOREIGN KEY |
| dept_id | VARCHAR(64) | 部门ID | FOREIGN KEY |
| created_at | DATETIME | 创建时间 | NOT NULL |
| updated_at | DATETIME | 更新时间 | NOT NULL |

**角色表 (ROLE)**
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| role_id | VARCHAR(64) | 角色ID | PRIMARY KEY |
| role_name | VARCHAR(100) | 角色名称 | NOT NULL |
| description | VARCHAR(500) | 角色描述 | |
| created_at | DATETIME | 创建时间 | NOT NULL |

**权限表 (PERMISSION)**
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| permission_id | VARCHAR(64) | 权限ID | PRIMARY KEY |
| resource_type | VARCHAR(50) | 资源类型 | NOT NULL |
| resource_id | VARCHAR(100) | 资源ID | |
| action | VARCHAR(50) | 操作类型 | NOT NULL |
| description | VARCHAR(500) | 权限描述 | |

**用户权限缓存表 (USER_PERMISSION_CACHE)**
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| user_id | VARCHAR(64) | 用户ID | PRIMARY KEY |
| permissions_data | JSON | 权限数据 | NOT NULL |
| cached_at | DATETIME | 缓存时间 | NOT NULL |
| expires_at | DATETIME | 过期时间 | NOT NULL |

### 3.2 SQL拦截配置数据模型

**表权限配置表 (TABLE_PERMISSION_CONFIG)**
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| table_name | VARCHAR(100) | 表名 | PRIMARY KEY |
| user_fields | JSON | 用户字段配置 | |
| org_field | VARCHAR(50) | 组织字段 | |
| dept_field | VARCHAR(50) | 部门字段 | |
| tenant_field | VARCHAR(50) | 租户字段 | |
| custom_fields | JSON | 自定义字段配置 | |
| enabled | BOOLEAN | 是否启用 | DEFAULT TRUE |

**租户Schema映射表 (TENANT_SCHEMA_MAPPING)**
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| tenant_id | VARCHAR(64) | 租户ID | PRIMARY KEY |
| schema_name | VARCHAR(100) | Schema名称 | NOT NULL |
| isolation_enabled | BOOLEAN | 是否启用隔离 | DEFAULT TRUE |
| created_at | DATETIME | 创建时间 | NOT NULL |

**SQL拦截日志表 (SQL_INTERCEPT_LOG)**
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| log_id | VARCHAR(64) | 日志ID | PRIMARY KEY |
| user_id | VARCHAR(64) | 用户ID | NOT NULL |
| original_sql | TEXT | 原始SQL | NOT NULL |
| modified_sql | TEXT | 修改后SQL | NOT NULL |
| api_path | VARCHAR(200) | API路径 | |
| intercept_time | DATETIME | 拦截时间 | NOT NULL |
| processing_time_ms | INT | 处理时间(毫秒) | |

## 4. 代理模块功能设计

### 4.1 代理模块架构

```mermaid
graph TB
    subgraph "BPF-Grafana-Proxy代理模块"
        A[配置管理器<br/>ConfigManager]
        B[认证过滤器<br/>AuthFilter]
        C[权限服务<br/>PermissionService]
        D[SQL拦截器<br/>SqlInterceptor]
        E[代理控制器<br/>ProxyController]
        F[缓存管理器<br/>CacheManager]
        G[监控指标<br/>MetricsCollector]
    end

    subgraph "外部依赖"
        H[BPF认证服务]
        I[Redis缓存]
        J[MySQL数据库]
        K[Grafana实例]
    end

    A --> B
    A --> C
    A --> D
    B --> H
    C --> I
    C --> J
    D --> C
    E --> K
    E --> D
    F --> I
    G --> F
```

### 4.2 核心功能模块

#### 4.2.1 认证模块 (AuthenticationModule)

**功能描述：**
- JWT Token验证
- 用户身份识别
- 会话管理
- Token刷新机制

**核心接口：**
```java
public interface AuthenticationService {
    // 验证JWT Token
    UserInfo validateToken(String token);

    // 刷新Token
    String refreshToken(String token);

    // 获取用户信息
    UserInfo getUserInfo(String userId);

    // 检查Token是否过期
    boolean isTokenExpired(String token);
}
```

#### 4.2.2 权限模块 (PermissionModule)

**功能描述：**
- 用户权限查询
- 角色权限管理
- 资源访问控制
- 权限缓存管理

**核心接口：**
```java
public interface PermissionService {
    // 获取用户权限
    UserPermissionDto getUserPermissions(String userId);

    // 检查资源访问权限
    boolean hasPermission(String userId, String resourceType, String resourceId, String action);

    // 获取用户可访问的仪表盘列表
    List<DashboardPermission> getUserDashboards(String userId);

    // 检查数据导出权限
    boolean hasExportPermission(String userId, String resourceId);
}
```

#### 4.2.3 SQL拦截模块 (SqlInterceptorModule)

**功能描述：**
- SQL语句解析
- 权限条件注入
- 多租户数据隔离
- 表级权限控制

**核心接口：**
```java
public interface SqlInterceptorService {
    // 拦截并修改SQL
    String interceptAndModifyRequest(String requestBody, String userId, String apiPath);

    // 构建权限过滤条件
    String buildPermissionFilter(UserPermissionDto userPermission, String tableName);

    // 应用租户Schema隔离
    String applyTenantSchemaIsolation(String sql, String tenantId);

    // 验证SQL安全性
    boolean validateSqlSecurity(String sql);
}
```

### 4.3 配置项设计

#### 4.3.1 应用配置 (application.yml)

```yaml
# BPF-Grafana代理模块配置
bpf:
  grafana:
    proxy:
      # Grafana服务配置
      grafana-base-url: ${GRAFANA_BASE_URL:http://localhost:3000}
      grafana-timeout: ${GRAFANA_TIMEOUT:30000}

      # 认证配置
      jwt-secret: ${JWT_SECRET:your-secret-key}
      jwt-expiration: ${JWT_EXPIRATION:3600}
      token-header-name: ${TOKEN_HEADER:Authorization}

      # 权限配置
      permission-api-url: ${PERMISSION_API_URL:http://localhost:8080/api/permission}
      permission-cache-enabled: ${PERMISSION_CACHE_ENABLED:true}
      permission-cache-ttl: ${PERMISSION_CACHE_TTL:1800}

      # SQL拦截配置
      sql-intercept-enabled: ${SQL_INTERCEPT_ENABLED:true}
      sql-intercept-paths: ${SQL_INTERCEPT_PATHS:/api/datasources/proxy,/api/query}
      sql-security-check: ${SQL_SECURITY_CHECK:true}

      # 多租户配置
      multi-tenant-enabled: ${MULTI_TENANT_ENABLED:true}
      tenant-schema-isolation: ${TENANT_SCHEMA_ISOLATION:true}
      default-schema-prefix: ${DEFAULT_SCHEMA_PREFIX:tenant_}

      # 缓存配置
      cache:
        type: redis
        redis:
          host: ${REDIS_HOST:localhost}
          port: ${REDIS_PORT:6379}
          password: ${REDIS_PASSWORD:}
          database: ${REDIS_DATABASE:0}
          timeout: ${REDIS_TIMEOUT:3000}

      # 监控配置
      metrics:
        enabled: ${METRICS_ENABLED:true}
        export-interval: ${METRICS_EXPORT_INTERVAL:60}

      # 安全配置
      security:
        cors-enabled: ${CORS_ENABLED:true}
        allowed-origins: ${ALLOWED_ORIGINS:http://localhost:3000,https://*.company.com}
        csrf-disabled: ${CSRF_DISABLED:true}

      # 日志配置
      logging:
        access-log-enabled: ${ACCESS_LOG_ENABLED:true}
        sql-log-enabled: ${SQL_LOG_ENABLED:true}
        performance-log-enabled: ${PERFORMANCE_LOG_ENABLED:true}
```

#### 4.3.2 表权限配置 (table-permissions.yml)

```yaml
# 表级权限配置
table-permissions:
  # 业务数据表配置
  business_data:
    user-fields:
      - "create_user_id"
      - "owner_id"
    org-field: "org_id"
    dept-field: "dept_id"
    tenant-field: "tenant_id"
    enabled: true

  # 系统日志表配置
  sys_log:
    user-fields:
      - "user_id"
      - "operator_id"
    org-field: "org_id"
    enabled: true

  # 订单表配置
  order_info:
    user-fields:
      - "create_user_id"
    org-field: "org_id"
    dept-field: "dept_id"
    tenant-field: "tenant_id"
    custom-fields:
      - field: "status"
        condition: "status IN ('active', 'pending')"
    enabled: true

  # 财务数据表配置
  financial_data:
    org-field: "org_id"
    dept-field: "dept_id"
    tenant-field: "tenant_id"
    custom-fields:
      - field: "data_level"
        condition: "data_level <= {user.data_level}"
    enabled: true
```

#### 4.3.3 租户Schema映射配置 (tenant-schema.yml)

```yaml
# 租户Schema映射配置
tenant-schema-mapping:
  # 零售北区
  tenant_retail_north:
    schema-name: "retail_north"
    isolation-enabled: true
    description: "零售业务北区数据"

  # 零售东区
  tenant_retail_east:
    schema-name: "retail_east"
    isolation-enabled: true
    description: "零售业务东区数据"

  # 零售西区
  tenant_retail_west:
    schema-name: "retail_west"
    isolation-enabled: true
    description: "零售业务西区数据"

  # 制造业租户
  tenant_manufacturing:
    schema-name: "manufacturing"
    isolation-enabled: true
    description: "制造业务数据"

  # 公共数据租户
  tenant_public:
    schema-name: "public"
    isolation-enabled: false
    description: "公共数据，不进行隔离"
```

### 4.4 缓存Key设计

#### 4.4.1 缓存Key命名规范

```yaml
# 缓存Key设计规范
cache-keys:
  # 用户权限缓存
  user-permission: "bpf:grafana:permission:user:{userId}"
  user-roles: "bpf:grafana:roles:user:{userId}"
  user-info: "bpf:grafana:userinfo:{userId}"

  # 权限相关缓存
  role-permissions: "bpf:grafana:permission:role:{roleId}"
  resource-permissions: "bpf:grafana:permission:resource:{resourceType}:{resourceId}"

  # 配置缓存
  table-config: "bpf:grafana:config:table:{tableName}"
  tenant-schema: "bpf:grafana:config:tenant:{tenantId}"
  system-config: "bpf:grafana:config:system"

  # SQL拦截缓存
  sql-result: "bpf:grafana:sql:result:{sqlHash}"
  sql-permission: "bpf:grafana:sql:permission:{userId}:{tableNames}"

  # 会话缓存
  user-session: "bpf:grafana:session:{sessionId}"
  token-blacklist: "bpf:grafana:token:blacklist:{tokenHash}"

  # 监控统计缓存
  metrics-counter: "bpf:grafana:metrics:counter:{metricName}"
  metrics-gauge: "bpf:grafana:metrics:gauge:{metricName}"

  # 限流缓存
  rate-limit: "bpf:grafana:ratelimit:{userId}:{apiPath}"
  api-quota: "bpf:grafana:quota:{userId}:{timeWindow}"
```

#### 4.4.2 缓存TTL配置

```yaml
# 缓存过期时间配置
cache-ttl:
  # 用户权限相关 (30分钟)
  user-permission: 1800
  user-roles: 1800
  user-info: 3600

  # 配置相关 (2小时)
  table-config: 7200
  tenant-schema: 7200
  system-config: 7200

  # SQL结果缓存 (5分钟)
  sql-result: 300
  sql-permission: 1800

  # 会话相关 (1小时)
  user-session: 3600
  token-blacklist: 86400

  # 监控统计 (1分钟)
  metrics-counter: 60
  metrics-gauge: 60

  # 限流相关 (1分钟)
  rate-limit: 60
  api-quota: 3600
```

#### 4.4.3 缓存操作工具类

```java
@Component
public class CacheKeyManager {

    private static final String PREFIX = "bpf:grafana:";

    // 用户权限缓存Key
    public String getUserPermissionKey(String userId) {
        return PREFIX + "permission:user:" + userId;
    }

    // 用户角色缓存Key
    public String getUserRolesKey(String userId) {
        return PREFIX + "roles:user:" + userId;
    }

    // 表配置缓存Key
    public String getTableConfigKey(String tableName) {
        return PREFIX + "config:table:" + tableName;
    }

    // 租户Schema缓存Key
    public String getTenantSchemaKey(String tenantId) {
        return PREFIX + "config:tenant:" + tenantId;
    }

    // SQL结果缓存Key
    public String getSqlResultKey(String sqlHash) {
        return PREFIX + "sql:result:" + sqlHash;
    }

    // 会话缓存Key
    public String getUserSessionKey(String sessionId) {
        return PREFIX + "session:" + sessionId;
    }

    // 限流缓存Key
    public String getRateLimitKey(String userId, String apiPath) {
        return PREFIX + "ratelimit:" + userId + ":" + apiPath.replace("/", "_");
    }
}
```

## 5. 部署架构设计

### 5.1 容器化部署配置

#### 5.1.1 Docker Compose配置

```yaml
version: '3.8'

services:
  # BPF-Grafana代理服务
  bpf-grafana-proxy-1:
    image: bpf-grafana-proxy:latest
    container_name: bpf-grafana-proxy-1
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - GRAFANA_BASE_URL=http://grafana:3000
      - REDIS_HOST=redis-master
      - MYSQL_HOST=mysql-master
      - JWT_SECRET=${JWT_SECRET}
    ports:
      - "8081:8080"
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
    depends_on:
      - redis-master
      - mysql-master
    restart: always

  bpf-grafana-proxy-2:
    image: bpf-grafana-proxy:latest
    container_name: bpf-grafana-proxy-2
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - GRAFANA_BASE_URL=http://grafana:3000
      - REDIS_HOST=redis-master
      - MYSQL_HOST=mysql-master
      - JWT_SECRET=${JWT_SECRET}
    ports:
      - "8082:8080"
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
    depends_on:
      - redis-master
      - mysql-master
    restart: always

  # Nginx负载均衡器
  nginx:
    image: nginx:1.20-alpine
    container_name: nginx-lb
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - bpf-grafana-proxy-1
      - bpf-grafana-proxy-2
    restart: always

  # Grafana服务
  grafana:
    image: grafana/grafana-enterprise:9.5.0
    container_name: grafana
    environment:
      - GF_AUTH_PROXY_ENABLED=true
      - GF_AUTH_PROXY_HEADER_NAME=X-WEBAUTH-USER
      - GF_AUTH_PROXY_AUTO_SIGN_UP=true
      - GF_SECURITY_ALLOW_EMBEDDING=true
    ports:
      - "3000:3000"
    volumes:
      - grafana-data:/var/lib/grafana
    restart: always

  # Redis主从集群
  redis-master:
    image: redis:6.2-alpine
    container_name: redis-master
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    ports:
      - "6379:6379"
    volumes:
      - redis-master-data:/data
    restart: always

  redis-slave:
    image: redis:6.2-alpine
    container_name: redis-slave
    command: redis-server --slaveof redis-master 6379 --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis-slave-data:/data
    depends_on:
      - redis-master
    restart: always

  # MySQL主从集群
  mysql-master:
    image: mysql:8.0
    container_name: mysql-master
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=bpf_grafana
    ports:
      - "3306:3306"
    volumes:
      - mysql-master-data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    restart: always

  mysql-slave:
    image: mysql:8.0
    container_name: mysql-slave
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=bpf_grafana
    volumes:
      - mysql-slave-data:/var/lib/mysql
    depends_on:
      - mysql-master
    restart: always

volumes:
  grafana-data:
  redis-master-data:
  redis-slave-data:
  mysql-master-data:
  mysql-slave-data:
```

### 5.2 Kubernetes部署配置

#### 5.2.1 Deployment配置

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: bpf-grafana-proxy
  namespace: monitoring
spec:
  replicas: 3
  selector:
    matchLabels:
      app: bpf-grafana-proxy
  template:
    metadata:
      labels:
        app: bpf-grafana-proxy
    spec:
      containers:
      - name: bpf-grafana-proxy
        image: bpf-grafana-proxy:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "k8s"
        - name: GRAFANA_BASE_URL
          value: "http://grafana-service:3000"
        - name: REDIS_HOST
          value: "redis-service"
        - name: MYSQL_HOST
          value: "mysql-service"
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: bpf-secrets
              key: jwt-secret
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
```

## 6. 监控与运维

### 6.1 监控指标设计

```yaml
# 监控指标配置
monitoring:
  metrics:
    # 应用性能指标
    application:
      - name: "bpf_grafana_proxy_request_duration"
        type: "histogram"
        description: "请求处理时间"
        labels: ["method", "uri", "status"]

      - name: "bpf_grafana_proxy_request_total"
        type: "counter"
        description: "请求总数"
        labels: ["method", "uri", "status"]

      - name: "bpf_grafana_proxy_active_sessions"
        type: "gauge"
        description: "活跃会话数"

    # 权限相关指标
    permission:
      - name: "bpf_grafana_permission_check_duration"
        type: "histogram"
        description: "权限检查时间"
        labels: ["user_id", "resource_type"]

      - name: "bpf_grafana_permission_cache_hit_rate"
        type: "gauge"
        description: "权限缓存命中率"

    # SQL拦截指标
    sql:
      - name: "bpf_grafana_sql_intercept_duration"
        type: "histogram"
        description: "SQL拦截处理时间"
        labels: ["table_name", "user_id"]

      - name: "bpf_grafana_sql_intercept_total"
        type: "counter"
        description: "SQL拦截总数"
        labels: ["table_name", "action"]

    # 缓存相关指标
    cache:
      - name: "bpf_grafana_cache_operations_total"
        type: "counter"
        description: "缓存操作总数"
        labels: ["operation", "cache_name"]

      - name: "bpf_grafana_cache_hit_rate"
        type: "gauge"
        description: "缓存命中率"
        labels: ["cache_name"]
```

### 6.2 告警规则配置

```yaml
# Prometheus告警规则
groups:
  - name: bpf-grafana-proxy-alerts
    rules:
      # 高响应时间告警
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, bpf_grafana_proxy_request_duration_seconds) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "BPF-Grafana代理响应时间过高"
          description: "95%的请求响应时间超过2秒"

      # 高错误率告警
      - alert: HighErrorRate
        expr: rate(bpf_grafana_proxy_request_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "BPF-Grafana代理错误率过高"
          description: "错误率超过10%"

      # 缓存命中率低告警
      - alert: LowCacheHitRate
        expr: bpf_grafana_cache_hit_rate < 0.8
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "缓存命中率过低"
          description: "缓存命中率低于80%"

      # 权限检查时间过长告警
      - alert: SlowPermissionCheck
        expr: histogram_quantile(0.95, bpf_grafana_permission_check_duration_seconds) > 0.5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "权限检查时间过长"
          description: "95%的权限检查时间超过500ms"
```

## 7. 总结

### 7.1 设计特点

本概要设计文档基于全新的BPF框架架构，具有以下特点：

1. **模块化设计**：代理模块可独立部署，也可集成到现有服务中
2. **配置化驱动**：通过配置文件即可实现Grafana集成功能
3. **五视图完整**：涵盖逻辑视图、开发视图、进程视图、物理视图、场景视图
4. **技术线路清晰**：分四个阶段逐步实现完整功能
5. **缓存设计完善**：详细的缓存Key设计和TTL配置
6. **监控运维完备**：完整的监控指标和告警规则

### 7.2 核心价值

- **统一认证**：基于JWT Token的单点登录机制
- **精细权限**：多租户、多层级的权限控制体系
- **数据隔离**：Schema级别的租户数据隔离
- **高可用性**：集群部署和故障自动切换
- **可扩展性**：模块化设计支持功能扩展
- **易集成性**：代理模块可轻松集成到任何Java服务中

### 7.3 实施建议

1. **分阶段实施**：按照技术线路分四个阶段推进
2. **重点关注安全**：确保权限控制和数据隔离的正确实现
3. **性能优化**：重点关注缓存策略和SQL拦截性能
4. **监控完善**：建立完整的监控和告警体系
5. **文档维护**：及时更新设计文档和配置文档

通过本概要设计文档，为BPF集成Grafana项目提供了完整的技术架构指导，确保项目能够按照既定目标顺利实施。
