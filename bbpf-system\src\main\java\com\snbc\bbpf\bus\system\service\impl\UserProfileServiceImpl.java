/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.snbc.bbpf.bus.system.constans.NumberConstant;
import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.mapstruct.ClassConvertorMapper;
import com.snbc.bbpf.bus.system.service.IUserProfileService;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.system.db.common.dto.UserProfileDto;
import com.snbc.bbpf.system.db.common.entity.UserProfile;
import com.snbc.bbpf.system.db.common.mapper.UserProfileMapper;
import com.snbc.bbpf.system.db.common.vo.UserProfileVo;
import com.snbc.bbpf.system.db.common.vo.ViewVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;


/**
 * 用户配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-12-05
 */
@Service
public class UserProfileServiceImpl implements IUserProfileService {
    @Autowired
    private UserProfileMapper userProfileMapper;


    /**
     * 查询用户配置
     * @param profileCode 用户配置编码
     * @return
     */
    @Override
    public UserProfileVo getUserProfileByProfileCode(String profileCode) {
        UserProfile userProfile=userProfileMapper.selectUserProfileByProfileCode(profileCode, CurrentUser.getUserId());
        if(userProfile!=null) {
            return ClassConvertorMapper.INSTANCE.covertUserProfileVo(userProfile);
        }
        return null;
    }

    /**
     * 查询用户配置列表
     * 
     * @param userProfileDto 用户配置
     * @param pageSize 分页大小
     * @param pageNum 分页数
     * @return 用户配置
     */
    @Override
    public PageInfo<UserProfileVo> selectUserProfileList(UserProfileDto userProfileDto, int pageSize, int pageNum) {
        UserProfile userProfile=ClassConvertorMapper.INSTANCE.covertUserProfile(userProfileDto);
        PageMethod.startPage(pageNum, pageSize);
        List<UserProfile> list= userProfileMapper.selectUserProfileList(userProfile);
        PageInfo<UserProfile> page=new PageInfo<>(list);
        PageInfo<UserProfileVo> pageInfo=new PageInfo<>(ClassConvertorMapper.INSTANCE.covertUserProfileVoList(list));
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        pageInfo.setPages(page.getPages());
        pageInfo.setTotal(page.getTotal());
        pageInfo.calcByNavigatePages(page.getNavigatePages());
        return pageInfo;
    }
    /**
     * 查询用户配置列表
     *
     * @param userProfileDto 用户配置
     * @return 用户配置
     */
    @Override
    public List<UserProfileVo> exportUserProfileList(UserProfileDto userProfileDto) {
        UserProfile userProfile = ClassConvertorMapper.INSTANCE.covertUserProfile(userProfileDto);
        List<UserProfile> list = userProfileMapper.selectUserProfileList(userProfile);
        return ClassConvertorMapper.INSTANCE.covertUserProfileVoList(list);
    }
    /**
     * 新增用户配置
     * 
     * @param userProfileDto 用户配置
     * @return 受影响结果数
     */
    @Override
    public int insertUserProfile(UserProfileDto userProfileDto) {
        UserProfile userProfile = ClassConvertorMapper.INSTANCE.covertUserProfile(userProfileDto);
        userProfile.setProfileId(UUID.randomUUID().toString());
        userProfile.setCreateTime(LocalDateTime.now());
        return userProfileMapper.insertUserProfile(userProfile);
    }

    @Override
    public int saveUserProfile(UserProfileDto userProfileDto) {
        UserProfile profile=userProfileMapper.selectUserProfileByProfileCode(userProfileDto.getProfileCode(),userProfileDto.getUserId());
        if(profile==null){
            insertUserProfile(userProfileDto);
        }else{
            profile.setProfileContent(userProfileDto.getProfileContent());
            userProfileMapper.updateUserProfile(profile);
        }
        return 1;
    }

    /**
     * 修改用户配置
     * 
     * @param userProfileDto 用户配置
     * @return 受影响结果数
     */
    @Override
    public int updateUserProfile(UserProfileDto userProfileDto) {
        UserProfile userProfile = ClassConvertorMapper.INSTANCE.covertUserProfile(userProfileDto);
        return userProfileMapper.updateUserProfile(userProfile);
    }

    /**
     * 批量删除用户配置
     * 
     * @param profileIds 需要删除的用户配置主键
     * @return 受影响结果数
     */
    @Override
    public int deleteUserProfileByprofileIds(String[] profileIds) {
        return userProfileMapper.deleteUserProfileByprofileIds(profileIds);
    }

    /**
     * 删除用户配置信息
     * 
     * @param profileId 用户配置主键
     * @return 受影响结果数
     */
    @Override
    public int deleteUserProfileByprofileId(String profileId) {
        return userProfileMapper.deleteUserProfileByprofileId(profileId);
    }

    /**
     * 保存或更新视图信息
     * 此方法根据传入的视图信息（ViewVo对象）来创建或更新用户配置（UserProfile对象）
     * 它首先构建一个UserProfile对象，然后根据视图类型查询是否存在重复的配置名称
     * 如果存在，则抛出业务异常；否则，根据视图信息是更新还是插入新的配置
     *
     * @param viewVo 视图信息对象，包含需要保存或更新的视图详细信息
     */
    @Override
    public void saveOrUpdateView(ViewVo viewVo) {
        // 根据ViewVo对象构建UserProfile对象
        UserProfile userProfile = UserProfile.builder().profileId(viewVo.getId())
                .profileName(viewVo.getName()).moduleType(viewVo.getModuleType())
                .tenantCode(viewVo.getTenantCode()).profileContent(viewVo.getGroups().toJSONString()).build();
        // 获取当前用户ID
        String userId = CurrentUser.getUserId();
        int count = 0;
        // 根据视图类型查询是否存在重复的配置名称
        if (viewVo.getViewType().equals(NumberConstant.NO_ZERO)) {
            count = userProfileMapper.selectCountByProfileName(viewVo.getName(), null, viewVo.getId());
        } else {
            count = userProfileMapper.selectCountByProfileName(viewVo.getName(), userId ,viewVo.getId());
        }
        // 如果存在重复的配置名称，抛出业务异常
        if (count > NumberConstant.NO_ZERO) {
            throw new BusinessException(ErrorMessage.VIEW_EXIST);
        }
        // 不为空就是修改
        if (StringUtils.isNotBlank(userProfile.getProfileId())) {
            userProfile.setUpdateTime(LocalDateTime.now());
            userProfileMapper.updateUserProfile(userProfile);
        } else {
            // 全部可见userId设为空
            if (viewVo.getViewType().equals(NumberConstant.NO_ZERO)) {
                userProfile.setUserId(null);
            } else {
                userProfile.setUserId(userId);
            }
            // 否则新增
            userProfile.setCreateTime(LocalDateTime.now());
            userProfile.setUpdateTime(LocalDateTime.now());
            userProfile.setProfileId(UUID.randomUUID().toString());
            userProfileMapper.insertUserProfile(userProfile);
        }
    }

    @Override
    public List<Map<String, Object>> getMyView(String userId, Integer moduleType, String tenantCode) {
        //查询视图信息
        List<Map<String, Object>> myViews = userProfileMapper.getMyView(userId, moduleType,tenantCode);
        //数据库存储的查询组是字符串格式，需要转为jsonArray
        for (Map<String, Object> myView : myViews) {
            if (myView.containsKey("groups")) {
                String groups = myView.get("groups").toString();
                myView.put("groups", JSON.parseArray(groups));
            }
            if (myView.containsKey("userId") && StringUtils.isNotBlank(myView.get("userId").toString())){
                myView.put("type", NumberConstant.NO_ONE);
            } else {
                myView.put("type", NumberConstant.NO_ZERO);
            }
        }
        return myViews;
    }
}
