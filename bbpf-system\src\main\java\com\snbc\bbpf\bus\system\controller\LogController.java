/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.controller;

import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.system.constans.NumberConstant;
import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.bus.system.service.LogService;
import com.snbc.bbpf.bus.system.utils.ExcelUtil;
import com.snbc.bbpf.bus.system.utils.ResultUtil;
import com.snbc.bbpf.system.db.common.vo.ExportLogVo;
import com.snbc.bbpf.system.db.common.vo.LogQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @ClassName: BusLogController
 * @module: bbpf-bus-system
 * @Author: liuyi
 * @date: 2021/5/21
 * copyright 2020 barm Inc. All rights reserver
 */

@RestController
@RequestMapping("/console/log")
public class LogController {

    @Value ("${bbpf.system.excel.maxRows:60000}")
    private int maxRows;
    @Autowired
    private LogService logService;

    /**
     * @description: 提供日志查询功能
     * @param logQuery bus log query params object
     * @return: com.snbc.bbpf.component.config.CommonResp
     * @author: liuyi
     * @time: 2021/5/24 13:12
     */
    @GetMapping(value = "/getLogList")
    public CommonResp getLogList(LogQuery logQuery) {
        PageInfo logList = logService.getLogList(logQuery);
        return CommonResp.builder().head(ResultUtil.success()).body(logList).build();
    }

    /**
     * @description: bus log export
     * @param logQuery bus log query params object
     * @param response http servlet response
     * @return: com.snbc.bbpf.component.config.CommonResp<java.lang.String>
     * @author: liuyi
     * @time: 2021/6/8 10:38
     */
    @GetMapping(value = "/exportLogs")
    public void exportLogs(LogQuery logQuery, HttpServletResponse response) {
        List<ExportLogVo> exportLogVoList = logService.exportLogs(logQuery);
        //定义字段名称
        String[] titleColumn = {"createTime", "userName", "ip", "logTarget", "logType", "logContent", "remarks"};
        //定义列表名称
        String[] titleName = {"日志时间", "用户名", "登录IP", "操作模块", "操作类型", "内容", "备注"};
        //定义列长度
        int[] titleSize = {NumberConstant.NO_TWENTY_FOUR, NumberConstant.NO_TWENTY, NumberConstant.NO_THIRTY,
                NumberConstant.NO_FIFTEEN, NumberConstant.NO_SEVENTEEN, NumberConstant.NO_SIXTY, NumberConstant.NO_SIXTY};
        ExcelUtil.writeBigExcel(response, "系统日志记录表", titleColumn, titleName, titleSize, exportLogVoList, null);
    }

}
