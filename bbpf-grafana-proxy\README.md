# BBPF Grafana Proxy Service

## 项目简介

BBPF Grafana代理服务是一个基于Spring Boot的微服务，用于在BBPF系统和Grafana之间提供安全的代理服务。该服务实现了用户认证、权限控制、请求转发等核心功能，确保只有授权用户才能访问相应的Grafana资源。

## 核心功能

### 1. 用户认证
- JWT Token验证
- 用户身份识别
- 会话管理

### 2. 权限控制
- 基于BBPF权限系统的访问控制
- 仪表盘级别的权限验证
- 数据源访问权限管理
- 数据导出权限控制

### 3. 请求代理
- HTTP请求转发到Grafana
- 请求头修改和适配
- 响应内容处理
- 错误处理和重试机制

### 4. 缓存优化
- Redis缓存权限信息
- 服务健康状态缓存
- 可配置的缓存策略

### 5. 监控和统计
- 请求统计信息
- 服务健康检查
- 性能监控指标

## 技术架构

### 技术栈
- **框架**: Spring Boot 2.x
- **安全**: Spring Security + JWT
- **缓存**: Redis
- **HTTP客户端**: Apache HttpClient
- **序列化**: Jackson
- **测试**: JUnit 5 + Mockito
- **监控**: Spring Boot Actuator + Micrometer

### 架构图
```
[BBPF前端] --> [BBPF Grafana Proxy] --> [Grafana]
                      |
                      v
                [BBPF权限API]
                      |
                      v
                   [Redis缓存]
```

## 快速开始

### 环境要求
- Java 8+
- Maven 3.6+
- Redis 5.0+
- Grafana 8.0+

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd bbpf-grafana-proxy
```

2. **配置环境**

编辑 `src/main/resources/application.yml` 文件，配置以下关键参数：

```yaml
bbpf:
  grafana:
    proxy:
      # Grafana服务地址
      grafana-base-url: http://localhost:3000
      # BBPF权限API地址
      bbpf-permission-api-url: http://localhost:8081/api/v1/permission
      # JWT密钥
      jwt-secret: your-jwt-secret-key
      # 代理服务地址
      proxy-base-url: http://localhost:8080/bbpf-grafana-proxy/grafana

spring:
  redis:
    host: localhost
    port: 6379
```

3. **启动Redis**
```bash
redis-server
```

4. **编译和运行**
```bash
mvn clean compile
mvn spring-boot:run
```

或者打包后运行：
```bash
mvn clean package
java -jar target/bbpf-grafana-proxy-1.0.0.jar
```

### Docker部署

1. **构建Docker镜像**
```bash
docker build -t bbpf-grafana-proxy .
```

2. **运行容器**
```bash
docker run -d \
  --name bbpf-grafana-proxy \
  -p 8080:8080 \
  -e GRAFANA_BASE_URL=http://grafana:3000 \
  -e BBPF_PERMISSION_API_URL=http://bbpf-api:8081/api/v1/permission \
  -e REDIS_HOST=redis \
  bbpf-grafana-proxy
```

## 配置说明

### 核心配置项

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `bbpf.grafana.proxy.grafana-base-url` | Grafana服务地址 | http://localhost:3000 |
| `bbpf.grafana.proxy.bbpf-permission-api-url` | BBPF权限API地址 | http://localhost:8081/api/v1/permission |
| `bbpf.grafana.proxy.jwt-secret` | JWT签名密钥 | bbpf-grafana-proxy-jwt-secret-key-2024 |
| `bbpf.grafana.proxy.enable-permission-cache` | 是否启用权限缓存 | true |
| `bbpf.grafana.proxy.permission-cache-expiration-seconds` | 权限缓存过期时间(秒) | 1800 |

### 环境配置

项目支持多环境配置：
- `dev`: 开发环境
- `test`: 测试环境
- `prod`: 生产环境

使用方式：
```bash
java -jar bbpf-grafana-proxy.jar --spring.profiles.active=prod
```

## API文档

### 健康检查
```
GET /health
```

响应示例：
```json
{
  "status": "UP",
  "grafanaAvailable": true,
  "permissionServiceAvailable": true,
  "timestamp": 1703123456789,
  "details": {
    "grafana": "UP",
    "permissionService": "UP"
  }
}
```

### 统计信息
```
GET /grafana/stats
Authorization: Bearer <jwt-token>
```

响应示例：
```json
{
  "totalRequests": 1000,
  "successfulRequests": 950,
  "failedRequests": 30,
  "deniedRequests": 20,
  "successRate": 95.0,
  "grafanaAvailable": true,
  "permissionServiceAvailable": true,
  "timestamp": 1703123456789
}
```

### 代理请求
```
<HTTP_METHOD> /grafana/<grafana-path>
Authorization: Bearer <jwt-token>
```

所有到 `/grafana/**` 的请求都会被代理到Grafana服务。

## 权限模型

### 用户权限结构
```json
{
  "userId": "user123",
  "username": "john.doe",
  "accessibleDashboardIds": ["dashboard-1", "dashboard-2"],
  "accessibleFolderIds": ["folder-1", "folder-2"],
  "accessibleDataSourceIds": ["datasource-1", "datasource-2"],
  "permissions": ["view", "edit", "export"],
  "dataFilter": "department='IT'",
  "expirationTime": 1703123456789
}
```

### 权限类型
- `view`: 查看权限
- `edit`: 编辑权限
- `export`: 导出权限
- `admin`: 管理权限

## 安全特性

### JWT认证
- 支持标准JWT Token格式
- 自动提取用户信息
- Token过期检查
- 签名验证

### 权限验证
- 请求级别的权限检查
- 资源级别的访问控制
- 操作级别的权限验证

### 安全头
- CORS配置
- 安全响应头
- 请求头过滤

## 监控和运维

### 健康检查
服务提供多层次的健康检查：
- 应用程序健康状态
- Grafana连接状态
- BBPF权限服务状态
- Redis连接状态

### 监控指标
通过Spring Boot Actuator提供的监控端点：
- `/actuator/health`: 健康检查
- `/actuator/metrics`: 性能指标
- `/actuator/prometheus`: Prometheus格式指标

### 日志配置
- 结构化日志输出
- 可配置的日志级别
- 文件滚动策略
- 请求追踪日志

## 故障排除

### 常见问题

1. **JWT认证失败**
   - 检查JWT密钥配置
   - 验证Token格式和有效期
   - 查看认证过滤器日志

2. **权限验证失败**
   - 检查BBPF权限API连接
   - 验证用户权限配置
   - 查看权限服务日志

3. **Grafana连接失败**
   - 检查Grafana服务状态
   - 验证网络连接
   - 查看代理服务日志

4. **Redis连接问题**
   - 检查Redis服务状态
   - 验证连接配置
   - 查看缓存相关日志

### 日志分析

关键日志模式：
```
# 认证成功
Authentication successful for user: username (userId), session: sessionId

# 权限验证失败
Access denied for user userId to resource resourceId

# 代理请求成功
Proxying request: GET /api/dashboards for user: userId

# 服务健康检查
Grafana health check result: true, status code: 200
```

## 开发指南

### 项目结构
```
src/
├── main/
│   ├── java/
│   │   └── com/snbc/bbpf/grafana/proxy/
│   │       ├── config/          # 配置类
│   │       ├── controller/      # 控制器
│   │       ├── dto/            # 数据传输对象
│   │       ├── filter/         # 过滤器
│   │       ├── service/        # 服务层
│   │       └── utils/          # 工具类
│   └── resources/
│       └── application.yml     # 配置文件
└── test/
    └── java/                   # 测试代码
```

### 扩展开发

1. **添加新的权限类型**
   - 修改 `UserPermissionDto`
   - 更新权限验证逻辑
   - 添加相应的测试

2. **自定义认证方式**
   - 实现新的认证过滤器
   - 修改Security配置
   - 更新JWT工具类

3. **添加新的监控指标**
   - 使用Micrometer添加自定义指标
   - 配置Prometheus导出
   - 更新监控面板

### 测试

运行单元测试：
```bash
mvn test
```

运行集成测试：
```bash
mvn verify
```

生成测试报告：
```bash
mvn jacoco:report
```

## 版本历史

### v1.0.0 (2024-12-13)
- 初始版本发布
- 实现基础代理功能
- 支持JWT认证
- 集成BBPF权限系统
- 添加Redis缓存支持
- 提供监控和健康检查

## 贡献指南

1. Fork项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目维护者: BBPF Team
- 邮箱: <EMAIL>
- 项目地址: [GitHub Repository](https://github.com/bbpf/bbpf-grafana-proxy)

## 致谢

感谢以下开源项目的支持：
- Spring Boot
- Spring Security
- Redis
- Apache HttpClient
- Grafana