package com.snbc.bbpf.bus.system.service;

import com.snbc.bbpf.system.db.common.vo.CheckSmsCodeVo;

/**
 * @ClassName: SmsService
 * @Description: 下发短信service
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2022/1/19
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
public interface SmsService {
    void getPhoneSmsCode(String userPhone, Integer msgType) throws Exception;

    /**
     *  @param checkSmsCode
     */
    void checkSmsCode(CheckSmsCodeVo checkSmsCode) throws Exception;

    void currentUserSendSmsCode(Integer msgType) throws Exception;
}
