/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service.impl;

import com.snbc.bbpf.bus.system.constans.CommonConstant;
import com.snbc.bbpf.bus.system.enums.SmsKeyEnum;
import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.handler.ThirdBindServiceFactory;
import com.snbc.bbpf.bus.system.handler.UserLoginFailHandler;
import com.snbc.bbpf.bus.system.handler.UserLoginHandler;
import com.snbc.bbpf.bus.system.service.ThirdLoginBind;
import com.snbc.bbpf.bus.system.service.ThirdLoginService;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.bus.system.utils.RedisCheckCodeLoginFlag;
import com.snbc.bbpf.bus.system.utils.SendMsgUtil;
import com.snbc.bbpf.commons.randoms.SnowflakeUtils;
import com.snbc.bbpf.system.db.common.dto.CheckSmsCodeDto;
import com.snbc.bbpf.system.db.common.dto.UserBindMsgDto;
import com.snbc.bbpf.system.db.common.entity.User;
import com.snbc.bbpf.system.db.common.entity.login.ReturnUser;
import com.snbc.bbpf.system.db.common.mapper.UserLoginMapper;
import com.snbc.bbpf.system.db.common.vo.QrcodeVo;
import com.snbc.bbpf.system.db.common.vo.UserBindVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
 * @ClassName: ThirdLoginService
 * @Description: 用户三方扫码登录业务处理,
 * 用户绑定、解绑、查询二维码状态、获取二维码，获取二维码绑定情况
 * 更改
 * @module: si-bbpf-umis
 * @Author: liangjb
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Slf4j
@Service
public class ThirdLoginServiceImpl implements ThirdLoginService {

    public static final int OFFSET = 48;
    //用户注册登录数据解些
    @Autowired
    private UserLoginMapper userLoginMapper;
    /**
     * Redis 缓存
     */
    @Autowired
    private RedisCheckCodeLoginFlag redisCheckCodeLoginFlag;
    // 雪花WORKID
    @Value("${bbpf.system.snowk.workerId}")
    private Long workerId;
    // 雪花WORKID
    @Value("${bbpf.system.snowk.datacenterId}")
    private Long datacenterId;
    // 雪花WORKID
    @Value("${bbpf.system.snowk.sequence}")
    private Long sequence;
    // 雪花算法
    /**
     * 用户登录公用HADELE
     */
    @Autowired
    private UserLoginHandler userLoginHandler;

    @Value("${bbpf.boss.login.type:ldap,sms,account,qrcode}")
    private String loginType;

    @Autowired
    private ShortenUrlService shortenUrlService;


    /**
     * 获得用户绑定二维码状态
     * 当长度为6表示正常放回
     * 长度为空表示已经失效
     * 长度大于六表示该Code 存的userID,初次使用，为未扫码
     *
     * @param bindId
     * @return
     * @throws Exception
     */
    @Override
    public String getUserBindQRcodeState(String bindId) throws Exception {
        String result = redisCheckCodeLoginFlag.getRedis(bindId);
        log.info("LoginReids key={},value={}", bindId, result);
        if (StringUtils.isEmpty(result)) {
            throw new BusinessException(ErrorMessage.USER_BIND_TIMEOUT);
        }
        //如果 已经处理过则删除
        if (!CommonConstant.NO_SIX.equals(result.length())) {
            throw new BusinessException(ErrorMessage.USER_BIND_NOT_SCAN);
        }
        redisCheckCodeLoginFlag.delRedis(bindId);
        throw new BusinessException("", result);
    }

    /**
     * 检验手机验证码获得用户绑定二维码
     *
     * @param checkSmsCodeDto
     * @return
     * @throws Exception
     */
    @Override
    public QrcodeVo checkSmsCode(CheckSmsCodeDto checkSmsCodeDto) throws Exception {
        UserBindMsgDto userBindDto = userLoginMapper.selectUserMsgbyUserId(CurrentUser.getUserId());
        if (null == userBindDto) {
            throw new BusinessException(ErrorMessage.USER_LOGIN_DISABLE);
        }
        //2调用检验验证码方法-校验短信验证码
        try {
            SendMsgUtil.checkSmsCode(String.format(SmsKeyEnum.getStatusName(Integer.valueOf(checkSmsCodeDto.getMsgType()))
                    , CurrentUser.getUserId()), checkSmsCodeDto.getSmsCode(), true);
        } catch (BusinessException e) {
            //登陆失败后 计算失败次数
            UserLoginFailHandler.modifyLoginFailCount(User.builder()
                    .userId(CurrentUser.getUserId()).userName(userBindDto.getUserName()).build(), 1);
            //抛出验证码错误异常
            log.warn("error:{}", e);
            throw new BusinessException(ErrorMessage.SMS_CODE_ERROR);
        }
        //3,检查成功则更新Redis
        return dealUserLoginQRcode("BIND" + SnowflakeUtils.genId(), checkSmsCodeDto.getMsgType(),
                CurrentUser.getUserId());
    }

    /**
     * 解除用户绑定
     * 1，删除对应表用户表数据
     *
     * @param bindType
     * @return
     * @throws Exception
     */
    @Override
    public String delUserThirdBind(String bindType) throws Exception {
        ThirdLoginBind thirdHandlerInterface = ThirdBindServiceFactory.
                getThirdLoginBindServiceImpl(bindType);
        //如果数据不存在报错误
        if (StringUtils.isEmpty(CurrentUser.getUserId()) || null == thirdHandlerInterface) {
            throw new BusinessException(ErrorMessage.FAILED);
        }
        return thirdHandlerInterface.delUserThirdBind(CurrentUser.getUserId());
    }


    /**
     * 获得用户登录二维码状态
     * 如果成功则返回的是该用户的userId
     *
     * @param bindId
     * @return
     * @throws Exception
     */
    @Override
    public ReturnUser getUserLoginQRcodeState(String bindId) throws Exception {
        //1.读取缓存状态
        String result = redisCheckCodeLoginFlag.getRedis(bindId);
        log.info("LoginReids key={},value={}", bindId, result);
        //2，判断是否为空
        if (StringUtils.isEmpty(result)) {
            throw new BusinessException(ErrorMessage.USER_LOGIN_TIMEOUT);
        }
        if (ErrorMessage.USER_LOGIN_NOT_SCAN.getCode().equals(result)) {
            throw new BusinessException(ErrorMessage.USER_LOGIN_NOT_SCAN);
        }
        redisCheckCodeLoginFlag.delRedis(bindId);
        //如果处理过则清空缓存
        if (CommonConstant.NO_SIX.equals(result.length())) {
            throw new BusinessException("", result);
        }
        return userLoginHandler.verifLogin(result, CommonConstant.ZERO.toString());
    }

    /**
     * 获得用户登录二维码
     *
     * @param thirdType
     * @return
     * @throws Exception
     */
    @Override
    public QrcodeVo getUserLoginQRcode(String thirdType) throws Exception {
        if (!loginType.contains(CommonConstant.QRCODE_LOGIN_TYPE)) {
            throw new BusinessException(ErrorMessage.USER_LOGIN_NOT_HANDLE);
        }
        //1，生成一个随机数Bind+snowID()
        return dealUserLoginQRcode("LOGIN" + SnowflakeUtils.genId(), thirdType,
                ErrorMessage.USER_LOGIN_NOT_SCAN.getCode());
    }

    /**
     * 获得用户登录绑定详情 请求主表数据查看是否
     * 其中第一位表示微信，第二位钉钉，如果获取失败则未保存
     *
     * @return
     * @throws Exception
     */
    @Override
    public List<UserBindVo> getUserBindInfo() throws Exception {
        String bindState = userLoginMapper.selectUserBindByid(CurrentUser.getUserId());
        List<UserBindVo> result = new ArrayList<>();
        result.add(UserBindVo.builder().bindName("WX").bindState(getBindState(bindState, 0)).build());
        result.add(UserBindVo.builder().bindName("DD").bindState(getBindState(bindState, 1)).build());
        return result;
    }

    /**
     * 根据位置获取相应状态 从0开始算
     *
     * @param bindState
     * @param size
     * @return
     */
    private Integer getBindState(String bindState, Integer size) {
        Integer result = 0;
        if (StringUtils.isNotEmpty(bindState) && bindState.length() > size) {
            result = bindState.charAt(size) - OFFSET;
        }
        return result.equals(1) ? 1 : 0;
    }

    /**
     * 获得用户登录二维码
     *
     * @param thirdType
     * @return
     * @throws Exception
     */
    private QrcodeVo dealUserLoginQRcode(String snowId, String thirdType, String redisValue) throws Exception {
        //1,获取独赢测思路的CODE
        ThirdLoginBind thirdHandlerInterface = ThirdBindServiceFactory.
                getThirdLoginBindServiceImpl(thirdType);
        String shortUrl=shortenUrlService.shortenUrl(thirdHandlerInterface.getUserLoginQRcode(snowId));
        QrcodeVo result = QrcodeVo.builder().qrCodeParams(snowId)
                .qrCodeUrl(shortUrl).build();
        //2,插入到Redis缓存
        // 验证码存入redis，失效5分钟为可配
        redisCheckCodeLoginFlag.insertRedis(snowId, redisValue);

        log.info("dealUserLoginQRcode key={},value={}", snowId, thirdType);
        return result;
    }
}
