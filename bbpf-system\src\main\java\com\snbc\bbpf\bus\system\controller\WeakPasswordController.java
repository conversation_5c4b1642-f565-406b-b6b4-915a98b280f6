/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.controller;

import com.snbc.bbpf.bus.system.service.IWeakPasswordService;
import com.snbc.bbpf.system.db.common.dto.WeakPasswordDto;
import com.snbc.bbpf.system.db.common.vo.WeakPasswordVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
/**
 * 弱密码管理Controller
 *
 * <AUTHOR>
 * @date 2023-01-16
 */
@RestController
@RequestMapping("/console/password")
public class WeakPasswordController {
    @Autowired
    private IWeakPasswordService weakPasswordService;


    /**
     * 导出弱密码管理列表
     *
     */
    @GetMapping("/list")
    public List<WeakPasswordVo> export() {
        return weakPasswordService.exportWeakPasswordList(null);
    }

    /**
     * 获取弱密码管理详细信息
     *
     */
    @GetMapping(value = "/detail")
    public WeakPasswordVo getInfo(@RequestParam("id") String id) {
        return weakPasswordService.selectWeakPasswordById(id);
    }

    /**
     * 新增弱密码管理
     */
    @PostMapping(value = "/add")
    public int add(@Validated @RequestBody WeakPasswordDto weakPasswordDto) {
        return  weakPasswordService.insertWeakPassword(weakPasswordDto);
    }

    /**
     * 修改弱密码管理
     */
    @PutMapping(value = "/edit")
    public int edit(@Validated @RequestBody WeakPasswordDto weakPasswordDto) {
        return weakPasswordService.updateWeakPassword(weakPasswordDto);
    }

    /**
     * 删除弱密码管理
     */
    @DeleteMapping("/delete")
    public int remove(@RequestParam("ids") String[] ids) {

        return weakPasswordService.deleteWeakPasswordByIds(ids);
    }
}
