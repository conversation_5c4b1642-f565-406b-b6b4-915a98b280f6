package com.snbc.bbpf.bus.system.utils;

import ma.glasnost.orika.MapperFacade;
import ma.glasnost.orika.MapperFactory;
import ma.glasnost.orika.impl.DefaultMapperFactory;
import ma.glasnost.orika.metadata.ClassMapBuilder;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/***********************************************************************
 * Packet
 * Project Name:vems
 *
 * <AUTHOR> href="mailto:<EMAIL>">许云锋</a>
 * @creator 许云锋
 * @create-time 2018-06-25 15:23
 ***********************************************************************/
public final class ConverterUtils {


    private static final MapperFactory MAPPER_FACTORY = new DefaultMapperFactory.Builder().build();
    /***
      * @Description:    class类型转换
      * @Author:         wangsong
      * @param :         sourceObject
      * @param :         targetClass
      * @CreateDate:     2020/10/12 16:26
      * @UpdateUser:     wangsong
      * @UpdateDate:     2020/10/12 16:26
      * @return :        T
     */
    public static <S, T> T getSimpleConverObject(S sourceObject, Class<T> targetClass) {

        ClassMapBuilder<?, T> tClassMapBuilder = MAPPER_FACTORY.classMap(sourceObject.getClass(), targetClass);
        tClassMapBuilder.exclude("");
        MapperFacade mapper = MAPPER_FACTORY.getMapperFacade();
        return mapper.map(sourceObject, targetClass);
    }

    /***
      * @Description:    两个class不同名属性转换
      * @Author:         wangsong
      * @param :         sourceObjectList
      * @param :         targetClass
      * @param :         fieldMap
      * @CreateDate:     2020/10/12 16:26
      * @UpdateUser:     wangsong
      * @UpdateDate:     2020/10/12 16:26
      * @return :        java.util.List<T>
     */
    public static <S, T> List<T> getConverObjectList(List<S> sourceObjectList, Class<T> targetClass, Map<String, String> fieldMap) {
        if (CollectionUtils.isEmpty(sourceObjectList)) {
            return new ArrayList<>();
        }
        if (MapUtils.isEmpty(fieldMap)) {
            MAPPER_FACTORY.classMap(sourceObjectList.get(0).getClass(), targetClass).byDefault();
        } else {
            ClassMapBuilder<?, T> classMapBuilder = MAPPER_FACTORY.classMap(sourceObjectList.get(0).getClass(), targetClass);
            for (Map.Entry<String,String> entry : fieldMap.entrySet()) {
                classMapBuilder.field(entry.getKey(), entry.getValue());
            }
            classMapBuilder.byDefault().register();
        }
        MapperFacade mapper = MAPPER_FACTORY.getMapperFacade();
        return sourceObjectList.stream().map(s -> mapper.map(s, targetClass)).collect(Collectors.toList());
    }

    public static <S, T> List<T> getConverObjectList(List<S> sourceObjectList, Class<T> targetClass) {
        if (CollectionUtils.isEmpty(sourceObjectList)) {
            return new ArrayList<>();
        }
        MAPPER_FACTORY.classMap(sourceObjectList.get(0).getClass(), targetClass).byDefault();
        MapperFacade mapper = MAPPER_FACTORY.getMapperFacade();
        return sourceObjectList.stream().map(s -> mapper.map(s, targetClass)).collect(Collectors.toList());

    }

    public static <S, T> T getSimpleConverObject(S sourceObject, Class<T> targetClass, Map<String, String> fieldMap) {
        if (MapUtils.isEmpty(fieldMap)) {
            MAPPER_FACTORY.classMap(sourceObject.getClass(), targetClass).byDefault();
        } else {
            ClassMapBuilder<?, T> classMapBuilder = MAPPER_FACTORY.classMap(sourceObject.getClass(), targetClass);
            for (Map.Entry<String, String> entry : fieldMap.entrySet()) {
                classMapBuilder.field(entry.getKey(), entry.getValue());
            }
            classMapBuilder.byDefault().register();
        }
        MapperFacade mapper = MAPPER_FACTORY.getMapperFacade();
        return mapper.map(sourceObject, targetClass);
    }
}
