/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service;

import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.system.db.common.entity.User;
import com.snbc.bbpf.system.db.common.vo.NoticeUserVo;
import com.snbc.bbpf.system.db.common.vo.UpdatePwdVo;
import com.snbc.bbpf.system.db.common.vo.UpdateUserPwdVo;
import com.snbc.bbpf.system.db.common.vo.UserVo;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: UserService
 * @Description: 用户业务处理
 * @module: si-bbpf-system
 * @Author: wangsong
 * @date: 2021/5/15
 *        Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all
 *        rights reserved.
 */
public interface UserService {

    UserVo getDetail(String userId);

    UserVo getUserById(String userId);
    /**
     * 新增角色时，反显当前用户默认的组织机构
     * @return
     */
    List<Map<String,String>> getUserOrgInfo4Role();

    User queryByJobNumber(String jobNumber);

    /**
     * 判断是否管理员
     * @param userId
     * @return
     */
    boolean isAdmin(String userId);
    /**
     * 更新密码， wangsong
     */
    void updatePwd(UpdatePwdVo userVo,String smsCodePrefix) throws Exception;

    /**
     * 修改用户状态
     */
    void updateUserStatus(String userId, Integer status);

    /**
     * 更新头像， liangjubin
     */
    void updateAvatar(String userAvatar, String userId);

    CommonResp smsVerifCode(String userId) throws Exception;

    void updateUserCondition(String userId, int userStatus);

    /**
     * 忘记密码修改密码， yangweipeng
     */
    void updateUserPwd(UpdateUserPwdVo userVo) throws Exception;

    void updateUserPhone(String phone, String smsVerifCode) throws Exception;

    void getSmsCodeAndVerifPhoneBind(String phone) throws Exception;

    List<NoticeUserVo> noticeUserList(String orgIds, List<String> userIds, String userNamePhone) throws Exception;
}
