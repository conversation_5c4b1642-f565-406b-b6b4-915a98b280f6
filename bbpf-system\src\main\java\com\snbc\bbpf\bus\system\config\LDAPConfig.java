package com.snbc.bbpf.bus.system.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * LDAP 配置项
 */
@Component
@Data
@ConditionalOnProperty(prefix = "bbpf.ldap", name = "ldapUrl")
@ConfigurationProperties(prefix = "bbpf.ldap")
public class LDAPConfig {

    /**
     * ldap 服务地址
     */
    private String ldapUrl;
    /**
     * ldap
     */
    private String domain;
    /**
     * base DN
     */
    @Value("bbpf.ldap.baseDn")
    private String baseDn;
    /**
     * 管理用户
     */
    private String adminUser;

    private String adminPass;
    /**
     * 过滤条件
     */
    private String filter;

    /**
     * ldap类型 ad表示window域，open表示openldap域
     */
    private String type;
}
