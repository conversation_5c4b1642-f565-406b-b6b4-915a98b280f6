package com.snbc.bbpf.bus.system.controller;

import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.bus.system.service.NoticeService;
import com.snbc.bbpf.bus.system.service.OrgService;
import com.snbc.bbpf.bus.system.service.UserService;
import com.snbc.bbpf.bus.system.utils.AllOrg;
import com.snbc.bbpf.bus.system.utils.ResultUtil;
import com.snbc.bbpf.system.db.common.vo.NoticeDetailVo;
import com.snbc.bbpf.system.db.common.vo.NoticeOrgVo;
import com.snbc.bbpf.system.db.common.vo.NoticeUserVo;
import com.snbc.bbpf.system.db.common.vo.OrgVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName: NoticeController
 * @Description: 给信息公告页面用 + bbpf-message-center服务feign调用
 * @module: SI-bbpf-tenant-config
 * @Author: wangsong
 * @date: 2023/1/6
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@RestController
@RequestMapping("/console/notice")
public class NoticeController {

    @Autowired
    private UserService userService;
    @Autowired
    private OrgService orgService;
    @Autowired
    private AllOrg allOrg;
    @Autowired
    private NoticeService noticeService;

    /**
     * boss前端调用，点击左侧组织机构树，查用户
     * @param orgIds
     * @param userIds
     * @param userNamePhone
     * @return
     * @throws Exception
     */
    @GetMapping("/noticeUserList")
    public CommonResp<List<NoticeUserVo>> getUserList(@RequestParam(value = "orgIds",required = false) String orgIds,
                                  @RequestParam(value = "userIds",required = false) List<String>userIds,
                                  @RequestParam(value = "userNamePhone",required = false) String userNamePhone) throws Exception {
        List<NoticeUserVo> noticeUserVoList = userService.noticeUserList(orgIds, userIds, userNamePhone);
        return CommonResp.<List<NoticeUserVo>>builder().head(ResultUtil.success()).body(noticeUserVoList).build();
    }

    @GetMapping("/noticeOrgName")
    public CommonResp noticeOrgName(@RequestParam(value = "orgIdList",required = false)List<String> orgIdList){
        List<NoticeOrgVo> noticeOrgName = orgService.getNoticeOrgName(orgIdList);
        return CommonResp.builder().head(ResultUtil.success()).body(noticeOrgName).build();
    }

    /***
     * @Description:    查询当前人的组织机构（带数据权限）,消息中调用
     * @Author:         wjc
     * @param :         userId
     * @return:         com.snbc.bbpf.component.config.CommonResp
     * @CreateDate:     2023/7/5 15:11
     * @UpdateDate:     2023/7/5 15:11
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @GetMapping("/getOrgId4DataRuleByUserId")
    public CommonResp<List<String>> getOrgId4DataRuleByUserId(String userId){
        List<OrgVo> orgVoList = allOrg.getDataRuleOrg(userId);
        List<String> orgIdList = orgVoList.stream().map(OrgVo::getOrgId).filter(orgId -> !"0".equals(orgId)).collect(Collectors.toList());
        return CommonResp.<List<String>>builder().head(ResultUtil.success()).body(orgIdList).build();
    }

    /**
     * 信息公告详情，调用消息中心
     * @param msgId
     * @return
     */
    @GetMapping("/noticeDetail")
    public CommonResp<NoticeDetailVo> noticeDetail(String msgId){
        NoticeDetailVo noticeDetailVo = noticeService.noticeDetail(msgId);
        return CommonResp.<NoticeDetailVo>builder().head(ResultUtil.success()).body(noticeDetailVo).build();
    }
}
