package com.snbc.bbpf.bus.system.job;

import com.snbc.bbpf.bus.system.constans.NumberConstant;
import com.snbc.bbpf.bus.system.service.LogService;
import com.snbc.bbpf.system.db.common.mapper.LogMapper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * @ClassName: ClearLogJob
 * @Description: 清理日志job
 * @module: SI-bbpf-system
 * @Author: wangsong
 * @date: 2023/6/12
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Component
@Slf4j
public class ClearLogJob {

    @Autowired
    private LogMapper logMapper;
    @Autowired
    private LogService logService;
    @Value("${spring.datasource.driver-class-name}")
    private String driverClass;

    @XxlJob("bossLogClear")
    public void clearLog() {
        //获取前一天日期
        String yesterday = LocalDateTime.now().plusDays(NumberConstant.F_NO_ONE)
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        if (driverClass.contains("mysql")) {
            int result = logMapper.clearLog(yesterday);
            if (result != NumberConstant.NO_ZERO){
                throw new RuntimeException();
            }
        }
        if (driverClass.contains("opengauss")) {
            logMapper.clearLogByOpenGauss(yesterday);
            logService.retentionDays();
        }
    }
}
