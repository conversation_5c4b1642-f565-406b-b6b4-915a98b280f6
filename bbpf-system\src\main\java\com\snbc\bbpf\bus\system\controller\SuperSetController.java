/*
 * 版权所有 2009-2024山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.bus.system.controller;

import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.resp.CallBaseResponse;
import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.bus.system.service.SuperSetService;
import com.snbc.bbpf.bus.system.utils.ResultUtil;
import com.snbc.bbpf.system.db.common.vo.SuperSetUserVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * superset相关数据获取
 *
 * @ClassName: SuperSetController
 * @module: SI-bbpf-system
 * @Author: wangsong
 * @date: 2024/3/16
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@RestController
public class SuperSetController {

    @Autowired
    private SuperSetService superSetService;

    /**
     * 通过 API 获取当前用户信息
     *
     * @return CommonResp 包含操作结果头部和用户信息主体的对象
     */
    @GetMapping("/api/superset/getuser")
    public CommonResp getUser(){
        // 从 superSetService 中获取用户信息
        SuperSetUserVo user = superSetService.getUser();
        // 构建并返回一个包含成功标志和用户信息的响应对象
        return CommonResp.builder().head(ResultUtil.success()).body(user).build();
    }

    @GetMapping("/api/datapermission")
    public CommonResp getDataPermission(@RequestParam String userId){
        if (StringUtils.isBlank(userId)){
            return CommonResp.builder()
                    .head(CallBaseResponse.builder()
                            .code(ErrorMessage.FAILED.getCode()).message("userId不能为空").build())
                    .build();
        }
        // 从 superSetService 中获取用户信息
        SuperSetUserVo dataPermission = superSetService.getDataPermission(userId);
        // 构建并返回一个包含成功标志和用户信息的响应对象
        return CommonResp.builder().head(ResultUtil.success()).body(dataPermission).build();
    }
}
