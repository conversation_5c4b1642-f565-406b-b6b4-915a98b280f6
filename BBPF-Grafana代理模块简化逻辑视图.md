# BBPF-Grafana代理模块简化逻辑视图

## 1. 整体架构概览

```mermaid
graph TB
    subgraph "用户层"
        A[用户浏览器]
    end
    
    subgraph "代理服务层"
        B[Token验证]
        C[请求代理]
        D[权限控制]
        E[响应处理]
    end
    
    subgraph "缓存层"
        F[Redis缓存]
    end
    
    subgraph "后端服务层"
        G[Grafana服务]
        H[权限服务]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    E --> A
    
    B -.-> F
    D -.-> F
    
    C --> G
    D --> H
```

## 2. 核心处理流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant P as 代理服务
    participant C as Redis缓存
    participant G as Grafana
    participant A as 权限服务
    
    U->>P: 1. 发送请求
    P->>P: 2. Token验证
    
    alt 需要权限检查
        P->>C: 3. 查询权限缓存
        alt 缓存未命中
            P->>A: 4. 获取用户权限
            A->>P: 5. 返回权限信息
            P->>C: 6. 更新缓存
        end
    end
    
    P->>G: 7. 转发请求
    G->>P: 8. 返回响应
    P->>P: 9. 处理响应(注入变量/控制分享)
    P->>U: 10. 返回最终响应
```

## 3. 五大核心功能

```mermaid
mindmap
  root((代理服务))
    Token验证
      JWT解析
      用户认证
      白名单放行
    接口代理
      请求转发
      响应处理
      错误处理
    SQL拦截
      SQL解析
      权限注入
      查询重写
    变量注入
      权限变量
      动态生成
      模板修改
    分享控制
      功能禁用
      UI修改
      权限控制
```

## 4. 数据流向图

```mermaid
flowchart LR
    subgraph "输入"
        A[HTTP请求]
        B[用户Token]
    end
    
    subgraph "处理中心"
        C[代理服务]
    end
    
    subgraph "数据源"
        D[权限缓存]
        E[配置信息]
    end
    
    subgraph "输出"
        F[修改后的响应]
        G[权限控制的数据]
    end
    
    A --> C
    B --> C
    C <--> D
    C <--> E
    C --> F
    C --> G
```

## 5. 关键特性

### 5.1 安全性
- **多层认证**: Token验证 + 权限控制
- **数据隔离**: SQL级别的权限过滤
- **功能控制**: 分享和导出功能管控

### 5.2 性能优化
- **缓存机制**: Redis缓存用户权限和配置
- **连接复用**: 与Grafana的长连接管理
- **异步处理**: 非阻塞的请求处理

### 5.3 透明代理
- **无感知**: 用户无需修改使用习惯
- **完整功能**: 支持Grafana所有功能
- **动态注入**: 自动注入权限相关变量

## 6. 部署架构

```mermaid
graph TB
    subgraph "负载均衡层"
        LB[负载均衡器]
    end
    
    subgraph "代理服务集群"
        P1[代理实例1]
        P2[代理实例2]
        P3[代理实例N]
    end
    
    subgraph "缓存集群"
        R1[Redis主节点]
        R2[Redis从节点]
    end
    
    subgraph "后端服务"
        GF[Grafana集群]
        PS[权限服务]
    end
    
    LB --> P1
    LB --> P2
    LB --> P3
    
    P1 --> R1
    P2 --> R1
    P3 --> R1
    R1 --> R2
    
    P1 --> GF
    P2 --> GF
    P3 --> GF
    
    P1 --> PS
    P2 --> PS
    P3 --> PS
```

## 7. 总结

这个简化的逻辑视图突出了BBPF-Grafana代理模块的核心架构和关键功能：

1. **清晰的分层结构**: 用户层 → 代理层 → 缓存层 → 后端层
2. **五大核心功能**: Token验证、接口代理、SQL拦截、变量注入、分享控制
3. **高效的数据流**: 请求 → 验证 → 代理 → 处理 → 响应
4. **完善的缓存机制**: Redis缓存提升性能
5. **灵活的部署方案**: 支持集群化部署

该架构确保了系统的安全性、性能和可扩展性，为企业级Grafana集成提供了可靠的解决方案。