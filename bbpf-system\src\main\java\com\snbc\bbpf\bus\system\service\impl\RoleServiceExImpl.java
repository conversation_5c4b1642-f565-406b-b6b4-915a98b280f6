/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.system.constans.Constant;
import com.snbc.bbpf.bus.system.mapstruct.ClassConvertorMapper;
import com.snbc.bbpf.bus.system.service.RoleServiceEx;
import com.snbc.bbpf.bus.system.utils.AllOrg;
import com.snbc.bbpf.bus.system.utils.BossDES3Util;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.commons.strings.security.parse.MobilePhoneParse;
import com.snbc.bbpf.system.db.common.dto.OrgNamesDto;
import com.snbc.bbpf.system.db.common.entity.Org;
import com.snbc.bbpf.system.db.common.entity.Role;
import com.snbc.bbpf.system.db.common.mapper.OrgMapper;
import com.snbc.bbpf.system.db.common.mapper.RoleMapper;
import com.snbc.bbpf.system.db.common.mapper.UserMapper;
import com.snbc.bbpf.system.db.common.mapper.UserRoleMapper;
import com.snbc.bbpf.system.db.common.vo.OrgVo;
import com.snbc.bbpf.system.db.common.vo.RoleDetail;
import com.snbc.bbpf.system.db.common.vo.RoleUserListVo;
import com.snbc.bbpf.system.db.common.vo.RoleUserPage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName: RoleServiceImpl
 * @Description: 角色业务层
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/5/18
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Service
@Transactional(rollbackFor = RuntimeException.class)
@Slf4j
public class RoleServiceExImpl implements RoleServiceEx {
    @Autowired
    private RoleMapper roleMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private OrgMapper orgMapper;
    @Autowired
    private AllOrg allOrg;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private UserRoleMapper userRoleMapper;

    /**
     * @param : orgId
     * @return :        void
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     * @Description: 获取为角色添加用户时的用户列表
     * @Author: wangsong
     * @CreateDate: 2021/5/20 16:35
     * @UpdateDate: 2021/5/20 16:35
     */
    @Override
    public List<RoleUserPage> getUserByOrg(String orgId) {
        String currentUser = CurrentUser.getUserId();
        boolean isAdmin = allOrg.isAdmin(currentUser);
        List<RoleUserPage> userListByOrg = Lists.newArrayList();
        // 是超管
        if(isAdmin){
            List<String> orgPathList = allOrg.processAdmin(orgId);
            userListByOrg = userMapper.getUserByOrgPath(orgPathList);
        }else{
            //true 前端的orgId是所属组织机构的上级
            List<OrgVo> orgIdPathListDataRule = allOrg.getDataRuleOrg(currentUser);
            boolean isSub = allOrg.isSub(orgIdPathListDataRule, orgId);
            Org orgExsit = allOrg.getOrgByOrgId(orgId);
            //点击的是前端 是 数据权限的组织机构的上级
            if(isSub){
                List<String> orgIdList = orgIdPathListDataRule.stream().map(OrgVo::getOrgId).collect(Collectors.toList());
                userListByOrg = userMapper.getUserByOrgId(orgIdList);
            }else {
                //如果点击的是末节点
                List<Org> childList = allOrg.queryOrgListByParentId(orgExsit.getParentId());
                if(CollectionUtils.isEmpty(childList)){
                    userListByOrg = userMapper.getUserByOrgId(Collections.singletonList(orgId));
                }else {
                    //查询当前orgId的下级，
                    List<String> orgIdList = allOrg.processGeneralUser(orgIdPathListDataRule, orgExsit.getOrgPath());
                    userListByOrg = userMapper.getUserByOrgId(orgIdList);
                }
            }
        }

        if (CollectionUtils.isEmpty(userListByOrg)) {
            return new ArrayList<>();
        }
        return phoneDesensitized(userListByOrg);
    }

    /***
     * @Description: 手机号解密脱敏
     * @Author: wangsong
     * @param :         roleUserPages
     * @CreateDate: 2021/6/3 10:49
     * @UpdateDate: 2021/6/3 10:49
     * @return :        void
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    private static List<RoleUserPage> phoneDesensitized(List<RoleUserPage> roleUserPages) {
        MobilePhoneParse parsePhone = new MobilePhoneParse();
        roleUserPages.forEach(roleUserPage ->
            roleUserPage.setPhone(parsePhone.parseString(BossDES3Util.decrypt(roleUserPage.getPhone())))
        );
        return roleUserPages;
    }

    @Override
    public List<String> queryRoleIdsByUserId(String userId) {
        Set<String> roleIds = new HashSet<>();
        List<String> integers = null;
        List<Role> roles = roleMapper.queryRoleListByUserId(userId);
        if (!CollectionUtils.isEmpty(roles)) {
            for (Role role : roles) {
                roleIds.add(role.getRoleId());
            }
            integers = new ArrayList<>(roleIds);
            redisTemplate.opsForValue().set(Constant.USERID_ROLE_ID_MAPPING + userId, integers);
        }
        return integers;
    }

    /**
     * @param : roleId
     * @param : pageNum
     * @param : pageSize
     * @param : queryParam
     * @param : status
     * @return :        com.github.pagehelper.Page
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     * @Description: 角色管理用户列表
     * @Author: wangsong
     * @CreateDate: 2021/5/20 14:46
     * @UpdateDate: 2021/5/20 14:46
     */
    @Override
    public RoleUserListVo userListPage(String roleId, Integer pageNum, Integer pageSize, String queryParam, String status) {
        List<RoleUserPage> roleUserPages;
        RoleDetail roleDetail = new RoleDetail();
        String phoneEncrypt = null;
        // queryParam 改为加密手机号
        phoneEncrypt = BossDES3Util.encrypt(queryParam);
        //获取用户本组织及下属组织机构id
        /**List<String> userAllOrgPaths = allOrg.getOrgIdsByUserId(CurrentUser.getUserId());*/
        boolean isAdmin = allOrg.isAdmin(CurrentUser.getUserId());
        if (StringUtils.isNotBlank(roleId)) {
            //超管
            if(isAdmin){
                PageHelper.startPage(pageNum, pageSize);
                roleUserPages = userMapper.selectUserListByAdmin(roleId, queryParam, phoneEncrypt);
            }else {
                //查询该角色下的用户
                PageHelper.startPage(pageNum, pageSize);
                roleUserPages = userMapper.selectUserListByRoleId(roleId, queryParam, phoneEncrypt);
            }

            Role role = roleMapper.selectRoleDetail(roleId);
            roleDetail = ClassConvertorMapper.INSTANCE.covertRoleDetail(role);
            // 反显 角色所属的组织机构
            List<Map<String, String>> orgList = roleMapper.selectOrgByRoleId(roleId);
            roleDetail.setOrgInfo(orgList);
            // 自身不能编辑和删除自己的角色
            roleDetail.setCanOperate(String.valueOf(convertCanOperate(isAdmin,roleId)));
        } else if(isAdmin){
            PageHelper.startPage(pageNum, pageSize);
            roleUserPages = userMapper.selectUnboundRoleUserListAdmin(queryParam, phoneEncrypt);
        }else {
            //查询未绑定角色的用户
            PageHelper.startPage(pageNum, pageSize);
            roleUserPages = userMapper.selectUnboundRoleUserList(queryParam, phoneEncrypt);
        }
        //用户id转list
        List<String> userIdList = roleUserPages.stream().map(RoleUserPage::getUserId).collect(Collectors.toList());
        log.debug("role find user,userIdList is ={} ",userIdList);
        if (!userIdList.isEmpty()) {
            List<OrgNamesDto> orgNamesDtos = orgMapper.selectOrgNamesByUserId(userIdList);
            roleUserPages.forEach(roleUserPage ->
                    orgNamesDtos.forEach(orgNamesDto -> {
                        if (roleUserPage.getUserId().equals(orgNamesDto.getUserId())) {
                            roleUserPage.setOrgNames(orgNamesDto.getOrgNames());
                        }
                    })
            );
        }
        return RoleUserListVo.builder().roleInfo(roleDetail)
                //手机号解密脱敏
                .userPage(new PageInfo<>(phoneDesensitized(roleUserPages)))
                .build();
    }

    /**
     * 给canOperate赋值:true能编辑和删除
     * @param roleId
     * @param isAdmin
     */
    private boolean convertCanOperate(Boolean isAdmin,String roleId){
        if(isAdmin){
            return true;
        }
        //查询该用户所属的角色列表
        List<String> roleIdList = userRoleMapper.selectRoleByUserId(CurrentUser.getUserId());
        return !roleIdList.contains(roleId);
    }
}
