package com.snbc.bbpf.bus.system.config;

import com.snbc.bbpf.bus.system.constans.NumberConstant;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName: LoginConfig
 * @Description: 登录配置
 * @module: SI-bbpf-system
 * @Author: wangsong
 * @date: 2023/5/22
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Configuration
@Data
@ConfigurationProperties(prefix = "bbpf.boss.login")
public class LoginConfig {
    private String type = "account,ldap,sms,qrcode";
    private Integer pwdExpireDay = NumberConstant.NO_ONEH_EIGHTY;
    /**
     * 密码强度：low 低，middle 中，high	高
     */
    @Value("${bbpf.boss.login.pwdStrength:high}")
    private String cipherStrength = "high";
    /**
     * 是否允许多端登录配置
     */
    @Value("${bbpf.system.allow.multiport.login}")
    private Boolean multiportLogin;
    /**
     * 默认密码
     */
    @Value("${bbpf.system.default.pwd}")
    private String defaultPassword;
    /**
     * default:默认密码  random:随机密码
     */
    @Value("${bbpf.system.generatepwd.type:random}")
    private String generateType;


    /**
     * 系统获取token是否允许登录
     */
    @Value("${bbpf.system.allow.gettoken.login:true}")
    private Boolean tokenLogin;
    /**
     * 系统获取菜单的默认父级节点
     */
    @Value("${bbpf.system.menu.parentid}")
    private String menuParentid;

    @Value("${bbpf.system.captcha.ischeck:true}")
    private String isCheckCaptcha;

}
