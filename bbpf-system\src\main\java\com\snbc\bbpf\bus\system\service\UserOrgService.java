/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service;

import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.system.db.common.entity.UserOrg;

/**
 * @ClassName: UserRoleService
 * 用户组织机构接口
 * @module: si-bbpf-system
 * @Author: wjc1
 * @date: 2021/5/19 18:19
 */
public interface UserOrgService {
    /**
     * 增加用户角色
     * @param userOrg
     * @return 影响行数
     * @throws Exception
     */
    int insert(UserOrg userOrg) throws BusinessException;

    void removeUser2Org(String userIds,String orgId);
}
