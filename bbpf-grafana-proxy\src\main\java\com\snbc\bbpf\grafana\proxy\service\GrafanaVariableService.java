package com.snbc.bbpf.grafana.proxy.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.snbc.bbpf.grafana.proxy.config.GrafanaProxyConfig;
import com.snbc.bbpf.grafana.proxy.dto.UserPermissionDto;
import com.snbc.bbpf.grafana.proxy.client.GrafanaApiClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Grafana变量管理服务
 * 
 * 负责管理Grafana仪表盘中的动态变量，实现数据级权限控制：
 * 1. 根据用户权限设置Grafana变量值
 * 2. 支持多种变量类型（Constant、Custom等）
 * 3. 提供变量值的动态计算和缓存
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2024-12-13
 */
@Service
public class GrafanaVariableService {

    private static final Logger logger = LoggerFactory.getLogger(GrafanaVariableService.class);

    @Autowired
    private GrafanaApiClient grafanaApiClient;

    @Autowired
    private PermissionService permissionService;

    /**
     * 为用户生成Grafana变量映射
     * 
     * @param userId 用户ID
     * @return Map<String, Object> 变量名到变量值的映射
     */
    public Map<String, Object> generateUserVariables(String userId) {
        Map<String, Object> variables = new HashMap<>();
        
        try {
            // 获取用户权限信息
            UserPermissionDto userPermission = permissionService.getUserPermissions(userId);
            if (userPermission == null) {
                logger.warn("No permission found for user: {}, using default variables", userId);
                return getDefaultVariables();
            }

            // 生成数据权限变量
            generateDataPermissionVariables(variables, userPermission);
            
            // 生成用户信息变量
            generateUserInfoVariables(variables, userPermission);
            
            // 生成组织权限变量
            generateOrgPermissionVariables(variables, userPermission);
            
            logger.debug("Generated {} variables for user: {}", variables.size(), userId);
            
        } catch (Exception e) {
            logger.error("Error generating variables for user {}: {}", userId, e.getMessage());
            return getDefaultVariables();
        }
        
        return variables;
    }

    /**
     * 生成数据权限相关的变量
     */
    private void generateDataPermissionVariables(Map<String, Object> variables, UserPermissionDto userPermission) {
        // 数据过滤条件变量
        String dataFilter = userPermission.getDataFilter();
        if (dataFilter != null && !dataFilter.trim().isEmpty()) {
            variables.put("data_filter", dataFilter);
            
            // 解析数据过滤条件，生成具体的过滤变量
            parseDataFilterToVariables(variables, dataFilter);
        } else {
            // 如果没有数据过滤条件，设置为空（管理员权限）
            variables.put("data_filter", "");
        }
        
        // 用户ID变量（用于行级数据过滤）
        variables.put("current_user_id", userPermission.getUserId());
        
        // 权限级别变量
        Object role = userPermission.getRawPermissionValue("role");
        variables.put("user_role", role != null ? role.toString() : "user");
    }

    /**
     * 生成用户信息相关的变量
     */
    private void generateUserInfoVariables(Map<String, Object> variables, UserPermissionDto userPermission) {
        variables.put("user_id", userPermission.getUserId());
        variables.put("user_name", userPermission.getUsername());
        
        // 从原始权限数据中提取组织信息
        Object orgId = userPermission.getRawPermissionValue("orgId");
        if (orgId != null) {
            variables.put("org_id", orgId.toString());
        }
        
        Object tenantId = userPermission.getRawPermissionValue("tenantId");
        if (tenantId != null) {
            variables.put("tenant_id", tenantId.toString());
        }
    }

    /**
     * 生成组织权限相关的变量
     */
    private void generateOrgPermissionVariables(Map<String, Object> variables, UserPermissionDto userPermission) {
        // 可访问的数据源列表
        if (userPermission.getAccessibleDataSourceIds() != null) {
            variables.put("accessible_datasources", String.join(",", userPermission.getAccessibleDataSourceIds()));
        }
        
        // 可访问的仪表盘列表
        if (userPermission.getAccessibleDashboardIds() != null) {
            variables.put("accessible_dashboards", String.join(",", userPermission.getAccessibleDashboardIds()));
        }
        
        // 权限标志变量
        if (userPermission.getPermissions() != null) {
            variables.put("can_export", userPermission.hasPermission("export") ? "true" : "false");
            variables.put("can_admin", userPermission.hasPermission("admin") ? "true" : "false");
            variables.put("can_write", userPermission.hasPermission("write") ? "true" : "false");
        }
    }

    /**
     * 解析数据过滤条件，生成具体的过滤变量
     */
    private void parseDataFilterToVariables(Map<String, Object> variables, String dataFilter) {
        try {
            // 解析常见的SQL过滤条件格式
            if (dataFilter.contains("department_id")) {
                // 解析部门ID过滤条件
                String deptFilter = extractFilterValue(dataFilter, "department_id");
                if (deptFilter != null) {
                    variables.put("department_filter", deptFilter);
                }
            }
            
            if (dataFilter.contains("user_id")) {
                // 解析用户ID过滤条件
                String userFilter = extractFilterValue(dataFilter, "user_id");
                if (userFilter != null) {
                    variables.put("user_filter", userFilter);
                }
            }
            
            if (dataFilter.contains("region_id")) {
                // 解析区域ID过滤条件
                String regionFilter = extractFilterValue(dataFilter, "region_id");
                if (regionFilter != null) {
                    variables.put("region_filter", regionFilter);
                }
            }
            
        } catch (Exception e) {
            logger.warn("Failed to parse data filter '{}': {}", dataFilter, e.getMessage());
        }
    }

    /**
     * 从SQL过滤条件中提取指定字段的值
     */
    private String extractFilterValue(String dataFilter, String fieldName) {
        try {
            // 处理 field = 'value' 格式
            String pattern1 = fieldName + "\\s*=\\s*'([^']+)'";
            java.util.regex.Pattern p1 = java.util.regex.Pattern.compile(pattern1);
            java.util.regex.Matcher m1 = p1.matcher(dataFilter);
            if (m1.find()) {
                return m1.group(1);
            }
            
            // 处理 field IN ('value1', 'value2') 格式
            String pattern2 = fieldName + "\\s+IN\\s*\\(([^)]+)\\)";
            java.util.regex.Pattern p2 = java.util.regex.Pattern.compile(pattern2);
            java.util.regex.Matcher m2 = p2.matcher(dataFilter);
            if (m2.find()) {
                return m2.group(1).replaceAll("'", ""); // 移除引号
            }
            
        } catch (Exception e) {
            logger.warn("Error extracting filter value for field '{}': {}", fieldName, e.getMessage());
        }
        
        return null;
    }

    /**
     * 获取默认变量（当用户权限获取失败时使用）
     */
    private Map<String, Object> getDefaultVariables() {
        Map<String, Object> variables = new HashMap<>();
        variables.put("data_filter", "1=0"); // 默认无权限访问任何数据
        variables.put("current_user_id", "unknown");
        variables.put("user_role", "guest");
        variables.put("can_export", "false");
        variables.put("can_admin", "false");
        variables.put("can_write", "false");
        return variables;
    }

    /**
     * 将变量映射转换为Grafana API格式
     */
    public Map<String, Object> convertToGrafanaFormat(Map<String, Object> variables) {
        Map<String, Object> grafanaVariables = new HashMap<>();
        
        for (Map.Entry<String, Object> entry : variables.entrySet()) {
            String varName = entry.getKey();
            Object varValue = entry.getValue();
            
            // 构建Grafana变量对象
            Map<String, Object> varDef = new HashMap<>();
            varDef.put("name", varName);
            varDef.put("type", "constant");
            varDef.put("current", createVariableValue(varValue));
            varDef.put("hide", 2); // 隐藏变量，用户不可见
            
            grafanaVariables.put(varName, varDef);
        }
        
        return grafanaVariables;
    }

    /**
     * 创建Grafana变量值对象
     */
    private Map<String, Object> createVariableValue(Object value) {
        Map<String, Object> valueObj = new HashMap<>();
        valueObj.put("value", value.toString());
        valueObj.put("text", value.toString());
        return valueObj;
    }

    /**
     * 验证变量值的安全性
     */
    public boolean isVariableValueSafe(String variableName, String variableValue) {
        if (variableValue == null) {
            return false;
        }
        
        // 检查SQL注入风险
        String lowerValue = variableValue.toLowerCase();
        String[] dangerousKeywords = {"drop", "delete", "update", "insert", "exec", "script", "--", "/*", "*/"};
        
        for (String keyword : dangerousKeywords) {
            if (lowerValue.contains(keyword)) {
                logger.warn("Potentially dangerous variable value detected: {} = {}", variableName, variableValue);
                return false;
            }
        }
        
        return true;
    }

    /**
     * 设置仪表盘变量
     * 为特定仪表盘注入基于用户权限的变量
     */
    public boolean setDashboardVariables(String dashboardId, UserPermissionDto userPermission) {
        try {
            logger.info("Setting dashboard variables for dashboard: {} and user: {}", 
                dashboardId, userPermission.getUserId());

            // 生成用户权限变量
            Map<String, Object> permissionVariables = generateUserVariables(userPermission.getUserId());

            // 获取当前仪表盘变量
            java.util.List<Map<String, Object>> currentVariables = grafanaApiClient.getDashboardVariables(dashboardId);
            
            // 创建新的变量列表
            java.util.List<Map<String, Object>> newVariables = new java.util.ArrayList<>();
            
            // 保留非权限相关的现有变量
            for (Map<String, Object> variable : currentVariables) {
                String varName = (String) variable.get("name");
                if (!isPermissionVariable(varName)) {
                    newVariables.add(variable);
                }
            }
            
            // 添加权限变量
            for (Map.Entry<String, Object> entry : permissionVariables.entrySet()) {
                String varName = entry.getKey();
                Object varValue = entry.getValue();
                
                // 验证变量值安全性
                String valueStr = String.valueOf(varValue);
                if (!grafanaApiClient.isVariableValueSafe(valueStr)) {
                    logger.warn("Unsafe variable value detected for {}: {}", varName, valueStr);
                    valueStr = grafanaApiClient.sanitizeVariableValue(valueStr);
                }
                
                // 创建变量配置
                Map<String, Object> variableConfig = grafanaApiClient.createPermissionVariable(
                    varName, valueStr, "User permission variable: " + varName);
                newVariables.add(variableConfig);
            }
            
            // 更新仪表盘变量
            boolean success = grafanaApiClient.updateDashboardVariables(dashboardId, newVariables);
            
            if (success) {
                logger.info("Successfully set {} permission variables for dashboard: {}", 
                    permissionVariables.size(), dashboardId);
            } else {
                logger.error("Failed to set permission variables for dashboard: {}", dashboardId);
            }
            
            return success;
            
        } catch (Exception e) {
            logger.error("Error setting dashboard variables for dashboard {} and user {}: {}", 
                dashboardId, userPermission.getUserId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查是否为权限变量
     */
    private boolean isPermissionVariable(String variableName) {
        if (variableName == null) {
            return false;
        }
        
        String[] permissionPrefixes = {
            "user_", "org_", "dept_", "role_", "perm_", 
            "data_filter_", "access_", "bbpf_"
        };
        
        String lowerName = variableName.toLowerCase();
        for (String prefix : permissionPrefixes) {
            if (lowerName.startsWith(prefix)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 获取仪表盘权限变量
     * 返回特定仪表盘的权限相关变量
     */
    public Map<String, Object> getDashboardPermissionVariables(String dashboardId) {
        try {
            List<Map<String, Object>> variables = grafanaApiClient.getDashboardVariables(dashboardId);
            Map<String, Object> permissionVars = new HashMap<>();
            
            for (Map<String, Object> variable : variables) {
                String varName = (String) variable.get("name");
                if (isPermissionVariable(varName)) {
                    Object current = variable.get("current");
                    if (current instanceof Map) {
                        Map<String, Object> currentMap = (Map<String, Object>) current;
                        permissionVars.put(varName, currentMap.get("value"));
                    }
                }
            }
            
            return permissionVars;
            
        } catch (Exception e) {
            logger.error("Error getting dashboard permission variables for {}: {}", 
                dashboardId, e.getMessage());
            return new HashMap<>();
        }
    }

    /**
     * 清除仪表盘权限变量
     * 移除仪表盘中的所有权限相关变量
     */
    public boolean clearDashboardPermissionVariables(String dashboardId) {
        try {
            logger.info("Clearing permission variables for dashboard: {}", dashboardId);
            
            List<Map<String, Object>> currentVariables = grafanaApiClient.getDashboardVariables(dashboardId);
            List<Map<String, Object>> filteredVariables = new ArrayList<>();
            
            // 保留非权限相关的变量
            for (Map<String, Object> variable : currentVariables) {
                String varName = (String) variable.get("name");
                if (!isPermissionVariable(varName)) {
                    filteredVariables.add(variable);
                }
            }
            
            boolean success = grafanaApiClient.updateDashboardVariables(dashboardId, filteredVariables);
            
            if (success) {
                logger.info("Successfully cleared permission variables for dashboard: {}", dashboardId);
            } else {
                logger.error("Failed to clear permission variables for dashboard: {}", dashboardId);
            }
            
            return success;
            
        } catch (Exception e) {
            logger.error("Error clearing dashboard permission variables for {}: {}", 
                dashboardId, e.getMessage());
            return false;
        }
    }
}