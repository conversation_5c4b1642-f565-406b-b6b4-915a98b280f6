<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snbc.bbpf.system.db.common.mapper.RoleMapper">
    <resultMap id="BaseResultMap" type="com.snbc.bbpf.system.db.common.entity.Role">
        <id column="role_id" jdbcType="VARCHAR" property="roleId"/>
        <result column="role_name" jdbcType="VARCHAR" property="roleName"/>
        <result column="role_code" jdbcType="VARCHAR" property="roleCode"/>
        <result column="role_desc" jdbcType="VARCHAR" property="roleDesc"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="create_org_id" jdbcType="VARCHAR" property="createOrgId"/>
        <result column="role_type" jdbcType="INTEGER" property="roleType"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    role_id, role_name, role_code, role_desc, create_user_id, create_org_id, role_type,
    create_time, update_time
  </sql>
    <select id="getAllRoleList" parameterType="java.lang.String" resultType="java.util.Map">
        SELECT
            role_id AS "roleId",
            role_name AS "roleName",
            role_desc AS "roleDesc",
            role_type as "roleType"
        FROM
            t_role
        order by create_time
	 </select>

    <select id="getOwnRoleList" parameterType="java.lang.String" resultType="java.util.Map">
        SELECT
            distinct (t1.role_id) as roleId,
                     t1.role_name as roleName,
                     t1.role_code as roleCode,
                     t1.role_desc as roleDesc,
                     t1.role_type as roleType,
                     t1.create_org_id as createOrgId,
                     t1.create_time as createTime,
                     t1.update_time as updateTime
        FROM t_role t1
                 LEFT JOIN t_user_role u ON t1.role_id=u.role_id
                 LEFT JOIN t_role_org o ON t1.role_id = o.role_id
        order by t1.create_time
    </select>
    <select id="getOwnRoleListBySelf" resultType="java.util.Map">
        SELECT
            distinct (t1.role_id) as roleId,
            t1.role_name as roleName,
            t1.role_code as roleCode,
            t1.role_desc as roleDesc,
            t1.role_type as roleType,
            t1.create_org_id as createOrgId,
            t1.create_time as createTime,
            t1.update_time as updateTime
        FROM t_role t1
                 LEFT JOIN t_user_role u ON t1.role_id=u.role_id
        order by t1.create_time
    </select>
    <select id="queryRoleListByUserId" resultMap="BaseResultMap">
		SELECT
        a.role_id,
        a.role_name,
        a.role_code,
        a.role_desc,
        a.role_type,
        a.create_org_id,
        a.create_time,
        a.update_time
    FROM t_role a
    LEFT JOIN t_user_role u
    ON a.role_id=u.role_id
    WHERE u.user_id=#{userId,jdbcType=VARCHAR}
  </select>
    <select id="selectRoleDetail" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
            t_role
        WHERE
            role_id = #{roleId,jdbcType=VARCHAR}
    </select>
    <update id="updateRole" parameterType="com.snbc.bbpf.system.db.common.entity.Role">
        UPDATE t_role
        SET role_desc = #{roleDesc,jdbcType=VARCHAR},
        role_name = #{roleName,jdbcType=VARCHAR}
        WHERE
            role_id = #{roleId,jdbcType=VARCHAR}
    </update>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from t_role
        where role_id = #{roleId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from t_role
    where role_id = #{roleId,jdbcType=VARCHAR}
        and role_type = 1;
    delete from t_permission_role where role_id = #{roleId,jdbcType=VARCHAR};
  </delete>
    <insert id="insertRole" parameterType="com.snbc.bbpf.system.db.common.entity.Role">
    insert into t_role (role_id, role_name, role_code,
      role_desc, create_user_id, create_org_id,
      role_type, create_time, update_time
      )
    values (#{roleId,jdbcType=VARCHAR}, #{roleName,jdbcType=VARCHAR}, #{roleCode,jdbcType=VARCHAR},
      #{roleDesc,jdbcType=VARCHAR}, #{createUserId,jdbcType=VARCHAR}, #{createOrgId,jdbcType=VARCHAR},
      #{roleType,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
    </insert>

    <select id="selectCountByRoleName" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(0) from t_role where role_name=#{roleName,jdbcType=VARCHAR}
    </select>
    <!--wjc1-->
    <select id="selectIdsByRoleNames" resultType="java.lang.String">
        SELECT
        string_agg(role_id,',')
        FROM t_role
        WHERE  role_name in
        <foreach collection="belongRoleNames" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <!--ljb保证角色名称不能重复1-->
    <select id="selectOnlyRoleName"  resultType="java.lang.Integer">
        select count(role_id) cid from t_role
        where role_name=#{roleName,jdbcType=VARCHAR}
        <if test="roleId != null" >
            and role_id!=#{roleId,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectRoleNamesByUserId" resultType="com.snbc.bbpf.system.db.common.dto.RoleNamesDto">
        SELECT DISTINCT
            t1.user_id as userId,
        string_agg(t3.role_name,',') AS roleNames
        FROM
          t_user t1
        LEFT JOIN t_user_role t2 ON t1.user_id = t2.user_id
        LEFT JOIN t_role t3 ON t2.role_id = t3.role_id
        <if test="userIdList != null and userIdList.size() > 0">
        WHERE
            t1.user_id IN
            <foreach collection="userIdList" item="userId" index="index"  open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        GROUP BY
        t1.user_id
    </select>
    <select id="selectRoleNames" resultType="java.lang.String">
        SELECT
        string_agg(role_name,',')
        FROM t_role
        WHERE  role_id in
        <foreach collection="roleIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <!--重置系统角色-->
    <select id="resetRole" parameterType="java.lang.String">
        ${restSql}
    </select>
    <select id="getRestSql" resultType="java.lang.String" parameterType="java.lang.String">
        select rest_sql from t_role where role_id = #{roleId}
    </select>
    <!-- 查询角色所属的组织机构-->
    <select id="selectOrgByRoleId" parameterType="java.lang.String" resultType="java.util.Map">
        SELECT
            t1.org_id AS orgId,
            t1.org_name AS orgName
        FROM
            t_org t1
                LEFT JOIN t_role_org t2 ON t1.org_id = t2.org_id
        WHERE
            t2.role_id = #{roleId,jdbcType=VARCHAR}
    </select>

    <select id="getRoleIdsByUserId" resultType="String">
        select t1.role_id from t_role t1
                                   left join t_user_role t2 on t1.role_id = t2.role_id
        where t2.user_id = #{userId,jdbcType=VARCHAR}
    </select>

    <select id="getRoleIdByRoleCode" resultType="java.lang.String">
        select role_id from t_role where role_code = #{roleCode}
    </select>

    <select id="getRolesByOrgId" resultType="map">
        select DISTINCT t1.role_id,t1.role_name,t1.role_code
        from t_role t1 LEFT JOIN t_role_org t2 on t1.role_id = t2.role_id
        where t2.org_id in
        <foreach collection="orgIds" item="orgId" open="(" separator="," close=")">
            #{orgId,jdbcType=VARCHAR}
        </foreach>
    </select>

</mapper>
