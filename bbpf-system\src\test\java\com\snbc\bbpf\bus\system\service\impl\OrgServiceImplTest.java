package com.snbc.bbpf.bus.system.service.impl;

import com.snbc.bbpf.bus.system.constans.OrgConstant;
import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.bus.system.utils.CheckOrgPermission;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.system.db.common.entity.Org;
import com.snbc.bbpf.system.db.common.entity.User;
import com.snbc.bbpf.system.db.common.mapper.OrgMapper;
import com.snbc.bbpf.system.db.common.mapper.UserMapper;
import com.snbc.bbpf.system.db.common.mapper.UserOrgMapper;
import com.snbc.bbpf.system.db.common.vo.OrgSort;
import com.snbc.bbpf.system.db.common.vo.OrgVo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.when;

/**
 * @ClassName: OrgServiceImplTest
 * @module: bbpf_project
 * @Author: wangsong
 * @date: 2021/6/10
 * copyright 2020 barm Inc. All rights reserver
 */
class OrgServiceImplTest {
    @Mock
    Logger LOGGER;
    @Mock
    OrgMapper orgMapper;
    @Mock
    UserOrgMapper userOrgMapper;
    @Mock
    UserMapper userMapper;
    @Mock
    RedisTemplate redisTemplate;
    @Mock
    CheckOrgPermission checkOrgPermission;

    @InjectMocks
    OrgServiceImpl orgServiceImpl = new OrgServiceImpl();

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("树形列表-没子节点")
    @Tag("@id:23516")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testGetOrgTree() {
        List<OrgVo> orgResList = new ArrayList<>();
        List<Org> orgList = new ArrayList<>();

        Org orgReq = new Org();
        orgReq.setOrgId("0");
        orgReq.setOrgStatus(OrgConstant.ORG_STATUS_ENABLE);
        Mockito.when((orgMapper.getOrgs(orgReq))).thenReturn(orgList);

        List<OrgVo> result = orgServiceImpl.getOrgTree("0", "0");
        Assertions.assertEquals(orgResList, result);
    }

    @Test
    @DisplayName("树形列表--异常")
    @Tag("@id:23516")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testGetOrgTree_exception() {
        List<Org> orgList = new ArrayList<>();
        Org org = new Org();
        org.setOrgId("0");
        orgList.add(org);

        Mockito.when((orgMapper.getOrgs(Mockito.any()))).thenReturn(orgList);
        try {
            orgServiceImpl.getOrgTree("中文", "中文");
        } catch (BusinessException e) {
            Assertions.assertEquals(e.getCode(), "991108");
        }
    }

    //@Test
    @DisplayName("树形列表-有一个子节点")
    @Tag("@id:23516")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testGetOrgTree_has_child() {
        List<OrgVo> orgResList = new ArrayList<>();
        List<OrgVo> childList = new ArrayList<>();
        OrgVo orgVo2 = new OrgVo();
        orgVo2.setOrgId("1");
        orgVo2.setParentId("0");
        childList.add(orgVo2);
        OrgVo orgVo = new OrgVo();
        orgVo.setOrgId("0");
        orgVo.setChildren(childList);
        orgResList.add(orgVo);


        List<Org> orgList = new ArrayList<>();
        Org org = new Org();
        org.setOrgId("0");
        orgList.add(org);
        Org orgReq = new Org();
        orgReq.setOrgId("0");
        orgReq.setOrgStatus(OrgConstant.ORG_STATUS_ENABLE);
        Mockito.when((orgMapper.getOrgs(orgReq))).thenReturn(orgList);

        List<Org> orgList2 = new ArrayList<>();
        Org org2 = new Org();
        org2.setOrgId("1");
        org2.setParentId("0");
        orgList2.add(org2);
        Org orgReq2 = new Org();
        orgReq2.setOrgStatus(OrgConstant.ORG_STATUS_ENABLE);
        orgReq2.setParentId("0");
        Mockito.when((orgMapper.getOrgs(orgReq2))).thenReturn(orgList2);

        List<OrgVo> result = orgServiceImpl.getOrgTree("0", "0");
        Assertions.assertEquals(orgResList, result);
        Assertions.assertEquals(orgResList.get(0).getChildren().get(0), result.get(0).getChildren().get(0));
    }

    //@Test
    @DisplayName("树形列表-返回根节点下一级")
    @Tag("@id:23516")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testGetOrgTree_orglevel() {
        List<OrgVo> orgResList = new ArrayList<>();
        List<OrgVo> childList = new ArrayList<>();
        OrgVo orgVo2 = new OrgVo();
        orgVo2.setOrgId("1");
        orgVo2.setParentId("0");
        childList.add(orgVo2);
        OrgVo orgVo = new OrgVo();
        orgVo.setOrgId("0");
        orgVo.setChildren(childList);
        orgResList.add(orgVo);

        List<Org> orgList = new ArrayList<>();
        Org org = new Org();
        org.setOrgId("0");
        orgList.add(org);
        Org orgReq = new Org();
        orgReq.setOrgId("0");
        orgReq.setOrgStatus(OrgConstant.ORG_STATUS_ENABLE);
        Mockito.when((orgMapper.getOrgs(orgReq))).thenReturn(orgList);

        List<Org> orgList2 = new ArrayList<>();
        Org org2 = new Org();
        org2.setOrgId("1");
        org2.setParentId("0");
        orgList2.add(org2);
        Org orgReq2 = new Org();
        orgReq2.setOrgStatus(OrgConstant.ORG_STATUS_ENABLE);
        orgReq2.setParentId("0");
        Mockito.when((orgMapper.getOrgs(orgReq2))).thenReturn(orgList2);

        List<Org> orgList3 = new ArrayList<>();
        Org org3 = new Org();
        org3.setOrgId("2");
        org3.setParentId("1");
        orgList3.add(org3);
        Org orgReq3 = new Org();
        orgReq3.setOrgStatus(OrgConstant.ORG_STATUS_ENABLE);
        orgReq3.setParentId("1");
        Mockito.when((orgMapper.getOrgs(orgReq3))).thenReturn(orgList3);

        List<OrgVo> result = orgServiceImpl.getOrgTree("0", "1");
        Assertions.assertEquals(orgResList, result);
        Assertions.assertEquals(orgResList.get(0).getChildren().get(0), result.get(0).getChildren().get(0));
    }

    @Test
    @DisplayName("列表-没子节点")
    @Tag("@id:23516")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testGetOrgListById() {
        List<OrgVo> orgResList = new ArrayList<>();
        List<Org> orgList = new ArrayList<>();

        Org orgReq = new Org();
        orgReq.setOrgId("0");
        orgReq.setOrgStatus(OrgConstant.ORG_STATUS_ENABLE);
        Mockito.when((orgMapper.getOrgs(orgReq))).thenReturn(orgList);

        List<OrgVo> result = orgServiceImpl.getOrgListById("0", "0");
        Assertions.assertEquals(orgResList, result);
    }
    @Test
    @DisplayName("列表--异常")
    @Tag("@id:23516")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testGetOrgListById_exception() {
        List<Org> orgList = new ArrayList<>();
        Org org = new Org();
        org.setOrgId("0");
        orgList.add(org);

        Mockito.when((orgMapper.getOrgs(Mockito.any()))).thenReturn(orgList);
        try {
           orgServiceImpl.getOrgListById("中文", "中文");
        } catch (BusinessException e) {
            Assertions.assertEquals(e.getCode(), "991108");
        }
    }
    //@Test
    @DisplayName("列表-有子节点")
    @Tag("@id:23516")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testGetOrgListById_has_child() {
        List<OrgVo> orgResList = new ArrayList<>();
        OrgVo orgVo2 = new OrgVo();
        orgVo2.setOrgId("1");
        orgVo2.setParentId("0");
        orgResList.add(orgVo2);
        OrgVo orgVo = new OrgVo();
        orgVo.setOrgId("0");
        orgResList.add(orgVo);


        List<Org> orgList = new ArrayList<>();
        Org org = new Org();
        org.setOrgId("0");
        orgList.add(org);
        Org orgReq = new Org();
        orgReq.setOrgId("0");
        orgReq.setOrgStatus(OrgConstant.ORG_STATUS_ENABLE);
        Mockito.when((orgMapper.getOrgs(orgReq))).thenReturn(orgList);

        List<Org> orgList2 = new ArrayList<>();
        Org org2 = new Org();
        org2.setOrgId("1");
        org2.setParentId("0");
        orgList2.add(org2);
        Org orgReq2 = new Org();
        orgReq2.setOrgStatus(OrgConstant.ORG_STATUS_ENABLE);
        orgReq2.setParentId("0");
        Mockito.when((orgMapper.getOrgs(orgReq2))).thenReturn(orgList2);

        List<OrgVo> result = orgServiceImpl.getOrgListById("0", "0");
        Assertions.assertEquals(orgResList, result);
    }

    //@Test
    @DisplayName("列表-返回根节点下一级")
    @Tag("@id:23516")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testGetOrgListById_orglevel() {
        List<OrgVo> orgResList = new ArrayList<>();
        OrgVo orgVo2 = new OrgVo();
        orgVo2.setOrgId("1");
        orgVo2.setParentId("0");
        orgResList.add(orgVo2);
        OrgVo orgVo = new OrgVo();
        orgVo.setOrgId("0");
        orgResList.add(orgVo);

        List<Org> orgList = new ArrayList<>();
        Org org = new Org();
        org.setOrgId("0");
        orgList.add(org);
        Org orgReq = new Org();
        orgReq.setOrgId("0");
        orgReq.setOrgStatus(OrgConstant.ORG_STATUS_ENABLE);
        Mockito.when((orgMapper.getOrgs(orgReq))).thenReturn(orgList);

        List<Org> orgList2 = new ArrayList<>();
        Org org2 = new Org();
        org2.setOrgId("1");
        org2.setParentId("0");
        orgList2.add(org2);
        Org orgReq2 = new Org();
        orgReq2.setOrgStatus(OrgConstant.ORG_STATUS_ENABLE);
        orgReq2.setParentId("0");
        Mockito.when((orgMapper.getOrgs(orgReq2))).thenReturn(orgList2);

        List<Org> orgList3 = new ArrayList<>();
        Org org3 = new Org();
        org3.setOrgId("2");
        org3.setParentId("1");
        orgList3.add(org3);
        Org orgReq3 = new Org();
        orgReq3.setOrgStatus(OrgConstant.ORG_STATUS_ENABLE);
        orgReq3.setParentId("1");
        Mockito.when((orgMapper.getOrgs(orgReq3))).thenReturn(orgList3);

        List<OrgVo> result = orgServiceImpl.getOrgListById("0", "1");
        Assertions.assertEquals(orgResList, result);
    }

    //@Test
    @DisplayName("组织机构明细--有父级有部门主管")
    @Tag("@id:23516")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testGetOrg_has_parent_has_supervisor() {
        OrgVo orgVo = new OrgVo();
        orgVo.setOrgId("1");
        orgVo.setParentId("0");
        orgVo.setParentName("组织机构");
        orgVo.setSupervisorName("张三");
        orgVo.setSupervisor("111");

        Org org = new Org();
        org.setOrgId("1");
        org.setParentId("0");
        org.setSupervisor("111");
        Mockito.when((orgMapper.selectByPrimaryKey("1"))).thenReturn(org);
        Org org2 = new Org();
        org2.setOrgName("组织机构");
        Mockito.when((orgMapper.selectByPrimaryKey("0"))).thenReturn(org2);
        User user = new User();
        user.setUserName("张三");
        Mockito.when((userMapper.selectByPrimaryKey("111"))).thenReturn(user);

        OrgVo result = orgServiceImpl.getOrg("1");
        Assertions.assertEquals(orgVo, result);
    }
    @Test
    @DisplayName("组织机构明细--异常")
    @Tag("@id:23516")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testGetOrg_exception() {
        try {
            orgServiceImpl.getOrg("中文");
        } catch (BusinessException e) {
            Assertions.assertEquals(e.getCode(), "991105");
        }
    }

    @Test
    @DisplayName("新增组织机构")
    @Tag("@id:23515")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testAddOrg() {
        try(MockedStatic<CurrentUser> user=Mockito.mockStatic(CurrentUser.class)) {
            Org org = new Org();
            org.setOrgId("0");
            Mockito.when(orgMapper.selectByPrimaryKey("0")).thenReturn(org);

            Mockito.when(orgMapper.queryMaxOrderByParentId("0")).thenReturn(1);
            Org orgReq = new Org();
            orgReq.setOrgCode("aaa");
            Mockito.when(orgMapper.getOrgs(orgReq)).thenReturn(null);

            List<Org> orgList = new ArrayList<>();
            Mockito.when(orgMapper.queryOrgListByParentId(Mockito.any())).thenReturn(orgList);

            OrgVo orgVo = new OrgVo();
            orgVo.setParentId("0");
            orgVo.setOrgName("测试");
            orgServiceImpl.addOrg(orgVo, "-1");
            Mockito.verify(orgMapper,Mockito.times(1)).insert(Mockito.any(Org.class));
        }
    }
    @Test
    @DisplayName("新增组织机构--机构名重复")
    @Tag("@id:23515")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testAddOrg_nameexist() {
        try(MockedStatic<CurrentUser> user=Mockito.mockStatic(CurrentUser.class)) {
            Org org = new Org();
            org.setOrgId("0");
            Mockito.when(orgMapper.selectByPrimaryKey("0")).thenReturn(org);

            Mockito.when(orgMapper.queryMaxOrderByParentId("0")).thenReturn(1);
            Org orgReq = new Org();
            orgReq.setOrgCode("aaa");
            Mockito.when(orgMapper.getOrgs(orgReq)).thenReturn(null);

            List<Org> orgList = new ArrayList<>();
            Org org2 = new Org();
            org2.setOrgName("测试");
            Mockito.when(orgMapper.queryOrgListByParentId(Mockito.any())).thenReturn(orgList);


            OrgVo orgVo = new OrgVo();
            orgVo.setParentId("0");
            orgVo.setOrgName("测试");

            orgServiceImpl.addOrg(orgVo, "-1");
        }catch (BusinessException e) {
        Assertions.assertEquals(e.getCode(), "991110");
    }

}
    @Test
    @DisplayName("新增组织机构--异常")
    @Tag("@id:23515")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testAddOrg_exception() {
        try {
            OrgVo orgVo = new OrgVo();
            orgServiceImpl.addOrg(orgVo, "-1");
        } catch (BusinessException e) {
            Assertions.assertEquals(e.getCode(), "991101");
        }
    }
    @Test
    @DisplayName("修改组织机构")
    @Tag("@id:23517")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testUpdateOrg() {
        try(MockedStatic<CurrentUser> currentUserMockedStatic = Mockito.mockStatic(CurrentUser.class)){
            Mockito.when(CurrentUser.getUserId()).thenReturn("-1");
            Org org = new Org();
            org.setOrgId("1");
            org.setOrgName("测试");
            Mockito.when(orgMapper.selectByPrimaryKey("1")).thenReturn(org);

            List<Org> orgList = new ArrayList<>();
            Org orgexist = new Org();
            orgexist.setOrgId("1");
            orgexist.setOrgName("测试");
            orgList.add(orgexist);
            Mockito.when(orgMapper.queryOrgListByParentId(Mockito.any())).thenReturn(orgList);

            OrgVo orgVo = new OrgVo();
            orgVo.setOrgId("1");
            orgVo.setOrgName("测试");
            orgServiceImpl.updateOrg(orgVo, "", "-1");
        }
    }
    @Test
    @DisplayName("修改组织机构--名称重复")
    @Tag("@id:23517")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testUpdateOrg_nameexist() {
        Org org = new Org();
        org.setOrgId("1");
        org.setOrgName("测试");
        Mockito.when(orgMapper.selectByPrimaryKey("1")).thenReturn(org);

        List<Org> orgList = new ArrayList<>();
        Org orgexist = new Org();
        orgexist.setOrgId("2");
        orgexist.setOrgName("测试");
        orgList.add(orgexist);

        Mockito.when(orgMapper.queryOrgListByParentId(Mockito.any())).thenReturn(orgList);

        OrgVo orgVo = new OrgVo();
        orgVo.setOrgId("1");
        orgVo.setOrgName("测试");

        try(MockedStatic<CurrentUser> currentUserMockedStatic = Mockito.mockStatic(CurrentUser.class)) {
            Mockito.when(CurrentUser.getUserId()).thenReturn("-1");
            orgServiceImpl.updateOrg(orgVo, "", "-1");
        } catch (BusinessException e) {
            Assertions.assertEquals(e.getCode(), "991110");
        }
    }
    @Test
    @DisplayName("修改组织机构--异常")
    @Tag("@id:23517")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testUpdateOrg_exception() {
        try {
            OrgVo orgVo = new OrgVo();
            orgServiceImpl.updateOrg(orgVo, "", "-1");
        } catch (BusinessException e) {
            Assertions.assertEquals(e.getCode(), "991104");
        }
    }

    @Test
    @DisplayName("删除组织机构--机构下有人")
    @Tag("@id:23518")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testDelOrg_has_person() {
        try(MockedStatic<CurrentUser> user=Mockito.mockStatic(CurrentUser.class)){
                when(CurrentUser.getUserId()).thenReturn(null);
            Org org = new Org();
            org.setOrgId("1");
            org.setOrgName("测试");
            Mockito.when(orgMapper.selectByPrimaryKey(Mockito.any())).thenReturn(org);

            Mockito.when(userOrgMapper.selectUserCountByOrgId(Mockito.any())).thenReturn(1);
            try {
                orgServiceImpl.delOrg("1");
            } catch (BusinessException e) {
                Assertions.assertEquals(e.getCode(), "991107");
            }
        }
    }
    @Test
    @DisplayName("删除组织机构--异常")
    @Tag("@id:23518")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testDelOrg_exception() {
        try(MockedStatic<CurrentUser> user=Mockito.mockStatic(CurrentUser.class)){
            when(CurrentUser.getUserId()).thenReturn(null);
            orgServiceImpl.delOrg("");
        } catch (BusinessException e) {
            Assertions.assertEquals(e.getCode(), "991106");
        }
    }
    @Test
    @DisplayName("删除组织机构--机构下没人")
    @Tag("@id:23518")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testDelOrg_no_person() {
        try(MockedStatic<CurrentUser> user=Mockito.mockStatic(CurrentUser.class)){
            when(CurrentUser.getUserId()).thenReturn(null);
            Org org = new Org();
            org.setOrgId("1");
            org.setOrgName("测试");
            Mockito.when(orgMapper.selectByPrimaryKey(Mockito.any())).thenReturn(org);

            Mockito.when(userOrgMapper.selectUserCountByOrgId(Mockito.any())).thenReturn(0);

            Mockito.when(orgMapper.queryOrgListByParentId(Mockito.any())).thenReturn(null);

            Mockito.when(orgMapper.queryOrgListByParentId(Mockito.any())).thenReturn(null);

            orgServiceImpl.delOrg("1");
        }
    }
    @Test
    @DisplayName("组织机构拖拽--异常")
    @Tag("@id:23522")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testOrgSort_exception() {
        try {
            OrgSort orgSort = new OrgSort();
            List<OrgVo> result = orgServiceImpl.orgSort(orgSort);
        } catch (BusinessException e) {
            Assertions.assertEquals(e.getCode(), "991109");
        }
    }
    @Test
    @DisplayName("组织机构拖拽--某部门内部")
    @Tag("@id:23522")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testOrgSort_inner() {
        List<OrgVo> orgSortList2 = new ArrayList<>();
        OrgVo org5 = new OrgVo();
        org5.setOrgId("1");
        org5.setOrgName("测试");
        orgSortList2.add(org5);
        OrgVo org6 = new OrgVo();
        org6.setOrgId("2");
        org6.setOrgName("测试2");
        orgSortList2.add(org6);

        OrgSort orgSort = new OrgSort();
        orgSort.setSortType("inner");
        orgSort.setTargetNodeId("0");
        orgSort.setThisNodeId("1");
        Mockito.when(orgMapper.queryMaxOrderByParentId(Mockito.any())).thenReturn(1);
        Org org = new Org();
        org.setOrgId("1");
        org.setOrgName("测试");
        Mockito.when(orgMapper.selectByPrimaryKey(Mockito.any())).thenReturn(org);
        Org org2 = new Org();
        org2.setOrgId("2");
        org2.setOrgName("测试2");
        org2.setParentId("0");
        Mockito.when(orgMapper.selectByPrimaryKey(Mockito.any())).thenReturn(org2);

        Mockito.when(orgMapper.updateByPrimaryKeySelective(Mockito.any())).thenReturn(1);
        List<Org> orgSortList = new ArrayList<>();
        Org org3 = new Org();
        org3.setOrgId("1");
        org3.setOrgName("测试");
        orgSortList.add(org3);
        Org org4 = new Org();
        org4.setOrgId("2");
        org4.setOrgName("测试2");
        orgSortList.add(org4);
        Mockito.when(orgMapper.queryOrgListByParentId(Mockito.any())).thenReturn(orgSortList);

        List<OrgVo> result = orgServiceImpl.orgSort(orgSort);
        Assertions.assertEquals(orgSortList2, result);
    }

    @Test
    @DisplayName("组织机构拖拽--某部门上下层")
    @Tag("@id:23522")
    @Tag("@author:jiafei")
    @Tag("@date:2021/6/11")
    void testOrgSort_prev_next() {
        List<OrgVo> orgSortList3 = new ArrayList<>();
        OrgVo org5 = new OrgVo();
        org5.setOrgId("1");
        org5.setOrgName("测试");
        org5.setOrgPath("/1");
        org5.setSequence(0);
        orgSortList3.add(org5);
        OrgVo org6 = new OrgVo();
        org6.setOrgId("2");
        org6.setOrgName("测试2");
        org6.setOrgPath("/2");
        org6.setSequence(1);
        orgSortList3.add(org6);

        OrgSort orgSort = new OrgSort();
        orgSort.setSortType("prev");
        orgSort.setTargetNodeId("1");
        orgSort.setThisNodeId("1");
        orgSort.setParentId("0");

        Org org8 = new Org();
        org8.setOrgId("1");
        org8.setParentId("0");
        org8.setOrgName("测试");
        org8.setSupervisor("111");
        Mockito.when((orgMapper.selectByPrimaryKey(Mockito.any()))).thenReturn(org8);
        List<Org> orgList = new ArrayList<>();
        Org org7 = new Org();
        org7.setOrgId("1");
        org7.setOrgName("测试");
        orgList.add(org7);
        Mockito.when(orgMapper.queryOrgListByParentId(Mockito.any())).thenReturn(orgList);

        List<Org> orgSortList = new ArrayList<>();
        Org org = new Org();
        org.setOrgId("1");
        org.setOrgName("测试");
        orgSortList.add(org);
        Org org2 = new Org();
        org2.setOrgId("2");
        org2.setOrgPath("/2");
        org2.setOrgName("测试2");
        orgSortList.add(org2);
        Mockito.when(orgMapper.queryOrgListByParentId(Mockito.any())).thenReturn(orgSortList);
        orgMapper.updateOrgList(Mockito.any());
        List<Org> orgSortList2 = new ArrayList<>();
        Org org3 = new Org();
        org3.setOrgId("1");
        org3.setOrgName("测试");
        org3.setOrgPath("/1");
        orgSortList2.add(org3);
        Org org4 = new Org();
        org4.setOrgId("2");
        org4.setOrgPath("/2");
        org4.setOrgName("测试2");
        orgSortList2.add(org4);
        Mockito.when(orgMapper.queryOrgListByParentId(Mockito.any())).thenReturn(orgSortList2);

        List<OrgVo> result = orgServiceImpl.orgSort(orgSort);
        Assertions.assertEquals(orgSortList3, result);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
