/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.mapper;


import com.snbc.bbpf.system.db.common.entity.BusLog;
import com.snbc.bbpf.system.db.common.vo.BusLogQuery;
import com.snbc.bbpf.system.db.common.vo.ExportLogVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName: BusLogMapper
 * BusLogMapper
 * @module: si-bbpf-system
 * @Author: wjc1 增加的注释
 * @date:   2021/5/25 16:56
 */
@Mapper
public interface BusLogMapper {

    int deleteByPrimaryKey(String logId);

    int insert(BusLog busLog);

    int insertSelective(BusLog busLog);
    //组合获取日志进行优化，先根据条件获取日志ID，然后根据日志ID获取日志列表
    List<BusLog> selectByExample(@Param ("logIdList") List<String> logIdList);
    List<String> selelctBusLogIds(BusLogQuery busLogQuery);

    List<ExportLogVo> selectExportLogs(BusLogQuery busLogQuery);

    BusLog selectByPrimaryKey(String logId);

    int updateByPrimaryKeySelective(BusLog busLog);

    int updateByPrimaryKey(BusLog busLog);
}
