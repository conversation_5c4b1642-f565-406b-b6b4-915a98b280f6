<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>WebSocket连接测试</title>
		<style>
			body {
				font-family: Arial, sans-serif;
				max-width: 800px;
				margin: 0 auto;
				padding: 20px;
			}
			.container {
				border: 1px solid #ddd;
				border-radius: 5px;
				padding: 20px;
				margin-bottom: 20px;
			}
			.status {
				padding: 10px;
				border-radius: 3px;
				margin-bottom: 10px;
			}
			.connected {
				background-color: #d4edda;
				color: #155724;
				border: 1px solid #c3e6cb;
			}
			.disconnected {
				background-color: #f8d7da;
				color: #721c24;
				border: 1px solid #f5c6cb;
			}
			.log {
				background-color: #f8f9fa;
				border: 1px solid #dee2e6;
				padding: 10px;
				height: 300px;
				overflow-y: auto;
				font-family: monospace;
				font-size: 12px;
			}
			button {
				background-color: #007bff;
				color: white;
				border: none;
				padding: 8px 16px;
				border-radius: 3px;
				cursor: pointer;
				margin-right: 10px;
			}
			button:hover {
				background-color: #0056b3;
			}
			button:disabled {
				background-color: #6c757d;
				cursor: not-allowed;
			}
			input[type='text'] {
				width: 300px;
				padding: 5px;
				border: 1px solid #ddd;
				border-radius: 3px;
			}
		</style>
	</head>
	<body>
		<h1>Grafana WebSocket代理测试</h1>

		<div class="container">
			<h3>连接状态</h3>
			<div id="status" class="status disconnected">未连接</div>

			<h3>连接控制</h3>
			<button id="connectBtn" onclick="connect()">连接</button>
			<button id="disconnectBtn" onclick="disconnect()" disabled>
				断开连接
			</button>
			<button onclick="clearLog()">清空日志</button>

			<h3>发送消息</h3>
			<input
				type="text"
				id="messageInput"
				placeholder="输入要发送的消息"
				disabled
			/>
			<button id="sendBtn" onclick="sendMessage()" disabled>发送</button>
		</div>

		<div class="container">
			<h3>连接日志</h3>
			<div id="log" class="log"></div>
		</div>

		<script>
			let websocket = null;
			const statusDiv = document.getElementById('status');
			const logDiv = document.getElementById('log');
			const connectBtn = document.getElementById('connectBtn');
			const disconnectBtn = document.getElementById('disconnectBtn');
			const sendBtn = document.getElementById('sendBtn');
			const messageInput = document.getElementById('messageInput');

			function log(message) {
				const timestamp = new Date().toLocaleTimeString();
				logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
				logDiv.scrollTop = logDiv.scrollHeight;
			}

			function updateStatus(connected) {
				if (connected) {
					statusDiv.textContent = '已连接';
					statusDiv.className = 'status connected';
					connectBtn.disabled = true;
					disconnectBtn.disabled = false;
					sendBtn.disabled = false;
					messageInput.disabled = false;
				} else {
					statusDiv.textContent = '未连接';
					statusDiv.className = 'status disconnected';
					connectBtn.disabled = false;
					disconnectBtn.disabled = true;
					sendBtn.disabled = true;
					messageInput.disabled = true;
				}
			}

			function connect() {
				try {
					// 使用当前域名和端口
					const wsUrl = `ws://${window.location.host}/api/live/ws`;
					log(`尝试连接到: ${wsUrl}`);

					websocket = new WebSocket(wsUrl);

					websocket.onopen = function (event) {
						log('WebSocket连接已建立');
						updateStatus(true);
					};

					websocket.onmessage = function (event) {
						log(`收到消息: ${event.data}`);
					};

					websocket.onclose = function (event) {
						log(
							`WebSocket连接已关闭 (代码: ${event.code}, 原因: ${event.reason})`
						);
						updateStatus(false);
						websocket = null;
					};

					websocket.onerror = function (error) {
						log(`WebSocket错误: ${error}`);
						updateStatus(false);
					};
				} catch (error) {
					log(`连接失败: ${error.message}`);
					updateStatus(false);
				}
			}

			function disconnect() {
				if (websocket) {
					websocket.close();
					websocket = null;
					log('主动断开连接');
					updateStatus(false);
				}
			}

			function sendMessage() {
				const message = messageInput.value.trim();
				if (
					message &&
					websocket &&
					websocket.readyState === WebSocket.OPEN
				) {
					websocket.send(message);
					log(`发送消息: ${message}`);
					messageInput.value = '';
				} else {
					log('无法发送消息：连接未建立或消息为空');
				}
			}

			function clearLog() {
				logDiv.innerHTML = '';
			}

			// 页面加载时的初始化
			document.addEventListener('DOMContentLoaded', function () {
				log('页面加载完成，可以开始测试WebSocket连接');

				// 回车键发送消息
				messageInput.addEventListener('keypress', function (event) {
					if (event.key === 'Enter') {
						sendMessage();
					}
				});
			});

			// 页面卸载时清理连接
			window.addEventListener('beforeunload', function () {
				if (websocket) {
					websocket.close();
				}
			});
		</script>
	</body>
</html>
