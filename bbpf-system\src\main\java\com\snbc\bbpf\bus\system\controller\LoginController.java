/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.controller;

import com.snbc.bbpf.bus.system.constans.CommonConstant;
import com.snbc.bbpf.bus.system.job.LDAPUserSynchJob;
import com.snbc.bbpf.bus.system.resp.CallBaseResponse;
import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.bus.system.service.UserLoginService;
import com.snbc.bbpf.bus.system.utils.BossDES3Util;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.bus.system.utils.IpUtil;
import com.snbc.bbpf.bus.system.utils.RedisCheckCodeLoginFlag;
import com.snbc.bbpf.bus.system.utils.ResultUtil;
import com.snbc.bbpf.buslog.annotations.Buslog;
import com.snbc.bbpf.component.captcha.config.Container;
import com.snbc.bbpf.component.captcha.model.common.NumConstant;
import com.snbc.bbpf.component.captcha.model.common.RepCodeEnum;
import com.snbc.bbpf.component.captcha.service.CaptchaCacheService;
import com.snbc.bbpf.component.captcha.util.RandomUtils;
import com.snbc.bbpf.system.db.common.dto.LoginTypeDto;
import com.snbc.bbpf.system.db.common.entity.login.LoginUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * @ProjectName: bus-module-sys
 * @Package: com.snbc.bbpf.organize.domain.module
 * @ClassName: LoginController
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 登录用户入口
 * @Author: Liangjb
 * @CreateDate: 2020/6/9 14:46
 */
@RestController
@RequestMapping("/console/certification")
public class LoginController {
    @Autowired
    private UserLoginService userLoginService;
    private static final Logger LOGGER = LoggerFactory.getLogger(LoginController.class);
    @Value("${bbpf.captcha.cache.type:mem}")
    private String cacheType;

    @Value("${bbpf.boss.login.type:ldap,sms,account,qrcode}")
    private String loginType;

    @Autowired
    private RedisCheckCodeLoginFlag redisCheckCodeLoginFlag;
    @Autowired
    private LDAPUserSynchJob  ldapUserSynchJob;

    /***
     * @Description: 用户登录接口
     * @Author: Liangjb
     * 根据之前BBPF1.5改造，优化如下：
     * 1，去掉一些没必要的入参和出参 角色列表相关
     * 2，优化了接口流程，校验参数提前
     * 3，增加一个异地IP登录提醒（预留）
     * 4，整体流程如下：获取IP，记录URL，验证用户是否存在，验证用户是否东注，验证用户是否允许多端登录
     * 验证用户密码，获取用户角色ID，获取TOKEN（IP，操作时间、登录时间、SYSTYPE、ID、状态、用户名、角色ID拼接）
     * ，存入Redis（key为systype:userid 内容为ID，username,tole,username,部门电话）,返回用户信息
     * @CreateDate: 2021/5/19 16:27
     * @UpdateDate: 2021/5/19 16:27
     * @return :        com.snbc.bbpf.component.config.CommonResp
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @RequestMapping(value = "/userLogin", method = RequestMethod.POST)
    public CommonResp verifyUserPwd(@RequestBody @Validated LoginUser loginUser) throws Exception {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = servletRequestAttributes.getRequest();
        String userAgent = request.getHeader("user-agent");
        String ip = IpUtil.getIpAddress();
        return userLoginService.verifyUserPwd(loginUser, userAgent, ip);
    }
    /***
     * @Description: 用户登录
     * @Author: Liangjb
     * 根据之前BBPF1.5改造，优化如下：
     * 1，去掉一些没必要的入参和出参 角色列表相关
     * 2，优化了接口流程，校验参数提前
     * 3，增加一个异地IP登录提醒（预留）
     * 4，整体流程如下：获取IP，记录URL，验证用户是否存在，验证用户是否东注，验证用户是否允许多端登录
     * 验证用户密码，获取用户角色ID，获取TOKEN（IP，操作时间、登录时间、SYSTYPE、ID、状态、用户名、角色ID拼接）
     * ，存入Redis（key为systype:userid 内容为ID，username,tole,username,部门电话）,返回用户信息
     * @CreateDate: 2021/5/19 16:27
     * @UpdateDate: 2021/5/19 16:27
     * @return :        com.snbc.bbpf.component.config.CommonResp
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @PutMapping("logOut")
    @Buslog(opration = "userLogout", zh = "登出系统", en = "Exit the system", target = "login")
    public CommonResp<Object>  userLogOut(@RequestParam String sysType, @RequestHeader(value = "sessionId", required = false) String sessionId) {
        String userId=CurrentUser.getUserId();    
        // 构建缓存key
        String redisKey = sysType + CommonConstant.JOINER + userId;
        if (sessionId != null && !sessionId.isEmpty()) {
            // 如果有sessionId，则使用包含sessionId的key，只删除当前会话的缓存
            redisKey = redisKey + CommonConstant.JOINER + sessionId;
        }
        
        redisCheckCodeLoginFlag.userLogOut(CommonConstant.USER_LOGIN_STATUS, redisKey);
        return CommonResp.builder().head(ResultUtil.success()).build();
    }

    /***
     * @Description: 用户临时获取TOKEN
     * @Author: Liangjb
     * 获取临时TOKEN
     * @CreateDate: 2021/5/19 16:27
     * @UpdateDate: 2021/5/19 16:27
     * @return :        com.snbc.bbpf.component.config.CommonResp
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @GetMapping("/getToken")
    public CommonResp<Object>  getToken(  @RequestParam String userName,@RequestParam String tenantId,@RequestParam String sysType) {
        String token=userLoginService.getUserIdToken(userName,tenantId,sysType, IpUtil.getIpAddress());
        return  (null!=token&&token.length()!=0)?
                CommonResp.builder().head(ResultUtil.success()).body(token).build():
                CommonResp.builder().head(ResultUtil.error("用户临时获取TOKEN")).build();
    }
    @GetMapping("/sysUser")
    public CommonResp<Object>  sysUser() {
        ldapUserSynchJob.ldapuserSynchHandler();
        return  CommonResp.builder().head(ResultUtil.success()).body(null).build();
    }
    /**
     * @Description:    获取登录验证码接口,自动化测试使用
     * @Author:         wangsong
     * @CreateDate:     2021/8/19 16:22
     * @UpdateDate:     2021/8/19 16:22
     * @return :        com.snbc.bbpf.component.config.CommonResp
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @GetMapping("/getSecondVerifCaptcha")
    public CommonResp getSecondVerifCaptcha() throws Exception {
        String captchaMark = RandomUtils.getUUID();
        CaptchaCacheService captchaCacheService = Container.getBean("captchaCacheServiceMemImpl", CaptchaCacheService.class);
        //redis缓存
        if ("redis".equals(cacheType)) {
            captchaCacheService = Container.getBean("captchaCacheServiceRedisImpl", CaptchaCacheService.class);
        }
        String secondKey = String.format("RUNNING:CAPTCHA:second-%s", captchaMark);
        captchaCacheService.set(secondKey, captchaMark, Long.valueOf(NumConstant.ONEHUNDREDEIGHTY));
        String encryptCaptchaMark = BossDES3Util.encrypt(captchaMark);
        return CommonResp.builder()
                .head(CallBaseResponse.builder()
                        .code(RepCodeEnum.SUCCESS.getCode())
                        .message(RepCodeEnum.SUCCESS.getMessage()).build())
                .body(encryptCaptchaMark).build();
    }

    /***
      * @Description:    获取支持的登陆方式
      * @Author:         WangSong
      * @return:         com.snbc.bbpf.component.config.CommonResp
      * @CreateDate:     2023/5/17 19:27
      * @UpdateDate:     2023/5/17 19:27
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
    */
    @GetMapping("/getLoginType")
    public CommonResp getLoginType(){
        LoginTypeDto loginTypeDto = LoginTypeDto.builder().accountLogin(loginType.contains(CommonConstant.ACCOUNT_LOGIN_TYPE))
                .smsLogin(loginType.contains(CommonConstant.SMS_LOGIN_TYPE)).ldapLogin(loginType.contains(CommonConstant.LDAP_LOGIN_TYPE))
                .qrCodeLogin(loginType.contains(CommonConstant.QRCODE_LOGIN_TYPE)).build();
        return CommonResp.builder().head(ResultUtil.success()).body(loginTypeDto).build();
    }
}
