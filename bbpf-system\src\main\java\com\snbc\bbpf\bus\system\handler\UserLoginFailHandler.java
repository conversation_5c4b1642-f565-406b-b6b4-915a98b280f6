package com.snbc.bbpf.bus.system.handler;

import com.snbc.bbpf.bus.system.constans.NumberConstant;
import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.queue.BusLogQueue;
import com.snbc.bbpf.bus.system.utils.IpUtil;
import com.snbc.bbpf.buslog.entity.BusLog;
import com.snbc.bbpf.system.db.common.entity.User;
import com.snbc.bbpf.system.db.common.mapper.UserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import static com.snbc.bbpf.bus.system.constans.NumberConstant.NO_ZERO;

/**
 * @ClassName: UserLoginStatusHandler
 * @Description:  用户锁定处理
 * @module: bbpf-system-manager
 * @Author: wangsong
 * @date: 2022/8/30
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Component
@Slf4j
public class UserLoginFailHandler {
    private static final UserLoginFailHandler userLoginStatusHandler = new UserLoginFailHandler();
    //用户登录状态redis key
    private static final String USER_LOGIN_ERROR_COUNT = "bpf-loginerrorcount-%s";
    private static final Integer ABOUT_THE_EXPIRE_COUNT = 4;
    private static final Integer USER_LOCKED_COUNT = 5;
    private static int stLockingTime;

    @Autowired
    private RedisTemplate<String,Integer> redisTemplate;
    @Autowired
    private UserMapper userMapper;
    /**
     * 登录锁定时间 单位：小时
     */
    @Value("${bbpf.login.lock.time:120}")
    private void setLockingTime(int lockingTime){
        stLockingTime = lockingTime;
    }

    @PostConstruct
    public void initRedisTemplate(){
        userLoginStatusHandler.redisTemplate = redisTemplate;
        userLoginStatusHandler.userMapper = userMapper;
    }

    /**
     * 查询用户登录失败次数
     * @param userId
     */
    public static void queryUserLoginStatus(User user) {
        if (null != user.getLoginLockTime()) {
            // 计算两个时间之间的Duration
            LocalDateTime now = LocalDateTime.now();
            // 创建日期时间格式器
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            // 格式化为字符串
            String nowTime = now.format(formatter);
            Duration duration = Duration.between(user.getLoginLockTime(), LocalDateTime.parse(nowTime, formatter));
            // 转为分钟数
            long minutes = duration.toMinutes();
            // 如果分钟数小于锁定时间，抛出未解锁时间
            if (stLockingTime > minutes) {
                throw new BusinessException(String.valueOf(stLockingTime - minutes), ErrorMessage.USER_LOCKED.getCode());
            } else {
                // 解锁
                userLoginStatusHandler.userMapper.loginUnLock(user.getUserId());
                user.setHasLock(NumberConstant.NO_ONE);
            }
        }
    }

    /***
     * @Description:    登录失败处理
     * @Author:         wangsong
     * @param :         userId
     * @CreateDate:     2022/8/31 15:52
     * @UpdateDate:     2022/8/31 15:52
     * @return :        void
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    public static void modifyLoginFailCount(User user,int checkType) {
        String loginCountRedisKey = String.format(USER_LOGIN_ERROR_COUNT, user.getUserId());
        if (userLoginStatusHandler.redisTemplate.hasKey(loginCountRedisKey)) {
            Integer loginFailCount = userLoginStatusHandler.redisTemplate.opsForValue().get(loginCountRedisKey);
            //登录失败次数+1
            loginFailCount++;
            //修改redis中的登陆失败次数
            userLoginStatusHandler.redisTemplate.opsForValue().set(loginCountRedisKey, loginFailCount,
                    calculateTimeUntilTomorrow(), TimeUnit.SECONDS);
            //失败达到四次提示将锁定
            if (loginFailCount.equals(ABOUT_THE_EXPIRE_COUNT)) {
                if (checkType == 0) {
                    throw new BusinessException(ErrorMessage.USER_WILL_BE_LOCKED.getMessage(),
                            ErrorMessage.USER_WILL_BE_LOCKED.getCode());
                }else {
                    throw new BusinessException(ErrorMessage.USER_BIND_TIMES_OUT.getMessage(),
                            ErrorMessage.USER_BIND_TIMES_OUT.getCode());
                }
            }
            //失败五次锁定用户
            userLoginLocked(loginFailCount,loginCountRedisKey,user,checkType);
        } else {
            //头一次登录失败
            userLoginStatusHandler.redisTemplate.opsForValue().set(loginCountRedisKey, NumberConstant.NO_ONE,
                    calculateTimeUntilTomorrow(), TimeUnit.SECONDS);
        }
    }

    /***
     * @Description:    登录成功后删除登录失败次数
     * @Author:         wangsong
     * @param :         userId
     * @CreateDate:     2022/8/31 16:51
     * @UpdateDate:     2022/8/31 16:51
     * @return :        void
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    public static void loginSuccessReset(String userId) {
        userLoginStatusHandler.redisTemplate.delete(String.format(USER_LOGIN_ERROR_COUNT, userId));
    }

    /***
     * @Description:    计算到凌晨的时间
     * @Author:         wangsong
     * @CreateDate:     2022/8/31 16:23
     * @UpdateDate:     2022/8/31 16:23
     * @return :        long
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    private static long calculateTimeUntilTomorrow(){
        //到凌晨的时长，单位：秒
        return ChronoUnit.SECONDS.between(LocalDateTime.now(),
                LocalDateTime.now().plusDays(NumberConstant.NO_ONE).withHour(NO_ZERO)
                        .withMinute(NO_ZERO).withSecond(NO_ZERO).withNano(NO_ZERO));
    }

    /***
     * @Description:    失败五次锁定用户
     * @Author:         wangsong
     * @param :         loginStatusCount
     * @param :         loginCountRedisKey
     * @param :         userId
     * @CreateDate:     2022/8/31 16:48
     * @UpdateDate:     2022/8/31 16:48
     * @return :        void
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    private static void userLoginLocked(Integer loginStatusCount, String loginCountRedisKey,
                                        User user,int checkType) {
        //失败达到五次处理
        if (loginStatusCount.equals(USER_LOCKED_COUNT)) {
            //删除登录失败的redis数据，转换为用户登录锁定，为了24小时redis锁定解除的同时解除用户登录锁定状态
            userLoginStatusHandler.redisTemplate.delete(loginCountRedisKey);
            //变更用户状态为锁定
            userLoginStatusHandler.userMapper.updateByPrimaryKeySelective(User.builder().hasLock(NO_ZERO)
                    .userId(user.getUserId()).beforeLockStatus(user.getHasLock()).loginLockTime(LocalDateTime.now()).build());
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            String userAgent = request.getHeader("user-agent");
            BusLogQueue.getInstance().addLog(BusLog.builder().logId(UUID.randomUUID().toString()).logType("login")
                    .userId(user.getUserId()).userName(user.getUserName())
                    .logTarget("loginLock").createTime(LocalDateTime.now()).enContent("Login failed for ["+ USER_LOCKED_COUNT + "] times and has been frozen")
                    .requestSource(userAgent).zhContent("登录失败达到［"+ USER_LOCKED_COUNT +"］次已被冻结")
                    .ip(IpUtil.getIpAddress()).build());
            throw new BusinessException(String.valueOf(stLockingTime),
                    checkType==0?ErrorMessage.USER_LOCKED.getCode():ErrorMessage.USER_BIND_LOCK.getCode());
        }
    }
}
