/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.utils;


import com.snbc.bbpf.bus.system.constans.NumberConstant;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.service.DataRuleService;
import com.snbc.bbpf.system.db.common.entity.Org;
import com.snbc.bbpf.system.db.common.mapper.OrgMapper;
import com.snbc.bbpf.system.db.common.mapper.RoleOrgMapper;
import com.snbc.bbpf.system.db.common.mapper.UserRoleMapper;
import com.snbc.bbpf.system.db.common.vo.OrgVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 验证 当前人的组织机构权限与操作的组织机构是否一样
 * @Description
 * <AUTHOR>
 * @Date 2023-06-21 17:38
 */
@Slf4j
@Component
public class CheckOrgPermission {
    @Autowired
    private OrgMapper orgMapper;
    @Autowired
    private UserRoleMapper userRoleMapper;
    @Autowired
    private RoleOrgMapper roleOrgMapper;
    @Autowired
    private DataRuleService dataRuleService;
    /**
     * 非超管：校验选中的组织机构是否是本人数据权限配置的组织机构
     * @param currentUserId 当前登录人
     * @param orgIdList 待判断的组织机构，orgIdArr为空时返回当前人数据权限的组织机构，不为空用于权限校验
     * @return List<OrgVo> 返回当前人所属组织机构(带数据权限)
     * */
    public List<OrgVo> checkOrg(String currentUserId,List<String> orgIdList){
        // 判断权限是否为本人,查询本人及本人创建的组织机构
        if(CollectionUtils.isEmpty(orgIdList)){
            // 查询userId的orgPath
            List<Org> list = orgMapper.getOrgIdPath4DataRule();
            log.info("current user[{}] data rule org is ={}",currentUserId,list);
            return list.stream().map(org -> {
                OrgVo newOrg = new OrgVo();
                newOrg.setOrgId(org.getOrgId());
                newOrg.setOrgPath(org.getOrgPath());
                return newOrg;
            }).collect(Collectors.toList());
        }
        //wjc 20230621 当时登录人，校验选中的组织机构是否是本人数据权限配置的组织机构
        List<String> listRole = userRoleMapper.selectRoleByUserId(currentUserId);
        // 不是超管
        if(CollectionUtils.isEmpty(listRole) || !listRole.contains(String.valueOf(NumberConstant.F_NO_ONE))){
            // 判断权限是否为本人,查询本人及本人创建的组织机构
            // 查询userId的orgPath
            List<Org> list = orgMapper.getOrgIdPath4DataRule();
            List<String> userOrgIdList = list.stream().map(Org::getOrgId).filter(orgId -> !"0".equals(orgId)).collect(Collectors.toList());
            log.info("用户所有的orgid{}",userOrgIdList);
            log.info("要操作的orgid{}",orgIdList);
            for (String userOrgId : orgIdList) {
                log.info("对比" + userOrgIdList.contains(userOrgId));
                ErrorMessage.NO_ORG_PERMISSION.assertEquals(userOrgIdList.contains(userOrgId), true);
            }
        }
        return Lists.newArrayList();
    }

    /**
     * 判断组织机构下是否存在角色：true存在角色
     * @param orgId
     * @return
     */
    public boolean selectRoleByOrgId(String orgId){
        return roleOrgMapper.selectRoleByOrgId(orgId)>NumberConstant.NO_ZERO;
    }
}
