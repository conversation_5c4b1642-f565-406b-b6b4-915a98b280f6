# SQL拦截器使用指南

## 概述

SQL拦截器是BBPF Grafana代理服务的一个重要功能，它可以在SQL查询执行前自动为查询语句添加用户权限过滤条件，从而实现数据级权限控制。

## 功能特性

- **自动SQL拦截**: 自动拦截Grafana数据查询API请求
- **权限过滤**: 根据用户权限自动为SQL添加过滤条件
- **灵活配置**: 支持多种配置选项，适应不同业务场景
- **安全保障**: 权限获取失败时可配置拒绝访问
- **详细日志**: 支持详细日志记录，便于调试和监控

## 工作原理

1. **请求拦截**: 拦截发往Grafana的数据查询API请求
2. **权限获取**: 通过PermissionService获取当前用户的权限信息
3. **SQL解析**: 解析请求体中的SQL查询语句
4. **权限注入**: 根据用户权限为SQL添加WHERE条件
5. **请求转发**: 将修改后的请求转发给Grafana

## 配置说明

### 基本配置

在`application.yml`或`application-sql-interceptor.yml`中配置：

```yaml
bbpf:
  sql:
    interceptor:
      # 是否启用SQL拦截功能
      enabled: true
      
      # 是否启用详细日志
      verbose-logging: false
      
      # 权限获取失败时是否拒绝访问
      deny-on-permission-failure: true
      
      # 完全排除权限过滤的表名列表
      exclude-tables:
        - "t_upgrade_plan"
        - "t_public_data"
```

### 拦截规则配置

```yaml
bbpf:
  sql:
    interceptor:
      # 需要拦截的API路径模式
      intercept-patterns:
        - "/api/ds/query"
        - "/api/datasources/proxy/.*"
      
      # 不需要拦截的API路径模式
      exclude-patterns:
        - "/api/health"
        - "/api/admin/.*"
```

### 表排除配置

#### 完全排除权限过滤

对于某些不需要进行权限过滤的表（如公共数据表、配置表等），可以将其添加到排除列表中：

```yaml
bbpf:
  sql:
    interceptor:
      exclude-tables:
        - "t_upgrade_plan"     # 升级计划表
        - "t_public_data"      # 公共数据表
        - "t_system_config"    # 系统配置表
```

**特点：**
- 排除列表中的表将完全跳过权限过滤
- 支持精确表名匹配
- 表名不区分大小写
- 优先级最高，会覆盖其他所有权限配置

### 权限字段映射

#### 全局权限字段映射

```yaml
bbpf:
  sql:
    interceptor:
      # 用户字段列表
      user-fields:
        - "user_id"
        - "created_by"
        - "owner_id"
      
      # 权限字段映射
      permission-field-mapping:
        orgId: "org_id"
        tenantId: "tenant_id"
        deptId: "dept_id"
```

#### 基于表名的权限字段映射

为不同的表配置不同的权限过滤字段：

```yaml
bbpf:
  sql:
    interceptor:
      # 表级权限映射配置
      table-permission-mapping:
        # 日志表配置
        sys_log:
          user-fields:
            - "user_id"
            - "operator_id"
          org-field: "org_id"
          dept-field: "dept_id"
          custom-fields:
            roleId: "role_id"
        
        # 用户表配置
        sys_user:
          user-fields:
            - "create_user_id"
            - "update_user_id"
          org-field: "create_org_id"
          tenant-field: "tenant_id"
        
        # 角色表配置（支持前缀匹配）
        "sys_role*":
          user-fields:
            - "create_user_id"
          org-field: "create_org_id"
          dept-field: "create_dept_id"
```

**配置说明：**
- `table-permission-mapping`: 表级权限映射配置
- 键名为表名，支持精确匹配和前缀匹配（使用 `*` 通配符）
- `user-fields`: 该表中用于用户ID过滤的字段列表
- `org-field`: 该表中用于组织ID过滤的字段
- `dept-field`: 该表中用于部门ID过滤的字段
- `tenant-field`: 该表中用于租户ID过滤的字段
- `custom-fields`: 自定义权限字段映射

## 使用示例

### 全局权限过滤示例

**原始SQL：**
```sql
SELECT * FROM sales_data WHERE date >= '2024-01-01'
```

**拦截后SQL（使用全局配置）：**
```sql
SELECT * FROM sales_data 
WHERE date >= '2024-01-01' 
  AND (user_id = 'user123' OR created_by = 'user123' OR owner_id = 'user123') 
  AND org_id = 'org456' 
  AND dept_id = 'dept789'
```

### 基于表名的权限过滤示例

#### 日志表查询

**原始SQL：**
```sql
SELECT * FROM sys_log WHERE log_level = 'ERROR'
```

**拦截后SQL（使用sys_log表配置）：**
```sql
SELECT * FROM sys_log 
WHERE log_level = 'ERROR' 
  AND (user_id = 'user123' OR operator_id = 'user123') 
  AND org_id = 'org456' 
  AND dept_id = 'dept789'
```

#### 用户表查询

**原始SQL：**
```sql
SELECT * FROM sys_user WHERE status = 1
```

**拦截后SQL（使用sys_user表配置）：**
```sql
SELECT * FROM sys_user 
WHERE status = 1 
  AND (create_user_id = 'user123' OR update_user_id = 'user123') 
  AND create_org_id = 'org456' 
  AND tenant_id = 'tenant789'
```

#### 角色表查询（前缀匹配）

**原始SQL：**
```sql
SELECT * FROM sys_role_permission WHERE permission_type = 'READ'
```

**拦截后SQL（使用sys_role*前缀配置）：**
```sql
SELECT * FROM sys_role_permission 
WHERE permission_type = 'READ' 
  AND create_user_id = 'user123' 
  AND create_org_id = 'org456' 
  AND create_dept_id = 'dept789'
```

## 支持的API路径

- `/api/ds/query` - Grafana数据源查询API
- `/api/datasources/proxy/*` - 数据源代理API
- `/api/tsdb/query` - 时序数据库查询API

## 支持的SQL字段

拦截器会检查以下字段中的SQL语句：

- `rawSql`
- `sql`
- `query`
- `statement`
- `rawQuery`
- `expr`

## 权限过滤逻辑

### 过滤条件构建优先级

权限过滤按以下优先级应用：

1. **表级配置优先**：如果SQL中的表名匹配了 `table-permission-mapping` 中的配置，则使用表级配置
2. **全局配置兜底**：如果没有匹配的表级配置，则使用全局的 `permission-field-mappings` 和 `user-fields` 配置
3. **数据过滤权限**：始终从用户权限中获取 `dataFilter` 字段并应用

### 表名提取和匹配

1. **SQL解析**：从SQL语句中提取所有表名（支持别名、数据库前缀等）
2. **精确匹配**：优先进行表名的精确匹配
3. **前缀匹配**：支持使用 `*` 通配符进行前缀匹配（如 `sys_role*` 匹配 `sys_role_permission`）
4. **首个匹配**：如果SQL涉及多个表，使用第一个匹配到配置的表

### 权限字段应用

#### 表级配置字段
- `user-fields`：用户ID字段列表，使用 `OR` 连接
- `org-field`：组织ID字段
- `dept-field`：部门ID字段
- `tenant-field`：租户ID字段
- `custom-fields`：自定义权限字段映射

#### 全局配置字段
- `permission-field-mappings`：权限字段到数据库字段的映射
- `user-fields`：默认用户ID字段列表

### 用户权限过滤

自动为SQL添加用户相关的过滤条件：

```sql
(user_id = '当前用户ID' OR created_by = '当前用户ID' OR owner_id = '当前用户ID')
```

### 组织权限过滤

根据用户的组织权限添加过滤条件：

```sql
org_id = '用户组织ID'
```

### 部门权限过滤

根据用户的部门权限添加过滤条件：

```sql
dept_id = '用户部门ID'
```

### SQL修改策略

1. **现有WHERE子句**：在现有条件后添加 `AND (权限过滤条件)`
2. **无WHERE子句**：添加新的 `WHERE (权限过滤条件)`
3. **复杂查询**：智能识别主查询并添加过滤条件
4. **条件组合**：使用 `AND` 连接不同类型的过滤条件，用户字段内部使用 `OR` 连接

### 权限获取失败处理

- **拒绝访问模式**：返回 `SELECT NULL WHERE 1=0`
- **允许访问模式**：返回原始SQL查询

## 安全考虑

1. **权限验证**: 每次请求都会重新验证用户权限
2. **SQL注入防护**: 对权限值进行转义处理
3. **失败处理**: 权限获取失败时可配置拒绝访问
4. **日志记录**: 详细记录拦截过程，便于审计

## 故障排除

### 启用详细日志

```yaml
bbpf:
  sql:
    interceptor:
      verbose-logging: true
```

### 常见问题

1. **SQL未被拦截**
   - 检查API路径是否匹配拦截模式
   - 确认拦截器是否已启用
   - 查看日志确认请求是否到达拦截器

2. **权限过滤不生效**
   - 检查权限字段映射配置
   - 确认用户权限数据是否正确
   - 查看生成的SQL是否包含权限条件

3. **查询返回空结果**
   - 检查权限过滤条件是否过于严格
   - 确认数据库表结构与配置的字段名匹配
   - 验证用户是否有相应的数据访问权限

## 性能优化

1. **缓存权限信息**: PermissionService应实现权限缓存
2. **异步处理**: 对于复杂的权限计算可考虑异步处理
3. **索引优化**: 确保权限相关字段有适当的数据库索引

## 最佳实践

### 配置建议

1. **启用详细日志**：在开发和测试环境中启用详细日志，便于调试
2. **权限获取失败处理**：生产环境建议设置为拒绝访问模式
3. **API路径配置**：精确配置需要拦截的API路径，避免不必要的性能开销
4. **SQL字段配置**：根据实际的Grafana查询结构配置SQL字段名

### 表级配置最佳实践

1. **优先使用表级配置**：为不同业务表配置专门的权限字段，提高过滤精度
2. **合理使用前缀匹配**：对于有规律命名的表组（如 `sys_*`、`biz_*`），使用前缀匹配简化配置
3. **字段命名规范**：建议数据库表使用统一的权限字段命名规范，便于配置管理
4. **配置分层管理**：
   - 全局配置：设置通用的权限字段
   - 表级配置：为特殊表设置专门的权限字段
   - 自定义字段：为特殊业务需求配置额外的权限字段

### 权限字段设计建议

1. **用户字段**：建议使用多个用户字段（如 `user_id`、`create_user_id`、`update_user_id`）提供灵活的权限控制
2. **组织架构字段**：根据业务需要配置 `org_id`、`dept_id`、`tenant_id` 等字段
3. **自定义权限**：为特殊业务场景配置自定义权限字段（如 `role_id`、`project_id`）

### 部署和维护

1. **渐进式部署**: 先在测试环境验证，再逐步部署到生产环境
2. **监控告警**: 设置相关监控指标和告警
3. **定期审计**: 定期检查权限配置和拦截日志
4. **文档维护**: 及时更新权限字段映射文档

## 扩展开发

如需扩展SQL拦截功能，可以：

1. 继承`SqlInterceptorService`类
2. 重写相关方法实现自定义逻辑
3. 通过配置类添加新的配置选项
4. 实现自定义的权限过滤策略