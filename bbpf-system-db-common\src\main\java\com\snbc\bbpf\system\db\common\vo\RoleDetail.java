/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: RoleDetail
 * @Description: 角色详情信息
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/5/20
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class RoleDetail {
    /**
     * 角色id
     */
    private String roleId;
    /**
     * 角色名称
     */
    private String roleName;
    /**
     * 角色描述
     */
    private String roleDesc;
    /**
     * 角色类型
     */
    private Integer roleType;
    /**
     * 组织机构
     */
    private List<Map<String,String>> orgInfo;
    /**
     * 是否删除和编辑：true 能
     */
    private String canOperate;
}
