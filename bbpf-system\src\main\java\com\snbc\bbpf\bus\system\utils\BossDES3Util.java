/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.utils;

import com.snbc.bbpf.bus.system.constans.CommonConstant;
import com.snbc.bbpf.commons.crypt.Base64Utils;
import com.snbc.bbpf.commons.crypt.DesUtils;
import com.snbc.bbpf.commons.crypt.Padding;
import org.apache.commons.codec.CharEncoding;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;

/**
 * @ClassName: BossDES3Util<br>
 *  手机号加密，解密
 * @module: si-bbpf-system-manage
 * @Author: wjc1
 * @date: 2023/6/1 10:35
 */
public class BossDES3Util {
    private static final Logger LOGGER = LoggerFactory.getLogger(BossDES3Util.class);

    /**
     * 手机号解密
     * @param encryptPhone 待解密的手机号密文
     * @return 手机号明文
     */
    public static String decrypt(String encryptPhone){
        if(StringUtils.isBlank(encryptPhone)){
            return "";
        }
        try {
            return new String(DesUtils.decryptCbc(CommonConstant.DES_KEY.getBytes(StandardCharsets.UTF_8),
                    CommonConstant.DES_KEY_IV.getBytes(StandardCharsets.UTF_8), Base64Utils.decode(encryptPhone), Padding.PKCS5PADDING), CharEncoding.UTF_8);
        } catch (Exception e) {
            LOGGER.error("Failed to decrypt the mobile phone number,phone={}", encryptPhone, e.getMessage());
        }
        return "";
    }

    /**
     * 手机号解密
     * @param encryptPhone 待加密的手机号明文
     * @param keyIv keyIv
     * @param key key
     * @return 手机号
     */
    public static String decrypt(String encryptPhone,String key,String keyIv){
        if(StringUtils.isBlank(encryptPhone)){
            return "";
        }
        try {
            return new String(DesUtils.decryptCbc(key.getBytes(StandardCharsets.UTF_8),
                    keyIv.getBytes(StandardCharsets.UTF_8), Base64Utils.decode(encryptPhone), Padding.PKCS5PADDING), CharEncoding.UTF_8);
        } catch (Exception e) {
            LOGGER.error("Failed to decrypt the mobile phone number,phone={}",encryptPhone,e);
        }
        return "";
    }


    /**
     * 加密
     * @param phone 手机号明文
     * @return
     */
    public static String encrypt(String phone){
        if(StringUtils.isBlank(phone)){
            return "";
        }
        try {
            byte[] bytes = DesUtils.encryptCbc(CommonConstant.DES_KEY.getBytes(StandardCharsets.UTF_8),
                    CommonConstant.DES_KEY_IV.getBytes(StandardCharsets.UTF_8),
                    phone.getBytes(StandardCharsets.UTF_8),
                    Padding.PKCS5PADDING);
            return new String(Base64.encodeBase64(bytes));
        } catch (Exception e) {
            LOGGER.error("Failed to encrypt the mobile phone number,phone={}",phone,e);
        }
        return "";
    }


    /**
     * 加密
     * @param phone 手机号明文
     * @return
     */
    public static String encrypt(String phone,String key,String keyIv){
        if(StringUtils.isBlank(phone)){
            return "";
        }
        try {
            byte[] bytes = DesUtils.encryptCbc(key.getBytes(StandardCharsets.UTF_8),
                    keyIv.getBytes(StandardCharsets.UTF_8),
                    phone.getBytes(StandardCharsets.UTF_8),
                    Padding.PKCS5PADDING);
            return new String(Base64.encodeBase64(bytes));
        } catch (Exception e) {
            LOGGER.error("Failed to encrypt the mobile phone number,phone={}",phone,e);
        }
        return "";
    }
}
