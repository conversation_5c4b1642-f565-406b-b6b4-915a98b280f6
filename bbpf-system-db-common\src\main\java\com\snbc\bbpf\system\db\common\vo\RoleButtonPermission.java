/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName: RoleMenuPermission
 * @Description: 角色详情接口、按钮权限
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/5/21
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class RoleButtonPermission {
    /**
     * 权限id
     */
    private String permissionId;
    /**
     * 权限名称
     */
    private String permissionName;
    /**
     * 权限类型
     */
    private String permissionType;
    /**
     * 角色数据
     */
    private List<RoleScopePermission> scopes;
}
