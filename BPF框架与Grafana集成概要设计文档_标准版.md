# BPF框架与Grafana集成概要设计文档

## 目录

1. [编写目的](#1-编写目的)
2. [术语定义](#2-术语定义)
3. [项目综述](#3-项目综述)
4. [总体设计](#4-总体设计)
5. [五类视图设计](#5-五类视图设计)
6. [功能模块描述](#6-功能模块描述)
7. [接口设计](#7-接口设计)
8. [界面设计](#8-界面设计)
9. [数据库设计](#9-数据库设计)
10. [系统性能指标](#10-系统性能指标)
11. [运行环境的配置](#11-运行环境的配置)
12. [维护设计](#12-维护设计)
13. [其它](#13-其它)

## 1. 编写目的

本文档旨在为BPF框架与Grafana集成项目提供详细的概要设计方案，指导开发团队进行系统架构设计、功能模块开发和系统集成工作。

文档面向的读者包括：

- 项目架构师和技术负责人
- 开发工程师和测试工程师
- 运维工程师和产品经理
- 项目管理人员

## 2. 术语定义

| 术语       | 定义                                          |
| ---------- | --------------------------------------------- |
| BPF        | 基础业务平台                                  |
| Grafana    | 开源的监控和可观测性平台                      |
| JWT        | JSON Web Token，用于身份验证的令牌标准        |
| RBAC       | Role-Based Access Control，基于角色的访问控制 |
| SQL拦截    | 对SQL查询语句进行拦截和修改的技术             |
| 多租户     | 支持多个租户共享同一应用实例的架构模式        |
| Schema隔离 | 通过数据库Schema实现数据隔离的技术            |
| 代理模块   | 位于客户端和服务端之间的中间件组件            |

## 3. 项目综述

### 3.1 项目综述

BPF框架与Grafana集成项目旨在构建一个统一的监控看板平台，通过代理模块实现BPF框架与Grafana的深度集成。项目主要解决以下业务需求：

- **3.1 用户能够登录直接访问Grafana**：实现基于JWT Token的单点登录
- **3.2 支持iframe方式集成Grafana图表**：支持业务系统嵌入监控图表
- **3.3 仪表盘权限控制与导出权限控制**：提供精细化的权限管理
- **3.4 提供Grafana图表查询和导出的能力**：封装标准化API接口
- **3.5 与业务平台深度集成**：实现多租户数据隔离和业务数据联动
- **3.6 数据分享功能和用户使用**：支持监控看板的分享和协作

### 3.2 存在什么问题和风险

**技术风险：**

- SQL拦截器性能影响：SQL解析和修改可能影响查询性能
- 多租户数据隔离失效：权限配置错误可能导致数据泄露
- JWT Token安全风险：Token泄露或伪造可能导致安全问题
- 系统复杂度增加：代理层增加可能影响系统稳定性

**业务风险：**

- 需求变更频繁：监控需求可能随业务发展而变化
- 用户接受度问题：新的集成方式可能需要用户适应
- 数据质量问题：监控数据的准确性和完整性需要保证

**运维风险：**

- 部署复杂度增加：多组件部署增加运维难度
- 监控盲区：代理层本身的监控需要完善
- 故障排查困难：多层架构可能增加问题定位难度

### 3.3 局限性

- **Grafana版本依赖**：需要Grafana Enterprise版本支持Auth Proxy功能
- **数据源限制**：主要支持MySQL、Prometheus等常见数据源
- **权限粒度**：权限控制粒度受限于Grafana原生功能
- **性能约束**：SQL拦截机制对复杂查询性能有一定影响
- **扩展性限制**：代理模块的扩展能力受限于当前架构设计

### 3.4 设计的前提条件

**技术前提：**

- 采用Spring Boot 2.7.x作为开发框架
- 使用Redis 6.2.x作为分布式缓存
- 使用MySQL 8.0.x作为关系型数据库
- 使用Grafana Enterprise 9.5.x作为监控平台
- 支持Docker容器化部署

**业务前提：**

- BPF框架已具备完善的用户认证和权限管理功能
- 业务系统支持JWT Token认证机制
- 监控数据源已经建立并可正常访问
- 用户权限体系已经建立并在BPF框架中维护

**环境前提：**

- 生产环境支持集群部署
- 网络环境支持HTTPS通信
- 具备完善的监控和日志收集系统
- 运维团队具备容器化部署和维护能力

## 4. 总体设计

### 4.1 总体设计思想

#### 4.1.1 总体技术路线

项目采用分层架构设计，通过代理模块实现BPF框架与Grafana的集成：

**第一阶段：基础架构搭建**

- 搭建Spring Boot代理服务框架
- 实现JWT认证机制
- 集成Redis缓存和MySQL数据库
- 配置Grafana Auth Proxy模式

**第二阶段：核心功能开发**

- 开发权限控制模块
- 实现RBAC权限模型
- 开发SQL拦截器
- 实现多租户数据隔离

**第三阶段：高级特性实现**

- 开发iframe集成功能
- 实现CORS跨域配置
- 集成监控指标收集
- 完善安全防护机制

**第四阶段：生产部署优化**

- 容器化部署配置
- 集群高可用部署
- 监控告警配置
- 性能优化调整

#### 4.1.2 总体技术架构

```mermaid
graph TB
    subgraph "用户接入层"
        A[Web浏览器]
        B[移动端App]
        C[业务系统iframe]
    end
  
    subgraph "负载均衡层"
        D[Nginx负载均衡器]
    end
  
    subgraph "应用服务层"
        E[BPF前端应用]
        F[BPF-Grafana代理服务]
        G[Grafana Enterprise]
    end
  
    subgraph "业务服务层"
        H[BPF后端服务]
        I[权限服务]
        J[认证服务]
    end
  
    subgraph "数据存储层"
        K[Redis集群]
        L[MySQL集群]
    end
  
    subgraph "监控数据源"
        M[Prometheus]
        N[业务数据库]
        O[日志系统]
    end
  
    A --> D
    B --> D
    C --> D
    D --> E
    E --> F
    F --> G
    F --> H
    F --> I
    F --> J
    F --> K
    H --> L
    I --> L
    J --> L
    G --> M
    G --> N
    G --> O
```

### 4.2 总体系统结构

系统采用微服务架构，主要包括以下组件：

**核心组件：**

- **BPF-Grafana代理服务**：核心代理模块，负责认证、权限控制、SQL拦截
- **Grafana Enterprise**：监控看板平台
- **BPF后端服务**：提供用户认证和权限管理功能

**支撑组件：**

- **Redis集群**：分布式缓存，代理服务用于存储从外部权限服务获取的权限信息和会话数据
- **MySQL集群**：关系型数据库，存储配置和日志数据
- **Nginx负载均衡器**：提供负载均衡和SSL终端功能

**外部系统：**

- **监控数据源**：Prometheus、业务数据库、日志系统
- **BPF前端应用**：业务系统前端界面

### 4.3 总体业务流程

#### 4.3.1 用户认证流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant BF as BPF前端
    participant GP as Grafana代理服务
    participant BA as BPF认证服务
    participant R as Redis缓存
    participant G as Grafana实例

    Note over U,G: 用户登录流程
    U->>BF: 1. 访问BPF系统
    BF->>BA: 2. 用户认证请求
    BA-->>BF: 3. 返回JWT Token
    BF-->>U: 4. 登录成功，获得Token

    Note over U,G: 访问Grafana流程
    U->>GP: 5. 访问Grafana(携带JWT Token)
    GP->>GP: 6. JwtAuthenticationFilter拦截
    GP->>BA: 7. 调用BPF后台验证Token
    BA-->>GP: 8. Token验证结果

    alt Token有效
        GP->>R: 9. 查询代理服务权限缓存
        alt 缓存命中
            R-->>GP: 10. 返回缓存的权限信息
        else 缓存未命中
            GP->>BA: 11. 调用权限API获取权限
            BA-->>GP: 12. 返回用户权限信息
            GP->>R: 13. 代理服务缓存权限信息
        end
        GP->>G: 14. 转发请求(设置X-WEBAUTH-USER头)
        G-->>GP: 15. 返回Grafana响应
        GP-->>U: 16. 返回最终响应
    else Token无效
        GP-->>U: 17. 返回401未授权
    end
```

#### 4.3.2 SQL拦截与权限控制流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant GP as Grafana代理服务
    participant PS as 权限服务
    participant R as Redis缓存
    participant G as Grafana实例
    participant DB as 数据库

    U->>GP: 1. 发起数据查询请求
    GP->>GP: 2. 检查API路径是否需要拦截

    alt 需要SQL拦截
        GP->>R: 3. 查询代理服务权限缓存
        alt 缓存命中
            R-->>GP: 4. 返回缓存的权限信息
        else 缓存未命中
            GP->>PS: 5. 调用权限服务获取权限
            PS-->>GP: 6. 返回用户权限信息
            GP->>R: 7. 代理服务缓存权限信息
        end

        GP->>GP: 8. 解析请求体中的SQL
        GP->>GP: 9. 提取SQL中的表名
        GP->>GP: 10. 根据表级权限配置构建过滤条件

        Note over GP: SQL拦截器内部处理:
        Note over GP: - 租户Schema隔离: tenant_a.table_name
        Note over GP: - 用户字段过滤: user_id = 'xxx'
        Note over GP: - 组织字段过滤: org_id = 'xxx'
        Note over GP: - 部门字段过滤: dept_id IN ('xxx','yyy')

        GP->>GP: 11. 修改SQL添加WHERE条件
        GP->>G: 12. 转发修改后的请求
    else 无需拦截
        GP->>G: 12. 直接转发原始请求
    end

    G->>DB: 13. 执行SQL查询
    DB-->>G: 14. 返回查询结果
    G-->>GP: 15. 返回Grafana响应
    GP-->>U: 16. 返回最终响应
```

#### 4.3.3 动态模板变量注入流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant GP as Grafana代理服务
    participant DV as 动态变量服务
    participant PS as 权限服务
    participant R as Redis缓存
    participant G as Grafana实例

    U->>GP: 1. 访问仪表盘API
    GP->>GP: 2. JWT Token验证
    GP->>G: 3. 转发仪表盘请求
    G-->>GP: 4. 返回仪表盘JSON
    
    GP->>R: 5. 查询代理服务权限缓存
    alt 缓存命中
        R-->>GP: 6. 返回缓存的权限信息
    else 缓存未命中
        GP->>PS: 7. 调用权限服务获取权限
        PS-->>GP: 8. 返回用户权限信息
        GP->>R: 9. 代理服务缓存权限信息
    end
    GP->>DV: 10. 调用动态变量注入(传入权限信息)
    
    DV->>DV: 11. 生成权限变量
    Note over DV: 生成变量:
    Note over DV: $_bbpf_user_id = 'user123'
    Note over DV: $_bbpf_tenant_id = 'tenant_001'
    Note over DV: $_bbpf_org_id = 'org_001'
    Note over DV: $_bbpf_dept_ids = 'dept_001,dept_002'
    
    DV->>DV: 12. 注入到仪表盘模板变量
    DV-->>GP: 13. 返回修改后的仪表盘JSON
    GP-->>U: 14. 返回包含动态变量的仪表盘
    
    Note over U,G: 用户在查询中使用变量
    U->>GP: 15. 执行包含$_bbpf_*变量的查询
    GP->>G: 16. 转发查询请求
    G->>G: 17. Grafana自动替换变量值
    G-->>GP: 18. 返回过滤后的数据
    GP-->>U: 19. 返回最终结果
```

#### 4.3.4 iframe集成流程

```mermaid
sequenceDiagram
    participant BS as 业务系统
    participant IF as iframe
    participant GP as Grafana代理服务
    participant G as Grafana实例

    Note over BS,G: iframe嵌入初始化
    BS->>IF: 1. 创建iframe元素
    BS->>IF: 2. 设置Grafana URL(包含Token)
    IF->>GP: 3. 加载Grafana页面
    GP->>GP: 4. 验证Token
    GP->>G: 5. 转发请求
    G-->>GP: 6. 返回页面内容
    GP-->>IF: 7. 返回处理后的页面
    IF-->>BS: 8. 显示Grafana图表

    Note over BS,G: 跨域通信
    IF->>BS: 9. postMessage(认证请求)
    BS->>IF: 10. postMessage(Token信息)
    IF->>GP: 11. 使用Token访问API
    GP->>G: 12. 转发API请求
    G-->>GP: 13. 返回数据
    GP-->>IF: 14. 返回处理后的数据
    IF-->>BS: 15. 更新图表显示
```

### 4.4 总体功能分配

| 功能模块 | 主要功能                    | 负责组件            | 实现方案 |
| -------- | --------------------------- | ------------------- | -------- |
| 用户认证 | JWT Token验证、用户身份识别 | BPF-Grafana代理服务 | JWT过滤器 |
| 权限控制 | 用户权限查询、资源访问控制  | BPF-Grafana代理服务 | RBAC权限模型 |
| 数据权限-方案1 | 动态模板变量注入、权限变量生成 | BPF-Grafana代理服务 | 响应拦截+变量注入 |
| 数据权限-方案2 | SQL解析、权限条件注入       | BPF-Grafana代理服务 | 请求拦截+SQL修改 |
| 数据隔离 | 多租户Schema隔离            | BPF-Grafana代理服务 | Schema前缀+权限过滤 |
| 代理转发 | HTTP/WebSocket请求代理      | BPF-Grafana代理服务 | Spring Cloud Gateway |
| 响应拦截 | 仪表盘响应拦截、变量注入    | BPF-Grafana代理服务 | HandlerInterceptor |
| 监控展示 | 仪表盘展示、图表渲染        | Grafana Enterprise  | Grafana原生功能 |
| 缓存管理 | 权限信息缓存、会话管理      | Redis集群           | Redis分布式缓存 |
| 配置存储 | 权限配置、系统配置存储      | MySQL集群           | 关系型数据库 |
| 负载均衡 | 请求分发、SSL终端           | Nginx负载均衡器     | Nginx反向代理 |

### 4.5 尚未解决的问题

1. **Grafana插件兼容性**：部分第三方插件可能与Auth Proxy模式存在兼容性问题
2. **大数据量查询优化**：对于大数据量的监控查询，SQL拦截器的性能优化方案
3. **实时数据推送**：WebSocket连接的权限控制和数据过滤机制
4. **多数据源权限**：跨多个数据源的统一权限控制方案
5. **动态权限更新**：权限变更时的实时生效机制

## 5. 五类视图设计

### 5.1 逻辑视图

```mermaid
graph TB
    subgraph "表现层"
        A1[Web前端]
        A2[移动端]
        A3[iframe嵌入]
    end

    subgraph "应用层"
        B1[认证模块]
        B2[权限模块]
        B3[代理模块]
        B4[监控模块]
    end

    subgraph "业务层"
        C1[用户管理]
        C2[权限管理]
        C3[租户管理]
        C4[配置管理]
    end

    subgraph "数据层"
        D1[用户数据]
        D2[权限数据]
        D3[配置数据]
        D4[日志数据]
    end

    subgraph "外部系统"
        E1[Grafana]
        E2[监控数据源]
        E3[BPF后端服务]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B1
    B1 --> B2
    B2 --> B3
    B3 --> B4
    B1 --> C1
    B2 --> C2
    B3 --> C3
    B4 --> C4
    C1 --> D1
    C2 --> D2
    C3 --> D3
    C4 --> D4
    B3 --> E1
    E1 --> E2
    B1 --> E3
```

逻辑视图展示了系统的功能分层结构：

- **表现层**：用户交互界面，包括Web前端、移动端和iframe嵌入
- **应用层**：核心业务逻辑，包括认证、权限、代理和监控模块
- **业务层**：业务实体管理，包括用户、权限、租户和配置管理
- **数据层**：数据存储，包括各类业务数据和日志数据
- **外部系统**：依赖的外部系统，包括Grafana、监控数据源和BPF后端服务

### 5.2 开发视图

```mermaid
graph TB
    subgraph "bpf-grafana-proxy代理模块"
        A1[认证过滤器<br/>AuthenticationFilter]
        A2[权限服务<br/>PermissionService]
        A3[SQL拦截器<br/>SqlInterceptor]
        A4[代理控制器<br/>ProxyController]
        A5[配置管理<br/>ConfigManager]
        A6[缓存管理<br/>CacheManager]
        A7[监控指标<br/>MetricsCollector]
    end

    subgraph "核心依赖"
        B1[Spring Boot Starter]
        B2[Spring Security]
        B3[Redis Template]
        B4[MySQL Driver]
        B5[HTTP Client]
        B6[Micrometer]
    end

    subgraph "工具类库"
        C1[JWT工具类<br/>JwtUtils]
        C2[SQL解析器<br/>SqlParser]
        C3[缓存工具类<br/>CacheUtils]
        C4[HTTP工具类<br/>HttpUtils]
        C5[加密工具类<br/>CryptoUtils]
    end

    A1 --> B2
    A2 --> B3
    A3 --> C2
    A4 --> B5
    A5 --> B4
    A6 --> B3
    A7 --> B6
    A1 --> C1
    A2 --> C3
    A4 --> C4
    A5 --> C5
```

开发视图展示了代理模块的组件结构：

**核心组件：**

- **AuthenticationFilter**：JWT认证过滤器，负责Token验证
- **PermissionService**：权限服务，负责权限查询和验证
- **SqlInterceptor**：SQL拦截器，负责SQL解析和权限注入
- **ProxyController**：代理控制器，负责请求转发
- **ConfigManager**：配置管理器，负责配置加载和管理
- **CacheManager**：缓存管理器，负责缓存操作
- **MetricsCollector**：监控指标收集器

**依赖关系：**

- 基于Spring Boot框架构建
- 使用Spring Security进行安全控制
- 集成Redis进行缓存管理
- 使用MySQL存储配置数据
- 集成Micrometer进行监控指标收集

### 5.3 运行视图

```mermaid
graph LR
    subgraph "用户请求处理流程"
        P1[用户请求] --> P2[认证过滤器]
        P2 --> P3[权限验证]
        P3 --> P4[SQL拦截]
        P4 --> P5[代理转发]
        P5 --> P6[响应处理]
        P6 --> P7[返回结果]
    end

    subgraph "缓存处理流程"
        C1[缓存查询] --> C2[缓存命中?]
        C2 -->|是| C3[返回缓存数据]
        C2 -->|否| C4[查询数据库]
        C4 --> C5[更新缓存]
        C5 --> C6[返回数据]
    end

    subgraph "权限处理流程"
        R1[权限请求] --> R2[用户识别]
        R2 --> R3[角色查询]
        R3 --> R4[权限计算]
        R4 --> R5[权限缓存]
        R5 --> R6[权限返回]
    end
```

运行视图展示了系统运行时的主要处理流程：

**用户请求处理流程：**

1. 用户发起请求
2. 认证过滤器验证JWT Token
3. 权限验证检查用户权限
4. SQL拦截器处理数据查询请求
5. 代理转发请求到Grafana
6. 响应处理和结果返回

**缓存处理流程：**

- 优先查询缓存数据
- 缓存未命中时查询数据库
- 更新缓存并返回数据

**权限处理流程：**

- 用户身份识别
- 角色和权限查询
- 权限信息缓存
- 权限验证结果返回

### 5.4 物理视图

```mermaid
graph TB
    subgraph "负载均衡层"
        LB1[Nginx-1<br/>192.168.1.10]
        LB2[Nginx-2<br/>192.168.1.11]
    end

    subgraph "应用服务层"
        APP1[BPF-Grafana-Proxy-1<br/>192.168.2.10<br/>8核16G]
        APP2[BPF-Grafana-Proxy-2<br/>192.168.2.11<br/>8核16G]
        GF1[Grafana<br/>192.168.2.20<br/>4核8G]
    end

    subgraph "数据服务层"
        REDIS1[Redis-Master<br/>192.168.3.10<br/>8核16G]
        REDIS2[Redis-Slave<br/>192.168.3.11<br/>8核16G]
        MYSQL1[MySQL-Master<br/>192.168.3.20<br/>16核32G]
        MYSQL2[MySQL-Slave<br/>192.168.3.21<br/>16核32G]
    end

    subgraph "监控服务层"
        PROM[Prometheus<br/>192.168.4.10<br/>4核8G]
        ELK[ELK Stack<br/>192.168.4.20<br/>8核16G]
    end

    LB1 --> APP1
    LB1 --> APP2
    LB2 --> APP1
    LB2 --> APP2
    APP1 --> GF1
    APP2 --> GF1
    APP1 --> REDIS1
    APP2 --> REDIS1
    REDIS1 --> REDIS2
    APP1 --> MYSQL1
    APP2 --> MYSQL1
    MYSQL1 --> MYSQL2
    APP1 --> PROM
    APP2 --> PROM
    GF1 --> PROM
    APP1 --> ELK
    APP2 --> ELK
    GF1 --> ELK
```

物理视图展示了系统的硬件部署架构：

**负载均衡层：**

- 2台Nginx服务器，提供高可用负载均衡

**应用服务层：**

- 2台BPF-Grafana代理服务器，8核16G配置
- 1台Grafana服务器，4核8G配置

**数据服务层：**

- Redis主从集群，8核16G配置
- MySQL主从集群，16核32G配置

**监控服务层：**

- Prometheus监控服务器，4核8G配置
- ELK日志分析集群，8核16G配置

### 5.5 数据视图

**用户权限数据模型：**

| 表名                  | 说明           | 主要字段                                             |
| --------------------- | -------------- | ---------------------------------------------------- |
| USER                  | 用户表         | user_id, username, email, tenant_id, org_id, dept_id |
| ROLE                  | 角色表         | role_id, role_name, description                      |
| PERMISSION            | 权限表         | permission_id, resource_type, resource_id, action    |
| USER_ROLE             | 用户角色关联表 | user_id, role_id                                     |
| ROLE_PERMISSION       | 角色权限关联表 | role_id, permission_id                               |
| USER_PERMISSION_CACHE | 用户权限缓存表 | user_id, permissions_data, cached_at, expires_at     |

**SQL拦截配置数据模型：**

| 表名                    | 说明             | 主要字段                                                     |
| ----------------------- | ---------------- | ------------------------------------------------------------ |
| TABLE_PERMISSION_CONFIG | 表权限配置表     | table_name, user_fields, org_field, dept_field, tenant_field |
| TENANT_SCHEMA_MAPPING   | 租户Schema映射表 | tenant_id, schema_name, isolation_enabled                    |
| SQL_INTERCEPT_LOG       | SQL拦截日志表    | log_id, user_id, original_sql, modified_sql, intercept_time  |

**系统配置数据模型：**

| 表名           | 说明          | 主要字段                                          |
| -------------- | ------------- | ------------------------------------------------- |
| SYSTEM_CONFIG  | 系统配置表    | config_key, config_value, description, updated_at |
| GRAFANA_CONFIG | Grafana配置表 | config_id, grafana_url, timeout, auth_header      |

## 6. 功能模块描述

### 6.1 token校验

#### 6.1.1 本模块的功能描述及结构组成

**功能描述：**
Token校验模块负责验证用户请求中携带的JWT Token，确保用户身份的合法性和有效性。

**主要功能：**

- JWT Token格式验证
- Token签名验证
- Token过期时间检查
- 用户身份信息提取
- Token黑名单检查

**结构组成：**

- **JwtAuthenticationFilter**：JWT认证过滤器
- **JwtTokenValidator**：Token验证器
- **JwtUtils**：JWT工具类
- **TokenBlacklistService**：Token黑名单服务

#### 6.1.2 程序设计

```java
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    @Autowired
    private JwtTokenValidator tokenValidator;

    @Autowired
    private TokenBlacklistService blacklistService;

    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                  HttpServletResponse response,
                                  FilterChain filterChain) throws ServletException, IOException {

        String token = extractTokenFromRequest(request);

        if (token != null) {
            try {
                // 验证Token格式和签名
                if (tokenValidator.validateToken(token)) {
                    // 检查Token是否在黑名单中
                    if (!blacklistService.isTokenBlacklisted(token)) {
                        // 提取用户信息并设置到SecurityContext
                        UserInfo userInfo = tokenValidator.getUserInfoFromToken(token);
                        setAuthentication(userInfo);
                        request.setAttribute("currentUser", userInfo);
                    } else {
                        handleTokenBlacklisted(response);
                        return;
                    }
                } else {
                    handleInvalidToken(response);
                    return;
                }
            } catch (Exception e) {
                handleTokenException(response, e);
                return;
            }
        }

        filterChain.doFilter(request, response);
    }

    private String extractTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
}
```

#### 6.1.3 本模块业务流和数据流图

```mermaid
flowchart TD
    A[用户请求] --> B[提取Token]
    B --> C{Token存在?}
    C -->|否| D[继续处理请求]
    C -->|是| E[验证Token格式]
    E --> F{格式正确?}
    F -->|否| G[返回401错误]
    F -->|是| H[验证Token签名]
    H --> I{签名有效?}
    I -->|否| G
    I -->|是| J[检查Token过期]
    J --> K{Token过期?}
    K -->|是| G
    K -->|否| L[检查黑名单]
    L --> M{在黑名单?}
    M -->|是| G
    M -->|否| N[提取用户信息]
    N --> O[设置认证信息]
    O --> D
```

#### 6.1.4 数据结构

**JWT Token结构：**

```json
{
  "header": {
    "alg": "RS256",
    "typ": "JWT"
  },
  "payload": {
    "sub": "user123",
    "username": "张三",
    "email": "<EMAIL>",
    "tenant_id": "tenant_001",
    "org_id": "org_001",
    "dept_id": "dept_001",
    "roles": ["user", "operator"],
    "iat": 1640995200,
    "exp": 1640998800
  },
  "signature": "..."
}
```

**用户信息结构：**

```java
public class UserInfo {
    private String userId;
    private String username;
    private String email;
    private String tenantId;
    private String orgId;
    private String deptId;
    private List<String> roles;
    private Date issuedAt;
    private Date expiresAt;
}
```

#### 6.1.5 界面描述

Token校验模块为后台服务模块，无用户界面。

#### 6.1.6 出错和异常处理

**异常类型及处理：**

- **TokenExpiredException**：Token过期异常，返回401状态码
- **TokenInvalidException**：Token无效异常，返回401状态码
- **TokenBlacklistedException**：Token在黑名单异常，返回401状态码
- **TokenParseException**：Token解析异常，返回400状态码

**错误响应格式：**

```json
{
  "code": 401,
  "message": "Token已过期",
  "timestamp": "2024-01-01T10:00:00Z",
  "path": "/api/grafana/dashboard"
}
```

#### 6.1.7 安全保密

- 使用RSA256算法进行Token签名
- Token密钥定期轮换
- 敏感信息不存储在Token中
- 实施Token黑名单机制
- 记录Token验证失败的审计日志

#### 6.1.8 本模块的性能指标

- **Token验证时间**：< 10ms (95%)
- **并发处理能力**：> 1000 TPS
- **内存使用**：< 100MB
- **CPU使用率**：< 20%

#### 6.1.9 本模块用到的开源软件

- **Spring Security**：安全框架
- **JJWT**：JWT处理库
- **Jackson**：JSON处理库
- **SLF4J**：日志框架

### 6.2 权限控制

#### 6.2.1 本模块的功能描述及结构组成

**功能描述：**
权限控制模块负责用户权限的查询、验证和管理，实现基于角色的访问控制(RBAC)。

**主要功能：**

- 用户权限查询和缓存
- 资源访问权限验证
- 角色权限管理
- 权限信息缓存管理

#### 6.2.2 程序设计

```java
@Service
public class PermissionService {

    @Autowired
    private PermissionRepository permissionRepository;

    @Autowired
    private CacheManager cacheManager;

    public UserPermissionDto getUserPermissions(String userId) {
        // 1. 先从缓存获取
        String cacheKey = "permission:user:" + userId;
        UserPermissionDto cached = cacheManager.get(cacheKey, UserPermissionDto.class);
        if (cached != null && !cached.isExpired()) {
            return cached;
        }

        // 2. 从数据库查询
        UserPermissionDto permission = permissionRepository.findByUserId(userId);

        // 3. 缓存权限信息
        if (permission != null) {
            cacheManager.put(cacheKey, permission, Duration.ofMinutes(30));
        }

        return permission;
    }

    public boolean hasPermission(String userId, String resourceType, String resourceId, String action) {
        UserPermissionDto userPermission = getUserPermissions(userId);
        if (userPermission == null) {
            return false;
        }

        return userPermission.hasPermission(resourceType, resourceId, action);
    }
}
```

### 6.3 SQL拦截

#### 6.3.1 本模块的功能描述及结构组成

**功能描述：**
SQL拦截模块负责拦截Grafana的数据查询请求，解析SQL语句并注入权限过滤条件，实现数据级权限控制。

**主要功能：**

- SQL语句解析和修改
- 权限条件注入
- 多租户数据隔离
- 表级权限控制

#### 6.3.2 程序设计

```java
@Service
public class SqlInterceptorService {

    public String interceptAndModifyRequest(String requestBody, String userId, String apiPath) {
        try {
            // 1. 解析请求体
            JsonNode rootNode = objectMapper.readTree(requestBody);
            JsonNode queriesNode = rootNode.get("queries");

            // 2. 获取用户权限
            UserPermissionDto userPermission = permissionService.getUserPermissions(userId);

            // 3. 处理每个查询
            for (JsonNode queryNode : queriesNode) {
                String originalSql = queryNode.get("rawSql").asText();

                // 4. 应用权限过滤
                String modifiedSql = applyPermissionFilter(originalSql, userPermission);

                // 5. 更新请求体
                ((ObjectNode) queryNode).put("rawSql", modifiedSql);
            }

            return objectMapper.writeValueAsString(rootNode);
        } catch (Exception e) {
            log.error("SQL拦截处理失败", e);
            throw new SqlInterceptException("SQL拦截处理失败", e);
        }
    }

    private String applyPermissionFilter(String sql, UserPermissionDto userPermission) {
        // 1. 应用租户Schema隔离
        String sqlWithSchema = applyTenantSchemaIsolation(sql, userPermission.getTenantId());

        // 2. 构建权限过滤条件
        String permissionFilter = buildPermissionFilter(userPermission, sqlWithSchema);

        // 3. 修改SQL添加WHERE条件
        return modifySqlWithPermissionFilter(sqlWithSchema, permissionFilter);
    }
}
```

### 6.4 数据权限

#### 6.4.1 本模块的功能描述及结构组成

**功能描述：**
数据权限模块实现多租户数据隔离和细粒度的数据访问控制，确保用户只能访问授权的数据。本模块提供两种数据权限控制方案：动态模板变量注入和SQL拦截修改。

**主要功能：**

- 租户级数据隔离
- 组织级数据过滤
- 部门级数据过滤
- 用户级数据过滤
- 动态模板变量注入
- SQL查询拦截和修改

#### 6.4.2 数据权限控制方案对比

本系统实现了两种数据权限控制方案，以满足不同场景的需求：

##### 方案一：动态模板变量注入

**实现原理：**
通过拦截Grafana仪表盘API响应，动态注入用户权限相关的模板变量，用户在Grafana查询中使用这些变量来实现数据过滤。

**核心组件：**
- `DynamicTemplateVariableService`：动态变量生成服务
- `DashboardResponseInterceptor`：仪表盘响应拦截器
- `GrafanaProxyServiceImpl`：代理服务集成

**变量示例：**
```
$_bbpf_user_id      # 当前用户ID
$_bbpf_tenant_id    # 租户ID
$_bbpf_org_id       # 组织ID
$_bbpf_dept_ids     # 部门ID列表
$_bbpf_user_roles   # 用户角色列表
```

**使用方式：**
```sql
SELECT * FROM user_data 
WHERE tenant_id = '$_bbpf_tenant_id' 
  AND org_id = '$_bbpf_org_id'
  AND user_id = '$_bbpf_user_id'
```

##### 方案二：SQL拦截修改

**实现原理：**
通过拦截Grafana的数据查询请求，解析SQL语句并自动注入权限过滤条件，对用户透明地实现数据权限控制。

**核心组件：**
- `SqlInterceptorService`：SQL拦截服务
- `SqlParser`：SQL解析器
- `PermissionFilterBuilder`：权限过滤条件构建器

**拦截示例：**
```sql
-- 原始SQL
SELECT * FROM user_data WHERE status = 'active'

-- 自动注入权限条件后
SELECT * FROM tenant_a.user_data 
WHERE status = 'active' 
  AND tenant_id = 'tenant_001'
  AND org_id = 'org_001' 
  AND dept_id IN ('dept_001', 'dept_002')
```

##### 两种方案对比分析

| 对比维度 | 动态模板变量注入 | SQL拦截修改 |
|----------|------------------|-------------|
| **通用性** | ✅ 高 - 适用于所有数据源和查询类型 | ⚠️ 中 - 依赖SQL解析，对复杂SQL支持有限 |
| **维护性** | ✅ 高 - 权限逻辑集中管理，易于维护 | ⚠️ 中 - 需要维护SQL解析规则和权限映射 |
| **侵入性** | ✅ 低 - 用户需要在查询中使用变量 | ✅ 无 - 对用户完全透明 |
| **安全性** | ✅ 高 - 变量在服务端生成和验证 | ✅ 高 - 服务端自动注入权限条件 |
| **性能** | ✅ 高 - 仅在仪表盘加载时处理 | ⚠️ 中 - 每次查询都需要SQL解析和修改 |
| **用户友好性** | ⚠️ 中 - 需要用户了解变量使用方法 | ✅ 高 - 用户无感知，正常编写SQL |
| **扩展性** | ✅ 高 - 易于添加新的权限变量 | ⚠️ 中 - 需要扩展SQL解析规则 |
| **调试难度** | ✅ 低 - 变量值可见，易于调试 | ⚠️ 高 - SQL修改过程复杂，调试困难 |
| **兼容性** | ✅ 高 - 兼容所有Grafana功能 | ⚠️ 中 - 可能与某些Grafana插件冲突 |
| **实现复杂度** | ✅ 低 - 实现相对简单 | ⚠️ 高 - 需要复杂的SQL解析逻辑 |

##### 方案选择建议

**推荐使用动态模板变量注入方案的场景：**
- 新建仪表盘和查询
- 需要灵活的权限控制逻辑
- 团队具备一定的Grafana使用经验
- 对性能要求较高的场景

**推荐使用SQL拦截修改方案的场景：**
- 已有大量历史仪表盘需要快速适配
- 用户对Grafana使用经验有限
- 需要对用户完全透明的权限控制
- 权限规则相对固定的场景

**混合使用策略：**
在实际部署中，建议采用混合策略：
1. 对于新建的仪表盘，优先使用动态模板变量方案
2. 对于历史仪表盘的快速适配，使用SQL拦截方案
3. 通过配置开关，允许管理员选择启用哪种方案

#### 6.4.3 动态模板变量实现设计

**程序设计：**

```java
@Service
public class DynamicTemplateVariableService {

    @Autowired
    private GrafanaProxyConfig config;

    @Autowired
    private PermissionService permissionService;

    /**
     * 拦截并注入动态模板变量
     */
    public String interceptAndInjectVariables(String responseBody, String userId) {
        if (!config.isEnableDynamicVariables()) {
            return responseBody;
        }

        try {
            JsonNode rootNode = objectMapper.readTree(responseBody);
            
            // 生成用户权限变量
            Map<String, Object> permissionVars = generatePermissionVariables(userId);
            
            // 注入到仪表盘模板变量中
            injectTemplateVariables(rootNode, permissionVars);
            
            return objectMapper.writeValueAsString(rootNode);
        } catch (Exception e) {
            log.error("动态变量注入失败", e);
            return responseBody;
        }
    }

    /**
     * 生成权限相关变量
     */
    private Map<String, Object> generatePermissionVariables(String userId) {
        UserPermissionDto permission = permissionService.getUserPermissions(userId);
        
        Map<String, Object> variables = new HashMap<>();
        variables.put("_bbpf_user_id", permission.getUserId());
        variables.put("_bbpf_tenant_id", permission.getTenantId());
        variables.put("_bbpf_org_id", permission.getOrgId());
        variables.put("_bbpf_dept_ids", permission.getDeptIds());
        variables.put("_bbpf_user_roles", permission.getRoles());
        
        return variables;
    }
}
```

#### 6.4.4 SQL拦截实现设计

**程序设计：**

```java
@Service
public class SqlInterceptorService {

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private SqlParser sqlParser;

    /**
     * 拦截并修改SQL查询
     */
    public String interceptAndModifyRequest(String requestBody, String userId) {
        try {
            JsonNode rootNode = objectMapper.readTree(requestBody);
            JsonNode queriesNode = rootNode.get("queries");

            UserPermissionDto userPermission = permissionService.getUserPermissions(userId);

            for (JsonNode queryNode : queriesNode) {
                String originalSql = queryNode.get("rawSql").asText();
                String modifiedSql = applyPermissionFilter(originalSql, userPermission);
                ((ObjectNode) queryNode).put("rawSql", modifiedSql);
            }

            return objectMapper.writeValueAsString(rootNode);
        } catch (Exception e) {
            log.error("SQL拦截处理失败", e);
            throw new SqlInterceptException("SQL拦截处理失败", e);
        }
    }

    /**
     * 应用权限过滤条件
     */
    private String applyPermissionFilter(String sql, UserPermissionDto userPermission) {
        // 1. 应用租户Schema隔离
        String sqlWithSchema = applyTenantSchemaIsolation(sql, userPermission.getTenantId());
        
        // 2. 构建权限过滤条件
        String permissionFilter = buildPermissionFilter(userPermission);
        
        // 3. 修改SQL添加WHERE条件
        return sqlParser.addWhereCondition(sqlWithSchema, permissionFilter);
    }
}
```

### 6.5 请求拦截

#### 6.5.1 本模块的功能描述及结构组成

**功能描述：**
请求拦截模块负责拦截所有发往Grafana的HTTP请求，进行认证验证、权限检查和请求转发。

### 6.6 获取token接口

#### 6.6.1 本模块的功能描述及结构组成

**功能描述：**
获取Token接口模块提供Token获取、刷新和验证的API接口，支持前端应用的认证需求。

## 7. 接口设计

### 7.1 内部接口

| 接口名称     | 方法 | 路径                          | 说明              |
| ------------ | ---- | ----------------------------- | ----------------- |
| 用户权限查询 | GET  | /api/permission/user/{userId} | 获取用户权限信息  |
| Token验证    | POST | /api/auth/validate            | 验证JWT Token     |
| SQL拦截处理  | POST | /api/sql/intercept            | SQL拦截和权限注入 |

### 7.2 外部接口

| 接口名称    | 方法 | 路径              | 说明              |
| ----------- | ---- | ----------------- | ----------------- |
| Grafana代理 | ALL  | /api/grafana/**   | 代理转发到Grafana |
| 健康检查    | GET  | /actuator/health  | 服务健康状态检查  |
| 监控指标    | GET  | /actuator/metrics | 服务监控指标      |

## 8. 界面设计

本系统主要为后台代理服务，无独立用户界面。用户通过以下方式访问：

- 直接访问Grafana界面
- 通过iframe嵌入业务系统
- 通过移动端App访问

## 9. 数据库设计

详见第5.5节数据视图中的数据模型设计。

## 10. 系统性能指标

| 指标类型 | 指标名称    | 目标值        | 说明                 |
| -------- | ----------- | ------------- | -------------------- |
| 响应时间 | API响应时间 | < 100ms (95%) | 代理服务API响应时间  |
| 吞吐量   | 并发请求数  | > 1000 TPS    | 系统并发处理能力     |
| 可用性   | 系统可用性  | > 99.9%       | 系统正常运行时间比例 |
| 缓存     | 缓存命中率  | > 80%         | 权限信息缓存命中率   |
| 资源     | CPU使用率   | < 70%         | 服务器CPU使用率      |
| 资源     | 内存使用率  | < 80%         | 服务器内存使用率     |

## 11. 运行环境的配置

### 11.1 设备环境需求

**生产环境：**

- CPU：8核以上
- 内存：16GB以上
- 存储：200GB SSD以上
- 网络：千兆网卡

**测试环境：**

- CPU：4核以上
- 内存：8GB以上
- 存储：100GB SSD以上

### 11.2 二次开发人员环境

- JDK 1.8+
- Maven 3.6+
- IDE：IntelliJ IDEA或VScode
- Git版本控制工具

### 11.3 软件环境需求

- 操作系统：Linux CentOS 7+或Ubuntu 18+
- 容器：Docker 20.x+
- 数据库：MySQL 8.0+
- 缓存：Redis 6.2+
- 监控：Grafana Enterprise 9.5+

## 12. 维护设计

### 12.1 部署

采用Docker容器化部署，支持以下部署方式：

- Docker Compose单机部署
- Kubernetes集群部署
- 传统虚拟机部署

### 12.2 日志收集

- 应用日志：使用SLF4J+Logback
- 访问日志：Nginx访问日志
- 系统日志：系统级日志收集
- 日志聚合：ELK Stack日志分析

## 13. 关键技术决策与评审点

### 13.1 数据权限控制方案选择（关键评审点）

**决策背景：**
在Grafana图表数据权限控制方面，系统设计了两种技术方案：动态模板变量注入和SQL拦截修改。这是本项目的核心技术决策点，直接影响系统的可维护性、性能和用户体验。

**技术方案对比：**

| 评估维度 | 动态模板变量注入 | SQL拦截修改 | 权重 | 评分说明 |
|----------|------------------|-------------|------|----------|
| **技术复杂度** | 8分 - 实现简单，逻辑清晰 | 5分 - 需要复杂的SQL解析 | 20% | 影响开发和维护成本 |
| **性能影响** | 9分 - 仅在仪表盘加载时处理 | 6分 - 每次查询都需要处理 | 25% | 直接影响用户体验 |
| **用户体验** | 6分 - 需要学习变量使用 | 9分 - 完全透明，无感知 | 20% | 影响用户接受度 |
| **维护成本** | 8分 - 集中管理，易维护 | 5分 - 需要维护解析规则 | 15% | 长期运维成本 |
| **扩展性** | 9分 - 易于添加新变量 | 6分 - 需要扩展解析规则 | 10% | 未来功能扩展能力 |
| **兼容性** | 9分 - 兼容所有Grafana功能 | 7分 - 可能存在插件冲突 | 10% | 与现有系统集成度 |

**综合评分：**
- 动态模板变量注入：8.1分
- SQL拦截修改：6.7分

**推荐决策：**
1. **主推方案**：动态模板变量注入（适用于新建仪表盘）
2. **兼容方案**：SQL拦截修改（适用于历史仪表盘迁移）
3. **实施策略**：采用混合模式，通过配置开关控制

### 13.2 架构设计关键评审点

#### 13.2.1 代理层设计合理性

**评审要点：**
- 代理层是否会成为性能瓶颈？
- 单点故障风险如何规避？
- 代理层的监控和运维策略是否完善？

**设计决策：**
- 采用无状态设计，支持水平扩展
- 实施健康检查和自动故障转移
- 集成完善的监控指标和告警机制

#### 13.2.2 权限缓存策略

**评审要点：**
- 缓存一致性如何保证？
- 权限变更时的实时生效机制？
- 缓存穿透和雪崩的防护措施？

**设计决策：**
- 采用Redis分布式缓存，TTL为30分钟
- 实施权限变更事件通知机制
- 使用布隆过滤器防止缓存穿透

#### 13.2.3 安全性设计

**评审要点：**
- JWT Token的安全性是否足够？
- SQL注入攻击的防护措施？
- 敏感数据的加密和脱敏策略？

**设计决策：**
- 使用RSA256签名算法，定期轮换密钥
- 实施参数化查询和SQL白名单机制
- 对敏感字段进行AES加密存储

### 13.3 性能优化关键点

#### 13.3.1 SQL拦截性能优化

**优化策略：**
- 实施SQL解析结果缓存
- 采用异步处理减少响应时间
- 优化正则表达式匹配算法

#### 13.3.2 权限查询优化

**优化策略：**
- 实施多级缓存策略（本地缓存+Redis）
- 权限数据预加载和批量查询
- 使用读写分离减少数据库压力

### 13.4 风险控制措施

#### 13.4.1 技术风险

| 风险项 | 风险等级 | 影响 | 应对措施 |
|--------|----------|------|----------|
| SQL解析失败 | 中 | 查询无法执行 | 实施降级机制，记录失败日志 |
| 权限缓存失效 | 高 | 性能下降 | 多级缓存+熔断机制 |
| 代理服务故障 | 高 | 服务不可用 | 集群部署+健康检查 |
| JWT Token泄露 | 高 | 安全风险 | Token黑名单+短期有效期 |

#### 13.4.2 业务风险

| 风险项 | 风险等级 | 影响 | 应对措施 |
|--------|----------|------|----------|
| 权限配置错误 | 高 | 数据泄露 | 权限配置审核流程+测试验证 |
| 用户接受度低 | 中 | 推广困难 | 用户培训+渐进式迁移 |
| 数据质量问题 | 中 | 监控失效 | 数据质量检查+告警机制 |

### 13.5 技术债务管理

#### 13.5.1 当前技术债务

- **SQL解析器优化**：当前使用正则表达式解析，需要升级为AST解析
- **权限缓存策略**：需要实施更精细的缓存失效策略
- **监控指标完善**：需要增加业务级监控指标
- **错误处理机制**：需要完善异常处理和降级策略

#### 13.5.2 技术债务优先级

1. **P0（立即处理）**：权限缓存失效机制
2. **P1（下个版本）**：SQL解析器升级
3. **P2（后续版本）**：监控指标完善
4. **P3（长期规划）**：错误处理机制优化

### 13.6 后续演进规划

#### 13.6.1 短期规划（3个月内）

- 完善动态模板变量功能
- 优化SQL拦截性能
- 增强监控和告警能力
- 完善用户文档和培训材料

#### 13.6.2 中期规划（6-12个月）

- 支持更多数据源类型（ClickHouse、PostgreSQL等）
- 实现细粒度权限控制（字段级、行级）
- 增加智能权限推荐功能
- 完善移动端支持

#### 13.6.3 长期规划（1年以上）

- 实现AI驱动的异常检测
- 支持多云部署架构
- 集成更多第三方监控工具
- 构建完整的数据治理体系

## 14. 其它

### 14.1 评审检查清单

#### 14.1.1 架构评审检查点

- [ ] 代理层设计是否合理，是否存在单点故障风险
- [ ] 数据权限方案选择是否符合业务需求
- [ ] 缓存策略是否能保证数据一致性
- [ ] 安全设计是否满足企业安全要求
- [ ] 性能指标是否满足业务需求

#### 14.1.2 技术实现评审检查点

- [ ] JWT Token验证机制是否安全可靠
- [ ] SQL拦截逻辑是否正确，是否存在绕过风险
- [ ] 权限缓存机制是否高效，失效策略是否合理
- [ ] 错误处理和降级机制是否完善
- [ ] 监控和日志记录是否充分

#### 14.1.3 运维部署评审检查点

- [ ] 容器化部署配置是否正确
- [ ] 集群高可用方案是否可行
- [ ] 监控告警配置是否完整
- [ ] 备份和恢复策略是否可靠
- [ ] 运维文档是否完善

### 14.2 关键决策记录

| 决策项 | 决策结果 | 决策理由 | 决策时间 | 决策人 |
|--------|----------|----------|----------|--------|
| 数据权限方案 | 动态模板变量+SQL拦截混合 | 兼顾性能和用户体验 | 2024-01 | 架构组 |
| 缓存策略 | Redis分布式缓存 | 支持集群部署，性能优异 | 2024-01 | 架构组 |
| 认证方式 | JWT Token | 无状态，易于扩展 | 2024-01 | 安全组 |
| 部署方式 | Docker容器化 | 便于运维和扩展 | 2024-01 | 运维组 |
