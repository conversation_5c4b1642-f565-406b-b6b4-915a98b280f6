<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snbc.bbpf.system.db.common.mapper.DictTypeMapper" >
  <resultMap id="BaseResultMap" type="com.snbc.bbpf.system.db.common.entity.DictType" >
    <constructor >
      <idArg column="id" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="type_code" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="type_name" jdbcType="VARCHAR" javaType="java.lang.String" />
    </constructor>
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, type_code, type_name
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap"  >
    select
    <include refid="Base_Column_List" />
    from t_dict_type where 1=1
    <if test="typeCode != null and typeCode !=''" >
      AND type_code =#{typeCode,jdbcType=VARCHAR}
    </if>
      order by type_code

  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from t_dict_type
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByTypeCode" parameterType="java.lang.String" >
    delete from t_dict_type
    where type_code = #{typeCode,jdbcType=VARCHAR}
  </delete>

  <insert id="insert" parameterType="com.snbc.bbpf.system.db.common.entity.DictType" >
    insert into t_dict_type (id, type_code, type_name
      )
    values (#{id,jdbcType=VARCHAR}, #{typeCode,jdbcType=VARCHAR}, #{typeName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.snbc.bbpf.system.db.common.entity.DictType" >
    insert into t_dict_type
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="typeCode != null" >
        type_code,
      </if>
      <if test="typeName != null" >
        type_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="typeCode != null" >
        #{typeCode,jdbcType=VARCHAR},
      </if>
      <if test="typeName != null" >
        #{typeName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="quertAllDictType" resultMap="BaseResultMap" >
    SELECT
    <include refid="Base_Column_List" />
    FROM t_dict_type
  </select>

  <update id="updateByExampleSelective" parameterType="map" >
    update t_dict_type
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.typeCode != null" >
        type_code = #{record.typeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.typeName != null" >
        type_name = #{record.typeName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update t_dict_type
    set id = #{record.id,jdbcType=VARCHAR},
      type_code = #{record.typeCode,jdbcType=VARCHAR},
      type_name = #{record.typeName,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.snbc.bbpf.system.db.common.entity.DictType" >
    update t_dict_type
    <set >
      <if test="typeCode != null" >
        type_code = #{typeCode,jdbcType=VARCHAR},
      </if>
      <if test="typeName != null" >
        type_name = #{typeName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.snbc.bbpf.system.db.common.entity.DictType" >
    update t_dict_type
    set type_code = #{typeCode,jdbcType=VARCHAR},
      type_name = #{typeName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <select id="queryDictTypeByName" resultMap="BaseResultMap" parameterType="java.lang.String">
    select
    <include refid="Base_Column_List" />
    from t_dict_type
    where 1=1
    <if test="typeName != null and typeName !=''">
      AND  type_name like CONCAT('%',#{typeName,jdbcType=VARCHAR},'%')
    </if>
  </select>
</mapper>