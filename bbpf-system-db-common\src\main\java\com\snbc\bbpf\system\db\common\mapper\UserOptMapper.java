/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.mapper;

import com.snbc.bbpf.system.db.common.entity.UserOrg;
import com.snbc.bbpf.system.db.common.entity.UserRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName: UserOptMapper
 * @Description: 数据库交互，用户插入修改判断条件，批量插入数据库数据
 * @module: si-bbpf-system
 * @Author: wangsong
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Mapper
public interface UserOptMapper {
    /**
     * 判断用户ID是否存在
     */
    int selectOnlyUserId(@Param("userId") String userId);

    int selectOnlyUserName(@Param("userName") String userName,@Param("userId") String userId);
    /**
     * 判断是否唯一手机号
     */
    int selectOnlyPhone(@Param("phone") String phone, @Param("userId") String userId);
    int selectOnlyEmail(@Param("email") String email, @Param("userId") String userId);
    /**
     * 判断是否唯一员工编号
     */
    int selectOnlyJobNumber(@Param("jobNumber") String jobNumber, @Param("userId") String userId);
    /**
     * 通过工号获取用户编号
     */
    String selectUserIdByJobNumber(@Param("jobNumber") String jobNumber);
    /**
     * 判断是否唯一员手机号，根据工号确定
     */
    int selectOnlyPhoneByJobNumber(@Param("phone") String phone, @Param("jobNumber") String jobNumber);
    /**
     * 批量插入用户角色信息
     */
    void addUserRoles(@Param("listUserRole")List<UserRole> userRoleList);
    /**
     * 批量插入用户部门信息
     */
    void addUserOrgs(@Param("listUserOrg")List<UserOrg> userOrgList);
}
