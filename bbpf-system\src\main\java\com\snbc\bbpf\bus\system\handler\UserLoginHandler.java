/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.handler;

import com.snbc.bbpf.bus.system.config.LoginConfig;
import com.snbc.bbpf.bus.system.constans.CommonConstant;
import com.snbc.bbpf.bus.system.constans.NumberConstant;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.queue.BusLogQueue;
import com.snbc.bbpf.bus.system.utils.IpUtil;
import com.snbc.bbpf.bus.system.utils.JasyptEncryptUtil;
import com.snbc.bbpf.bus.system.utils.JwtTokenUtil;
import com.snbc.bbpf.bus.system.utils.RedisCheckCodeLoginFlag;
import com.snbc.bbpf.bus.system.dto.TokenPayload;
import com.snbc.bbpf.buslog.entity.BusLog;
import com.snbc.bbpf.component.captcha.model.common.NumConstant;
import com.snbc.bbpf.system.db.common.entity.User;
import com.snbc.bbpf.system.db.common.entity.login.ReturnUser;
import com.snbc.bbpf.system.db.common.mapper.UserLoginMapper;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;


/**
 * @ClassName: UserLoginService
 * @Description: 用户登录业务具体处理
 * @module: si-bbpf-system
 * @Author: liangjb
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Slf4j
@Component
public class UserLoginHandler {
    //Redis 缓存
    @Autowired
    private RedisCheckCodeLoginFlag redisCheckCodeLoginFlag;
    //JWT token
    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    //用户注册登录数据解些
    @Autowired
    private UserLoginMapper userLoginMapper;
    @Autowired
    private LoginConfig loginConfig;
    // 常量判断条件
    public ReturnUser verifLogin(String userId, String sysType) throws IOException {
        User user = userLoginMapper.selectUserByUserId(userId);
        // 验证user是否存在，为空不存在抛异常
        ErrorMessage.USERNAME_OR_PASSWORD_ERROR.assertNotNull(user);
        //查询用户锁定状态
        UserLoginFailHandler.queryUserLoginStatus(user);
        //密码正确就算登陆成功,清空登录失败次数
        UserLoginFailHandler.loginSuccessReset(user.getUserId());
        //验证用户冻结, 不等于1 为冻结，抛异常
        ErrorMessage.USE_FREEZE.assertEquals(NumberConstant.NO_ONE == user.getHasLock(), true);

        //验证多端登录
        ErrorMessage.MULTI_PORT_LOGIN.assertEquals(ifMultiportLogin(sysType, user), false);

        //进行类转换
        ReturnUser returnUser = ReturnUser.builder().hasLock(user.getHasLock()).userName(user.getUserName())
                .avatar(user.getAvatar()).userStatus(user.getUserStatus().toString()).jobNumber(user.getJobNumber()).build();
        //获取角色ID列表
        List<String> roleIds = userLoginMapper.selectUserRoleIdsByUserId(user.getUserId());
        String sessionId =UUID.randomUUID().toString();
		//获取TOKEN
        String token = jwtTokenUtil.generateToken(TokenPayload.builder()
                .userId(user.getUserId())
                .userName(user.getUserName())
                .password(user.getUserPwd())
                .roleIds(roleIds)
                .sysType(sysType)
				.sessionId(sessionId)
                .tenantId("")
                .build());
        returnUser.setToken(token);
        //校验初始密码及密码时效
        checkOriginPwdAndAging(returnUser, user);
        //读取ip 和url
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = servletRequestAttributes.getRequest();
        String userAgent = request.getHeader("user-agent");
        String ip = IpUtil.getIpAddress();

        //redis中保存登录状态 ,String ip,String logUrl
        redisCheckCodeLoginFlag.userLoginStatus(user, ip, sysType, token, sessionId);
        BusLogQueue.getInstance().addLog(BusLog.builder().logId(UUID.randomUUID().toString()).logType("userLogin")
                .logTarget("login").createTime(LocalDateTime.now()).enContent("[Account thirdScan] login succeeded")
                .zhContent("［三方登录］登录成功").ip(ip).userId(user.getUserId()).userName(user.getUserName())
                .requestSource(userAgent).build());
        MDC.put("userId", user.getUserId());
        return returnUser;
    }

    /**
     * 检查是否是默认密码,密码是否到期
     *
     * @param returnUser
     * @param user
     */
    private void checkOriginPwdAndAging(ReturnUser returnUser, User user) {
        //  验证是否初始化密码
        String defaultSecret = JasyptEncryptUtil.desencrypt(loginConfig.getDefaultPassword(), CommonConstant.KEY_IV);
        if (defaultSecret == null) {
            defaultSecret = CommonConstant.DEFAULT_P_NAME;
        }
        String secret = JasyptEncryptUtil.desencrypt(user.getUserPwd(), CommonConstant.KEY_IV);
        if (defaultSecret.equals(secret) && "default".equals(loginConfig.getGenerateType()) && user.getIsLdap().equals(NumConstant.ZERO)) {
            returnUser.setIsOriginPwd("true");
        } else {
            returnUser.setIsOriginPwd("false");
        }
        // 验证密码是否过期
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime beforeDay = now.minusDays(loginConfig.getPwdExpireDay());
        returnUser.setIsPwdExpired(null != user.getUpdatePwdTime() &&
                user.getUpdatePwdTime().isBefore(beforeDay));
    }


    /***
     * @Description: 验证是否允许多端登录
     * @Author: wangsong
     * @param :         loginUser
     * @param :         user
     * @param :         callResponse
     * @CreateDate: 2020/6/15 15:53
     * @UpdateUser: wangsong
     * @UpdateDate: 2020/6/15 15:53
     */
    private Boolean ifMultiportLogin(String systType, User user) {
        // 如果不允许多端登录，则检查用户状态
        if (!loginConfig.getMultiportLogin()) {
            //获取redis中的用户状态
            return redisCheckCodeLoginFlag.getUserStatus(systType, user.getUserId());
        }
        return false;
    }

}
