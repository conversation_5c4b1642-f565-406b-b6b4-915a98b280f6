/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.mapper;

import com.snbc.bbpf.system.db.common.entity.PermissionRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName:      PermissionRoleMapper.java
 * @Description:    角色、权限关联mapper
 * @module:
 * @Author:         wangsong
 * @date:           2021/5/19 18:16
 * copyright 2020 SNBC. All rights reserver
 */

@Mapper
public interface PermissionRoleMapper {

    void insertPermissionRole(List<PermissionRole> permissionRoleList);

    void deletePermissionByRoleId(String roleId);

    int selectCountByPermissionId(@Param("permissionId") String permissionId);

    void  deletePermissionByPermissionId(@Param("permissionId") String permissionId);
}
