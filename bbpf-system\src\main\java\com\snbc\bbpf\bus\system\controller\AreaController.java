package com.snbc.bbpf.bus.system.controller;

import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.bus.system.service.AreaService;
import com.snbc.bbpf.bus.system.utils.ResultUtil;
import com.snbc.bbpf.system.db.common.dto.AreaDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: AreaController
 * @Description: 获取省市区信息
 * @module: SI-bbpf-tenant-config
 * @Author: wangsong
 * @date: 2021/12/30
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@RestController
public class AreaController {

    @Autowired
    private AreaService areaService;

    /***
      * @Description:    根据地区code获取地区信息
      * @Author:         wangsong
      * @param :         areaCode
      * @CreateDate:     2021/12/30 17:27
      * @UpdateDate:     2021/12/30 17:27
      * @return :        com.snbc.bbpf.component.config.CommonResp
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @GetMapping("/console/area/getArea")
    public CommonResp getArea(@RequestParam(value = "areaCode",required = false) String areaCode) {
        List<AreaDto> areaList = areaService.getArea(areaCode);
        return CommonResp.builder().head(ResultUtil.success()).body(areaList).build();
    }

    /***
      * @Description:    获取所有地区信息
      * @Author:         wangsong
      * @param :         areaCode
      * @CreateDate:     2021/12/31 13:38
      * @UpdateDate:     2021/12/31 13:38
      * @return :        com.snbc.bbpf.component.config.CommonResp
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @GetMapping("/console/area/getAllArea")
    public CommonResp getAllArea() {
        List<Map<String,String>> areaAllList = areaService.getAllArea();
        return CommonResp.builder().head(ResultUtil.success()).body(areaAllList).build();
    }

}
