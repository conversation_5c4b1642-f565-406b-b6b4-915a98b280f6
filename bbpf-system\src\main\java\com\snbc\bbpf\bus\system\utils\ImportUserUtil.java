/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.utils;

import com.alibaba.excel.EasyExcelFactory;
import com.snbc.bbpf.bus.system.constans.CommonConstant;
import com.snbc.bbpf.bus.system.constans.NumberConstant;
import com.snbc.bbpf.system.db.common.dto.UserImportDto;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @ClassName: ExcelReadListener
 * @Description: 提供Excel导入用户相关的私有方法，主要用来处理sonar复杂度
 * @module: bbpf-bus-system
 * @Author: LJB
 * @date: 2021/5/20
 * copyright 2020 barm Inc. All rights reserver
 */
public class ImportUserUtil {
    private static final String TYPEEXCLE=".xlsx";

    private static final Logger LOGGER = LoggerFactory.getLogger(ImportUserUtil.class);
    /**
     * 验证 用户名， 手机号，邮箱(如果空不校验，不为空则校验合法性)
     * */
    public static boolean isResult(UserImportDto importDto, boolean result) {
        // 验证 用户名， 手机号，邮箱
        if (StringUtils.isBlank(match(importDto.getUserName(), "[\\s\\S]{1,20}"))) {
            importDto.setErrorMsg(importDto.getErrorMsg() + ",用户名最大长度为20位");
            result = false;
        }
        if (StringUtils.isBlank(match(importDto.getPhone(), "[0-9\\-]{1,16}"))) {
            importDto.setErrorMsg(importDto.getErrorMsg() + ",手机号长度为16位内的数字");
            result = false;
        }
        if (StringUtils.isNotBlank(importDto.getEmail())){
            if(importDto.getEmail().length()> NumberConstant.NO_SIXTY_FIVE) {
                importDto.setErrorMsg(importDto.getErrorMsg() + ",用户邮箱地址最多64位");
                result = false;
            }
            if(StringUtils.isBlank(match(importDto.getEmail(), CommonConstant.USER_EMAIL_MATCH))){
                importDto.setErrorMsg(importDto.getErrorMsg() + ",用户邮箱格式错误");
                result = false;
            }
        }
        return result;
    }

    /***
     * @Description:    对用户错误导入数据进行插入
     * @Author:         liangjb
     * @param :         List<UserImportDto>  错误导入用户信息
     * @param :         response  请求头
     * @CreateDate:     2021/6/8 10:24
     * @UpdateDate:     2021/6/8 10:24
     * @return :        boolean
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    public static void insertErrorImport(List<UserImportDto> errorList, HttpServletResponse response)
            throws IOException {
        // 将 errorList 数据保存到excle
        if (!CollectionUtils.isEmpty(errorList)){
            List<List<String>> excelData = new ArrayList<>();
            errorList.forEach(u ->{
                List<String> data = new ArrayList<>();
                data.add(u.getUserName());
                data.add(u.getJobNumber());
                data.add(u.getPhone());
                data.add(u.getEmail());
                data.add(u.getBelongOrgNames());
                data.add(u.getBelongRoleNames());
                data.add(u.getHasLockName());
                //增加一个判空处理
                if(StringUtils.isNotEmpty(u.getErrorMsg())) {
                    data.add(u.getErrorMsg().replace("null," , ""));
                }
                excelData.add(data);
            });
            String[] col = {"姓名", "工号", "手机号码", "邮箱", "部门", "角色", "状态","错误信息"};
            List<List<String>> list = new ArrayList<>();
            for (String c:col) {
                List<String> strings=new ArrayList<>();
                strings.add(c);
                list.add(strings);
            }
            LOGGER.info("Number of exported data items：{}",excelData.size());
            setResponse(response);
            try(OutputStream out = response.getOutputStream()) {
                EasyExcelFactory.write(out).head(list).sheet("用户信息").doWrite(excelData);
            } catch (Exception e) {
                LOGGER.error("Error user information is imported", e);
            }
        }
    }
    /**
     * @param temp
     * 字符串
     * @param reg
     * 正则表达式
     * <AUTHOR> 正则表达式 验证输入
     */
    public static String match(String temp, String reg) {
        Pattern pattern = null;
        Matcher matcher = null;
        if (StringUtils.isBlank(temp)) {
            return temp;
        } else {
            pattern = Pattern.compile(reg);
            matcher = pattern.matcher(temp.replace("/", ""));
            if (matcher.matches()) {
                return temp;
            }
        }
        return null;
    }
    /**
     * wjc1 2021-05-28
     * 设置导出时excle名称
     * @param response
     */
    public static void setResponse(HttpServletResponse response) throws UnsupportedEncodingException {
        response.setContentType("application/force-download");
        String name="导入出错的用户信息"+"_"+new SimpleDateFormat("yyyyMMddHHmmss").format(Date.from(Instant.now())) + TYPEEXCLE;
        String fileName= new String(name.getBytes(CommonConstant.CHARSET_UTF8),CommonConstant.CHARSET_ISO8859);
        response.addHeader("Content-Disposition", "attachment;filename="+fileName);
    }

}
