/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.exception;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * <p>Title: ErrorMessage</p>
 * <p>Description: 错误信息</p>
 * <AUTHOR>
 * @date 2019年9月3日
 */
@Getter
@AllArgsConstructor
public enum ErrorMessage implements BusinessExceptionAssert{
	      /*前两位：表示系统。根据公司规范系统管理为：99；
			第三位：表示出错的类型。
			0：参数错误；
			1：业务逻辑错误；
			2：外部（第三方）调用错误；
			3：数据解析错误；
			4：系统异常 （数据库、网络、配置、环境等）。
			第四位：为系统管理子模块。
			0：全系统；
			1：组织机构；
			2：登录；
			3：用户管理；
			4：角色管理；
			5：权限管理；
			6：消息管理；
			7：安全管理；
			8：用户组管理；
			9：扩展功能。
			后两位：表示各模块下的具体业务错误。
			*/

	//请求成功
	SUCCESS("000000", "操作成功"),
	//请求失败
	FAILED("111111", "操作失败"),

	/**
	 * 通用的系统异常定义
	 */
	REQUEST_TIME_OUT("994001", "请求超时"),
	DATASOURCE_CONNECT_FAILED("994002", "数据库连接失败"),
	SQL_EXECUTE_FAILED("994003", "sql执行失败"),
	SYS_NETWORK_EXCEPTION("994004", "网络异常"),
	SYSTEM_IO_ERROR("994005", "系统IO错误"),
	EXPORT_EXCEL_ERROR("994006", "导出excel错误"),

	/**
	 * 全系统
	 */
	//参数错误
	PARAM_NOTNULL("990001", "缺少{0}关键参数"),
	//数据解析错误
	INPUT_PARAM_ANALYSIS_ERROR("993001", "数据解析错误"),
	INPUT_PARAM_PAST_ERROR("993002", "格式转换错误"),

	/**
	 * 外部调用
	 */
	OTHER_SYSTEM_INVOK_FAILED("992001", "外部调用失败"),
	OTHER_SYSTEM_RESPONSE_NULL("992002", "返回内容为空"),
	OTHER_SYSTEM_ERROR("992003", "第三方系统错误"),
	OTHER_SYSTEM_CONNECT_TIMEOUT("992004", "连接超时"),

	/**
	 * 登录
	 */
	USER_WILL_BE_LOCKED("990201","您登录失败已达4次，再次失败账号将被冻结"),
	USER_LOCKED("990202","因超过登录失败次数账号已冻结，{0}小时后恢复正常"),
	PHONE_OR_CODE_ERROR("990203","手机号或验证码错误"),
	MULTI_PORT_LOGIN("990706", "账号已在其他端登录"),
	USERNAME_OR_PASSWORD_ERROR("991701", "用户名或密码错误"),
	USE_FREEZE("991702", "用户被冻结"),
	CHECK_CODE_ERROR("991705", "验证码错误"),

	PARAM_ID_EXIST("991703","配置ID不能存在"),
	PARAM_ID_NOT_EXIST("991704","配置Id错误"),
	PARAM_ID_NULL("991706","配置ID不能为空"),
	/**
	 * 配置管理
	 */
	PARAM_EXIST("991708","配置编码已存在"),
	PARAM_DELETE_FAIL("991707","配置信息删除失败"),

	PARAM_NAME_IS_NULL("999101","配置名称不能为空"),
	PARAM_CODE_IS_NULL("999102","配置编码不能为空"),
	PARAM_VALUE_IS_NULL("999103","配置值不能为空"),
	PARAM_TYPE_CODE_IS_NULL("999104","配置类型编码不能为空"),
	PARAM_TYPE_NAME_IS_NULL("999105","配置类型名称不能为空"),
	PARAM_CREATE_ORG_NAME_IS_NULL("999106","配置创建组织机构名称不能为空"),
	PARAM_ID_IS_NULL("999107","配置唯一标识不能为空"),

	/**
	 * 用户模块
	 */
	PWD_ERROR("990302","确认密码与新密码不一致"),
	PWD_STRENGTH_ERROR("990303","密码强度不符合,请重新输入"),
	USER_INVALID("990304","用户信息为空，请登录系统后操作!"),
	USER_ID_IS_NULL("990307","用户ID不能为空"),
	USER_ID_EXIST("990308","用户ID不能存在"),
	SMS_CODE_ERROR("990310","您输入的验证码错误或已失效，请重新输入"),
	SMS_CODE_INVALIDATION("990311","您输入的验证码次数过多，请重新发送验证码"),
	PHONE_BIND_USER("990312","此号码已绑定用户，请更换其他手机号"),
	WEAK_PASSWORD("990313","弱密码校验失败"),
	USER_EMAIL_INVALID("993303","用户邮箱地址格式不正确"),
	USER_EMAIL_LENGTH_INVALID("993304","用户邮箱地址最多64位"),

	USER_EXIST("991301","用户名已存在"),
	USER_NOT_EXIST("991303","用户ID错误"),
	USER_STATUS_INVALID("991305","入参用户状态不合法"),
	USER_FILE_INVALID("991306","每次必须且只能上传一个文件"),
	USER_FILE_TYPE_INVALID("991307","只能上传excel文件"),
	USER_JOB_NUMBER_EXIST("991308","用户工号已经存在"),
	USER_PHONE_EXIST("991309","用户手机号已经存在"),
	//20230606 wjc add 校验用户和邮箱重复
	USER_EMAIL_EXIST("991310","用户邮箱已经存在"),
	PHONE_UNREGISTERED("991312","手机号码未注册"),
	NO_PERMISSION_UPDATE("991313","不是超管不能修改用户手机号码"),
    OUT_OF_NUM("991314","导入数据量超过1000条"),
	/**
	 * 组织机构模块
	 */
	ORG_NOT_EXIST("991103","组织机构ID错误"),
	ADD_ORG_FAIL("991101","新增组织机构失败"),
	ORG_ID_IS_NULL("991102","组织机构ID不能为空"),
	EDIT_ORG_FAIL("991104","修改组织机构失败"),
	QUERY_ORG_FAIL("991105","查询组织机构失败"),
	DEL_ORG_FAIL("991106","删除组织机构失败"),
	ORG_HAS_USER_FAIL("991107","{0}组织机构下有用户，无法删除"),
	QUERY_ORG_TREE_FAIL("991108","获取组织机构树失败"),
	ORG_SORT_FAIL("991109","组织机构拖拽失败"),
	ORG_NAME_EXIST("991110","组织机构名称重复"),
	ORG_UNBIND_USER_ERROR("991111","用户组织机构关系解绑失败"),
	NO_ORG_PERMISSION("991113","没有操作权限"),
	ORG_HAS_ROLE_FAIL("991115","{0}组织机构下有角色，无法删除"),

	/**
	 * 角色模块
	 */
	ROLE_NAME_ONLY_EXIST("991401","角色名称已经存在"),
	ROLE_BOUND_USER_DEL("991402","先解绑角色关联的用户再删除"),
	SYS_ROLE_CANNOT_DEL("991404","系统角色无法删除"),
	SUPER_ROLE_CANNOT_UPDATE("991405","超级管理员不能被修改"),
	ROLE_CANNOT_RESET("991406","该角色无法重置"),
	/**
	 * 日志模块
	 */
	LOG_EXPORT_TIMES_ERROR("991905","导出日志的时间差请小于%s天"),

	/**
	 * 顶部导航
	 */
	MAXIMUM_QUANTITY_LIMIT("991501","最多只能添加10个顶部菜单"),
	NAVIGATION_CODE_EXIST("991503","导航菜单编码已存在"),
	/**
	 * 组织结构人员导入
	 */
	ORG_BIND_USER_ERROR("992001","人员绑定部门失败"),
	PERMISSION_BIND_USER_ERROR("992002","人员绑定权限失败"),

	/**
	 * 权限管理
	 */
	PERMISSION_UNBIND_ROLE_FIRST("990502","先解绑角色权限关联再删除"),
	PERMISSION_DATA_AUTH("990503","请先删除数据权限配置后再删除"),
	PERMISSION_CHILD("990504","请删除子节点后再删除"),

	/**
	 * 地域管理
	 */
	REGION_CODE_EXIST("991801","地域编号{0}已存在"),
	REGION_CODE_IS_NONE("991802","地域编号{0}不存在"),
	REGION_CODE_IS_ZERO("991803","地域编号不能为0"),

	/**
	 * 字典异常定义
	 */
	DICT_VALUE_EXIST("991901", "字典值名称或字典值已存在"),
	DICT_CODE_EXIST("991903", "字典类型编号{0}已存在"),
	DICT_CODE_IS_NONE("991905", "字典类型编号{0}不存在"),
	DELETE_COUNT("991999","删除{0}条数据"),
	/**
	 * 视图
	 */
	VIEW_EXIST("992001", "视图名称已存在"),

	/**
	 * 以下是为三方登录相关业务错误提示
	 */
	USER_BIND_NOT_SCAN("900001","用户绑定未扫码"),
	USER_BIND_TIMEOUT("900002","用户绑定已超时"),
	USER_BIND_ENABLE_BIND("900003","用户已经绑定其它账号"),
	USER_HAS_UNBIND("900016","该用户绑定已解除"),
	USER_LOGIN_NOT_BIND("900011","用户OPENID未绑定"),
	USER_LOGIN_DISABLE("900012","该用户已禁用"),
	USER_LOGIN_TIMEOUT("900013","二维码超时"),
	USER_LOGIN_NOT_SCAN("900014","用户未扫码"),
	USER_LOGIN_NOT_HANDLE("900015","未识别该登录类型"),
	USER_BIND_TIMES_OUT("900017","您短信验证失败已达4次，再次失败账号将被冻结"),
	USER_BIND_LOCK("900018","因超过短信验证失败次数账号已被冻结，{0}小时候恢复正常")
	;
	/**
	 * 编码
	 */
	private final String code;
	/**
	 * 消息
	 */
	private final String message;
}
