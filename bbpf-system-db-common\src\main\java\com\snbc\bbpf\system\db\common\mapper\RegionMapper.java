/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.mapper;

import com.snbc.bbpf.system.db.common.entity.Region;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */

/**
 * @ClassName: RegionMapper
 * RegionMapper
 * @module: si-bbpf-system
 * @Author: wjc1 增加的注释
 * @date:   2021/5/25 16:56
 */
@Mapper
public interface RegionMapper {


    int deleteByPrimaryKey(String id);

    int insert(Region region);

    int insertSelective(Region region);

    List<Region> selectByExample(String regionCode,String parentCode );

    Region selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(Region region);

    int updateByPrimaryKey(Region region);

    List<Map<String,String>> selectRegionHasChild(@Param("regionIdList") List<String> regionIdList);
}
