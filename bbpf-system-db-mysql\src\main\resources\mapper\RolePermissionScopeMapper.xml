<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snbc.bbpf.system.db.common.mapper.RolePermissionScopeMapper">
    <resultMap id="BaseResultMap" type="com.snbc.bbpf.system.db.common.entity.RolePermissionScope">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="role_id" jdbcType="VARCHAR" property="roleId"/>
        <result column="permission_id" jdbcType="VARCHAR" property="permissionId"/>
        <result column="scope_id" jdbcType="VARCHAR" property="scopeId"/>
    </resultMap>
    <insert id="insertRolePermissionScope" parameterType="java.util.List">
        insert into t_role_permission_scope (id,permission_id, role_id,scope_id)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix=" (" suffix=")" suffixOverrides=",">
                #{item.id,jdbcType=VARCHAR},
                #{item.permissionId,jdbcType=VARCHAR},
                #{item.roleId,jdbcType=VARCHAR},
                #{item.scopeId,jdbcType=VARCHAR},
            </trim>
        </foreach>
    </insert>

    <delete id="deleteSocpeByRoleId" parameterType="java.lang.String">
          delete from t_role_permission_scope where role_id = #{roleId,jdbcType=VARCHAR}
  </delete>
</mapper>