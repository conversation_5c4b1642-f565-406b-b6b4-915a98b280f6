package com.snbc.bbpf.bus.system.controller;


import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.bus.system.service.ThirdLoginService;
import com.snbc.bbpf.bus.system.utils.ResultUtil;
import com.snbc.bbpf.buslog.annotations.Buslog;
import com.snbc.bbpf.system.db.common.dto.CheckSmsCodeDto;
import com.snbc.bbpf.system.db.common.entity.login.ReturnUser;
import com.snbc.bbpf.system.db.common.vo.QrcodeVo;
import com.snbc.bbpf.system.db.common.vo.ThirdUnbind;
import com.snbc.bbpf.system.db.common.vo.UserBindVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <p>
 *  负责三方扫码绑定接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-04
 */
@RestController
@Validated
@RequestMapping("/console/third")
public class ThirdLoginController {
    /**
     * 用户绑定登录实现
     */
    @Autowired
    private ThirdLoginService thirdLoginService;
    /**
     * 获得用户绑定二维码状态
     * @param bindId
     * @return
     * @throws Exception
     */
    @GetMapping(path = "/getUserBindQRcodeState")
    public CommonResp getUserBindQRcodeState(@NotBlank(message = "请求序号不能为空") @RequestParam String bindId) throws Exception {
        thirdLoginService.getUserBindQRcodeState(bindId);
        return CommonResp.builder().head(ResultUtil.success()).build();
    }
    /**
     * 检验手机验证码获得用户绑定二维码
     * @param checkSmsCodeDto
     * @return
     * @throws Exception
     */
    @PostMapping(path = "/checkSmsCode")
    public CommonResp<QrcodeVo> checkSmsCode(@Valid @RequestBody CheckSmsCodeDto checkSmsCodeDto) throws Exception {
        return CommonResp.<QrcodeVo>builder().head(ResultUtil.success()).
                body(thirdLoginService.checkSmsCode(checkSmsCodeDto)).build();
    }
    /**
     * 解除用户绑定
     * @param bindType
     * @return
     * @throws Exception
     */
    @DeleteMapping(path = "/delUserThirdBind")
    @Buslog(opration = "unbindThreeLogin", zh = "解除[${bindType}]用户绑定", en = "Unbind user [${bindType}]",
            target = "login", type = ThirdUnbind.class)
    public CommonResp<String> delUserThirdBind(String bindType) throws Exception {
        return CommonResp.<String>builder().head(ResultUtil.success()).
                body(thirdLoginService.delUserThirdBind(bindType)).build();
    }


    /**
     * 获得用户登录二维码状态
     * @param bindId
     * @return
     * @throws Exception
     */
    @GetMapping(path = "/getUserLoginQRcodeState")
    public CommonResp<ReturnUser> getUserLoginQRcodeState(@NotBlank(message = "请求序号不能为空") @RequestParam String bindId) throws Exception {
        return CommonResp.<ReturnUser>builder().head(ResultUtil.success()).
                body(thirdLoginService.getUserLoginQRcodeState(bindId)).build();
    }

    /**
     * 获得用户登录二维码
     * @param thirdType
     * @return
     * @throws Exception
     */
    @GetMapping(path = "/getUserLoginQRcode")
    public CommonResp<QrcodeVo> getUserLoginQRcode(@RequestParam String thirdType) throws Exception {
        return CommonResp.<QrcodeVo>builder().head(ResultUtil.success()).
                body(thirdLoginService.getUserLoginQRcode(thirdType)).build();
    }

    /**
     * 获得用户登录绑定详情
     * @return  bbpf_product__bbpfboss_FD000001
     * @throws Exception   "available"
     */
    @GetMapping(path = "/getUserBindInfo")
    public CommonResp<List<UserBindVo>> getUserBindInfo() throws Exception {
        return CommonResp.<List<UserBindVo>>builder().head(ResultUtil.success()).
                body(thirdLoginService.getUserBindInfo()).build();
    }

}
