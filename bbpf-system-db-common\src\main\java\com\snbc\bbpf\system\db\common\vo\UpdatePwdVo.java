/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @ClassName: UpdatePwdVo
 * @Description: 修改密码的参数
 * @module: si-bbpf-system
 * @Author: wangsong
 * @date: 2021/5/18
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Data
public class UpdatePwdVo {
    @NotBlank(message = "确认密码不能为空")
    //二次确认密码
    private String confirmPwd;
    @NotBlank(message = "新密码不能为空")
    //新密码
    private String newPwd;
    @NotBlank(message = "验证码不能为空")
    //验证码
    private String smsCaptcha;

    private String userId;

}
