package com.snbc.bbpf.bus.system.strategy.impl;

import com.snbc.bbpf.bus.system.constans.CommonConstant;
import com.snbc.bbpf.bus.system.strategy.LoginStrategy;
import com.snbc.bbpf.bus.system.utils.JasyptEncryptUtil;
import com.snbc.bbpf.commons.crypt.Base64Utils;
import com.snbc.bbpf.commons.crypt.DesUtils;
import com.snbc.bbpf.commons.crypt.Padding;
import com.snbc.bbpf.commons.crypt.RSAUtils;
import com.snbc.bbpf.system.db.common.entity.User;
import com.snbc.bbpf.system.db.common.entity.login.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @ClassName: AccountLoginStrategy
 * @Description: 账密登录策略实现
 * @module: SI-bbpf-system
 * @Author: wangsong
 * @date: 2023/5/17
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Slf4j
@Component
public class AccountLoginStrategy implements LoginStrategy {
    /**
     * 私钥
     */
    @Value("${bbpf.system.RSA.privateKey}")
    private String privateKey;

    /***
      * @Description:    校验账号登录
      * @Author:         WangSong
      * @param :         loginUser
      * @param :         user
      * @return:         boolean
      * @CreateDate:     2023/5/17 17:26
      * @UpdateDate:     2023/5/17 17:26
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
    */
    @Override
    public boolean verify(LoginUser loginUser, User user) {
        Boolean result = false;
        try {
            // 先进行RSA解密 测试阶段可放开
            String desPwd = RSAUtils.rsaDecrypt(loginUser.getUserPwd(), privateKey);
            // 进行3DES解密
            if (!StringUtils.isBlank(desPwd)) {
                byte[] password = DesUtils.decryptCbc(CommonConstant.KEY.getBytes()
                        , CommonConstant.DES_KEY_IV.getBytes(), Base64Utils.decode(desPwd), Padding.PKCS5PADDING);
                //将数据库密码进行加盐解密
                result = new String(password, CommonConstant.CHARSET_UTF8).
                        equals(JasyptEncryptUtil.desencrypt(user.getUserPwd(), CommonConstant.KEY_IV));
            }
        } catch (Exception ex) {
            log.error("The password verification is abnormal. Procedure：", ex);
            result = false;
        }
        return result;
    }
}
