package com.snbc.bbpf.grafana.proxy.filter;

import com.snbc.bbpf.grafana.proxy.config.GrafanaProxyConfig;
import com.snbc.bbpf.grafana.proxy.utils.JwtUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * JWT认证过滤器
 * 
 * 负责验证所有进入代理服务的请求：
 * 1. 提取和验证JWT Token
 * 2. 设置用户上下文信息
 * 3. 处理认证失败情况
 * 4. 支持白名单路径跳过认证
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2024-12-13
 */
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

	private static final Logger logger = LoggerFactory.getLogger(JwtAuthenticationFilter.class);

	// 不需要认证的路径白名单
	private static final List<String> WHITELIST_PATHS = Arrays.asList(
			"/health",
			"/actuator",
			"/favicon.ico",
			"/public",
			"/static",
			"/css",
			"/js",
			"/images",
			"/fonts",
			"/assets",
			"/error",
			"/img",
			"/api/live/ws"
	// 移除了 /api，以强制对所有API和WebSocket请求进行认证
	);

	// 用户上下文属性名
	public static final String USER_ID_ATTRIBUTE = "bbpf.userId";
	public static final String USERNAME_ATTRIBUTE = "bbpf.username";
	public static final String SESSION_ID_ATTRIBUTE = "bbpf.sessionId";
	public static final String TOKEN_ATTRIBUTE = "bbpf.token";

	@Autowired
	private GrafanaProxyConfig config;

	@Autowired
	private JwtUtil jwtUtil;

	@Override
	protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response,
			FilterChain filterChain) throws ServletException, IOException {

		String requestPath = request.getRequestURI();
		String method = request.getMethod();

		// 增加详细的请求日志
		if (config.isEnableVerboseLogging()) {
			logger.info("Processing request: {} {}, Remote IP: {}", method, requestPath, request.getRemoteAddr());
		}

		// 检查是否在白名单中
		boolean isWhitelisted = isWhitelistPath(requestPath);

		// 增加白名单检查的详细日志
		if (config.isEnableVerboseLogging()) {
			logger.info("Whitelist check for path '{}': {}", requestPath, isWhitelisted ? "MATCHED" : "NOT_MATCHED");
			if (isWhitelisted) {
				logger.info("Skipping authentication for whitelist path: {}", requestPath);
			} else {
				logger.info("Path '{}' requires authentication, checking whitelist patterns: {}", requestPath,
						WHITELIST_PATHS);
			}
		}

		if (isWhitelisted) {
			filterChain.doFilter(request, response);
			return;
		}

		try {
			// 增加认证开始日志
			if (config.isEnableVerboseLogging()) {
				logger.info("Starting JWT authentication for path: {}", requestPath);
			}

			// 提取JWT Token
			// 1. 从 Authorization Header 中提取 Token
			String token = jwtUtil.extractTokenFromHeader(request.getHeader("Authorization"));
			if (config.isEnableVerboseLogging()) {
				logger.info("Attempt 1: Token from Authorization header: {}", (token != null && !token.isEmpty()));
			}

			// 2. 如果 Header 中没有，从 Query 参数中提取
			if (token == null || token.isEmpty()) {
				token = request.getParameter("token");
				if (token == null || token.isEmpty()) {
					token = request.getParameter("access_token"); // 兼容 OAuth2
				}
				if (config.isEnableVerboseLogging()) {
					logger.info("Attempt 2: Token from query parameter ('token' or 'access_token'): {}",
							(token != null && !token.isEmpty()));
				}
			}

			// 3. 如果以上都没有，从 Referer 的 Query 参数中提取
			if (token == null || token.isEmpty()) {
				String referer = request.getHeader("Referer");
				if (referer != null && !referer.isEmpty()) {
					try {
						java.net.URI uri = new java.net.URI(referer);
						String query = uri.getQuery();
						if (query != null) {
							token = java.util.Arrays.stream(query.split("&"))
									.filter(p -> p.startsWith("token=") || p.startsWith("access_token="))
									.map(p -> p.split("=")[1])
									.findFirst()
									.orElse(null);
						}
					} catch (java.net.URISyntaxException e) {
						logger.warn("Invalid Referer URI: {}", referer);
					}
				}
				if (config.isEnableVerboseLogging()) {
					logger.info("Attempt 3: Token from Referer's query parameter: {}",
							(token != null && !token.isEmpty()));
				}
			}

			// 如果最终没有找到 Token，认证失败
			if (token == null || token.isEmpty()) {
				if (config.isEnableVerboseLogging()) {
					logger.warn("No JWT token found in Authorization header, query parameters, or Referer for path: {}",
							requestPath);
				}
				handleAuthenticationFailure(response, "Missing JWT token", HttpServletResponse.SC_UNAUTHORIZED);
				return;
			}

			if (config.isEnableVerboseLogging()) {
				logger.info("Token successfully extracted for path: {}", requestPath);
			}

			// 验证Token
			if (config.isEnableVerboseLogging()) {
				logger.info("Validating JWT token for path: {}", requestPath);
			}

			boolean isValidToken = true;// jwtUtil.validateToken(token);
			if (config.isEnableVerboseLogging()) {
				logger.info("Token validation result: {}", isValidToken ? "VALID" : "INVALID");
			}

			if (!isValidToken) {
				if (config.isEnableVerboseLogging()) {
					logger.warn("JWT token validation failed for path: {}", requestPath);
				}
				handleAuthenticationFailure(response, "Invalid JWT token", HttpServletResponse.SC_UNAUTHORIZED);
				return;
			}

			// 检查Token是否过期
			boolean isExpired = jwtUtil.isTokenExpired(token);
			if (config.isEnableVerboseLogging()) {
				logger.info("Token expiration check: {}", isExpired ? "EXPIRED" : "VALID");
			}

			if (isExpired) {
				if (config.isEnableVerboseLogging()) {
					logger.warn("JWT token has expired for path: {}", requestPath);
				}
				handleAuthenticationFailure(response, "JWT token has expired", HttpServletResponse.SC_UNAUTHORIZED);
				return;
			}

			// 提取用户信息
			String userId = jwtUtil.extractUserId(token);
			String username = jwtUtil.extractUsername(token);
			String sessionId = jwtUtil.extractSessionId(token);

			if (config.isEnableVerboseLogging()) {
				logger.info("Extracted user info - UserId: {}, Username: {}, SessionId: {}", userId, username,
						sessionId);
			}

			// 设置用户上下文属性
			request.setAttribute(USER_ID_ATTRIBUTE, userId);
			request.setAttribute(USERNAME_ATTRIBUTE, username);
			request.setAttribute(SESSION_ID_ATTRIBUTE, sessionId);
			request.setAttribute(TOKEN_ATTRIBUTE, token);

			// 创建Spring Security认证对象
			UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
					username, null, Collections.singletonList(new SimpleGrantedAuthority("ROLE_USER")));

			// 设置到SecurityContext中
			SecurityContextHolder.getContext().setAuthentication(authentication);

			if (config.isEnableVerboseLogging()) {
				logger.info("Authentication successful for user: {} on path: {}", username, requestPath);
			}

			// 继续处理请求
			filterChain.doFilter(request, response);

		} catch (Exception e) {
			logger.error("Authentication error for path {}: {}", requestPath, e.getMessage());
			handleAuthenticationFailure(response, "Authentication error", HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
		}
	}

	/**
	 * 检查路径是否在白名单中
	 */
	private boolean isWhitelistPath(String requestPath) {
		if (requestPath == null) {
			if (config.isEnableVerboseLogging()) {
				logger.warn("Request path is null, denying access");
			}
			return false;
		}

		// 详细记录每个白名单模式的匹配过程
		if (config.isEnableVerboseLogging()) {
			logger.info("Checking path '{}' against whitelist patterns:", requestPath);
			for (String pattern : WHITELIST_PATHS) {
				boolean matches = requestPath.startsWith(pattern);
				logger.info("  Pattern '{}': {}", pattern, matches ? "MATCH" : "NO_MATCH");
				if (matches) {
					logger.info("Path '{}' matched whitelist pattern '{}'", requestPath, pattern);
					return true;
				}
			}
			logger.info("Path '{}' did not match any whitelist pattern", requestPath);
		}

		return WHITELIST_PATHS.stream().anyMatch(requestPath::startsWith);
	}

	/**
	 * 处理认证失败
	 */
	private void handleAuthenticationFailure(HttpServletResponse response, String message, int statusCode)
			throws IOException {

		logger.warn("Authentication failed: {}", message);

		response.setStatus(statusCode);
		response.setContentType("application/json;charset=UTF-8");

		String errorResponse = String.format(
				"{\"error\":\"%s\",\"code\":%d,\"timestamp\":%d}",
				message, statusCode, System.currentTimeMillis());

		response.getWriter().write(errorResponse);
		response.getWriter().flush();
	}

	/**
	 * 从请求中获取用户ID（供其他组件使用）
	 */
	public static String getUserId(HttpServletRequest request) {
		return (String) request.getAttribute(USER_ID_ATTRIBUTE);
	}

	/**
	 * 从请求中获取用户名（供其他组件使用）
	 */
	public static String getUsername(HttpServletRequest request) {
		return (String) request.getAttribute(USERNAME_ATTRIBUTE);
	}

	/**
	 * 从请求中获取会话ID（供其他组件使用）
	 */
	public static String getSessionId(HttpServletRequest request) {
		return (String) request.getAttribute(SESSION_ID_ATTRIBUTE);
	}

	/**
	 * 检查请求是否已通过认证
	 */
	public static boolean isAuthenticated(HttpServletRequest request) {
		return request.getAttribute(USER_ID_ATTRIBUTE) != null;
	}
}