/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service.impl;

import com.snbc.bbpf.bus.system.config.LDAPConfig;
import com.snbc.bbpf.bus.system.constans.NumberConstant;
import com.snbc.bbpf.bus.system.service.LdapService;
import com.snbc.bbpf.bus.system.utils.StringUtil;
import com.snbc.bbpf.system.db.common.dto.LdapUserDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.naming.Context;
import javax.naming.NamingEnumeration;
import javax.naming.NamingException;
import javax.naming.directory.SearchControls;
import javax.naming.directory.SearchResult;
import javax.naming.ldap.InitialLdapContext;
import javax.naming.ldap.LdapContext;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;

@Service
@Slf4j
public class LdapServiceImpl implements LdapService {

    @Autowired
    private LDAPConfig ldapConfig;

    /**
     * @return 登录是否成功
     * <AUTHOR>
     * @date 2023/4/28
     * @since 1.0.0
     */
    @Override
    public boolean adLogin(String userName, String passWord) {
        // 判断用户是否为空
        if (null == userName || "".equals(userName)) {
            return false;
        }
        // 判断密码是否为空
        if (null == passWord || "".equals(passWord)) {
            return false;
        }
        LdapContext ctx = null;
        boolean valide = false;
        try {
            /**** 定义LDAP的基本连接信息 ******/
            Properties env = new Properties();
            env.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
            env.put(Context.PROVIDER_URL, ldapConfig.getLdapUrl() + "/" + ldapConfig.getBaseDn());
            env.put(Context.SECURITY_AUTHENTICATION, "simple");
            env.put(Context.SECURITY_PRINCIPAL, userName);
            env.put(Context.SECURITY_CREDENTIALS, passWord);

            // 链接ldap
            ctx = new InitialLdapContext(env, null);
            // 初始化ctx成功说明用户和密码正确
            valide = true;
            ctx.close();
        } catch (javax.naming.AuthenticationException e) {
            //这里的sonar错误不要改，设计如此
            log.error("Authentication faild: ", e);
        } catch (Exception e) {
            log.error("Something wrong while authenticating: ", e);
        }
        return valide;
    }

    /**
     * @return BCD编码字节数组
     * <AUTHOR>
     * @date 2023/4/28
     * @since 1.0.0
     */
    @Override
    public List<LdapUserDto> getUserList() {
        List<LdapUserDto> ldapUserDtoList = new ArrayList<>();
        try {
            //构建环境
            SearchControls searchCtls = new SearchControls();
            searchCtls.setSearchScope(SearchControls.SUBTREE_SCOPE);
            String searchFilter = ldapConfig.getFilter();
            String searchBase = ldapConfig.getBaseDn();
            LdapContext ctx = getContext();
            NamingEnumeration<SearchResult> answer = ctx.search(searchBase, searchFilter, searchCtls);
            //循环解析数据
            while (answer.hasMoreElements()) {
                extracted(ldapUserDtoList, answer);
            }
            ctx.close();
        } catch (Exception e) {
            log.warn("Problem searching directory: ", e);
        }
        return ldapUserDtoList;
    }

    /**
     * 提取方法
     * @param ldapUserDtoList
     * @param answer
     */
    private void extracted(List<LdapUserDto> ldapUserDtoList, NamingEnumeration<SearchResult> answer) {
        try {
            SearchResult sr = answer.next();
            LdapUserDto userDto = new LdapUserDto();
            //username截取逗号前数据，去除OU=部门
            String userName = sr.getAttributes().get("displayName").get().toString();
            int commaIndex = userName.indexOf(',');
            if (commaIndex != -1) {
                userName = userName.substring(NumberConstant.NO_ZERO, commaIndex);
            }
            if ("open".equals(ldapConfig.getType())) {
                String userDn = sr.getNameInNamespace();
                //cn=yangweipeng,ou=北分公司,ou=新北洋,dc=byintra,dc=com
                int c = StringUtil.indexOf(userDn, ",");
                userDto.setJobNumber(StringUtil.substringAfter(StringUtil.substringBefore(userDn, ","), "="));
                userDto.setUserName(userName);
                userDto.setPhone(sr.getAttributes().get("telephoneNumber").get().toString());
                userDto.setUserDN(userDn.substring(c + 1));
                userDto.setEmail(sr.getAttributes().get("mail").get().toString());
            } else {
                userDto.setJobNumber(sr.getAttributes().get("sAMAccountName").get().toString());
                userDto.setUserName(userName);
                userDto.setUserDN(sr.getNameInNamespace());
                userDto.setPhone(sr.getAttributes().get("mobile").get().toString());
                userDto.setEmail(sr.getAttributes().get("mail").get().toString());
            }
            List<String> orgList = new ArrayList<>();
            Arrays.stream(sr.getNameInNamespace().split(",")).forEach(
                    item -> {
                        if (item.contains("ou") || item.contains("OU")) {
                            orgList.add(StringUtil.substringAfter(item, "="));
                        }
                    });
            StringBuilder orgName = new StringBuilder();
            for (int i = orgList.size() - 1; i >= 0; i--) {
                orgName.append(orgList.get(i) + "/");
            }
            String orgNameStr = StringUtil.trimEnd(orgName.toString(), "/");
            userDto.setOrgNames(orgNameStr);
            ldapUserDtoList.add(userDto);
        } catch (Exception ex) {
            log.warn("parse data error: ", ex);
        }
    }

    private LdapContext getContext() throws NamingException {
        Properties env = getLdapProperties();
        return new InitialLdapContext(env, null);
    }

    /***
     * @Description: 获取ldap配置
     * @Author: WangSong
     * @return: java.util.Properties
     * @CreateDate: 2023/5/31 10:01
     * @UpdateDate: 2023/5/31 10:01
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    private Properties getLdapProperties() {
        Properties env = new Properties();
        env.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
        env.put(Context.SECURITY_AUTHENTICATION, "simple");//"none","simple","strong"
        env.put(Context.SECURITY_PRINCIPAL, ldapConfig.getAdminUser());
        env.put(Context.SECURITY_CREDENTIALS, ldapConfig.getAdminPass());
        env.put(Context.PROVIDER_URL, ldapConfig.getLdapUrl());
        return env;
    }

}
