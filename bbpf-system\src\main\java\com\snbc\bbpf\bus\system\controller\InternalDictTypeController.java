/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.controller;

import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.bus.system.service.DictService;
import com.snbc.bbpf.bus.system.utils.ResultUtil;
import com.snbc.bbpf.system.db.common.entity.DictValue;
import com.snbc.bbpf.system.db.common.vo.DictValueVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @ClassName: InternalDictTypeController
 * @Description: 提供内部接口调用
 * @module: bbpf-bus-system
 * @Author: liuyi
 * @date: 2021/5/31
 * copyright 2020 barm Inc. All rights reserver
 */

@Slf4j
@RestController
@RequestMapping("/service/v1/dict")
public class InternalDictTypeController {

    @Autowired
    private DictService dictService;
    /**
     * @description: 查询字典值...........................................
     * @param  valueIds 字典值ID
     * @return: CommonResp<List<DictValue>>
     * @author: liuyi
     * @time: 2021/6/7 16:13
     */
    @PostMapping(value = "/getDictValue")
    public CommonResp<List<DictValue>> getDictTypeList(@RequestBody List<String> valueIds) {
        CommonResp<List<DictValue>> commonResp = new CommonResp<>();
        try {
            //通过SERVER拿去对应的字典列表
            List<DictValue> dictValueList = dictService.selectByValueIds(valueIds);
            commonResp.setHead(ResultUtil.success());
            commonResp.setBody(dictValueList);
        } catch (Exception e) {
            commonResp.setHead(ResultUtil.error("异常", e.getMessage()));
            log.error("get dict value error", e);
        }
        //返回结果
        return commonResp;
    }
    @PostMapping(value = "/getDictValueList")
    public CommonResp<List<DictValueVo>> getDictValueList(@RequestBody String typeCode) {
        CommonResp<List<DictValueVo>> commonResp = new CommonResp<>();
        try {
            //通过SERVER拿去对应的字典列表
            List<DictValueVo> dictValueList = dictService.getDictValueList(typeCode);
            commonResp.setHead(ResultUtil.success());
            commonResp.setBody(dictValueList);
        } catch (Exception e) {
            commonResp.setHead(ResultUtil.error("异常", e.getMessage()));
            log.error("get dict value list error", e);
        }
        //返回结果
        return commonResp;
    }
    /**
     * @description: 更新字典类型...........................................
     * @param typeCode
     * @param valueCode
     * @return: void
     * @author: liuyi
     * @time: 2021/6/7 16:13
     */
    @GetMapping(value = "/getDictByTypeValueCode")
    public CommonResp<DictValueVo> getAllDictValue(@RequestParam("typeCode") String typeCode,
                                                             @RequestParam("valueCode") String valueCode) {
        CommonResp<DictValueVo> commonResp = new CommonResp<>();
        try {
            DictValueVo dictValue = dictService.getDictByTypeValueCode(typeCode, valueCode);
            commonResp.setHead(ResultUtil.success());
            commonResp.setBody(dictValue);
        } catch (Exception e) {
            commonResp.setHead(ResultUtil.error("异常", e.getMessage()));
            log.error("get dict by typecode and valuecode error", e);
        }
        return commonResp;
    }
}
