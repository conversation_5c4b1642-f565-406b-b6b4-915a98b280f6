package com.snbc.bbpf.grafana.proxy.service;

import com.snbc.bbpf.grafana.proxy.client.GrafanaApiClient;
import com.snbc.bbpf.grafana.proxy.dto.UserPermissionDto;
import com.snbc.bbpf.grafana.proxy.service.PermissionService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GrafanaVariableService测试类
 * 
 * 测试权限变量生成和管理功能
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2024-12-13
 */
@ExtendWith(MockitoExtension.class)
class GrafanaVariableServiceTest {

    private static final Logger logger = LoggerFactory.getLogger(GrafanaVariableServiceTest.class);

    @Mock
    private GrafanaApiClient grafanaApiClient;

    @Mock
    private PermissionService permissionService;

    @InjectMocks
    private GrafanaVariableService grafanaVariableService;

    private UserPermissionDto testUserPermission;

    @BeforeEach
    void setUp() {
        // 创建测试用户权限
        testUserPermission = new UserPermissionDto();
        testUserPermission.setUserId("testUser");
        testUserPermission.setUsername("Test User");
        testUserPermission.setAccessibleDashboardIds(Arrays.asList("dash1", "dash2", "dash3"));
        testUserPermission.setAccessibleDataSourceIds(Arrays.asList("ds1", "ds2"));
        testUserPermission.setAccessibleFolderIds(Arrays.asList("folder1", "folder2"));
        testUserPermission.setPermissions(new HashSet<>(Arrays.asList("read", "write")));
        
        // 设置数据过滤条件
        testUserPermission.setDataFilter("department_id = '5' AND role = 'manager'");
        
        // 设置原始权限数据
        Map<String, Object> rawData = new HashMap<>();
        rawData.put("org_id", Arrays.asList(1, 2, 3));
        rawData.put("dept_id", 5);
        rawData.put("role", "manager");
        testUserPermission.setRawPermissionData(rawData);
    }

    @Test
    void testGenerateUserVariables() {
        logger.info("Testing generateUserVariables");
        
        // Mock PermissionService
        when(permissionService.getUserPermissions("testUser")).thenReturn(testUserPermission);
        
        // 执行测试
        Map<String, Object> variables = grafanaVariableService.generateUserVariables("testUser");
        
        // 验证结果
        assertNotNull(variables);
        assertFalse(variables.isEmpty());
        
        // 验证用户信息变量
        assertEquals("testUser", variables.get("user_id"));
        assertEquals("Test User", variables.get("user_name"));
        
        // 验证权限变量
        assertTrue(variables.containsKey("accessible_dashboards"));
        assertTrue(variables.containsKey("accessible_datasources"));
        
        // 验证数据过滤变量
        assertTrue(variables.containsKey("data_filter"));
        assertTrue(variables.containsKey("current_user_id"));
        assertTrue(variables.containsKey("user_role"));
        
        // 验证Mock调用
        verify(permissionService).getUserPermissions("testUser");
        
        logger.info("Generated variables: {}", variables.keySet());
    }

    @Test
    void testGenerateUserVariablesWithNullUser() {
        logger.info("Testing generateUserVariables with null user");
        
        // Mock PermissionService返回null
        when(permissionService.getUserPermissions(null)).thenReturn(null);
        
        // 执行测试
        Map<String, Object> variables = grafanaVariableService.generateUserVariables(null);
        
        // 验证结果
        assertNotNull(variables);
        // 应该返回默认变量，不应该为空
        assertFalse(variables.isEmpty());
        
        // 验证Mock调用
        verify(permissionService).getUserPermissions(null);
    }

    @Test
    void testSetDashboardVariables() {
        logger.info("Testing setDashboardVariables");
        
        String dashboardId = "test-dashboard";
        
        // Mock Grafana API调用
        List<Map<String, Object>> existingVariables = new ArrayList<>();
        Map<String, Object> existingVar = new HashMap<>();
        existingVar.put("name", "existing_var");
        existingVar.put("type", "query");
        existingVariables.add(existingVar);
        
        when(grafanaApiClient.getDashboardVariables(dashboardId)).thenReturn(existingVariables);
        when(grafanaApiClient.isVariableValueSafe(anyString())).thenReturn(true);
        when(grafanaApiClient.createPermissionVariable(anyString(), anyString(), anyString()))
            .thenReturn(createMockVariableConfig());
        when(grafanaApiClient.updateDashboardVariables(eq(dashboardId), anyList())).thenReturn(true);
        
        // 执行测试
        boolean result = grafanaVariableService.setDashboardVariables(dashboardId, testUserPermission);
        
        // 验证结果
        assertTrue(result);
        
        // 验证API调用
        verify(grafanaApiClient).getDashboardVariables(dashboardId);
        verify(grafanaApiClient).updateDashboardVariables(eq(dashboardId), anyList());
        
        logger.info("Dashboard variables set successfully");
    }

    @Test
    void testSetDashboardVariablesWithApiFailure() {
        logger.info("Testing setDashboardVariables with API failure");
        
        String dashboardId = "test-dashboard";
        
        // Mock API失败
        when(grafanaApiClient.getDashboardVariables(dashboardId)).thenReturn(new ArrayList<>());
        when(grafanaApiClient.isVariableValueSafe(anyString())).thenReturn(true);
        when(grafanaApiClient.createPermissionVariable(anyString(), anyString(), anyString()))
            .thenReturn(createMockVariableConfig());
        when(grafanaApiClient.updateDashboardVariables(eq(dashboardId), anyList())).thenReturn(false);
        
        // 执行测试
        boolean result = grafanaVariableService.setDashboardVariables(dashboardId, testUserPermission);
        
        // 验证结果
        assertFalse(result);
        
        logger.info("Dashboard variables setting failed as expected");
    }

    @Test
    void testGetDashboardPermissionVariables() {
        logger.info("Testing getDashboardPermissionVariables");
        
        String dashboardId = "test-dashboard";
        
        // Mock现有变量
        List<Map<String, Object>> variables = new ArrayList<>();
        
        // 权限变量
        Map<String, Object> permVar = new HashMap<>();
        permVar.put("name", "user_id");
        Map<String, Object> current = new HashMap<>();
        current.put("value", "testUser");
        permVar.put("current", current);
        variables.add(permVar);
        
        // 非权限变量
        Map<String, Object> normalVar = new HashMap<>();
        normalVar.put("name", "time_range");
        variables.add(normalVar);
        
        when(grafanaApiClient.getDashboardVariables(dashboardId)).thenReturn(variables);
        
        // 执行测试
        Map<String, Object> result = grafanaVariableService.getDashboardPermissionVariables(dashboardId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("testUser", result.get("user_id"));
        
        logger.info("Retrieved permission variables: {}", result);
    }

    @Test
    void testClearDashboardPermissionVariables() {
        logger.info("Testing clearDashboardPermissionVariables");
        
        String dashboardId = "test-dashboard";
        
        // Mock现有变量
        List<Map<String, Object>> variables = new ArrayList<>();
        
        // 权限变量
        Map<String, Object> permVar = new HashMap<>();
        permVar.put("name", "user_id");
        variables.add(permVar);
        
        // 非权限变量
        Map<String, Object> normalVar = new HashMap<>();
        normalVar.put("name", "time_range");
        variables.add(normalVar);
        
        when(grafanaApiClient.getDashboardVariables(dashboardId)).thenReturn(variables);
        when(grafanaApiClient.updateDashboardVariables(eq(dashboardId), anyList())).thenReturn(true);
        
        // 执行测试
        boolean result = grafanaVariableService.clearDashboardPermissionVariables(dashboardId);
        
        // 验证结果
        assertTrue(result);
        
        // 验证只保留非权限变量
        verify(grafanaApiClient).updateDashboardVariables(eq(dashboardId), argThat(list -> 
            list.size() == 1 && "time_range".equals(((Map<String, Object>) list.get(0)).get("name"))
        ));
        
        logger.info("Permission variables cleared successfully");
    }

    @Test
    void testConvertToGrafanaFormat() {
        logger.info("Testing convertToGrafanaFormat");
        
        // 准备测试数据
        Map<String, Object> variables = new HashMap<>();
        variables.put("user_id", "testUser");
        variables.put("user_name", "Test User");
        variables.put("data_filter", "department_id = '5'");
        
        // 执行测试
        Map<String, Object> grafanaVariables = grafanaVariableService.convertToGrafanaFormat(variables);
        
        // 验证结果
        assertNotNull(grafanaVariables);
        assertFalse(grafanaVariables.isEmpty());
        
        logger.info("Converted to Grafana format: {}", grafanaVariables.keySet());
    }

    @Test
    void testIsVariableValueSafe() {
        logger.info("Testing isVariableValueSafe");
        
        // 测试安全的变量值
        assertTrue(grafanaVariableService.isVariableValueSafe("user_id", "testUser123"));
        assertTrue(grafanaVariableService.isVariableValueSafe("dept_id", "5"));
        
        // 测试不安全的变量值（如果有相关验证逻辑）
        // 这里根据实际的安全验证逻辑进行测试
        
        logger.info("Variable value safety check completed");
    }

    /**
     * 创建模拟变量配置
     */
    private Map<String, Object> createMockVariableConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("name", "test_var");
        config.put("type", "constant");
        config.put("hide", 2);
        
        Map<String, Object> query = new HashMap<>();
        query.put("query", "test_value");
        config.put("query", query);
        
        Map<String, Object> current = new HashMap<>();
        current.put("value", "test_value");
        current.put("text", "test_value");
        config.put("current", current);
        
        return config;
    }

    /**
     * 测试权限变量识别
     */
    @Test
    void testPermissionVariableIdentification() {
        logger.info("Testing permission variable identification");
        
        // 通过反射访问私有方法进行测试
        // 这里简化处理，实际项目中可以将方法设为包级别可见或提供公共方法
        
        // 验证权限变量前缀识别逻辑
        String[] permissionVars = {"user_id", "org_data", "dept_filter", "role_check", "perm_access", "bbpf_config"};
        String[] normalVars = {"time_range", "server_name", "metric_type", "dashboard_title"};
        
        // 这里只是示例，实际测试需要根据具体实现调整
        logger.info("Permission variables should be identified: {}", Arrays.toString(permissionVars));
        logger.info("Normal variables should not be identified: {}", Arrays.toString(normalVars));
    }
}