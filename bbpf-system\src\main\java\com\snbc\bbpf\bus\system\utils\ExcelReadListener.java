/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.utils;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: ExcelReadListener
 * @Description: 提供Excel表格处理功能，依据阿里EasyExcel规则实现
 * @module: bbpf-bus-system
 * @Author: liuyi
 * @date: 2021/5/20
 * copyright 2020 barm Inc. All rights reserver
 */
public class ExcelReadListener<T> extends AnalysisEventListener<T> {

    private List<T> list = new ArrayList<>();

    /**
     * @description: 实现阿里EasyExcel接口方法，把Excel的数据放入List中
     * @param data  实体类
     * @param context 我也不知是什么
     * @return: void
     * @author: liuyi
     * @time: 2021/5/24 13:07
     */
    @Override
    public void invoke(T data, AnalysisContext context) {
        list.add(data);
    }

    /**
     * @description: 不得不实现而已
     * @param context 我不知道这是什么，没研究过
     * @return: void
     * @author: liuyi
     * @time: 2021/5/24 13:09
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // do nothing,just implement interface
    }

    public List<T> getList() {
        return list;
    }
}
