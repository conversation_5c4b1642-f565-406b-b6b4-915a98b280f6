package com.snbc.bbpf.grafana.proxy.controller;

import com.snbc.bbpf.grafana.proxy.config.GrafanaProxyConfig;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * 静态资源代理控制器
 * 专门处理Grafana的静态资源请求，确保CSS、JS、图片等文件能够正确加载
 */
@RestController
@RequestMapping
public class StaticResourceController {

    private static final Logger logger = LoggerFactory.getLogger(StaticResourceController.class);

    @Autowired
    private GrafanaProxyConfig config;

    private final CloseableHttpClient httpClient = HttpClients.createDefault();

    /**
     * 代理静态资源请求
     */
    @GetMapping({"/public/**", "/static/**", "/assets/**", "/app/**"})
    public ResponseEntity<byte[]> proxyStaticResource(HttpServletRequest request) {
        String requestPath = request.getRequestURI();
        String targetUrl = config.getGrafanaBaseUrl() + requestPath;
        
        logger.debug("Proxying static resource: {} -> {}", requestPath, targetUrl);

        try {
            HttpGet httpGet = new HttpGet(targetUrl);
            
            // 添加必要的请求头
            httpGet.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
            httpGet.setHeader("Accept", "*/*");
            httpGet.setHeader("Accept-Encoding", "gzip, deflate, br");
            
            try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
                int statusCode = response.getStatusLine().getStatusCode();
                
                if (statusCode == 200) {
                    HttpEntity entity = response.getEntity();
                    byte[] content = EntityUtils.toByteArray(entity);
                    
                    // 设置响应头
                    HttpHeaders headers = new HttpHeaders();
                    
                    // 根据文件扩展名设置Content-Type
                    String contentType = getContentType(requestPath);
                    if (contentType != null) {
                        headers.setContentType(MediaType.parseMediaType(contentType));
                    }
                    
                    // 复制其他重要的响应头
                    org.apache.http.Header[] responseHeaders = response.getAllHeaders();
                    for (org.apache.http.Header header : responseHeaders) {
                        String headerName = header.getName().toLowerCase();
                        if (!headerName.equals("transfer-encoding") && 
                            !headerName.equals("connection") &&
                            !headerName.equals("server")) {
                            headers.add(header.getName(), header.getValue());
                        }
                    }
                    
                    // 添加缓存控制
                    headers.setCacheControl("public, max-age=3600");
                    
                    return new ResponseEntity<>(content, headers, HttpStatus.OK);
                } else {
                    logger.warn("Failed to fetch static resource: {} - Status: {}", targetUrl, statusCode);
                    return new ResponseEntity<>(HttpStatus.valueOf(statusCode));
                }
            }
        } catch (IOException e) {
            logger.error("Error proxying static resource: {}", targetUrl, e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 根据文件路径获取Content-Type
     */
    private String getContentType(String path) {
        if (path.endsWith(".css")) {
            return "text/css";
        } else if (path.endsWith(".js")) {
            return "application/javascript";
        } else if (path.endsWith(".png")) {
            return "image/png";
        } else if (path.endsWith(".jpg") || path.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (path.endsWith(".gif")) {
            return "image/gif";
        } else if (path.endsWith(".svg")) {
            return "image/svg+xml";
        } else if (path.endsWith(".ico")) {
            return "image/x-icon";
        } else if (path.endsWith(".woff") || path.endsWith(".woff2")) {
            return "font/woff";
        } else if (path.endsWith(".ttf")) {
            return "font/ttf";
        } else if (path.endsWith(".eot")) {
            return "application/vnd.ms-fontobject";
        }
        return null;
    }
}