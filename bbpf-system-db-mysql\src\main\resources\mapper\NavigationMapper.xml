<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snbc.bbpf.system.db.common.mapper.NavigationMapper">
    
    <resultMap type="com.snbc.bbpf.system.db.common.entity.Navigation" id="NavigationResult">
        <result property="navigationId"    column="navigation_id"    />
        <result property="navigationName"    column="navigation_name"    />
        <result property="navigationCode"    column="navigation_code"    />
        <result property="navigationIcon"    column="navigation_icon"    />
        <result property="navigationOrder"    column="navigation_order"    />
        <result property="createTime"    column="create_time"    />
        <result property="createUserId"    column="create_user_id"    />
    </resultMap>

    <sql id="selectNavigationVo">
        select navigation_id, navigation_name, navigation_code, navigation_icon, navigation_order, create_time, create_user_id from t_navigation
    </sql>

    <select id="selectNavigationList" parameterType="com.snbc.bbpf.system.db.common.entity.Navigation" resultMap="NavigationResult">
        <include refid="selectNavigationVo"/>
        <where>  
            <if test="navigationName != null  and navigationName != ''"> and navigation_name like concat('%', #{navigationName}, '%')</if>
            <if test="navigationCode != null  and navigationCode != ''"> and navigation_code = #{navigationCode}</if>
            <if test="navigationIcon != null  and navigationIcon != ''"> and navigation_icon = #{navigationIcon}</if>
            <if test="navigationOrder != null  and navigationOrder != ''"> and navigation_order = #{navigationOrder}</if>
            <if test="createUserId != null  and createUserId != ''"> and create_user_id = #{createUserId}</if>
        </where>
        order by navigation_order asc
    </select>
    <insert id="saveNavigationPermission" parameterType="java.util.List">
        insert into t_navigation_permission(id,navigation_id,permission_id)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix=" (" suffix=")" suffixOverrides=",">
                #{item.id,jdbcType=VARCHAR},
                #{item.navigationId,jdbcType=VARCHAR},
                #{item.permissionId,jdbcType=VARCHAR}
            </trim>
        </foreach>
    </insert>
    <delete id="deleteRelationship" parameterType="String">
        delete from t_navigation_permission where navigation_id = #{navigationId}
    </delete>
    <select id="selectNavigationByNavigationId" parameterType="String" resultMap="NavigationResult">
        <include refid="selectNavigationVo"/>
        where navigation_id = #{navigationId}
    </select>
    <select id="selectNavigationByNavigationCode" parameterType="String" resultMap="NavigationResult">
        <include refid="selectNavigationVo"/>
        where navigation_code = #{navigationCode}
    </select>
     <select id="selectRelationship" parameterType="String"  resultType="String">
         select permission_id from t_navigation_permission
         where navigation_id = #{navigationId}
     </select>
    <insert id="insertNavigation" parameterType="com.snbc.bbpf.system.db.common.entity.Navigation">
        insert into t_navigation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="navigationId != null">navigation_id,</if>
            <if test="navigationName != null">navigation_name,</if>
            <if test="navigationCode != null">navigation_code,</if>
            <if test="navigationIcon != null">navigation_icon,</if>
            <if test="navigationOrder != null">navigation_order,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createUserId != null">create_user_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="navigationId != null">#{navigationId},</if>
            <if test="navigationName != null">#{navigationName},</if>
            <if test="navigationCode != null">#{navigationCode},</if>
            <if test="navigationIcon != null">#{navigationIcon},</if>
            <if test="navigationOrder != null">#{navigationOrder},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createUserId != null">#{createUserId},</if>
         </trim>
    </insert>

    <update id="updateNavigation" parameterType="com.snbc.bbpf.system.db.common.entity.Navigation">
        update t_navigation
        <trim prefix="SET" suffixOverrides=",">
            <if test="navigationName != null">navigation_name = #{navigationName},</if>
            <if test="navigationCode != null">navigation_code = #{navigationCode},</if>
            <if test="navigationIcon != null">navigation_icon = #{navigationIcon},</if>
            <if test="navigationOrder != null">navigation_order = #{navigationOrder},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createUserId != null">create_user_id = #{createUserId},</if>
        </trim>
        where navigation_id = #{navigationId}
    </update>

    <delete id="deleteNavigationByNavigationId" parameterType="String">
        delete from t_navigation where navigation_id = #{navigationId}
    </delete>

    <delete id="deleteNavigationByNavigationIds" parameterType="String">
        delete from t_navigation where navigation_id in 
        <foreach item="navigationId" collection="array" open="(" separator="," close=")">
            #{navigationId}
        </foreach>;
        delete from t_navigation_permission where navigation_id in
        <foreach item="navigationId" collection="array" open="(" separator="," close=")">
            #{navigationId}
        </foreach>;
    </delete>
</mapper>