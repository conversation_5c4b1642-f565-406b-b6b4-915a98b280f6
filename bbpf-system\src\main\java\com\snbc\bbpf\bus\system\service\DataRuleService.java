package com.snbc.bbpf.bus.system.service;

import com.snbc.bbpf.system.db.common.entity.PermissionScope;

import java.io.IOException;
import java.util.List;

/**
 * @ClassName: DataRuleService
 * 数据规则服务类
 * @module: bbpf-bus-system
 * @Author: yangweipeng
 * @date: 2021/6/7
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 */
public interface DataRuleService {
    /**
     * 获取当前用户的url的数据权限
     * @param url
     * @return
     */
    List<PermissionScope> getUserDataRule(String url);

    /**
     * 获取当前用户所在组织机构下的所有用户
     * @param orgType 0:当前组织机构，1：当前组织机构及以下
     * @return
     */
    List<String> getUserListByCurrentUserOrg(String orgType) throws IOException;
    /**
     * 获取数据权限下的 组织机构id：1：获取本人组织机构及本人创建的 2、本人组织机构 3 本人及下级组织机构
     * @return
     */
    List<String> getOrgIdListByDataRuleType(String orgType);

}
