/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service.impl;

import com.snbc.bbpf.bus.system.constans.NumberConstant;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.bus.system.service.RegionService;
import com.snbc.bbpf.bus.system.utils.ResultUtil;
import com.snbc.bbpf.system.db.common.entity.Region;
import com.snbc.bbpf.system.db.common.mapper.RegionMapper;
import com.snbc.bbpf.system.db.common.vo.RegionVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @ClassName: RegionServiceImpl
 * 提供阿里云的文件上传下载功能
 * @module: bbpf-bus-system
 * @Author: liuyi
 * @date: 2021/5/20
 * copyright 2020 barm Inc. All rights reserver
 */
@Service
public class RegionServiceImpl implements RegionService {

    public static final String STR_ZERO = "0";
    @Autowired
    private RegionMapper regionMapper;

    /**
     * @param region     地域json体
     * @param commonResp 返回响应json体
     * @description: 添加地域信息
     * @return: void
     * @author: liuyi
     * @time: 2021/6/7 16:24
     */
    @Override
    public void addRegion(Region region, CommonResp<String> commonResp) {
        if (region.getRegionCode() != null && STR_ZERO.equals(region.getRegionCode())) {
            commonResp.setHead(ResultUtil.error(ErrorMessage.REGION_CODE_IS_ZERO));
            return;
        }
        if (StringUtils.isNotEmpty(region.getParentCode())) {
            List<Region> parents = regionMapper.selectByExample(region.getParentCode(), null);
            if (parents.size() != NumberConstant.NO_ONE) {
                commonResp.setHead(ResultUtil.error(ErrorMessage.REGION_CODE_IS_NONE, region.getParentCode()));
                return;
            }
        } else {
            region.setParentCode(STR_ZERO);
        }
        List<Region> list = regionMapper.selectByExample(region.getRegionCode(), null);
        if (!list.isEmpty()) {
            commonResp.setHead(ResultUtil.error(ErrorMessage.REGION_CODE_EXIST, region.getRegionCode()));
        } else {
            region.setId(UUID.randomUUID().toString());
            int num = regionMapper.insert(region);
            if (num == NumberConstant.NO_ONE) {
                commonResp.setHead(ResultUtil.success());
            } else {
                commonResp.setHead(ResultUtil.error(ErrorMessage.SQL_EXECUTE_FAILED));
            }
        }
    }

    /**
     * @param region     地域json体
     * @param commonResp 返回json体
     * @description: 更新地域信息
     * @return: void
     * @author: liuyi
     * @time: 2021/6/7 16:23
     */
    @Override
    public void updateRegion(Region region, CommonResp<String> commonResp) {
        Region dbRegion = regionMapper.selectByPrimaryKey(region.getId());
        if (dbRegion == null) {
            commonResp.setHead(ResultUtil.error(ErrorMessage.REGION_CODE_IS_NONE, region.getRegionCode()));
        } else {
            if (!"0".equals(region.getParentCode())) {
                //判断父级编码是否存在
                List<Region> parents = regionMapper.selectByExample(region.getParentCode(), null);
                if (parents.isEmpty()) {
                    commonResp.setHead(ResultUtil.error(ErrorMessage.REGION_CODE_IS_NONE, region.getParentCode()));
                    return;
                }
            }
            if (!"0".equals(region.getParentCode())) {
                List<Region> children = regionMapper.selectByExample(null, dbRegion.getRegionCode());
                for (Region child : children) {
                    child.setParentCode(region.getRegionCode());
                    regionMapper.updateByPrimaryKey(child);
                }
            }
            int num = regionMapper.updateByPrimaryKey(region);
            if (num == 1) {
                commonResp.setHead(ResultUtil.success());
            } else {
                commonResp.setHead(ResultUtil.error(ErrorMessage.SQL_EXECUTE_FAILED));
            }

        }
    }

    /**
     * @param regionCode 地域编码
     * @param commonResp 返回json体
     * @description: 删除地域信息，通过地域编码
     * @return: void
     * @author: liuyi
     * @time: 2021/6/7 16:22
     */
    @Override
    public void deleteRegion(String regionCode, CommonResp<String> commonResp) {
        List<Region> list = regionMapper.selectByExample(regionCode, null);
        if (list.size() != 1) {
            commonResp.setHead(ResultUtil.error(ErrorMessage.REGION_CODE_IS_NONE, regionCode));
        } else {
            int num = recursionDelByPId(list.get(0).getId(), list.get(0).getRegionCode());
            if (num > 0) {
                commonResp.setHead(ResultUtil.success());
                commonResp.setBody(String.valueOf(num));
            } else {
                commonResp.setHead(ResultUtil.error(ErrorMessage.SQL_EXECUTE_FAILED));
            }
        }

    }

    /**
     * @param parentCode 父级地域编码
     * @description: 查询子地域信息
     * @return: void
     * @author: liuyi
     * @time: 2021/6/7 16:20
     */
    @Override
    public List<RegionVo> getRegionList(String parentCode) {
        if (StringUtils.isEmpty(parentCode)) {
            parentCode = "0";
        }
        List<RegionVo> regionVoList = new ArrayList<>();
        List<Region> regions = regionMapper.selectByExample(null, parentCode);
        List<String> regionIdList = regions.stream().map(Region::getRegionCode).collect(Collectors.toList());
        List<Map<String, String>> parentRegionCountList = regionMapper.selectRegionHasChild(regionIdList);
        Iterator<Map<String, String>> iterator = parentRegionCountList.iterator();
        Map<String, Integer> parentRegionMap = new HashMap<>();
        while (iterator.hasNext()) {
            Map<String, String> next = iterator.next();
            String parentCode1 = next.get("parentCode");
            Integer childCount = Integer.valueOf(String.valueOf(next.get("childCount")));
            parentRegionMap.put(parentCode1, childCount);
        }
        for (Region region : regions) {
            boolean hasChild = false;
            if (parentRegionMap.containsKey(region.getRegionCode())) {
                hasChild = !parentRegionMap.get(region.getRegionCode()).equals(NumberConstant.NO_ZERO);
            }
            RegionVo regionVo = RegionVo.builder().id(region.getId()).parentCode(region.getParentCode())
                    .regionDesc(region.getRegionDesc()).regionName(region.getRegionName()).regionCode(region.getRegionCode())
                    .hasChild(hasChild).build();
            regionVoList.add(regionVo);
        }
        return regionVoList;
    }

    /**
     * @param parentId 父级地域编码
     * @description: 递归删除地域
     * @return: int 删除数量
     * @author: liuyi
     * @time: 2021/6/7 16:20
     */
    private int recursionDelByPId(String parentId, String regionCode) {
        AtomicInteger num = new AtomicInteger(regionMapper.deleteByPrimaryKey(parentId));
        List<Region> list = regionMapper.selectByExample(null, regionCode);
        list.forEach((region -> num.set(recursionDelByPId(region.getId(), region.getRegionCode()))));
        return num.get();
    }

    /**
     * @param list       地域List集合
     * @param commonResp 返回json体
     * @description: 地域导入，保存地域信息
     * @return: void
     * @author: liuyi
     * @time: 2021/6/7 16:18
     */
    @Override
    public void importRegion(List<Region> list, CommonResp<String> commonResp) {
        int size = 0;
        while (size != list.size()) {
            size = list.size();
            recurAdd(list, commonResp);
        }
    }

    /**
     * @param list       地域List 集合
     * @param commonResp 返回json体
     * @description: 递归添加地域
     * @return: void
     * @author: liuyi
     * @time: 2021/6/7 16:17
     */
    private void recurAdd(List<Region> list, CommonResp<String> commonResp) {
        Iterator<Region> iterator = list.iterator();
        while (iterator.hasNext()) {
            Region region = iterator.next();
            addRegion(region, commonResp);
            if (commonResp.getHead().getCode().equals(ResultUtil.success().getCode())) {
                iterator.remove();
            }
        }
    }

}
