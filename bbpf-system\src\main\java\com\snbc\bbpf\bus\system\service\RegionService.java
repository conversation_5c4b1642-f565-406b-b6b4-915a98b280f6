/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service;

import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.system.db.common.entity.Region;
import com.snbc.bbpf.system.db.common.vo.RegionVo;

import java.util.List;
/**
 * @ClassName: RegionService
 * 区域 接口类
 * @module: si-bbpf-system
 * @Author: wjc1 增加的注释
 * @date:   2021/5/25 16:56
 */
public interface RegionService {

    void addRegion(Region region, CommonResp<String> commonResp);

    void updateRegion(Region region, CommonResp<String> commonResp);

    void deleteRegion(String regionCode, CommonResp<String> commonResp);

    List<RegionVo> getRegionList(String parentCode);

    void importRegion(List<Region> list, CommonResp<String> commonResp);

}
