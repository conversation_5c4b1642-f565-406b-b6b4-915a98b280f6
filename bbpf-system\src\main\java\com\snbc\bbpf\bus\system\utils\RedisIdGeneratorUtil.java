/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.bus.system.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.UUID;

/**
 * 分布式全局id生成
 *
 * @ClassName: RedisIdGeneratorUtil
 * @module: SI-bbpf-tenant-config
 * @Author: wangsong
 * @date: 2023/11/7
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Component
public class RedisIdGeneratorUtil {

    private static final RedisIdGeneratorUtil redisIdGeneratorUtil = new RedisIdGeneratorUtil();

    @Autowired
    private RedisTemplate<String, Long> redisTemplate;
    private static final String ID_KEY = "bbpf-uniqueId";
    @Value("${bbpf.userid.generation:uuid}")
    private String userIdGenerationMode;

    @Value("${bbpf.userid.increment.number:1000000}")
    private long incrementStartNumber;

    @PostConstruct
    public void init() {
        redisIdGeneratorUtil.userIdGenerationMode = this.userIdGenerationMode;
        redisIdGeneratorUtil.incrementStartNumber = this.incrementStartNumber;
        redisIdGeneratorUtil.redisTemplate = this.redisTemplate;
    }

    /**
     * 全局唯一id生成uuid或自增id
     * @return java.lang.String
     * @throws //
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/11/7
     */
    public static String nextId() {
        if (redisIdGeneratorUtil.userIdGenerationMode.equals("increment")) {
            long inc = redisIdGeneratorUtil.redisTemplate.opsForValue().increment(ID_KEY);
            return String.valueOf(redisIdGeneratorUtil.incrementStartNumber + inc);
        }
        return UUID.randomUUID().toString();
    }
}

