/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service.impl;

import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.service.DictService;
import com.snbc.bbpf.system.db.common.entity.Param;
import com.snbc.bbpf.system.db.common.mapper.ParamMapper;
import com.snbc.bbpf.system.db.common.mapper.UserOrgMapper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.fail;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * @ClassName: ParamServiceImplTest
 * 参数配置单元测试
 * @module: si-bbpf-system
 * @Author: wjc1
 * @date: 2021/7/2 15:55
 */
public class ParamServiceImplTest {
    @InjectMocks
    private ParamServiceImpl paramService;
    @Mock
    private ParamMapper paramMapper;
    @Mock
    private UserOrgMapper userOrgMapper;
    @Mock
    private DictService dictService;
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("测试参数配置新增_登录用户不存在")
    @Tag("@id:23561-1")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/2")
    public void test_save_user_not_exsit_exception(){
        Param param=Param.builder()
                .paramName("abc")
                .paramCode("abc")
                .paramTypeCode("abc1")
                .paramTypeName("asd").build();
        when(paramMapper.selectByParamCode("abc")).thenReturn(new Param());
        try {
            paramService.saveOrUpdateParam(param);
            fail("businessException is expected");
        }catch (Exception e){
            Assertions.assertEquals(e.getMessage(), ErrorMessage.USER_INVALID.getMessage());
        }
        verify(paramMapper,times(0)).insertSelective(param);
    }
    @Test
    @DisplayName("测试参数配置新增_配置编码存在")
    @Tag("@id:23561-2")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/2")
    public void test_save_paramCode_exsit_exception(){
        Param param=Param.builder().createUserId("111")
                .paramName("a")
                .paramCode("c")
                .paramTypeCode("abc1")
                .paramTypeName("d").build();
        when(paramMapper.selectByParamCode("c")).thenReturn(new Param());
        try {
            paramService.saveOrUpdateParam(param);
            fail("businessException is expected");
        }catch (Exception e){
            Assertions.assertEquals(e.getMessage(), ErrorMessage.PARAM_EXIST.getMessage());
        }
        verify(paramMapper,times(0)).insertSelective(param);
        verify(paramMapper,times(0)).updateByPrimaryKeySelective(param);
    }
    @Test
    @DisplayName("测试参数配置新增_成功")
    @Tag("@id:23561-3")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/2")
    public void test_save_paramCode_success(){
        Param param=Param.builder()
                .paramName("a")
                .paramCode("c")
                .paramTypeCode("abc1").createUserId("123")
                .paramTypeName("d").build();
        when(paramMapper.selectByParamCode(param.getParamCode())).thenReturn(null);
        when(userOrgMapper.queryOrgListByUserId(param.getCreateUserId())).thenReturn(Mockito.anyString());
        try {
            paramService.saveOrUpdateParam(param);
        }catch (Exception e){
            fail("businessException is not expected");
        }
        verify(paramMapper,times(1)).insertSelective(param);
        verify(paramMapper,times(0)).updateByPrimaryKeySelective(param);
    }

    @Test
    @DisplayName("测试参数配置列表查询")
    @Tag("@id:23562-1")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/2")
    public void test_queryParamListPage(){
        Map<String, Object> map= new HashMap<>();
        String paramName=null;
        String paramTypeName=null;
        Integer pageNum=1;
        Integer pageSize=10;
        map.put("paramName", paramName);
        map.put("paramTypeName", paramTypeName);
        when(paramMapper.queryParamByMap(map)).thenReturn(new ArrayList<>());
        paramService.queryParamListPage(paramName,paramTypeName,pageNum,pageSize);
        verify(paramMapper,times(1)).queryParamByMap(map);
    }
    @Test
    @DisplayName("测试根据ID获取参数配置值")
    @Tag("@id:23562-2")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/2")
    public void test_getParamByPrimary(){
        String paramId=null;
        when(paramMapper.selectByPrimaryKey(paramId)).thenReturn(new Param());
        paramService.getParamByPrimary(paramId);
        verify(paramMapper,times(1)).selectByPrimaryKey(paramId);
    }
    @Test
    @DisplayName("测试根据Code获取参数配置值")
    @Tag("@id:23562-3")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/2")
    public void test_getParamByCode(){
        String paramCode=null;
        when(paramMapper.selectByParamCode(paramCode)).thenReturn(new Param());
        paramService.getParamByCode(paramCode);
        verify(paramMapper,times(1)).selectByParamCode(paramCode);
    }
    @Test
    @DisplayName("测试根据MAP获取参数配置值")
    @Tag("@id:23562-4")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/2")
    public void test_getParamByMap(){
        Map<String, Object> map= new HashMap<>();
        when(paramMapper.queryParamByMap(map)).thenReturn(new ArrayList<>());
        paramService.getParamByMap(map);
        verify(paramMapper,times(1)).queryParamByMap(map);
    }
    @Test
    @DisplayName("测试获取参数配置值CODE")
    @Tag("@id:23562-5")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/2")
    public void test_queryParamCode(){
        when(dictService.getDictValueList("param_type")).thenReturn(new ArrayList<>());
        paramService.queryParamCode();
        verify(dictService,times(1)).getDictValueList("param_type");
    }

    @Test
    @DisplayName("测试参数配置更新-用户不存在")
    @Tag("@id:23563-1")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/2")
    public void test_update_user_not_exsit_exception(){
        Param param=Param.builder()
                .paramName("abc")
                .paramCode("abc")
                .paramTypeCode("abc1")
                .paramTypeName("asd").build();
        when(paramMapper.selectByParamCode("abc")).thenReturn(new Param());
        try {
            paramService.saveOrUpdateParam(param);
            fail("businessException is expected");
        }catch (Exception e){
            Assertions.assertEquals(e.getMessage(), ErrorMessage.USER_INVALID.getMessage());
        }
        verify(paramMapper,times(0)).insertSelective(param);
        verify(paramMapper,times(0)).updateByPrimaryKeySelective(param);
    }
    @Test
    @DisplayName("测试参数配置更新_配置不存在")
    @Tag("@id:23563-2")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/2")
    public void test_update_param_not_exsit_exception(){
        Param param=Param.builder().paramId("qazwsx").createUserId("111")
                .paramName("abc")
                .paramCode("abc")
                .paramTypeCode("abc1")
                .paramTypeName("asd").build();
        when(paramMapper.selectByParamCode("abc")).thenReturn(new Param());
        when(paramMapper.selectByPrimaryKey(param.getParamId())).thenReturn(null);
        try {
            paramService.saveOrUpdateParam(param);
            fail("businessException is expected");
        }catch (Exception e){
            Assertions.assertEquals(e.getMessage(), ErrorMessage.PARAM_ID_NOT_EXIST.getMessage());
        }
        verify(paramMapper,times(0)).insertSelective(param);
        verify(paramMapper,times(0)).updateByPrimaryKeySelective(param);
    }
    @Test
    @DisplayName("测试参数配置更新_成功")
    @Tag("@id:23563-3")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/2")
    public void test_update_param_success(){
        Param param=Param.builder().paramId("qazwsx").createUserId("111")
                .paramName("abc")
                .paramCode("abc")
                .paramTypeCode("abc1")
                .paramTypeName("asd").build();
        when(paramMapper.selectByParamCode(param.getParamCode())).thenReturn(new Param());
        Param paramExsit=Param.builder().paramId("qazwsx").createUserId("111")
                .paramName("abc")
                .paramCode("abc")
                .paramTypeCode("abc1")
                .paramTypeName("asd2").build();
        when(paramMapper.selectByPrimaryKey(param.getParamId())).thenReturn(paramExsit);
        try {
            paramService.saveOrUpdateParam(param);
        }catch (Exception e){
            fail("businessException is not expected");
        }
        verify(paramMapper,times(0)).insertSelective(param);
        verify(paramMapper,times(1)).updateByPrimaryKeySelective(Mockito.any());
    }
    @Test
    @DisplayName("测试参数配置删除")
    @Tag("@id:23564-1")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/2")
    public void test_del(){
        String paramIds="a,b,c";
        when(paramMapper.deleteByPrimaryKey("a")).thenReturn(1);
        when(paramMapper.deleteByPrimaryKey("b")).thenReturn(0);
        when(paramMapper.deleteByPrimaryKey("c")).thenReturn(0);
        try {
            paramService.batchDeleteParam(paramIds);
            fail("businessException is expected");
        }catch (Exception e){
            Assertions.assertEquals(e.getMessage(), ErrorMessage.PARAM_DELETE_FAIL.getMessage());
            verify(paramMapper,times(1)).deleteByPrimaryKey("a");
            verify(paramMapper,times(1)).deleteByPrimaryKey("b");
            verify(paramMapper,times(0)).deleteByPrimaryKey("c");
        }
    }
    @Test
    @DisplayName("测试参数配置删除")
    @Tag("@id:23564-2")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/2")
    public void test_del2(){
        String paramIds="a,b,c";
        when(paramMapper.deleteByPrimaryKey("a")).thenReturn(0);
        when(paramMapper.deleteByPrimaryKey("b")).thenReturn(0);
        when(paramMapper.deleteByPrimaryKey("c")).thenReturn(1);
        try {
            paramService.batchDeleteParam(paramIds);
            fail("businessException is expected");
        }catch (Exception e){
            Assertions.assertEquals(e.getMessage(), ErrorMessage.PARAM_DELETE_FAIL.getMessage());
            verify(paramMapper,times(1)).deleteByPrimaryKey("a");
            verify(paramMapper,times(0)).deleteByPrimaryKey("b");
            verify(paramMapper,times(0)).deleteByPrimaryKey("c");
        }

    }
    @Test
    @DisplayName("测试参数配置删除")
    @Tag("@id:23564-3")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/2")
    public void test_del3(){
        String paramIds="a,b,c";
        when(paramMapper.deleteByPrimaryKey("a")).thenReturn(1);
        when(paramMapper.deleteByPrimaryKey("b")).thenReturn(1);
        when(paramMapper.deleteByPrimaryKey("c")).thenReturn(1);
        try {
            paramService.batchDeleteParam(paramIds);
            verify(paramMapper,times(1)).deleteByPrimaryKey("a");
            verify(paramMapper,times(1)).deleteByPrimaryKey("b");
            verify(paramMapper,times(1)).deleteByPrimaryKey("c");
        }catch (Exception e){
            fail("businessException is not expected");
        }
    }
}
