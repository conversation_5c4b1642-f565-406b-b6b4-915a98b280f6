/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: RoleOrg
 * 用户组织机构实体类
 * @module: si-bbpf-system
 * @Author: wjc1 增加的注释
 * @date:   2021/5/25 16:56
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RoleOrg {
    private String id;
    private String roleId;
    private String orgId;
}
