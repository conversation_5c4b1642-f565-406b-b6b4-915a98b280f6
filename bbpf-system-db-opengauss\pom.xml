<?xml version="1.0" encoding="UTF-8"?>
<!-- 此 XML 文件是一个 Maven 项目的 POM（Project Object Model）文件，用于配置项目的构建和依赖信息等 -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <!-- POM 文件的模型版本，通常使用 4.0.0 -->
    <modelVersion>4.0.0</modelVersion>
    <!-- 该项目继承自父项目，使用父项目的部分配置信息 -->
    <parent>
        <!-- 父项目的组 ID，一般是组织或公司的标识符 -->
        <groupId>com.snbc.bbpf</groupId>
        <!-- 父项目的 artifact ID，用于在组内唯一标识父项目 -->
        <artifactId>bbpf-system</artifactId>
        <!-- 父项目的版本号 -->
        <version>2.0.0</version>
        <!-- 父项目 POM 文件的相对路径，这里是相对于当前项目的上一级目录 -->
        <relativePath>../pom.xml</relativePath>
    </parent>
    <!-- 本项目的组 ID，遵循组织或公司的标识符 -->
    <groupId>com.snbc.bbpf</groupId>
    <!-- 本项目的 artifact ID，用于在组内唯一标识本项目 -->
    <artifactId>bbpf-system-db-opengauss</artifactId>
    <!-- 本项目的版本号 -->
    <version>2.0.0</version>
    <!-- 本项目的名称 -->
    <name>bbpf-system-db-openguass</name>
    <!-- 本项目的描述信息，对项目进行简单描述 -->
    <description>project for bbpf-system-db</description>
    <!-- 项目的属性，可在 POM 文件的其他部分使用这些属性 -->
    <properties>
        <!-- Java 版本属性，指定使用 Java 1.8 -->
        <java.version>1.8</java.version>
    </properties>
    <!-- 项目的依赖列表，包含项目所需的外部库或模块 -->
    <dependencies>
        <!-- 该项目对另一个组件的依赖，这里是 bbpf-component-opengauss-spring-boot-starter -->
        <dependency>
            <groupId>com.snbc.bbpf</groupId>
            <artifactId>bbpf-component-opengauss-spring-boot-starter</artifactId>
        </dependency>
    </dependencies>
    <!-- 项目的构建配置信息 -->
    <build>
        <!-- 项目构建后的最终名称 -->
        <finalName>bbpf-system-db-opengauss</finalName>
        <!-- 项目资源的配置，包括资源文件的目录等 -->
        <resources>
            <!-- 包含 src/main/resources 目录作为资源目录 -->
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <!-- 包含 src/main/java 目录下的所有.xml 文件作为资源文件 -->
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <!-- 项目使用的插件列表，用于扩展 Maven 的构建功能 -->
        <plugins>
            <!-- MyBatis 代码生成器插件，可根据数据库表结构自动生成 MyBatis 的代码 -->
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <configuration>
                    <!-- 生成代码时是否输出详细信息，true 表示输出 -->
                    <verbose>true</verbose>
                    <!-- 若已存在生成的代码文件，是否覆盖 -->
                    <overwrite>true</overwrite>
                </configuration>
            </plugin>
            <!-- Maven 的 Surefire 插件，用于运行单元测试 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <!-- 系统属性变量的配置，这里设置了 Jacoco 代理文件的存储位置 -->
                    <systemPropertyVariables>
                        <jacoco-agent.destfile>${project.build.directory}/jacoco.exec</jacoco-agent.destfile>
                    </systemPropertyVariables>
                </configuration>
            </plugin>
            <!-- Jacoco 插件，用于代码覆盖率分析 -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <executions>
                    <!-- 对代码进行插桩操作，以便进行代码覆盖率分析 -->
                    <execution>
                        <id>default-instrument</id>
                        <goals>
                            <goal>instrument</goal>
                        </goals>
                    </execution>
                    <!-- 恢复插桩后的类，将代码恢复到原始状态 -->
                    <execution>
                        <id>default-restore-instrumented-classes</id>
                        <goals>
                            <goal>restore-instrumented-classes</goal>
                        </goals>
                    </execution>
                    <!-- 在单元测试前的准备工作，如设置 Jacoco 代理 -->
                    <execution>
                        <id>pre-unit-test</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                        <configuration>
                            <!-- 是否追加到已有的 Jacoco 数据文件，false 表示不追加 -->
                            <append>false</append>
                            <!-- Jacoco 数据文件的存储位置 -->
                            <destFile>${basedir}/target/jacoco.exec</destFile>
                            <!-- 系统属性的名称，将 Jacoco 代理的参数存储在此属性中 -->
                            <propertyName>surefireArgLine</propertyName>
                        </configuration>
                    </execution>
                    <!-- 在 prepare-package 阶段生成代码覆盖率报告 -->
                    <execution>
                        <id>default-report</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                        <configuration>
                            <!-- 用于生成报告的数据文件位置 -->
                            <dataFile>${basedir}/target/jacoco.exec</dataFile>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- Maven 编译器插件，用于编译 Java 代码 -->
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <!-- 编译 Java 代码时使用的源版本 -->
                    <source>${java.version}</source>
                    <!-- 编译后的目标 Java 版本 -->
                    <target>${java.version}</target>
                    <!-- 编码方式，使用 UTF-8 -->
                    <encoding>UTF-8</encoding>
                    <!-- 编译器参数，-parameters 可在运行时保留方法的参数名称 -->
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>