<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snbc.bbpf.system.db.common.mapper.RegionMapper" >
  <resultMap id="BaseResultMap" type="com.snbc.bbpf.system.db.common.entity.Region" >
    <constructor >
      <idArg column="id" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="region_name" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="region_code" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="parent_code" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="region_desc" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="SEQUENCE" jdbcType="INTEGER" javaType="java.lang.Integer" />
    </constructor>
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, region_name, region_code, parent_code, region_desc, SEQUENCE
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap"  >
    select
    <include refid="Base_Column_List" />
    from t_region where 1=1
    <if test="regionCode != null and regionCode !=''" >
      AND region_code = #{regionCode,jdbcType=VARCHAR}
    </if>
    <if test="parentCode != null and parentCode !=''" >
      AND parent_code = #{parentCode,jdbcType=VARCHAR}
    </if>
      order by region_code asc
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from t_region
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from t_region
    where id = #{id,jdbcType=VARCHAR}
  </delete>

  <insert id="insert" parameterType="com.snbc.bbpf.system.db.common.entity.Region" >
    insert into t_region (id, region_name, region_code, 
      parent_code, region_desc, SEQUENCE
      )
    values (#{id,jdbcType=VARCHAR}, #{regionName,jdbcType=VARCHAR}, #{regionCode,jdbcType=VARCHAR}, 
      #{parentCode,jdbcType=VARCHAR}, #{regionDesc,jdbcType=VARCHAR}, #{sequence,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.snbc.bbpf.system.db.common.entity.Region" >
    insert into t_region
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="regionName != null" >
        region_name,
      </if>
      <if test="regionCode != null" >
        region_code,
      </if>
      <if test="parentCode != null" >
        parent_code,
      </if>
      <if test="regionDesc != null" >
        region_desc,
      </if>
      <if test="sequence != null" >
        SEQUENCE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="regionName != null" >
        #{regionName,jdbcType=VARCHAR},
      </if>
      <if test="regionCode != null" >
        #{regionCode,jdbcType=VARCHAR},
      </if>
      <if test="parentCode != null" >
        #{parentCode,jdbcType=VARCHAR},
      </if>
      <if test="regionDesc != null" >
        #{regionDesc,jdbcType=VARCHAR},
      </if>
      <if test="sequence != null" >
        #{sequence,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

  <update id="updateByExampleSelective" parameterType="map" >
    update t_region
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.regionName != null" >
        region_name = #{record.regionName,jdbcType=VARCHAR},
      </if>
      <if test="record.regionCode != null" >
        region_code = #{record.regionCode,jdbcType=VARCHAR},
      </if>
      <if test="record.parentCode != null" >
        parent_code = #{record.parentCode,jdbcType=VARCHAR},
      </if>
      <if test="record.regionDesc != null" >
        region_desc = #{record.regionDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.sequence != null" >
        SEQUENCE = #{record.sequence,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update t_region
    set id = #{record.id,jdbcType=VARCHAR},
      region_name = #{record.regionName,jdbcType=VARCHAR},
      region_code = #{record.regionCode,jdbcType=VARCHAR},
      parent_code = #{record.parentCode,jdbcType=VARCHAR},
      region_desc = #{record.regionDesc,jdbcType=VARCHAR},
      SEQUENCE = #{record.sequence,jdbcType=INTEGER}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.snbc.bbpf.system.db.common.entity.Region" >
    update t_region
    <set >
      <if test="regionName != null" >
        region_name = #{regionName,jdbcType=VARCHAR},
      </if>
      <if test="regionCode != null" >
        region_code = #{regionCode,jdbcType=VARCHAR},
      </if>
      <if test="parentCode != null" >
        parent_code = #{parentCode,jdbcType=VARCHAR},
      </if>
      <if test="regionDesc != null" >
        region_desc = #{regionDesc,jdbcType=VARCHAR},
      </if>
      <if test="sequence != null" >
        SEQUENCE = #{sequence,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.snbc.bbpf.system.db.common.entity.Region" >
    update t_region
    set region_name = #{regionName,jdbcType=VARCHAR},
      region_code = #{regionCode,jdbcType=VARCHAR},
      parent_code = #{parentCode,jdbcType=VARCHAR},
      region_desc = #{regionDesc,jdbcType=VARCHAR},
      SEQUENCE = #{sequence,jdbcType=INTEGER}
    where id = #{id,jdbcType=VARCHAR}
  </update>

  <select id="selectRegionHasChild" parameterType="list" resultType="map">
    SELECT
    parent_code as parentCode,COUNT(0) as childCount
    FROM
    t_region
    WHERE
    parent_code IN
    <foreach item="parentCode" collection="regionIdList" separator="," open="(" close=")" index="">
      #{parentCode}
    </foreach>
    GROUP BY parent_code;
  </select>
</mapper>