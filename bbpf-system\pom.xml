<?xml version="1.0" encoding="UTF-8"?>
<!-- 这是一个 Maven 的项目对象模型（POM）文件，用于配置 bbpf-system-manager 项目的构建和依赖信息 -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <!-- 定义 POM 文件的模型版本，通常使用 4.0.0 -->
    <modelVersion>4.0.0</modelVersion>
    <!-- 此项目继承自父项目，以下是父项目的相关信息 -->
    <parent>
        <!-- 父项目的组 ID，一般代表组织或公司的标识 -->
        <groupId>com.snbc.bbpf</groupId>
        <!-- 父项目的 artifact ID，用于区分父项目中的不同模块 -->
        <artifactId>bbpf-system</artifactId>
        <!-- 父项目的版本号 -->
        <version>2.0.0</version>
        <!-- 父项目 POM 文件的相对路径，相对于当前项目的上一级目录 -->
        <relativePath>../pom.xml</relativePath>
    </parent>
    <!-- 本项目的组 ID，遵循组织或公司的标识 -->
    <groupId>com.snbc.bbpf</groupId>
    <!-- 本项目的 artifact ID，用于唯一标识本项目 -->
    <artifactId>bbpf-system-manager</artifactId>
    <!-- 本项目的版本号 -->
    <version>2.0.0</version>
    <!-- 项目的打包方式，这里是打包为 JAR 文件 -->
    <packaging>jar</packaging>
    <!-- 本项目的名称 -->
    <name>bbpf-system-manager</name>
    <!-- 本项目的描述信息，对项目功能的简要描述 -->
    <description>project for bbpf-system-manager</description>
    <!-- 项目的属性，可在整个 POM 文件中引用 -->
    <properties>
        <!-- Java 开发和编译使用的版本 -->
        <java.version>1.8</java.version>
    </properties>
    <!-- 项目的依赖信息，包含项目所需的各种库和组件 -->
    <dependencies>
        <!-- Prometheus 监控相关依赖，用于监控应用程序的指标 -->
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>
        <!-- Spring Boot 的 Actuator 启动器，提供应用程序的监控和管理端点 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <exclusions>
                <!-- 排除 Spring Boot 的日志启动器，可能是为了使用其他日志系统 -->
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- xxl-job 依赖，可能用于分布式任务调度 -->
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>
        <!-- Spring Boot 的安全启动器，提供安全相关功能，如认证、授权等 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <!-- 标准的自定义组件启动器，可能包含项目所需的通用功能 -->
        <dependency>
            <groupId>com.snbc.bbpf</groupId>
            <artifactId>bbpf-component-spring-boot-starter</artifactId>
        </dependency>
        <!-- Spring Cloud 的 Feign 启动器，用于服务间的 HTTP 客户端调用 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <!-- Apache Commons Lang 依赖，提供了许多常用的工具类 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <!-- 验证码组件启动器，可能用于生成和验证验证码，版本为 2.0.10 -->
        <dependency>
            <groupId>com.snbc.bbpf</groupId>
            <artifactId>bbpf-component-captcha-spring-boot-starter</artifactId>
            <version>2.0.10</version>
        </dependency>
        <!-- 灰度发布组件启动器，可能用于实现灰度发布功能，版本为 2.0.6-SNAPSHOT，排除了 Spring Cloud Gateway 服务器 -->
        <dependency>
            <groupId>com.snbc.bbpf</groupId>
            <artifactId>bbpf-component-gray-spring-boot-starter</artifactId>
            <version>2.0.6-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>spring-cloud-gateway-server</artifactId>
                    <groupId>org.springframework.cloud</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 文件上传组件启动器，可能用于文件上传功能 -->
        <dependency>
            <groupId>com.snbc.bbpf</groupId>
            <artifactId>bbpf-component-oss-spring-boot-starter</artifactId>
        </dependency>
        <!-- Apollo 客户端依赖，用于配置管理 -->
        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
        </dependency>
        <!-- Eureka 客户端依赖，用于服务发现 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        </dependency>
        <!-- Servlet API 依赖，范围为 provided，表明该依赖由运行时环境提供，避免冲突 -->
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- Feign 的 HTTP 客户端依赖，版本为 11.10 -->
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-httpclient</artifactId>
            <version>11.10</version>
        </dependency>
        <!-- Orika 依赖，用于对象映射和转换 -->
        <dependency>
            <groupId>ma.glasnost.orika</groupId>
            <artifactId>orika-core</artifactId>
        </dependency>
        <!-- Apache Commons Collections 依赖，提供了集合相关的工具类 -->
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
        </dependency>
        <!-- EasyExcel 相关依赖，用于 Excel 文件处理，包括 Apache POI 的相关版本 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
            <version>4.1.2</version>
        </dependency>
        <!-- Apache SkyWalking 工具包，可能用于日志监控 -->
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-log4j-2.x</artifactId>
        </dependency>
        <!-- Pinyin4j 依赖，可能用于汉字转拼音等操作 -->
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
        </dependency>
        <!-- Spring Boot 的 AOP 启动器，用于面向切面编程 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <!-- MapStruct 依赖，用于对象映射和转换，版本为 1.5.3.Final -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>1.5.3.Final</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>1.5.3.Final</version>
        </dependency>
        <!-- Jasypt 启动器，用于数据库加密 -->
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
        </dependency>
        <!-- 以下是单元测试相关的依赖，范围为 test，表示仅在测试时使用 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.opentest4j</groupId>
            <artifactId>opentest4j</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.jacoco</groupId>
            <artifactId>org.jacoco.agent</artifactId>
            <classifier>runtime</classifier>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-mapper-asl</artifactId>
        </dependency>
        <!-- 自定义的系统数据库通用组件，版本为 2.0.0 -->
        <dependency>
            <groupId>com.snbc.bbpf</groupId>
            <artifactId>bbpf-system-db-common</artifactId>
            <version>2.0.0</version>
        </dependency>
        <!-- JWT 工具类加密属性配置依赖，包括 API、Jackson 序列化和实现部分 -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
        </dependency>
        <!-- Commons-IO 依赖，可能用于文件操作，排除了 Spring Cloud Gateway 服务器 -->
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-cloud-gateway-server</artifactId>
                    <groupId>org.springframework.cloud</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- Sentinel 依赖，用于服务限流、熔断等功能 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>
        <!-- Excel XML 导入相关依赖，版本为 4.0.8 -->
        <dependency>
            <groupId>org.antlr</groupId>
            <artifactId>ST4</artifactId>
            <version>4.0.8</version>
            <scope>compile</scope>
        </dependency>
        <!-- Spring Boot 的 AMQP 启动器，可能用于消息队列操作 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.amqp</groupId>
            <artifactId>spring-amqp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.amqp</groupId>
            <artifactId>spring-rabbit</artifactId>
        </dependency>
        <dependency>
            <groupId>com.rabbitmq</groupId>
            <artifactId>amqp-client</artifactId>
        </dependency>
        <!-- MySQL 连接器，用于连接 MySQL 数据库 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <!-- 自定义的安全组件启动器，版本为 2.0.3 -->
        <dependency>
            <groupId>com.snbc.bbpf</groupId>
            <artifactId>bbpf-component-security-spring-boot-starter</artifactId>
            <version>2.0.3</version>
        </dependency>
        <!-- 自定义的通用组件 -->
        <dependency>
            <groupId>com.snbc.bbpf</groupId>
            <artifactId>bbpf-commons</artifactId>
        </dependency>

    </dependencies>
    <!-- 项目的构建配置信息 -->
    <build>
        <!-- 项目构建后的最终名称 -->
        <finalName>bbpf-system-manager</finalName>
        <!-- 项目的资源文件目录配置 -->
        <resources>
            <!-- 包含 src/main/resources 目录下的资源文件 -->
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <!-- 包含 src/main/java 目录下的 xml 文件 -->
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <!-- 项目使用的插件列表 -->
        <plugins>
            <!-- Spring Boot 的 Maven 插件，用于将应用程序打包为可执行的 JAR 文件 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <finalName>${project.build.finalName}</finalName>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- Maven 的 Surefire 插件，用于运行单元测试 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <!-- 系统属性变量，用于存储 Jacoco 代理文件的位置 -->
                    <systemPropertyVariables>
                        <jacoco-agent.destfile>${project.build.directory}/jacoco.exec</jacoco-agent.destfile>
                    </systemPropertyVariables>
                </configuration>
            </plugin>
            <!-- Jacoco 插件，用于代码覆盖率分析 -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <executions>
                    <!-- 对代码进行插桩操作，用于代码覆盖率统计 -->
                    <execution>
                        <id>default-instrument</id>
                        <goals>
                            <goal>instrument</goal>
                        </goals>
                    </execution>
                    <!-- 恢复插桩后的类，将代码恢复到原始状态 -->
                    <execution>
                        <id>default-restore-instrumented-classes</id>
                        <goals>
                            <goal>restore-instrumented-classes</goal>
                        </goals>
                    </execution>
                    <!-- 在单元测试前准备 Jacoco 代理，进行插桩操作 -->
                    <execution>
                        <id>pre-unit-test</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                        <configuration>
                            <!-- 是否追加到已有的 Jacoco 数据文件，这里设置为不追加 -->
                            <append>false</append>
                            <!-- Jacoco 数据文件的存储位置 -->
                            <destFile>${basedir}/target/jacoco.exec</destFile>
                            <!-- 将 Jacoco 代理的配置存储在 surefireArgLine 属性中 -->
                            <propertyName>surefireArgLine</propertyName>
                        </configuration>
                    </execution>
                    <!-- 在 prepare-package 阶段生成代码覆盖率报告 -->
                    <execution>
                        <id>default-report</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                        <configuration>
                            <!-- 用于生成报告的数据文件位置 -->
                            <dataFile>${basedir}/target/jacoco.exec</dataFile>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- Maven 编译器插件，用于编译 Java 代码 -->
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <!-- 编译代码使用的 Java 版本 -->
                    <source>${java.version}</source>
                    <!-- 编译后代码的目标 Java 版本 -->
                    <target>${java.version}</target>
                    <!-- 代码的编码方式，使用 UTF-8 -->
                    <encoding>UTF-8</encoding>
                    <!-- 编译器参数，-parameters 可在运行时保留方法的参数名称 -->
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <!-- 项目的配置文件，可根据不同的配置激活不同的依赖 -->
    <profiles>
        <!-- 针对 OpenGauss 数据库的配置文件 -->
        <profile>
            <id>opengauss</id>
            <activation>
                <!-- 默认不激活该配置文件 -->
                <activeByDefault>false</activeByDefault>
            </activation>
            <dependencies>
                <!-- 依赖 OpenGauss 数据库相关的组件，版本使用项目的版本 -->
                <dependency>
                    <groupId>com.snbc.bbpf</groupId>
                    <artifactId>bbpf-system-db-opengauss</artifactId>
                    <version>${project.version}</version>
                </dependency>
            </dependencies>
        </profile>
        <!-- 针对 MySQL 数据库的配置文件，默认激活 -->
        <profile>
            <id>mysql</id>
            <activation>
                <!-- 默认激活该配置文件 -->
                <activeByDefault>true</activeByDefault>
            </activation>
            <dependencies>
                <!-- 依赖 MySQL 数据库相关的组件，版本使用项目的版本 -->
                <dependency>
                    <groupId>com.snbc.bbpf</groupId>
                    <artifactId>bbpf-system-db-mysql</artifactId>
                    <version>${project.version}</version>
                </dependency>
            </dependencies>
            <properties>
                <!-- 自定义属性，可能用于区分使用的数据库模块 -->
                <db-module>bbpf-system-db-mysql</db-module>
            </properties>
        </profile>
    </profiles>
</project>