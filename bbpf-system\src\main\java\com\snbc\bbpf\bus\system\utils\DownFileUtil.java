/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.utils;

import com.snbc.bbpf.bus.system.constans.CommonConstant;
import com.snbc.bbpf.bus.system.constans.NumberConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

public final class DownFileUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(DownFileUtil.class);
    /**
     * wjc1 增加
     *该类都是静态变量，
     * 不需要公有的构造方法
     *1、声明一个私有的构造方法
     *2、将类声明成final
     * 不能有空方法
     * 所以throw IllegalStateException
     */
    private DownFileUtil() {
        throw new IllegalStateException("DownFileUtil class");
    }

    public static void downloadEx(HttpServletRequest request, HttpServletResponse response, String fileFullName) throws IOException {
        request.setCharacterEncoding(CommonConstant.CHARSET_UTF8);
        long offBegin = 0;// 默认从0开始
        String offStr = request.getHeader("offBegin");// 从header中获取下载的起始点
        if (offStr != null) {
            Long offValue = Long.valueOf(offStr);
            if (offValue != null && offValue.intValue() > 0) {
                offBegin = offValue.intValue();
            }
        }
        // 获取下载文件
        File file = new File(fileFullName);
        long fileLength = file.length();
        String softName = file.getName();
        // 设置文件输出类型
        response.setContentType("application/octet-stream");
        response.setHeader("Content-disposition", "attachment; filename="
                + new String(softName.getBytes(CommonConstant.CHARSET_UTF8), CommonConstant.CHARSET_ISO8859));
        // 设置输出长度
        response.setHeader("Content-Length", String.valueOf(fileLength - offBegin));
        // 获取文件输入流\ 输出流
        ServletOutputStream servletOutputStream = response.getOutputStream();
        try (FileInputStream fin=new FileInputStream(file);
             BufferedInputStream bis=new BufferedInputStream(fin);
             BufferedOutputStream bos = new BufferedOutputStream(servletOutputStream)) {
            byte[] buff = new byte[NumberConstant.NO_TWO_K_FORTYE];
            int bytesRead;
            if (offBegin > 0) {
                // wjc1 sonar 2019-6-10 8:34:40
                long size = bis.skip(offBegin);
                if (size > offBegin) {
                    LOGGER.warn("The readable length does not match when downloading the file ");
                }
            }
            while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
                bos.write(buff, 0, bytesRead);
            }
            bos.flush();
        }
    }
}
