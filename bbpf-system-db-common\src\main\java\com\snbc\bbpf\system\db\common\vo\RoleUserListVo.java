/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.vo;

import com.github.pagehelper.PageInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: RoleUserListVo
 * @Description: 返回角色管理的用户信息
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/5/20
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RoleUserListVo {
    /**
     * 角色信息
     */
    private RoleDetail roleInfo;
    /**
     * 用户分页信息
     */
    private PageInfo<RoleUserPage> userPage;
}
