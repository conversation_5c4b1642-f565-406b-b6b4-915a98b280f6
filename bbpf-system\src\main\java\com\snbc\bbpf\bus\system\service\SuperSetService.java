/*
 * 版权所有 2009-2024山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.bus.system.service;

import com.snbc.bbpf.system.db.common.vo.SuperSetUserVo;

/**
 * superset逻辑处理
 *
 * @ClassName: SuperSetService
 * @module: SI-bbpf-system
 * @Author: wangsong
 * @date: 2024/3/16
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
public interface SuperSetService {
    SuperSetUserVo getUser();

    SuperSetUserVo getDataPermission(String userId);
}
