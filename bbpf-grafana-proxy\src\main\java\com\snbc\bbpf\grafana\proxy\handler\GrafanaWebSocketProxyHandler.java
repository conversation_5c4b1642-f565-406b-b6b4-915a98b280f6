package com.snbc.bbpf.grafana.proxy.handler;

import com.snbc.bbpf.grafana.proxy.config.GrafanaProxyConfig;
import com.snbc.bbpf.grafana.proxy.filter.JwtAuthenticationFilter;
import com.snbc.bbpf.grafana.proxy.service.GrafanaAuthService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.web.socket.WebSocketHttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;
import org.springframework.web.socket.client.WebSocketClient;
import org.springframework.web.socket.client.standard.StandardWebSocketClient;

import java.net.URI;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.ExecutionException;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;

/**
 * Grafana WebSocket代理处理器
 * 
 * 处理WebSocket连接的代理转发，支持Grafana Live功能
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2024-12-13
 */
@Component
public class GrafanaWebSocketProxyHandler implements WebSocketHandler {

	private static final Logger logger = LoggerFactory.getLogger(GrafanaWebSocketProxyHandler.class);

	@Autowired
	private GrafanaProxyConfig config;

	@Autowired
	private GrafanaAuthService authService;

	// 存储客户端会话和对应的Grafana WebSocket会话的映射
	private final ConcurrentMap<String, WebSocketSession> clientSessions = new ConcurrentHashMap<>();
	private final ConcurrentMap<String, WebSocketSession> grafanaSessions = new ConcurrentHashMap<>();
	private final WebSocketClient webSocketClient = new StandardWebSocketClient();

	@Override
	public void afterConnectionEstablished(WebSocketSession session) throws Exception {
		logger.info("WebSocket connection established: {}", session.getId());

		// 从会话中获取用户信息（需要在握手拦截器中设置）
		String userId = (String) session.getAttributes().get("userId");
		if (userId == null) {
			logger.warn("No user ID found in WebSocket session, closing connection");
			session.close(CloseStatus.NOT_ACCEPTABLE.withReason("Authentication required"));
			return;
		}

		logger.info("WebSocket connection for user: {}", userId);

		// 建立到Grafana的WebSocket连接
		establishGrafanaConnection(session, userId);
	}

	@Override
	public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
		logger.debug("Received message from client {}: {}", session.getId(), message.getPayload());

		// 转发消息到Grafana
		WebSocketSession grafanaSession = grafanaSessions.get(session.getId());
		if (grafanaSession != null && grafanaSession.isOpen()) {
			grafanaSession.sendMessage(message);
			logger.debug("Forwarded message to Grafana for session: {}", session.getId());
		} else {
			logger.warn("No active Grafana session found for client session: {}", session.getId());
			session.close(CloseStatus.SERVER_ERROR.withReason("Lost connection to Grafana"));
		}
	}

	@Override
	public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
		logger.error("WebSocket transport error for session {}: {}", session.getId(), exception.getMessage(),
				exception);
		cleanupSession(session.getId());
	}

	@Override
	public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
		logger.info("WebSocket connection closed for session {}: {}", session.getId(), closeStatus);
		cleanupSession(session.getId());
	}

	@Override
	public boolean supportsPartialMessages() {
		return false;
	}

	/**
	 * 异步建立到Grafana的WebSocket连接
	 */
	private void establishGrafanaConnection(WebSocketSession session, String userId) {
		try {
			String grafanaWsUrl = buildGrafanaWebSocketUrl();
			logger.info("Connecting to Grafana WebSocket: {}", grafanaWsUrl);

			// 先存储客户端会话
			clientSessions.put(session.getId(), session);

			// 使用异步方式建立连接
			ListenableFuture<WebSocketSession> future = webSocketClient.doHandshake(
					new GrafanaWebSocketHandler(session, userId),
					createWebSocketHeaders(userId),
					URI.create(grafanaWsUrl));

			// 异步处理连接结果
			future.addCallback(new ListenableFutureCallback<WebSocketSession>() {
				@Override
				public void onSuccess(WebSocketSession grafanaSession) {
					handleConnectionSuccess(session, userId, grafanaSession);
				}

				@Override
				public void onFailure(Throwable throwable) {
					handleConnectionFailure(session, userId, throwable);
				}
			});

		} catch (Exception e) {
			logger.error("Failed to initiate connection to Grafana WebSocket: {}", e.getMessage(), e);
			handleConnectionFailure(session, userId, e);
		}
	}

	/**
	 * 处理连接成功
	 */
	private void handleConnectionSuccess(WebSocketSession session, String userId, WebSocketSession grafanaSession) {
		try {
			// 存储Grafana会话映射
			grafanaSessions.put(session.getId(), grafanaSession);
			logger.info("Successfully established proxy connection for session: {} (user: {})", session.getId(),
					userId);
		} catch (Exception e) {
			logger.error("Error handling successful connection for session {}: {}", session.getId(), e.getMessage());
			handleConnectionFailure(session, userId, e);
		}
	}

	/**
	 * 处理连接失败
	 */
	private void handleConnectionFailure(WebSocketSession session, String userId, Throwable throwable) {
		try {
			String errorMessage;
			CloseStatus closeStatus;

			if (throwable instanceof TimeoutException) {
				errorMessage = "Connection to Grafana timed out";
				closeStatus = CloseStatus.SERVER_ERROR.withReason("Connection timeout");
				logger.error("Grafana WebSocket connection timeout for user {}: {}", userId, throwable.getMessage());
			} else if (throwable instanceof ExecutionException) {
				errorMessage = "Failed to connect to Grafana";
				closeStatus = CloseStatus.SERVER_ERROR.withReason("Connection failed");
				logger.error("Grafana WebSocket connection failed for user {}: {}", userId,
						throwable.getCause() != null ? throwable.getCause().getMessage() : throwable.getMessage());
			} else {
				errorMessage = "Unexpected error connecting to Grafana";
				closeStatus = CloseStatus.SERVER_ERROR.withReason("Unexpected error");
				logger.error("Unexpected error connecting to Grafana for user {}: {}", userId, throwable.getMessage(),
						throwable);
			}

			// 清理客户端会话
			clientSessions.remove(session.getId());

			// 关闭客户端连接
			if (session.isOpen()) {
				session.close(closeStatus);
			}

		} catch (Exception e) {
			logger.error("Error handling connection failure for session {}: {}", session.getId(), e.getMessage());
		}
	}

	/**
	 * 构建Grafana WebSocket URL
	 */
	private String buildGrafanaWebSocketUrl() {
		String baseUrl = config.getGrafanaBaseUrl();
		// 将HTTP URL转换为WebSocket URL
		String wsUrl = baseUrl.replace("http://", "ws://").replace("https://", "wss://");
		return wsUrl + "/api/live/ws";
	}

	/**
	 * 创建WebSocket请求头，包含认证信息
	 */
	private WebSocketHttpHeaders createWebSocketHeaders(String userId) {
		WebSocketHttpHeaders headers = new WebSocketHttpHeaders();

		try {
			// 获取认证token
			String authToken = authService.getAuthToken(userId);
			if (authToken != null) {
				headers.add("Authorization", authToken);
			}

			// 如果启用了Auth Proxy模式，添加用户头
			if (config.isEnableAuthProxy()) {
				headers.add(config.getAuthProxyHeaderName(), userId);
			}

			// 添加代理标识
			headers.add("X-BBPF-PROXY", "true");
			headers.add("X-BBPF-USER-ID", userId);

		} catch (Exception e) {
			logger.error("Failed to create WebSocket headers for user {}: {}", userId, e.getMessage());
		}

		return headers;
	}

	/**
	 * 清理会话资源
	 */
	private void cleanupSession(String sessionId) {
		try {
			WebSocketSession clientSession = clientSessions.remove(sessionId);
			WebSocketSession grafanaSession = grafanaSessions.remove(sessionId);

			if (clientSession != null && clientSession.isOpen()) {
				clientSession.close();
			}

			if (grafanaSession != null && grafanaSession.isOpen()) {
				grafanaSession.close();
			}

			logger.debug("Cleaned up sessions for: {}", sessionId);

		} catch (Exception e) {
			logger.error("Error cleaning up session {}: {}", sessionId, e.getMessage());
		}
	}

	/**
	 * Grafana WebSocket处理器
	 * 处理从Grafana返回的WebSocket消息
	 */
	private class GrafanaWebSocketHandler implements WebSocketHandler {

		private final WebSocketSession clientSession;
		private final String userId;

		public GrafanaWebSocketHandler(WebSocketSession clientSession, String userId) {
			this.clientSession = clientSession;
			this.userId = userId;
		}

		@Override
		public void afterConnectionEstablished(WebSocketSession session) throws Exception {
			logger.info("Connected to Grafana WebSocket for user: {}", userId);
		}

		@Override
		public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
			logger.debug("Received message from Grafana for user {}: {}", userId, message.getPayload());

			// 转发消息到客户端
			if (clientSession.isOpen()) {
				clientSession.sendMessage(message);
				logger.debug("Forwarded message to client for user: {}", userId);
			} else {
				logger.warn("Client session closed for user: {}", userId);
				session.close();
			}
		}

		@Override
		public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
			logger.error("Grafana WebSocket transport error for user {}: {}", userId, exception.getMessage(),
					exception);
			if (clientSession.isOpen()) {
				clientSession.close(CloseStatus.SERVER_ERROR.withReason("Grafana connection error"));
			}
		}

		@Override
		public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
			logger.info("Grafana WebSocket connection closed for user {}: {}", userId, closeStatus);
			if (clientSession.isOpen()) {
				clientSession.close(closeStatus);
			}
		}

		@Override
		public boolean supportsPartialMessages() {
			return false;
		}
	}
}