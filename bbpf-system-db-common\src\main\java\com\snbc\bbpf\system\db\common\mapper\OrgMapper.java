/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.mapper;

import com.snbc.bbpf.component.dataauth.annotation.IgnoreAuth;
import com.snbc.bbpf.component.security.annotations.Ignore;
import com.snbc.bbpf.system.db.common.dto.OrgItemDto;
import com.snbc.bbpf.system.db.common.dto.OrgNamesDto;
import com.snbc.bbpf.system.db.common.entity.Org;
import com.snbc.bbpf.system.db.common.vo.NoticeOrgVo;
import com.snbc.bbpf.system.db.common.vo.OrgSupervisorVo;
import com.snbc.bbpf.system.db.common.vo.OrgVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName: UserMapper
 * @Description: 数据库交互
 * @module: si-bbpf-system
 * @Author: wangsong
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Mapper
public interface OrgMapper {
    /**
     * 获取组织机构
     *
     * @param org
     * @return
     */
    List<Org> getOrgs(Org org);

    /**
     * 新增组织机构
     *
     * @param org
     */
    void insert(Org org);

    /**
     * 父级机构最大序列
     *
     * @param parentId
     * @return
     */
    int queryMaxOrderByParentId(@Param("parentId") String parentId);

    /**
     * 查询组织机构
     *
     * @param orgId
     * @return
     */
    @IgnoreAuth
    Org selectByPrimaryKey(@Param("orgId") String orgId);

    /**
     * 根据名称查找组织机构
     * @param orgName 组织机构名称
     * @param parentId 父节点id
     * @return
     */
    Org getOrgByName(@Param("orgName") String orgName,@Param("parentId") String parentId);
    /**
     * 修改组织机构
     *
     * @param org
     * @return
     */
    int updateByPrimaryKeySelective(Org org);

    int deleteByPrimaryKey(@Param("orgId") String orgId);

    /**
     * 根据父ID查询组织机构
     *
     * @param parentOrgId
     * @return
     */
    List<Org> queryOrgListByParentId(@Param("parentOrgId") String parentOrgId);

    /**
     * 调整组织机构
     *
     * @param orgList
     */
    void updateOrgList(List<Org> orgList);

    /**
     * 查询所属组织机构
     * @param userId
     * @return
     */
    List<Org> getOrgsByUserId(String userId);

    /**
     * wjc1:根据组织机构数据权限查询当前人应该有的组织机构
     * @date 2023-07-05 12:43
     * @return
     */
    List<Org> getOrgIdPath4DataRule();

    /**
     * wjc1 用户导入用到
     * 根据 组织机构名 查询组织机构id
     */
    String selectIdsByOrgNames(@Param("belongOrgNames") List<String> belongOrgNames);

    List<String> selectOrgIdsByParentId(String orgId);

    void updateOrgPathAndLevel(String oldPath, String newPath);
    /**
     * 将组织机构的负责人清空
     *
     * @param supervisorId
     */
    int updateBySupervisor(String supervisorId);
    /**
     * LJB 查找可用的组织机构算法对象
     */
    List<OrgItemDto> getAllEnableOrgItems();

    /**
     * 查询 组织机构及下级
     * @param orgPathList
     * @return
     */
    @Ignore
    List<String> selectAllOrgIdByOrgPath(@Param("orgPathList") List<String> orgPathList);

    List<OrgNamesDto> selectOrgNamesByUserId(@Param("userIdList") List<String> userIdList);

    // 根据userId查部门主管，wjc1 20220107
    List<OrgSupervisorVo> selectSupervisorByUserId(@Param("userIdList") List<String> userIdList);
    /**
     * 超管，返回所有组织机构树
     * @param orgReq
     * @return
     */
    List<OrgVo> getAllOrgTree(Org orgReq);

    /**
     * 根据userId查询其创建的组织机构
     * @param userId
     * @return
     */
    List<String> getOrgIdByCreateUserId(@Param("userId") String userId);

    List<NoticeOrgVo> getNoticeOrgName(@Param("orgIdList") List<String> orgIdList);
}
