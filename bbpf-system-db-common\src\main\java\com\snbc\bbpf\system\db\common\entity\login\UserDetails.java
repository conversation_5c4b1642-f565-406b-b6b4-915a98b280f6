/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.entity.login;

import java.io.Serializable;

/**
 * @ProjectName: bus-module-sys
 * @Package: com.snbc.bbpf.organize.domain.module
 * @ClassName: UserDetails
 * <p>
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 * @copyright Copyright: 2014-2020
 * @Description: 登录用户TOKEN类
 * @Author: Liangjb
 * @CreateDate: 2020/6/9 14:46
 */
public interface UserDetails extends Serializable {
    String getAuthorities();

    String getPassword();

    String getUsername();

    boolean isAccountNonExpired();

    boolean isAccountNonLocked();

    boolean isCredentialsNonExpired();

    boolean isEnabled();
}
