/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;


/**
 * @ClassName: BusLogVo
 * @Description: bus log entity
 * @module: bbpf-bus-system
 * @Author: liuyi
 * @date: 2021/5/21
 * copyright 2020 barm Inc. All rights reserver
 */
@Data
@AllArgsConstructor
@Builder
public class BusLogVo {


    /**
     * 创建人组织机构id
     */
    private String createOrgId;


    /**
     * 操作类型
     */
    private String logType;


    /** */
    private String remarks;
    /**
     * 日志内容
     */
    private String logContent;
    /**
     * 组织机构名称
     */
    private String orgName;

    /**
     * 组织机构ID
     */
    private String orgId;
    /**
     * 日志时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8"
    )
    private LocalDateTime createTime;
    /**
     * 日志ID
     */
    private String logId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 操作员
     */
    private String userId;
    /**
     * 操作模块
     */
    private String logTarget;

    /**
     * 登录IP
     */
    private String ip;



    public BusLogVo() {
        super();
    }

}
