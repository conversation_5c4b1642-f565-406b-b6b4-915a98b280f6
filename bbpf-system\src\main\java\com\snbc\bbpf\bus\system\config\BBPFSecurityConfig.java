/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * bbpf安全模块
 * 初始化
 * jwt组件配置初始化
 */
@Component
@ConditionalOnProperty(prefix = "bbpf.system.security.jwt", name = "secret")
@ConfigurationProperties(prefix = "bbpf.system.security.jwt")
public class BBPFSecurityConfig {
	/**
	 * 日志
	 */
	private static final Logger LOGGER = LoggerFactory.getLogger(BBPFSecurityConfig.class);

	/**
	 * 请求头key
	 */
	private String header="Authorization";
	/**
	 * 密钥
	 */
	private String secret = "w-oasis123456";
	/**
	 * RSA私钥，用来给JWT加密
	 */
	private String privateKey ;
	/**
	 * RSA公+钥，用来给JWT解密
	 */
	private String publicKey ;
	/**
	 * 过期时间
	 */
	private Long expiration ;
	/**
	 * 请求头value 前缀
	 */
	private String tokenHead = "Bearer";
	/**
	 * 不需要检查的URL，
	 * 多个之间用 ','  隔开
	 */
	private String exceptUrl;
	
	public String getHeader() {
		return header;
	}
	public void setHeader(String header) {
		this.header = header;
	}
	public String getSecret() {
		return secret;
	}
	public void setSecret(String secret) {
		this.secret = secret;
	}
	public Long getExpiration() {
		return expiration;
	}
	public void setExpiration(Long expiration) {
		this.expiration = expiration;
	}
	public String getTokenHead() {
		return tokenHead;
	}
	public void setTokenHead(String tokenHead) {
		this.tokenHead = tokenHead;
	}
	
	public String getExceptUrl() {
		return exceptUrl;
	}
	public void setExceptUrl(String exceptUrl) {
		this.exceptUrl = exceptUrl;
	}
	public String getPrivateKey() {
		return privateKey;
	}
	public void setPrivateKey(String privateKey) {
		this.privateKey = privateKey;
	}

	public String getPublicKey() {
		return publicKey;
	}
	public void setPublicKey(String publicKey) {
		this.publicKey = publicKey;
	}
	/**
	 * 处理需要拦截或者放行的路径
	 * @param filterPath
	 * @return
	 */
	public String[] copyToFilterPath(String[] filterPath){
		if(this.exceptUrl!=null && !"".equals(this.exceptUrl)){
			String[] userFilterPath = this.exceptUrl.split(",");
			List<String> list = new ArrayList(Arrays.asList(filterPath));
			list.addAll(Arrays.asList(userFilterPath));
			String[] str = new String[list.size()];
			list.toArray(str);  
			for(int x=0;x<str.length;x++){
				LOGGER.info(str[x]);
			}
			return str;
		}else{
			return filterPath;
		}
	}
}
