/**
 * 版权所有 2018-2021山东新北洋信息技术股份有限公司
 * 保留所有权利。
 */
package com.snbc.bbpf.bus.system.queue;


import com.snbc.bbpf.buslog.entity.BusLog;
import org.springframework.util.CollectionUtils;

import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * @Description: 信息变更通知待发送队列
 * @Author: zhngnan
 * @CreateDate: 2020/3/24 01:35
 */
public final class BusLogQueue {

    //ConcurrentLinkedQueue安全队列，大小不限制
    private static ConcurrentLinkedQueue<BusLog> readyBusLogQueue = new ConcurrentLinkedQueue<>();

    //自身的引用
    private static BusLogQueue busLogQueue;

    //私有的无参构造
    private BusLogQueue() {

    }

    //单例模式，实例获取的方法
    public static synchronized BusLogQueue getInstance() {
        if (busLogQueue == null) {
            busLogQueue = new BusLogQueue();
        }
        return busLogQueue;
    }

    /**
     * 插入消息方法
     * @param busLog
     */
    public static void addLog(BusLog busLog) {
        readyBusLogQueue.add(busLog);
    }

    /**
     * 取出消息方法
     * @return
     */
    public static BusLog pollLog() {
        if (null != busLogQueue && !isEmpty()) {
            return readyBusLogQueue.poll();
        } else {
            return null;
        }
    }

    /**
     * 判空方法
     * @return
     */
    public static boolean isEmpty() {
        return CollectionUtils.isEmpty(readyBusLogQueue);
    }
}
