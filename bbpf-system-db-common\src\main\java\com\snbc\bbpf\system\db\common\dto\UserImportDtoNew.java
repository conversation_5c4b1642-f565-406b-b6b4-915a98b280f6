/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.snbc.bbpf.commons.strings.security.annotation.Desensitized;
import com.snbc.bbpf.commons.strings.security.annotation.SensitiveType;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * @ClassName: UserRequest
 * 导入用户请求实体类
 * @module: si-bbpf-system
 * @Author: wjc1
 * @date: 2021/5/19 14:07
 */
@Data
public class UserImportDtoNew {
    @ExcelProperty(value = "序号", index = 0)
    private String number;
    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名", index = 1)
    @Length(max=20, message="姓名最大长度为20")
    private String userName;
    /**
     * 工号
     */
    @ExcelProperty(value = "工号", index = 2)
    @Length(max=10, message="工号最大长度为10")
    private String jobNumber;

    @ExcelProperty(value = "手机号码", index = 3)
    @Desensitized(type = SensitiveType.MOBILE_PHONE)
    private String phone;

    @ExcelProperty(value = "邮箱", index = 4)
    private String email;
    /**
     * 所属组织机构id
     * */
    @ExcelProperty(value = "部门", index = 5)
    private String belongOrgNames;
    /**
     * 部门描述
     */
    @ExcelProperty(value = "部门描述", index = 6)
    private String orgRemarks;
    /**
     * 是否为部门主管(是/否)
     */
    @ExcelProperty(value = "是否为部门主管(是/否)", index = 7)
    private String isManagers;
    /**
     * 所属角色id
     * */
    @ExcelProperty(value = "角色", index = 8)
    private String belongRoleNames;
    /**
     * 状态
     */
    @ExcelProperty(value = "状态", index = 9)
    private String hasLockName;
    /**
     * 导入报错的错误信息
     * */
    @ExcelProperty(value = "错误信息", index = 10)
    private String errorMsg;

    @Override
    public String toString() {
        return "UserImportDtoNew{" +
                "number='" + number + '\'' +
                ", userName='" + userName + '\'' +
                ", jobNumber='" + jobNumber + '\'' +
                ", phone='" + phone + '\'' +
                ", email='" + email + '\'' +
                ", belongOrgNames='" + belongOrgNames + '\'' +
                ", orgRemarks='" + orgRemarks + '\'' +
                ", isManagers='" + isManagers + '\'' +
                ", belongRoleNames='" + belongRoleNames + '\'' +
                ", hasLockName='" + hasLockName + '\'' +
                ", errorMsg='" + errorMsg + '\'' +
                '}';
    }
}
