/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.controller;

import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.bus.system.service.ParamService;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.bus.system.utils.ResultUtil;
import com.snbc.bbpf.buslog.annotations.Buslog;
import com.snbc.bbpf.buslog.annotations.OperationType;
import com.snbc.bbpf.system.db.common.entity.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @ClassName: ParamController
 * @Description: 配置控制器
 * @module: si-bbpf-system
 * @Author: wjc
 * @date: 2021/5/17
 */
@RestController
@RequestMapping("/console/param")
public class ParamController {
    @Autowired
    private ParamService paramService;

    /**
      * @Description: 配置信息新增
      * @Author:  wjc1
      * @param :         param
      * @CreateDate: 2021/5/23 9:14
      * @UpdateDate: 2021/5/23 9:14
       * @return :        CommonResp
     */
    @PutMapping(value = "/addParam", consumes = {"application/json"})
    @Buslog(target = "confManage", opration = "addParam", zh = "新增参数【参数名称：${paramName},参数编号：${paramCode}】",
            en = "New parameter [parameter name:${paramName}, parameter number:${paramCode}]")
    public CommonResp<Object> addParam(@Validated @RequestBody Param param) throws BusinessException {
        param.setCreateUserId(CurrentUser.getUserId());
        ErrorMessage.PARAM_ID_EXIST.assertNull(param.getParamId());
        paramService.saveOrUpdateParam(param);
        return CommonResp.builder().head(ResultUtil.success()).build();
    }
    /**
     * @Description: 配置信息更新
     * @Author:  wjc1
     * @param :         param
     * @CreateDate: 2021/5/23 9:14
     * @UpdateDate: 2021/5/23 9:14
     * @return :        CommonResp
     */
    @PutMapping(value = "/updateParam", consumes = {"application/json"})
    @Buslog(oprationType = OperationType.MODIFY,opration = "ModifyParam", target = "confManage",type = Param.class )
    public CommonResp<Object> updateParam(@Validated @RequestBody Param param) throws BusinessException {
        param.setCreateUserId(CurrentUser.getUserId());
        ErrorMessage.PARAM_ID_NULL.assertNotNull(param.getParamId());
        paramService.saveOrUpdateParam(param);
        return CommonResp.builder().head(ResultUtil.success()).build();
    }
    /**
     * @Description: 配置信息删除
     * @Author:  wjc1
     * @param :         paramId
     * @CreateDate: 2021/5/23 9:14
     * @UpdateDate: 2021/5/23 9:14
     * @return :        CommonResp
     */
    @DeleteMapping(value = "/delParam")
    @Buslog (opration = "delParam", zh = "删除参数【参数名称：${paramName}，参数编号：${paramCode}】",
            en = "Delete parameter [parameter name:${paramName}, parameter number:${paramCode}]",target = "confManage")
    public CommonResp<Object> delParam(@RequestParam String paramIds) throws BusinessException {
        paramService.batchDeleteParam(paramIds);
        return CommonResp.builder().head(ResultUtil.success()).build();
    }
    /**
     * @Description: 根据主键id查询配置信息：用于返显详情
     * @Author:  wjc1
     * @param :         paramId
     * @CreateDate: 2021/5/23 9:14
     * @UpdateDate: 2021/5/23 9:14
     * @return :        CommonResp
     */
    @GetMapping(value = "/getParamById")
    public CommonResp<Param> getParam(@RequestParam String paramId) throws BusinessException {
        return CommonResp.<Param>builder().head(ResultUtil.success()).body(paramService.getParamByPrimary(paramId)).build();
    }
    /**
     * @Description: 根据配置编码查询配置信息：用于验证 配置编码是否重复
     * @Author:  wjc1
     * @param :         paramIds
     * @CreateDate: 2021/5/23 9:14
     * @UpdateDate: 2021/5/23 9:14
     * @return :        CommonResp
     */
    @GetMapping(value = "/getParamByCode")
    public CommonResp<Param> getParamByCode(@RequestParam String paramCode) throws BusinessException {
        return CommonResp.<Param>builder().head(ResultUtil.success()).body(paramService.getParamByCode(paramCode)).build();
    }
    /**
     * @Description: 查询配置列表
     * @Author:  wjc1
     * @param :         paramName
     * @param :         paramTypeCode
     * @param :         pageNum
     * @param :         pageSize
     * @CreateDate: 2021/5/23 9:14
     * @UpdateDate: 2021/5/23 9:14
     * @return :        CommonResp
     */
    @GetMapping(value = "/getParamList")
    public CommonResp<PageInfo<Param>> getParamList(String paramName,String paramTypeName
            ,@RequestParam Integer pageNum,@RequestParam Integer pageSize) throws BusinessException {
        PageInfo<Param> info=paramService.queryParamListPage(paramName, paramTypeName, pageNum, pageSize);
        return CommonResp.<PageInfo<Param>>builder().head(ResultUtil.success()).body(info).build();
    }

    /**
     * @Description: 查询所有配置类型,用于查询条件 下拉选择
     * @Author:  wjc1
     * @param :         paramName
     * @param :         paramTypeCode
     * @param :         pageNum
     * @param :         pageSize
     * @CreateDate: 2021/5/23 9:14
     * @UpdateDate: 2021/5/23 9:14
     * @return :        CommonResp
     */
    @GetMapping(value = "/queryParamCode")
    public CommonResp<List<String>> queryParamCode() throws BusinessException {
        return CommonResp.<List<String>>builder().head(ResultUtil.success()).body(paramService.queryParamCode()).build();
    }
}
