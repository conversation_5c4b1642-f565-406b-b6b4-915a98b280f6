package com.snbc.bbpf.grafana.proxy.service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Grafana代理服务接口
 * 
 * 定义了代理服务的核心功能：
 * 1. 请求转发到Grafana
 * 2. 响应处理和返回
 * 3. 权限验证集成
 * 4. 请求/响应修改
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2024-12-13
 */
public interface GrafanaProxyService {

    /**
     * 代理请求到Grafana
     * 
     * @param request 原始HTTP请求
     * @param response HTTP响应
     * @param targetPath 目标路径（去除代理前缀后的路径）
     * @param userId 用户ID（已通过JWT验证）
     * @return boolean true表示代理成功，false表示代理失败
     */
    boolean proxyRequest(HttpServletRequest request, HttpServletResponse response, 
                        String targetPath, String userId);

    /**
     * 验证用户是否有权限访问指定的Grafana资源
     * 
     * @param userId 用户ID
     * @param requestPath 请求路径
     * @param httpMethod HTTP方法
     * @return boolean true表示有权限，false表示无权限
     */
    boolean hasAccessPermission(String userId, String requestPath, String httpMethod);

    /**
     * 构建目标Grafana URL
     * 
     * @param targetPath 目标路径
     * @param queryString 查询字符串
     * @return String 完整的Grafana URL
     */
    String buildTargetUrl(String targetPath, String queryString);

    /**
     * 处理仪表盘访问请求
     * 
     * @param request 原始HTTP请求
     * @param response HTTP响应
     * @param dashboardId 仪表盘ID
     * @param userId 用户ID
     * @return boolean true表示处理成功，false表示处理失败
     */
    boolean handleDashboardAccess(HttpServletRequest request, HttpServletResponse response, 
                                 String dashboardId, String userId);

    /**
     * 处理API请求
     * 
     * @param request 原始HTTP请求
     * @param response HTTP响应
     * @param apiPath API路径
     * @param userId 用户ID
     * @return boolean true表示处理成功，false表示处理失败
     */
    boolean handleApiRequest(HttpServletRequest request, HttpServletResponse response, 
                            String apiPath, String userId);

    /**
     * 处理数据导出请求
     * 
     * @param request 原始HTTP请求
     * @param response HTTP响应
     * @param resourceId 资源ID
     * @param userId 用户ID
     * @return boolean true表示处理成功，false表示处理失败
     */
    boolean handleDataExport(HttpServletRequest request, HttpServletResponse response, 
                            String resourceId, String userId);

    /**
     * 修改请求头以适配Grafana
     * 
     * @param request 原始HTTP请求
     * @param userId 用户ID
     * @param username 用户名
     * @return java.util.Map<String, String> 修改后的请求头
     */
    java.util.Map<String, String> modifyRequestHeaders(HttpServletRequest request, 
                                                       String userId, String username);

    /**
     * 修改响应内容（如替换URL、添加权限标识等）
     * 
     * @param originalContent 原始响应内容
     * @param userId 用户ID
     * @param requestPath 请求路径
     * @return String 修改后的响应内容
     */
    String modifyResponseContent(String originalContent, String userId, String requestPath);

    /**
     * 检查Grafana服务是否可用
     * 
     * @return boolean true表示可用，false表示不可用
     */
    boolean isGrafanaAvailable();

    /**
     * 获取代理服务统计信息
     * 
     * @return java.util.Map<String, Object> 统计信息
     */
    java.util.Map<String, Object> getProxyStatistics();

    /**
     * 获取面板的PNG图像
     * @param userId 用户ID
     * @param dashboardUid 仪表盘UID
     * @param panelId 面板ID
     * @param from 时间范围起始
     * @param to 时间范围结束
     * @param width 图片宽度
     * @param height 图片高度
     * @param request HTTP请求
     * @return byte[] 图片字节数组
     */
    byte[] getPanelAsPng(String userId, String dashboardUid, int panelId, long from, long to, int width, int height, HttpServletRequest request);
}