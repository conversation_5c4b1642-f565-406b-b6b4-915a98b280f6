/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.utils;

import com.snbc.bbpf.bus.system.config.BBPFSecurityConfig;
import com.snbc.bbpf.bus.system.constans.CommonConstant;
import com.snbc.bbpf.system.db.common.dto.JwtCamlsResult;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.impl.DefaultClaims;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;

import javax.annotation.PostConstruct;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.snbc.bbpf.bus.system.dto.TokenPayload;
/**
 * @ClassName: JwtTokenUtil
 * @Description: JWTTokn工具类
 * 将版本进行升级到0.11.2
 * 对方法进行优化，主要是登录的时候使用
 * 提供一个获取TOKEN，跟解析TOKEN ,对过期时间进行判断，以及对TOKEN进行解密处理废除过去验签
 * @module: si-bbpf-system
 * @Author: Liangjb
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Component
public class JwtTokenUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(JwtTokenUtil.class);
    private static final String CLAIM_KEY_USER_ACCOUNT_ID = "userId";
    private static final String CLAIM_KEY_USER_TENANT_ID = "tenantId";
    private static final String CLAIM_KEY_USER_ACCOUNT = "userName";
    private static final String CLAIM_KEY_CREATED = "created";
    private static final String CLAIM_KEY_ROLE_IDS = "roleIds";
    private static final String CLAIM_KEY_SYS_TYPE = "sysType";
    private static final String CLAIM_KEY_SESSION_ID = "sessionId";
    private static final String ERROR_SET = "用户TOKEN解析错误";
    private static final String AFTER_ROR_SET = "用户TOKEN过期";
    private static final String TIME_DATE_ERROR = "用户日期转换错误";
    private static final String REFRESH_TOKEN_ERROR = "刷新用户TOKEN错误";
    private static final String NO_SUCH_KEY = "转换密钥没有算法";
    private static final String INVALID_KEY_SPEC = "转换密钥KEY失败";
    private static final String IO_EXCEPTION_KEY = "转换密钥IO失败";
    /**
     * JWT 配置信息类，含TOKEN过期时间（小时），公私钥字符串
     */
    @Autowired
    private BBPFSecurityConfig securityConfig;

    /**
     * API调用认证工具类，公私钥
     */
    private PrivateKey privateKey;
    private PublicKey publicKey;

    /**
     * 指定该方法在对象被创建后马上调用 相当于配置文件中的init-method属性，含TOKEN过期时间（小时），公私钥字符串
     */
    @PostConstruct
    public void init(){
        privateKey=getPrivateKey(securityConfig.getPrivateKey());
        publicKey=getPublicKey(securityConfig.getPublicKey());
    }
    /**
     * 生成token
     * 使用TokenPayload对象组装参数
     * @param payload 包含所有令牌信息的TokenPayload对象
     * @return TOKEN
     */
    public String generateToken(TokenPayload payload) {
        DefaultClaims defaultClaims = new DefaultClaims();
        defaultClaims.put(CLAIM_KEY_USER_ACCOUNT_ID, payload.getUserId());
        defaultClaims.put(CLAIM_KEY_USER_TENANT_ID, payload.getTenantId());
        defaultClaims.put(CLAIM_KEY_USER_ACCOUNT, payload.getUserName());
        //转成DATA格式才能正常赋值
        defaultClaims.put(CLAIM_KEY_CREATED,
                Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant()));
        defaultClaims.put(CLAIM_KEY_ROLE_IDS, getAuthorities(payload.getRoleIds()));
        defaultClaims.put(CLAIM_KEY_SYS_TYPE, payload.getSysType());
        // 添加会话ID作为唯一标识，如果提供了则使用，否则生成新的
        String sessionId = payload.getSessionId();
        if (sessionId == null || sessionId.isEmpty()) {
            sessionId = UUID.randomUUID().toString();
        }
        defaultClaims.put(CLAIM_KEY_SESSION_ID, sessionId);
        defaultClaims.setSubject(payload.getUserName());
        return generateToken(defaultClaims);
    }
    
    /**
     * 生成token
     * 组装参数
     * @param id  ID
     * @param userName 用户名
     * @param password 密码
     * @param roleIds  角色ID
     * @param sysType  系统类型
     * @param tenantId 租户ID
     * @return   TOKEN
     * @deprecated 请使用 {@link #generateToken(TokenPayload)} 替代
     */
    @Deprecated
    public String generateToken(String id, String userName, String password,
                                List<String> roleIds, String sysType, String tenantId) {
        TokenPayload payload = TokenPayload.builder()
                .userId(id)
                .userName(userName)
                .password(password)
                .roleIds(roleIds)
                .sysType(sysType)
                .tenantId(tenantId)
                .build();
        return generateToken(payload);
    }
    /**
     * 生成token
     * 过期时间的最小单位为小时
     * @param claims  具体参数
     * @return   TOKEN
     */
    public String generateToken(Map<String, Object> claims) {
        return Base64Utils.encodeToUrlSafeString(Jwts.builder()
                .setClaims(claims)
                //s设置过时时间
                .setExpiration(Date.from( LocalDateTime.now().plusHours(securityConfig.getExpiration() ).
                        atZone( ZoneId.systemDefault()).toInstant()))
                .signWith(privateKey)
                .compact().getBytes());
    }

    /**
     * 从token中获取claims
     *
     * @param token
     * @return
     */
    private JwtCamlsResult getClaimsFromToken(String token) {
        JwtCamlsResult result=JwtCamlsResult.builder().status(CommonConstant.ONE).build() ;
        try {
            result.setClaims( Jwts.parserBuilder().setSigningKey(publicKey).build()
                    .parseClaimsJws(new String(Base64Utils.decodeFromString(token), CommonConstant.CHARSET_UTF8)).getBody());
        } catch (ExpiredJwtException e) {
            // 在解析JWT字符串时，如果‘过期时间字段’已经早于当前时间，将会抛出ExpiredJwtException异常，说明本次请求已经失效
            result.setStatus(CommonConstant.ZERO);
            LOGGER.error(AFTER_ROR_SET, e);
        } catch (Exception ex) {
            result.setStatus(CommonConstant.EIGHT);
            LOGGER.error(ERROR_SET, ex);
        }
        return result;
    }
    /**
     * 从token的Expiration中获取具体的过期时间
     * 需要从date 转成localDateTime
     * *
     * @param token
     * @return
     */
    public LocalDateTime generateTokenToken(String token) {
        JwtCamlsResult claims= getClaimsFromToken(token);
        return (CommonConstant.ONE!=claims.getStatus())?null:
                claims.getClaims().getExpiration().toInstant().
                        atZone( ZoneId.systemDefault()).toLocalDateTime();
    }

    /**
     * 从token中Claimi具体参数CLAIM_KEY_CREATED获取创建时间
     *将DATE 类型强转为LocalData
     * @param token
     * @return
     */
    public LocalDateTime getCreatedDateFromToken(String token) {
        final JwtCamlsResult claims = getClaimsFromToken(token);
        if (CommonConstant.ONE==claims.getStatus()) {
            //直接读取得将DATE转成LOCALTIME
            try {
                return LocalDateTime.ofInstant(
                        Instant.ofEpochMilli((Long) claims.getClaims().get(CLAIM_KEY_CREATED))
                        , ZoneId.systemDefault());
            }catch (Exception e) {
                LOGGER.error(TIME_DATE_ERROR, e);
            }
        }
        return null;
    }
    /**
     * token 是否可刷新
     *如果过期了表示可以刷新
     * @param token
     * @return
     */
    public Boolean canTokenBeRefreshed(String token) {
        final JwtCamlsResult claims = getClaimsFromToken(token);
        return (CommonConstant.ZERO==claims.getStatus());
    }
    /**
     * 刷新token
     *
     * @param token
     * @return String
     */
    public String refreshToken(String token) {
        final JwtCamlsResult claims = getClaimsFromToken(token);
        if (CommonConstant.ONE==claims.getStatus()) {
            //直接读取得将DATE转成LOCALTIME
            try {
                claims.getClaims().put(CLAIM_KEY_CREATED,
                        Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant()));
                return generateToken(claims.getClaims());
            } catch (Exception e) {
                LOGGER.error(REFRESH_TOKEN_ERROR, e);
            }
        }
        return "";
    }
    /**
     * 获取私钥
     *
     * @param priKey 私钥字符串
     * @return PrivateKey
     */
    private  PrivateKey getPrivateKey(String priKey) {
        PrivateKey privateKeyTmp = null;
        PKCS8EncodedKeySpec priPKCS8;
        try {

            priPKCS8 = new PKCS8EncodedKeySpec(Base64Utils.decodeFromString(priKey));
            KeyFactory keyf = KeyFactory.getInstance(CommonConstant.JWT_RSA);
            privateKeyTmp = keyf.generatePrivate(priPKCS8);
        } catch (NoSuchAlgorithmException ne) {
            LOGGER.error(INVALID_KEY_SPEC, ne);
        } catch (InvalidKeySpecException ie) {
            LOGGER.error(IO_EXCEPTION_KEY, ie);
        } catch (Exception e) {
            LOGGER.error(NO_SUCH_KEY, e);
        }
        return privateKeyTmp;
    }
    /**
     * 获取公钥
     *
     * @param pubKey 私公钥字符串
     * @return PrivateKey
     */
    private  PublicKey getPublicKey(String    pubKey) {
        PublicKey publicKeyTmp = null;
        try {
            //定义加密公钥
            X509EncodedKeySpec bobPubKeySpec = new X509EncodedKeySpec(
                    Base64Utils.decodeFromString(pubKey));
            KeyFactory keyf = KeyFactory.getInstance(CommonConstant.JWT_RSA);
            publicKeyTmp = keyf.generatePublic(bobPubKeySpec);
        }  catch (NoSuchAlgorithmException ne) {
            LOGGER.error(INVALID_KEY_SPEC, ne);
        } catch (InvalidKeySpecException iet) {
            LOGGER.error(IO_EXCEPTION_KEY, iet);
        }catch (Exception eq) {
            LOGGER.error(NO_SUCH_KEY, eq);
        }
        return publicKeyTmp;
    }
    /**
     * 返回分配给用户的角色列表
     * @param roleIds LIST ID
     * @return
     */
    public String getAuthorities( List<String> roleIds) {
        String result="";
        if(null!=roleIds && roleIds.size() > CommonConstant.ZERO) {
            StringBuilder commaBuilder = new StringBuilder();
            for (String role : roleIds) {
                commaBuilder.append(role).append(CommonConstant.JWT_FILTER_CHAR);
            }
            result=commaBuilder.substring( CommonConstant.ZERO,commaBuilder.length()- CommonConstant.ONE);
        }
        return result;
    }
}

