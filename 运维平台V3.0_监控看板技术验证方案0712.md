# 运维平台V3.0监控看板技术验证方案

## 1. 项目背景

基于运维平台V3.0用户需求说明书，需要构建一个基于Grafana的监控看板系统，实现统一的监控数据展示、权限控制和用户体验。本方案基于现有的BBPF Grafana代理服务架构，提供完整的技术验证方案。

## 2. 技术架构概述

### 2.1 整体架构

```mermaid
graph TD
    subgraph "运维平台V3.0前端"
        A[用户登录] --> B[获取JWT Token]
        C[监控看板页面] --> D[iframe嵌入Grafana]
        E[看板管理界面] --> F[模板选择]
    end

    subgraph "BBPF Grafana代理服务"
        G[JWT认证过滤器] --> H[权限验证]
        H --> I[请求转发]
        J[变量注入服务] --> K[数据权限控制]
        L[WebSocket代理] --> M[实时数据推送]
    end

    subgraph "Grafana实例"
        N[仪表盘渲染]
        O[数据源查询]
        P[图表生成]
    end

    subgraph "数据源"
        Q[监控数据库]
        R[日志系统]
        S[指标收集器]
    end

    D --> G
    F --> G
    I --> N
    K --> O
    M --> D
    O --> Q
    O --> R
    O --> S
```

### 2.2 核心组件

1. **BBPF Grafana代理服务** (已实现)
   - JWT认证和权限控制
   - 请求代理和转发
   - 数据级权限变量注入
   - WebSocket实时通信

2. **Grafana实例**
   - 监控看板渲染
   - 图表设计和配置
   - 数据源管理

3. **运维平台V3.0前端**
   - 用户界面集成
   - iframe嵌入
   - 看板模板管理

## 3. 技术验证方案

### 3.1 阶段一：基础环境搭建和验证

#### 3.1.1 环境准备

**目标**: 搭建完整的开发和测试环境

**验证内容**:
1. Grafana实例部署和配置
2. BBPF Grafana代理服务部署
3. Redis缓存服务配置
4. 数据源连接测试

**验证步骤**:

1. **部署Grafana实例**
```bash
# 使用Docker部署Grafana
docker run -d \
  --name grafana \
  -p 3000:3000 \
  -e "GF_AUTH_PROXY_ENABLED=true" \
  -e "GF_AUTH_PROXY_HEADER_NAME=X-WEBAUTH-USER" \
  -e "GF_AUTH_PROXY_HEADER_PROPERTY=username" \
  -e "GF_AUTH_PROXY_AUTO_SIGN_UP=true" \
  grafana/grafana:latest
```

2. **配置代理服务**
```yaml
# application-dev.yml
bbpf:
  grafana:
    proxy:
      grafana-base-url: http://localhost:3000
      bbpf-permission-api-url: http://localhost:8081/api/v1/permission
      jwt-secret: dev-jwt-secret-key
      enable-permission-cache: true
      permission-cache-expiration-seconds: 300

spring:
  redis:
    host: localhost
    port: 6379
    timeout: 2000ms
```

3. **启动服务验证**
```bash
# 启动代理服务
cd bbpf-grafana-proxy
mvn spring-boot:run -Dspring.profiles.active=dev

# 验证服务健康状态
curl http://localhost:8080/health
```

**验证标准**:
- [ ] Grafana实例正常启动，可通过3000端口访问
- [ ] 代理服务正常启动，健康检查返回UP状态
- [ ] Redis连接正常
- [ ] 代理服务可以成功转发请求到Grafana

#### 3.1.2 认证集成验证

**目标**: 验证JWT认证和用户身份传递

**验证内容**:
1. JWT Token生成和验证
2. 用户身份传递到Grafana
3. 免登录访问验证

**验证步骤**:

1. **生成测试JWT Token**
```java
// 创建测试用例
@Test
public void testJwtTokenGeneration() {
    String userId = "test_user";
    String username = "测试用户";
    String token = jwtUtil.generateToken(userId, username);
    
    // 验证Token有效性
    assertTrue(jwtUtil.validateToken(token));
    assertEquals(userId, jwtUtil.getUserIdFromToken(token));
}
```

2. **测试认证流程**
```bash
# 生成测试Token
TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

# 测试代理访问
curl -H "Authorization: Bearer $TOKEN" \
     http://localhost:8080/grafana/api/dashboards/home
```

**验证标准**:
- [ ] JWT Token可以正确生成和验证
- [ ] 用户身份可以正确传递到Grafana
- [ ] 无效Token被正确拒绝
- [ ] Grafana可以识别代理传递的用户信息

### 3.2 阶段二：权限控制验证

#### 3.2.1 基础权限验证

**目标**: 验证基于BBPF权限系统的访问控制

**验证内容**:
1. 用户权限获取
2. 仪表盘访问控制
3. 权限缓存机制

**验证步骤**:

1. **模拟权限API**
```java
// 创建权限模拟服务
@RestController
@RequestMapping("/api/v1/permission")
public class MockPermissionController {
    
    @GetMapping("/user/{userId}/dashboards")
    public ResponseEntity<List<String>> getUserDashboards(@PathVariable String userId) {
        // 模拟不同用户的权限
        List<String> dashboards = new ArrayList<>();
        if ("admin".equals(userId)) {
            dashboards.addAll(Arrays.asList("dashboard1", "dashboard2", "dashboard3"));
        } else if ("user1".equals(userId)) {
            dashboards.addAll(Arrays.asList("dashboard1", "dashboard2"));
        } else {
            dashboards.add("dashboard1");
        }
        return ResponseEntity.ok(dashboards);
    }
}
```

2. **权限验证测试**
```bash
# 测试管理员权限
ADMIN_TOKEN="admin_jwt_token"
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
     http://localhost:8080/grafana/api/dashboards/db/dashboard2

# 测试普通用户权限
USER_TOKEN="user_jwt_token"
curl -H "Authorization: Bearer $USER_TOKEN" \
     http://localhost:8080/grafana/api/dashboards/db/dashboard3
# 应该返回403 Forbidden
```

**验证标准**:
- [ ] 不同用户看到不同的仪表盘列表
- [ ] 无权限访问的仪表盘被正确拒绝
- [ ] 权限信息被正确缓存
- [ ] 权限变更后缓存能及时更新

#### 3.2.2 数据级权限验证

**目标**: 验证基于用户权限的数据过滤

**验证内容**:
1. 权限变量注入
2. SQL查询过滤
3. 数据安全性

**验证步骤**:

1. **创建测试数据源**
```sql
-- 创建测试表
CREATE TABLE monitoring_data (
    id BIGINT PRIMARY KEY,
    metric_name VARCHAR(100),
    metric_value DECIMAL(10,2),
    org_id INT,
    dept_id INT,
    user_id VARCHAR(50),
    created_time TIMESTAMP
);

-- 插入测试数据
INSERT INTO monitoring_data VALUES
(1, 'cpu_usage', 75.5, 1, 10, 'user1', NOW()),
(2, 'memory_usage', 60.2, 1, 10, 'user1', NOW()),
(3, 'cpu_usage', 80.1, 2, 20, 'user2', NOW()),
(4, 'disk_usage', 45.8, 1, 11, 'user3', NOW());
```

2. **配置权限变量**
```java
// 权限变量生成测试
@Test
public void testPermissionVariables() {
    String userId = "user1";
    Map<String, Object> variables = grafanaVariableService.generateUserPermissionVariables(userId);
    
    assertEquals("user1", variables.get("user_id"));
    assertEquals("1", variables.get("accessible_orgs"));
    assertEquals("org_id IN (1) AND dept_id IN (10)", variables.get("data_filter_condition"));
}
```

3. **测试数据过滤**
```sql
-- 在Grafana中创建查询，使用权限变量
SELECT 
    metric_name,
    AVG(metric_value) as avg_value
FROM monitoring_data 
WHERE ${data_filter_condition}
GROUP BY metric_name
ORDER BY metric_name;
```

**验证标准**:
- [ ] 权限变量正确注入到仪表盘
- [ ] 不同用户看到不同的数据范围
- [ ] SQL注入攻击被有效防护
- [ ] 数据过滤性能满足要求

### 3.3 阶段三：前端集成验证

#### 3.3.1 iframe集成验证

**目标**: 验证Grafana图表在运维平台中的嵌入效果

**验证内容**:
1. iframe嵌入功能
2. 跨域问题解决
3. 响应式布局

**验证步骤**:

1. **创建测试页面**
```html
<!DOCTYPE html>
<html>
<head>
    <title>监控看板测试</title>
    <style>
        .dashboard-container {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .dashboard-iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
</head>
<body>
    <h1>运维平台V3.0 - 监控看板</h1>
    
    <div class="dashboard-container">
        <iframe 
            id="grafana-dashboard"
            class="dashboard-iframe"
            src="http://localhost:8080/grafana/d/dashboard1?orgId=1&kiosk=tv"
            frameborder="0">
        </iframe>
    </div>
    
    <script>
        // 动态设置认证Token
        function loadDashboard(token, dashboardId) {
            const iframe = document.getElementById('grafana-dashboard');
            const url = `http://localhost:8080/grafana/d/${dashboardId}?orgId=1&kiosk=tv&token=${token}`;
            iframe.src = url;
        }
        
        // 模拟用户登录后获取Token
        const userToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...';
        loadDashboard(userToken, 'dashboard1');
    </script>
</body>
</html>
```

2. **配置跨域支持**
```java
// 在代理服务中配置CORS
@Configuration
public class CorsConfig {
    
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(Arrays.asList("*"));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setAllowCredentials(true);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
}
```

**验证标准**:
- [ ] Grafana图表可以正常嵌入iframe
- [ ] 跨域访问正常
- [ ] 图表交互功能正常
- [ ] 响应式布局适配不同屏幕尺寸

#### 3.3.2 实时数据验证

**目标**: 验证WebSocket实时数据推送功能

**验证内容**:
1. WebSocket连接建立
2. 实时数据推送
3. 连接稳定性

**验证步骤**:

1. **WebSocket连接测试**
```javascript
// 创建WebSocket连接测试
function testWebSocketConnection() {
    const token = 'your-jwt-token';
    const wsUrl = `ws://localhost:8080/api/live/ws?token=${token}`;
    
    const ws = new WebSocket(wsUrl);
    
    ws.onopen = function(event) {
        console.log('WebSocket连接已建立');
        
        // 订阅仪表盘更新
        ws.send(JSON.stringify({
            type: 'subscribe',
            channel: 'dashboard/dashboard1'
        }));
    };
    
    ws.onmessage = function(event) {
        const data = JSON.parse(event.data);
        console.log('收到实时数据:', data);
        
        // 更新图表数据
        updateDashboard(data);
    };
    
    ws.onerror = function(error) {
        console.error('WebSocket错误:', error);
    };
    
    ws.onclose = function(event) {
        console.log('WebSocket连接已关闭');
        // 实现重连机制
        setTimeout(testWebSocketConnection, 5000);
    };
}
```

2. **模拟实时数据**
```java
// 创建实时数据推送测试
@Component
public class RealtimeDataSimulator {
    
    @Autowired
    private SimpMessagingTemplate messagingTemplate;
    
    @Scheduled(fixedRate = 5000) // 每5秒推送一次
    public void pushRealtimeData() {
        Map<String, Object> data = new HashMap<>();
        data.put("timestamp", System.currentTimeMillis());
        data.put("cpu_usage", Math.random() * 100);
        data.put("memory_usage", Math.random() * 100);
        data.put("disk_usage", Math.random() * 100);
        
        messagingTemplate.convertAndSend("/topic/dashboard/dashboard1", data);
    }
}
```

**验证标准**:
- [ ] WebSocket连接可以正常建立
- [ ] 实时数据可以正常推送
- [ ] 连接断开后可以自动重连
- [ ] 数据推送延迟在可接受范围内

### 3.4 阶段四：性能和安全验证

#### 3.4.1 性能测试

**目标**: 验证系统在高并发场景下的性能表现

**验证内容**:
1. 并发用户访问
2. 响应时间
3. 系统资源使用

**验证步骤**:

1. **使用JMeter进行压力测试**
```xml
<!-- JMeter测试计划配置 -->
<TestPlan>
    <ThreadGroup>
        <numThreads>100</numThreads>
        <rampTime>60</rampTime>
        <duration>300</duration>
    </ThreadGroup>
    
    <HTTPSampler>
        <domain>localhost</domain>
        <port>8080</port>
        <path>/grafana/api/dashboards/home</path>
        <method>GET</method>
        <headers>
            <header name="Authorization" value="Bearer ${jwt_token}"/>
        </headers>
    </HTTPSampler>
</TestPlan>
```

2. **监控系统指标**
```bash
# 监控CPU和内存使用
top -p $(pgrep -f bbpf-grafana-proxy)

# 监控网络连接
netstat -an | grep :8080

# 监控Redis性能
redis-cli info stats
```

**验证标准**:
- [ ] 100并发用户下响应时间 < 2秒
- [ ] 系统CPU使用率 < 80%
- [ ] 内存使用稳定，无内存泄漏
- [ ] Redis缓存命中率 > 90%

#### 3.4.2 安全测试

**目标**: 验证系统的安全防护能力

**验证内容**:
1. JWT Token安全性
2. SQL注入防护
3. XSS攻击防护

**验证步骤**:

1. **JWT安全测试**
```bash
# 测试过期Token
EXPIRED_TOKEN="expired_jwt_token"
curl -H "Authorization: Bearer $EXPIRED_TOKEN" \
     http://localhost:8080/grafana/api/dashboards/home
# 应该返回401 Unauthorized

# 测试伪造Token
FAKE_TOKEN="fake_jwt_token"
curl -H "Authorization: Bearer $FAKE_TOKEN" \
     http://localhost:8080/grafana/api/dashboards/home
# 应该返回401 Unauthorized
```

2. **SQL注入测试**
```bash
# 尝试SQL注入攻击
curl -H "Authorization: Bearer $VALID_TOKEN" \
     "http://localhost:8080/grafana/api/variables/user-permissions?userId=1'; DROP TABLE users; --"
# 应该被安全过滤器拦截
```

**验证标准**:
- [ ] 无效JWT Token被正确拒绝
- [ ] SQL注入攻击被有效防护
- [ ] XSS攻击被正确过滤
- [ ] 敏感信息不会泄露到日志中

## 4. 预期成果

### 4.1 技术成果

1. **完整的监控看板系统**
   - 基于Grafana的可视化监控界面
   - 支持多种数据源和图表类型
   - 响应式设计，适配不同设备

2. **安全的权限控制体系**
   - JWT认证和授权
   - 细粒度的权限控制
   - 数据级权限过滤

3. **高性能的代理服务**
   - 支持高并发访问
   - Redis缓存优化
   - 实时数据推送

### 4.2 业务价值

1. **提升运维效率**
   - 统一的监控视图
   - 实时数据展示
   - 快速问题定位

2. **增强数据安全**
   - 基于角色的访问控制
   - 数据权限隔离
   - 审计日志记录

3. **降低维护成本**
   - 标准化的监控模板
   - 自动化的权限管理
   - 简化的部署流程

## 5. 风险评估和应对策略

### 5.1 技术风险

| 风险项 | 风险等级 | 影响 | 应对策略 |
|--------|----------|------|----------|
| Grafana版本兼容性 | 中 | 功能异常 | 充分测试，制定升级计划 |
| 性能瓶颈 | 中 | 用户体验差 | 性能监控，优化缓存策略 |
| 安全漏洞 | 高 | 数据泄露 | 安全审计，定期更新 |

### 5.2 业务风险

| 风险项 | 风险等级 | 影响 | 应对策略 |
|--------|----------|------|----------|
| 用户接受度低 | 中 | 推广困难 | 用户培训，界面优化 |
| 数据质量问题 | 中 | 决策错误 | 数据校验，质量监控 |
| 运维复杂度高 | 低 | 维护成本增加 | 自动化部署，文档完善 |

## 6. 实施计划

### 6.1 时间安排

| 阶段 | 时间 | 主要任务 | 交付物 |
|------|------|----------|--------|
| 阶段一 | 1-2周 | 环境搭建和基础验证 | 可运行的演示环境 |
| 阶段二 | 2-3周 | 权限控制验证 | 权限控制演示 |
| 阶段三 | 2-3周 | 前端集成验证 | 完整的用户界面 |
| 阶段四 | 1-2周 | 性能和安全验证 | 测试报告和优化方案 |

### 6.2 资源需求

1. **人力资源**
   - 后端开发工程师：2人
   - 前端开发工程师：1人
   - 测试工程师：1人
   - 运维工程师：1人

2. **硬件资源**
   - 开发服务器：4核8G内存
   - 测试服务器：8核16G内存
   - Redis服务器：2核4G内存

3. **软件资源**
   - Grafana Enterprise License（可选）
   - JMeter性能测试工具
   - 安全扫描工具

## 7. 总结

本技术验证方案基于现有的BBPF Grafana代理服务架构，通过四个阶段的验证，确保监控看板系统能够满足运维平台V3.0的需求。方案涵盖了从基础环境搭建到性能安全验证的完整流程，为后续的正式开发和部署提供了可靠的技术基础。

通过本方案的实施，预期能够构建一个安全、高效、易用的监控看板系统，为运维团队提供强大的数据可视化和分析能力，提升整体运维效率和质量。