/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @ClassName: UserRequest
 * 用户请求实体类
 * @module: si-bbpf-system
 * @Author: wjc1
 * @date: 2021/5/19 14:07
 */
@Data
public class RemoveUserDto {
    @NotBlank(message = "用户ID不能为空")
    private String userIds;
    @NotBlank(message = "组织机构ID不能为空")
    private String orgId;
}
