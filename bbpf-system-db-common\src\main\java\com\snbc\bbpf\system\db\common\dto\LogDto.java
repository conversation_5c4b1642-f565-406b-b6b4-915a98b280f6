package com.snbc.bbpf.system.db.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * @ClassName: LogDot
 * @Description: 页面展示log
 * @module: SI-bbpf-tenant-config
 * @Author: wangsong
 * @date: 2022/11/10
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LogDto {

    /** 日志时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime logTime;

    /** 用户名 */
    private String userName;

    /** 登录IP */
    private String ip;

    /** 操作模块 */
    private String logTarget;

    /** 操作类型 */
    private String logType;

    /** 日志内容 */
    private String logContent;
    /** 请求来源 */
    private String requestSource;
    /** */
    private String remark;
}
