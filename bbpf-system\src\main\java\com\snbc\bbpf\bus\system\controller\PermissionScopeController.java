/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.controller;

import com.alibaba.fastjson.JSONObject;
import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.bus.system.service.PermissionScopeService;
import com.snbc.bbpf.bus.system.utils.ResultUtil;
import com.snbc.bbpf.buslog.annotations.Buslog;
import com.snbc.bbpf.buslog.annotations.OperationType;
import com.snbc.bbpf.system.db.common.entity.PermissionScope;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName: PermissionScopeController
 * @Description: 数据权限控制器
 * @module: si-bbpf-system
 * @Author: zhouzheng
 * @date: 2021/5/19
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@RestController
@RequestMapping("/console/permission")
public class
PermissionScopeController {

    @Resource
    private PermissionScopeService permissionScopeService;

    /**
      * @Description:    数据权限新增
      * @Author:         zhouzheng
      * @param:  [permissionScope]
      * @CreateDate:     2021/5/19 17:54
      * @UpdateDate:     2021/5/19 17:54
      * @return: com.snbc.bbpf.component.config.CommonResp<java.lang.Object>
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @PostMapping("addDataScope")
    @Buslog(opration = "addDataPermission", zh = "在【权限名称：${permissionName}，权限编码：${permissionCode}]】下新增数据权限【${scopeName}，${scopeCode}】",
            en = "Newly add data permission [permission name:${permissionName}, permission code:${permissionCode}] " +
                    "under [${scopeName}, ${scopeCode}]",
            target = "permissionManage")
    public CommonResp<Object> addDataScope(@RequestBody PermissionScope permissionScope) throws BusinessException {
        permissionScopeService.addDataScope(permissionScope);
        return CommonResp.builder().body(null).head(ResultUtil.success()).build();
    }
    /**
      * @Description:    数据权限更新
      * @Author:         zhouzheng
      * @param:  [permissionScope]
      * @CreateDate:     2021/5/20 11:21
      * @UpdateDate:     2021/5/20 11:21
      * @return: com.snbc.bbpf.component.config.CommonResp<java.lang.Object>
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @PutMapping("updateDataScope")
    @Buslog(oprationType = OperationType.MODIFY,opration = "modifyDataPermission", target = "permissionManage",type = PermissionScope.class )
    public CommonResp<Object> updateDataScope(@RequestBody PermissionScope permissionScope) throws BusinessException {
        permissionScopeService.updateDataScope(permissionScope);
        return CommonResp.builder().body(null).head(ResultUtil.success()).build();
    }
    /**
      * @Description:    数据权限查询
      * @Author:         zhouzheng
      * @param:  [scopeId]
      * @CreateDate:     2021/5/20 11:22
      * @UpdateDate:     2021/5/20 11:22
      * @return: com.snbc.bbpf.component.config.CommonResp<java.lang.Object>
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @GetMapping("getDataScopeDetail")
    public CommonResp<Object> getDataScopeDetail(@RequestParam String scopeId) throws BusinessException {
        PermissionScope permissionScope = permissionScopeService.getDataScopeDetail(scopeId);
        return CommonResp.builder().body(permissionScope).head(ResultUtil.success()).build();
    }
    /**
      * @Description:    数据权限批量删除
      * @Author:         zhouzheng
      * @param:  [scopeIds]
      * @CreateDate:     2021/5/20 11:22
      * @UpdateDate:     2021/5/20 11:22
      * @return: com.snbc.bbpf.component.config.CommonResp<java.lang.Object>
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @DeleteMapping("delDataScope")
    @Buslog(opration = "delDataPermission", zh = "删除【权限名称：${permissionName}，权限编码：${permissionCode}】下的数据权限" +
            "【权限名称：${scopeNames}，权限编码：${scopeCodes}】",
            en = "Delete the data permission [permission name:${permissionName}, permission code:${permissionCode}]" +
                    " under [permission name:${scopeNames}, permission code:${scopeCodes}]", target = "permissionManage")
    public CommonResp<Object> delDataScope(@RequestBody String scope) throws BusinessException {
        //获取json中roleId的值
        JSONObject jsonObject = JSONObject.parseObject(scope);
        permissionScopeService.delDataScope(jsonObject.getJSONArray("scopeIds").toArray(new String[0]));
        return CommonResp.builder().body(null).head(ResultUtil.success()).build();
    }
    /**
      * @Description:    数据权限列表查询
      * @Author:         zhouzheng
      * @param:  [permissionId]
      * @CreateDate:     2021/5/20 11:22
      * @UpdateDate:     2021/5/20 11:22
      * @return: com.snbc.bbpf.component.config.CommonResp<java.lang.Object>
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @GetMapping("getDataScopeList")
    public CommonResp<Object> getDataScopeList(@RequestParam("permissionId") String permissionId) throws BusinessException {
        PermissionScope permissionScope= new PermissionScope();
        permissionScope.setPermissionId(permissionId);
        List<PermissionScope> list = permissionScopeService.getDataScopeList(permissionScope);
        return CommonResp.builder().body(list).head(ResultUtil.success()).build();
    }
}
