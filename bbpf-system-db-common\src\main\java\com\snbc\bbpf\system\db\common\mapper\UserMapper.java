/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.mapper;


import com.snbc.bbpf.component.dataauth.annotation.IgnoreAuth;
import com.snbc.bbpf.component.security.annotations.Ignore;
import com.snbc.bbpf.system.db.common.dto.UserExportDtoNew;
import com.snbc.bbpf.system.db.common.entity.User;
import com.snbc.bbpf.system.db.common.vo.NoticeUserVo;
import com.snbc.bbpf.system.db.common.vo.OrgUserPageVo;
import com.snbc.bbpf.system.db.common.vo.RoleUserPage;
import com.snbc.bbpf.system.db.common.vo.TenantClerkVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: UserMapper
 * @Description: 数据库交互
 * @module: si-bbpf-system
 * @Author: wangsong
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Mapper
public interface UserMapper {

    /**
     * 根据用户id获取用户信息
     * @param userId
     * @return
     */
    @IgnoreAuth
    User selectByPrimaryKey(String userId);

    User selectByJobNumber(String jobNumber);
    User selectByPhone(String phone);
    int insertSelective(User user);

    int updateByPrimaryKeySelective(User user);

    void updatePwd(@Param("newPwd") String newPwd,@Param("userId")String userId);

    /**
     *查询用户所属的角色，有数据权限
     * @return
     */
    List<Map<String,String>> selectUserRole4DataRule();

    /**
     *根据userId查询用户所属的角色，没有数据权限
     * @param userId
     * @return
     */
    List<Map<String,String>> selectRoleByUserNoDataAuth(String userId);
    /**
     * 根据userId查询用户所属的组织机构，有数据权限
     * @return
     */
    List<Map<String,String>> selectUserOrg4DataRule();
    /**
     *根据userId查询用户所属的组织机构，没有数据权限
     * @param userId
     * @return
     */
    List<Map<String,String>> selectOrgByUserNoDataAuth(String userId);

    List<String> selectOrgIdListByUserId(String userId);

    @Ignore
    List<OrgUserPageVo> queryUserListPage(@Param("orgPathList") List<String> orgPathList,
                                          @Param("queryParam")String queryParam,
                                          @Param("phoneParam")String phoneParam);
    @Ignore
    List<OrgUserPageVo> queryUserListPageByOrgId(@Param("orgIdList") List<String> orgIdList,
                                          @Param("queryParam")String queryParam,
                                          @Param("phoneParam")String phoneParam);
    @Ignore
    List<UserExportDtoNew> queryExportUserListByOrgPath(@Param("orgPathList")List<String> orgPathList);

    /**
     * 导出用户
     * @param orgIdList
     * @return
     */
    @Ignore
    List<UserExportDtoNew> queryExportUserListByOrgPathByOrgId(@Param("orgIdList") List<String> orgIdList);

    @Ignore
    List<RoleUserPage> selectUserListByRoleId(@Param("roleId") String roleId,
                                              @Param("queryParam") String queryParam,
                                              @Param("phoneEncrypt") String phoneEncrypt);

    /**
     * 超管，查询该角色下的用户
     * @param roleId
     * @param queryParam
     * @param phoneEncrypt
     * @return
     */
    @Ignore
    List<RoleUserPage> selectUserListByAdmin(@Param("roleId") String roleId, @Param("queryParam") String queryParam,
                                             @Param("phoneEncrypt") String phoneEncrypt);
    @Ignore
    List<RoleUserPage> selectUnboundRoleUserList(@Param("queryParam") String queryParam, @Param("phoneEncrypt") String phoneEncrypt);

    /**
     * 超管，查未绑定角色的用户
     * @param queryParam
     * @param phoneEncrypt
     * @return
     */
    @Ignore
    List<RoleUserPage> selectUnboundRoleUserListAdmin(@Param("queryParam") String queryParam, @Param("phoneEncrypt") String phoneEncrypt);
    @Ignore
    List<RoleUserPage> getUserByOrgPath(@Param("orgPathList")List<String> orgPathList);
    @Ignore
    List<RoleUserPage> getUserByOrgId(@Param("orgIdList")List<String> orgIdList);
    /**
     * 根据组织机构id获取用户列表
     * @param orgList 组织机构id列表
     * @return
     */
    List<String> getUserListByOrg(List<String> orgList);

    /**
     * 查询组织机构及下级组织机构下的人员
     * @param orgPathList
     * @return
     */
    @Ignore
    List<Map<String, String>> getUserListByOrgAndSub(@Param("orgPathList") List<String> orgPathList);

    List<User> selectUserByOrgId(@Param("orgIds") List<String> orgIds, @Param("hasLock")Integer hasLock);

    List<TenantClerkVo> selectUserIdAndName(@Param("orgIds") List<String> orgIds, @Param("hasLock")Integer hasLock);

    List<UserExportDtoNew> queryExportUserListByUserId( @Param("userIds") List<String> userIds);

    int checkPhoneIsBind(String phone);

    @Ignore
    List<NoticeUserVo> noticeUserList(@Param("userIds") List<String> userIds);

    /**
     * 页面使用
     * @param userName
     * @param orgPathList
     * @param phone
     * @return
     */
    @Ignore
    List<NoticeUserVo> noticeUserListConsole(@Param("orgPathList") List<String> orgPathList,
                                      @Param("userName") String userName, @Param("queryParam") String phone);
    /**
     * 页面使用
     * @param userName
     * @param orgIdList
     * @param phone
     * @return
     */
    @Ignore
    List<NoticeUserVo> noticeUserListConsoleByRoleId(@Param("orgIdList") List<String> orgIdList,
                                      @Param("userName") String userName, @Param("queryParam") String phone);

    void loginUnLock(String userId);
}

