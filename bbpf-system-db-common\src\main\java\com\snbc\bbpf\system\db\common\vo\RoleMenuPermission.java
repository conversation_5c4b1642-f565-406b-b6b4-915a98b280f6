/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName: RoleMenuPermission
 * @Description: 角色详情菜单权限
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/5/21
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class RoleMenuPermission {
    /**
     * 权限id
     */
    private String permissionId;
    /**
     * 权限名称
     */
    private String permissionName;

    /**
     * 父级id
     */
    private String parentId;

    /**
     * 权限叶子结点
     */
    private List<RoleButtonPermission> nodes;
}
