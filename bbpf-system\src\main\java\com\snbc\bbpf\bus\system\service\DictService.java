/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service;

import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.system.db.common.dto.DictValueDto;
import com.snbc.bbpf.system.db.common.entity.DictType;
import com.snbc.bbpf.system.db.common.entity.DictValue;
import com.snbc.bbpf.system.db.common.vo.DictTypeVo;
import com.snbc.bbpf.system.db.common.vo.DictValueVo;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: DictService
 * 字典 接口类
 * @module: si-bbpf-system
 * @Author: wjc1 增加的注释
 * @date:   2021/5/25 16:56
 */
public interface DictService {

    void updateDictType(DictType dictType, CommonResp<String> commonResp);

    void addDictType(DictTypeVo dictTypeVo, CommonResp<String> commonResp);

    void addDictValue(DictValueVo dictValueVo, CommonResp<String> commonResp);

    void updateDictValue(DictValueVo dictValueVo, CommonResp<String> commonResp);

    void delDictType(String typeCode, CommonResp<String> commonResp);

    void delDictValue(String valueId, CommonResp<String> commonResp);

    List<DictValueVo> getDictValueList(String typeCode);

    List<DictValueVo> getDictValueByTypeCodeParentId(String typeCode, String parentId);


    List<DictValueVo> getByTypeCodeAndValueName(String typeCode, String typeValueName);

    /**
     * @description: * @param
     * @return: java.util.List<com.snbc.bbpf.system.db.common.entity.DictType>
     * @author: liuyi
     * @time: 2021/5/20 9:59
     */
    List<DictType> quertAllDictTypeCode();

    /**
     * @description: * @param typeName
     * @return: java.util.List<com.snbc.bbpf.system.db.common.entity.DictType>
     * @author: liuyi
     * @time: 2021/5/20 9:54
     */
    List<DictType> getDictTypeByName(String typeName);

    List<DictValue> selectByTypeCodeAndValueCode(String typeCode, String valueCode);

    List<DictValue> selectByValueIds(List<String> valueIds);

    DictValueVo getDictByTypeValueCode(String typeCode, String valueCode);

    Map<String, List<DictValueDto>> getMultipleDictValues(List<String> dictTypeCodes);
}
