/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.mapstruct.ClassConvertorMapper;
import com.snbc.bbpf.bus.system.service.INavigationService;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.system.db.common.dto.NavigationDto;
import com.snbc.bbpf.system.db.common.dto.NavigationPermissionDto;
import com.snbc.bbpf.system.db.common.entity.Navigation;
import com.snbc.bbpf.system.db.common.entity.NavigationPermission;
import com.snbc.bbpf.system.db.common.mapper.NavigationMapper;
import com.snbc.bbpf.system.db.common.vo.NavigationVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 顶部导航功能Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-12-27
 */
@Service
@Transactional
public class NavigationServiceImpl implements INavigationService {
    public static final int NAV_MAX_NUM = 10;
    @Autowired
    private NavigationMapper navigationMapper;

    /**
     * 查询顶部导航功能
     * 
     * @param navigationId 顶部导航功能主键
     * @return 顶部导航功能
     */
    @Override
    public NavigationVo selectNavigationByNavigationId(String navigationId) {
        Navigation navigation = navigationMapper.selectNavigationByNavigationId(navigationId);
        return ClassConvertorMapper.INSTANCE.covertNavigationVo(navigation);
    }

    @Override
    public List<String> getRelationship(String navigationId) {
        return navigationMapper.selectRelationship(navigationId);
    }

    @Override
    public void saveNavigationPermission(NavigationPermissionDto navigationPermissionDto) {
        List<NavigationPermission> navigationPermissions=new ArrayList<>();
        String navigationId=navigationPermissionDto.getNavigationId();
        navigationMapper.deleteRelationship(navigationId);
        if(!navigationPermissionDto.getPermissionIds().isEmpty()) {
            navigationPermissionDto.getPermissionIds().stream().forEach(item ->
                navigationPermissions.add(
                        NavigationPermission.builder().permissionId(item).navigationId(navigationId)
                                .id(UUID.randomUUID().toString()).build())
            );
            navigationMapper.saveNavigationPermission(navigationPermissions);
        }
    }

    /**
     * 查询顶部导航功能列表
     * 
     * @param navigationDto 顶部导航功能
     * @param pageSize 分页大小
     * @param pageNum 分页数
     * @return 顶部导航功能
     */
    @Override
    public PageInfo<Navigation> selectNavigationList(NavigationDto navigationDto, int pageSize, int pageNum) {
        Navigation navigation = ClassConvertorMapper.INSTANCE.covertNavigation(navigationDto);
        PageMethod.startPage(pageNum, pageSize);
        List<Navigation> list = navigationMapper.selectNavigationList(navigation);
        PageInfo<Navigation> page = new PageInfo<>(list);
        page.setPageNum(pageNum);
        page.setPageSize(pageSize);
        return page;
    }
    /**
     * 查询顶部导航功能列表
     *
     * @return 顶部导航功能
     */
    @Override
    public List<NavigationVo> getNavigationList() {
        List<Navigation> list = navigationMapper.selectNavigationList(null);
        return ClassConvertorMapper.INSTANCE.covertNavigationList(list);
    }
    /**
     * 新增顶部导航功能
     * 
     * @param navigationDto 顶部导航功能
     * @return 受影响结果数
     */
    @Override
    public int insertNavigation(NavigationDto navigationDto) {
        List<Navigation> list=navigationMapper.selectNavigationList(null);
        // 判断数据库现有数量等于10，抛异常
        ErrorMessage.MAXIMUM_QUANTITY_LIMIT.assertEquals(list!=null&&list.size()== NAV_MAX_NUM,false);

        Navigation item=navigationMapper.selectNavigationByNavigationCode(navigationDto.getNavigationCode());
        // 判断 菜单编码是否存在，不为空表示存在，抛异常
        ErrorMessage.NAVIGATION_CODE_EXIST.assertNull(item);

        Navigation navigation = ClassConvertorMapper.INSTANCE.covertNavigation(navigationDto);
        navigation.setNavigationId(UUID.randomUUID().toString());
        navigation.setCreateUserId(CurrentUser.getUserId());
        navigation.setCreateTime(LocalDateTime.now());
        return navigationMapper.insertNavigation(navigation);
    }

    /**
     * 修改顶部导航功能
     * 
     * @param navigationDto 顶部导航功能
     * @return 受影响结果数
     */
    @Override
    public int updateNavigation(NavigationDto navigationDto) {
        Navigation item = navigationMapper.selectNavigationByNavigationCode(navigationDto.getNavigationCode());
        if (item != null && !item.getNavigationId().equals(navigationDto.getNavigationId())) {
            throw new BusinessException(ErrorMessage.NAVIGATION_CODE_EXIST.getMessage(), ErrorMessage.NAVIGATION_CODE_EXIST.getCode());
        }
        Navigation navigation = ClassConvertorMapper.INSTANCE.covertNavigation(navigationDto);
        return navigationMapper.updateNavigation(navigation);
    }

    /**
     * 批量删除顶部导航功能
     * 
     * @param navigationIds 需要删除的顶部导航功能主键
     * @return 受影响结果数
     */
    @Override
    public int deleteNavigationByNavigationIds(String[] navigationIds) {
        return navigationMapper.deleteNavigationByNavigationIds(navigationIds);
    }

    /**
     * 删除顶部导航功能信息
     * 
     * @param navigationId 顶部导航功能主键
     * @return 受影响结果数
     */
    @Override
    public int deleteNavigationByNavigationId(String navigationId) {
        return navigationMapper.deleteNavigationByNavigationId(navigationId);
    }
}
