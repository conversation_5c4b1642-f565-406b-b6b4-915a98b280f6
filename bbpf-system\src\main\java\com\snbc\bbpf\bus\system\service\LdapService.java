package com.snbc.bbpf.bus.system.service;

import com.snbc.bbpf.system.db.common.dto.LdapUserDto;

import java.util.List;

/**
 * Ldap 登录接口
 */
public interface LdapService {
    /**
     * 验证Ldap用户
     *
     * @param userName
     * @param passWord
     * @return
     */
    boolean adLogin(String userName, String passWord);


    /**
     * 获取LDAP用户列表
     *
     * @return
     */
    List<LdapUserDto> getUserList();

}
