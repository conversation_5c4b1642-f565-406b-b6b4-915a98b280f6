/*
 * 版权所有 2009-2024山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.bus.system.utils;

import com.snbc.bbpf.bus.system.constans.NumberConstant;
import com.snbc.bbpf.system.db.common.entity.Permission;
import com.snbc.bbpf.system.db.common.vo.PermissionInfo;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 权限树工具类
 *
 * @ClassName: TreeUtil
 * @module: SI-bbpf-system
 * @Author: wangsong
 * @date: 2024/9/24
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
public class TreeUtil {

    /**
     * 将字符串转换为权限ID列表。
     *
     * @param menuList 权限字符串列表，每个权限字符串格式为"1/2/3/4"，其中每个数字代表一个权限ID
     * @return 返回一个权限ID列表，其中权限ID按照在输入字符串中出现的顺序进行排序
     */
    public static Set<String> PermissionIdConvert(List<Permission> menuList) {
        // 创建一个 LinkedHashSet 用于存储所有的权限ID， LinkedHashSet 既保留了元素的插入顺序，又避免了重复元素的存入
        Set<String> allParentIds = new LinkedHashSet<>();

        // 遍历权限字符串列表
        menuList.forEach(permission -> {
            // 分割每个权限字符串，转换为权限ID数组，跳过第一个元素（通常是空或不重要的标识）
            String[] permissionIds = Arrays.stream(permission.getPermissionPath().split("/")).skip(NumberConstant.NO_ONE).toArray(String[]::new);

            // 将权限ID数组添加到权限ID集合中
            allParentIds.addAll(Arrays.asList(permissionIds));
        });

        // 返回权限ID集合
        return allParentIds;
    }

    /**
     * 构建权限列表的树形结构。
     *
     * @param permissionList   权限列表
     * @param roleMenuList
     * @return 树形结构的权限列表
     */
    public static List<PermissionInfo> getTreeList(List<Permission> permissionList, List<String> roleMenuList) {
        if (permissionList == null || permissionList.isEmpty()) {
            // 如果列表为空或null，直接返回空列表
            return Collections.emptyList();
        }

        // 使用HashMap缓存parentId和PermissionInfo的映射关系，以减少遍历次数
        Map<String, List<PermissionInfo>> childMap = new HashMap<>();
        List<PermissionInfo> resultList = new ArrayList<>();
        for (Permission permission : permissionList) {
            PermissionInfo permissionInfo = PermissionInfo.builder().permissionId(permission.getPermissionId())
                    .permissionName(permission.getPermissionName()).parentId(permission.getParentId())
                    .permissionType(Integer.valueOf(permission.getPermissionType())).checked(false).build();;
            // 筛选出顶层元素，即parentId为null及类型为1的元素
            if (permission.getParentId() != null && Integer.valueOf(permission.getPermissionType()) == NumberConstant.NO_ONE) {
                resultList.add(permissionInfo);
            }
            // 构建子元素映射关系
            childMap.computeIfAbsent(permission.getParentId(), k -> new ArrayList<>()).add(permissionInfo);
        }
        // 构建树形结构
        for (PermissionInfo permissionInfo : resultList) {
            // 根据角色权限ID列表设置PC端的选中状态
            permissionInfo.setChecked(roleMenuList.contains(permissionInfo.getPermissionId()));
            // 为每个顶层元素设置子元素
            List<PermissionInfo> childList = getChildList(permissionInfo.getPermissionId(), roleMenuList, childMap);
            permissionInfo.setChildrenPermissions(childList);
        }
        return resultList;
    }

    /**
     * 递归获取子元素列表。
     *
     * @param parentId     父元素的ID
     * @param childMap     子元素映射关系
     * @param roleMenuList 角色权限ID列表，用于设置菜单的选中状态
     * @return 子元素列表
     */
    private static List<PermissionInfo> getChildList(String parentId,
                                                     List<String> roleMenuList,
                                                     Map<String, List<PermissionInfo>> childMap) {
        // 根据父元素ID从子元素映射关系中获取子元素列表
        List<PermissionInfo> childList = childMap.get(parentId);
        if (childList == null) {
            // 如果没有子元素，返回null
            return null;
        }

        for (PermissionInfo child : childList) {
            // 递归调用getChildList，继续获取下一级子元素
            List<PermissionInfo> childListResult = getChildList(child.getPermissionId(), roleMenuList, childMap);
            // 根据角色权限ID列表设置菜单的选中状态
            child.setChecked(roleMenuList.contains(child.getPermissionId()));
            // 设置当前元素的子元素列表
            child.setChildrenPermissions(childListResult);
        }
        // 返回处理后的子元素列表
        return childList;
    }

}
