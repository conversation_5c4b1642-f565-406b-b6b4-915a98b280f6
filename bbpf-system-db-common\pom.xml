<?xml version="1.0" encoding="UTF-8"?>
<!-- 这是一个 Maven 的项目对象模型（POM）文件，用于配置 bbpf-system-db-common 项目的构建和依赖等信息 -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <!-- 定义 POM 文件的模型版本，通常使用 4.0.0 -->
    <modelVersion>4.0.0</modelVersion>
    <!-- 此项目继承自父项目，通过以下信息指定父项目 -->
    <parent>
        <!-- 父项目的组 ID，一般表示组织或公司的标识 -->
        <groupId>com.snbc.bbpf</groupId>
        <!-- 父项目的 artifact ID，用于区分父项目中的不同模块 -->
        <artifactId>bbpf-system</artifactId>
        <!-- 父项目的版本号 -->
        <version>2.0.0</version>
        <!-- 父项目 POM 文件的相对路径，这里是相对于当前项目的上一级目录 -->
        <relativePath>../pom.xml</relativePath>
    </parent>
    <!-- 本项目的组 ID，遵循组织或公司的标识 -->
    <groupId>com.snbc.bbpf</groupId>
    <!-- 本项目的 artifact ID，用于唯一标识本项目 -->
    <artifactId>bbpf-system-db-common</artifactId>
    <!-- 本项目的版本号 -->
    <version>2.0.0</version>
    <!-- 本项目的名称 -->
    <name>bbpf-system-db-common</name>
    <!-- 本项目的描述信息，简单描述项目的功能或用途 -->
    <description>project for bbpf-system-db</description>
    <!-- 项目的属性，可在 POM 文件中引用这些属性 -->
    <properties>
        <!-- Java 开发和编译使用的版本 -->
        <java.version>1.8</java.version>
    </properties>
    <!-- 项目的依赖信息，列出了项目所依赖的各种库和组件 -->
    <dependencies>
        <!-- Lombok 依赖，用于简化 Java 代码，自动生成 getter、setter 等方法 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <!-- Spring Boot 验证启动器，提供数据验证功能 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <!-- 自定义的安全组件启动器，可能提供一些安全相关的功能，如认证、授权等，指定了版本 2.0.3 -->
        <dependency>
            <groupId>com.snbc.bbpf</groupId>
            <artifactId>bbpf-component-security-spring-boot-starter</artifactId>
            <version>2.0.3</version>
        </dependency>
        <!-- PageHelper 依赖，用于分页功能 -->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
        </dependency>
        <!-- JWT 工具类加密属性配置依赖，JJWT 的 API 部分 -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
        </dependency>
        <!-- JWT 工具类加密属性配置依赖，JJWT 的 Jackson 序列化部分 -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <!-- 可以根据需要选择使用 Gson 序列化 -->
            <artifactId>jjwt-jackson</artifactId>
        </dependency>
        <!-- JWT 工具类加密属性配置依赖，JJWT 的实现部分 -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
        </dependency>
        <!-- EasyExcel 依赖，用于处理 Excel 文件 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <!-- 数据权限组件启动器，可能用于数据权限的管理和控制 -->
        <dependency>
            <groupId>com.snbc.bbpf</groupId>
            <artifactId>bbpf-component-dataauth-spring-boot-starter</artifactId>
        </dependency>
        <!-- MyBatis Spring Boot 启动器，用于集成 MyBatis 到 Spring Boot 项目中 -->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>
        <!-- MyBatis-Plus 的启动器，提供增强的 MyBatis 功能，指定了版本 3.1.2 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.1.2</version>
        </dependency>
        <!-- MyBatis-Plus 的代码生成器，指定了版本 3.1.2 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>3.1.2</version>
        </dependency>
        <!-- 业务日志组件启动器，可能用于记录业务操作日志 -->
        <dependency>
            <groupId>com.snbc.bbpf</groupId>
            <artifactId>bbpf-component-buslog-spring-boot-starter</artifactId>
        </dependency>
    </dependencies>
    <!-- 项目的构建配置 -->
    <build>
        <!-- 项目构建后的最终名称 -->
        <finalName>bbpf-system-db-common</finalName>
        <!-- 项目的资源文件目录配置 -->
        <resources>
            <!-- 包含 src/main/resources 目录作为资源目录 -->
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <!-- 包含 src/main/java 目录下的所有 xml 文件作为资源文件 -->
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <!-- 项目使用的插件列表 -->
        <plugins>
            <!-- MyBatis 生成器插件，可根据数据库表结构自动生成 MyBatis 的代码 -->
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <configuration>
                    <!-- 生成代码时是否输出详细信息，true 表示输出 -->
                    <verbose>true</verbose>
                    <!-- 是否覆盖已有的生成代码文件，true 表示覆盖 -->
                    <overwrite>true</overwrite>
                </configuration>
            </plugin>
            <!-- Maven 的 Surefire 插件，用于运行单元测试 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <!-- 系统属性变量，用于设置 Jacoco 代理文件的存储位置 -->
                    <systemPropertyVariables>
                        <jacoco-agent.destfile>${project.build.directory}/jacoco.exec</jacoco-agent.destfile>
                    </systemPropertyVariables>
                </configuration>
            </plugin>
            <!-- Jacoco 插件，用于代码覆盖率分析 -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <executions>
                    <!-- 对代码进行插桩操作，用于代码覆盖率统计 -->
                    <execution>
                        <id>default-instrument</id>
                        <goals>
                            <goal>instrument</goal>
                        </goals>
                    </execution>
                    <!-- 恢复插桩后的类，将代码恢复到原始状态 -->
                    <execution>
                        <id>default-restore-instrumented-classes</id>
                        <goals>
                            <goal>restore-instrumented-classes</goal>
                        </goals>
                    </execution>
                    <!-- 在单元测试前准备 Jacoco 代理，进行插桩操作 -->
                    <execution>
                        <id>pre-unit-test</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                        <configuration>
                            <!-- 是否追加到已有的 Jacoco 数据文件，false 表示不追加 -->
                            <append>false</append>
                            <!-- Jacoco 数据文件的存储位置 -->
                            <destFile>${basedir}/target/jacoco.exec</destFile>
                            <!-- 将 Jacoco 代理的配置存储在 surefireArgLine 属性中 -->
                            <propertyName>surefireArgLine</propertyName>
                        </configuration>
                    </execution>
                    <!-- 在 prepare-package 阶段生成代码覆盖率报告 -->
                    <execution>
                        <id>default-report</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                        <configuration>
                            <!-- 用于生成报告的数据文件位置 -->
                            <dataFile>${basedir}/target/jacoco.exec</dataFile>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- Maven 编译器插件，用于编译 Java 代码 -->
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <!-- 编译代码使用的 Java 版本 -->
                    <source>${java.version}</source>
                    <!-- 编译后代码的目标 Java 版本 -->
                    <target>${java.version}</target>
                    <!-- 代码的编码方式，使用 UTF-8 -->
                    <encoding>UTF-8</encoding>
                    <!-- 编译器参数，-parameters 可在运行时保留方法的参数名称 -->
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>