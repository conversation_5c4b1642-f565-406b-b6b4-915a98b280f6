# BBPF Grafana Proxy Docker Environment Configuration
# Docker环境下使用Apollo配置中心，只配置Apollo相关信息

spring.application.name=bbpf-grafana-proxy

# Apollo配置中心配置
apollo.meta=${APOLLO_META:http://apollo-config:8080}
apollo.bootstrap.enabled=true
apollo.bootstrap.namespaces=application,bbpf-grafana-proxy
apollo.bootstrap.eagerLoad.enabled=true
apollo.cacheDir=/opt/data/apollo-cache

# Grafana分析功能控制配置
# 是否禁用Grafana分析功能（Explore和Inspect）
# true: 禁用分析功能，false: 启用分析功能
bbpf.grafana.proxy.disable-analysis-features=true




