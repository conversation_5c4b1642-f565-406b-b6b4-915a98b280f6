package com.snbc.bbpf.system.db.common.dto;

import lombok.Data;

/**
 * @ClassName: RetentionInfoDto
 * @Description: 清理日志保留天数等信息
 * @module: SI-bbpf-system
 * @Author: wangsong
 * @date: 2023/6/21
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Data
public class RetentionInfoDto {
    private String tableName;
    private Integer retentionDay;
    private String extableName;

}
