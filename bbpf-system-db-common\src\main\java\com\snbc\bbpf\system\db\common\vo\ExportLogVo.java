package com.snbc.bbpf.system.db.common.vo;

import lombok.Builder;
import lombok.Data;


/**
 * @ClassName: ExportLogVo
 * @Description: 导出日志vo
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/9/3
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Data
@Builder
public class ExportLogVo {
    /** 日志时间 */
    private String createTime;

    /** 用户名 */
    private String userName;

    /** 登录IP */
    private String ip;

    /** 操作模块 */
    private String logTarget;

    /** 操作类型 */
    private String logType;


    /** 日志内容 */
    private String logContent;

    /**备注 */
    private String remarks;
}
