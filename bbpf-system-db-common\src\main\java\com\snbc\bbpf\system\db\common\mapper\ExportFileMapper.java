/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.mapper;

import com.snbc.bbpf.system.db.common.entity.ExportFile;
import com.snbc.bbpf.system.db.common.vo.ExportFileQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 导出文件管理Mapper接口
 *
 * <AUTHOR>
 * @date 2022-11-29
 */
@Mapper
public interface ExportFileMapper {
    /**
     * 查询导出文件管理
     *
     * @param exportId 导出文件管理主键
     * @return 导出文件管理
     */
    ExportFile selectExportFileByExportId(String exportId);

    /**
     * 查询导出文件管理列表
     *
     * @param exportFile 导出文件管理
     * @return 导出文件管理集合
     */
    List<ExportFile> selectExportFileList(ExportFileQuery exportFile);

    /**
     * 获取所有到期文件
     * @return
     */
    List<ExportFile> getAllExpireFiles();
    /**
     * 新增导出文件管理
     *
     * @param exportFile 导出文件管理
     */
    void insertExportFile(ExportFile exportFile);

    /**
     * 修改导出文件管理
     *
     * @param exportFile 导出文件管理
     */
    void updateExportFile(ExportFile exportFile);

    /**
     * 删除导出文件管理
     *
     * @param exportId 导出文件管理主键
     * @return 受影响行数
     */
    int deleteExportFileByExportId(String exportId);

    /**
     * 批量删除导出文件管理
     *
     * @param exportIds 需要删除的数据主键集合
     * @return 受影响行数
     */
    int deleteExportFileByExportIds(String[] exportIds);
}
