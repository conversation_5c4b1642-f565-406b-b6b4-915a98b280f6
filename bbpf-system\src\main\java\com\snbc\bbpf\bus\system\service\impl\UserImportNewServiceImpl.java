/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.snbc.bbpf.bus.system.constans.CommonConstant;
import com.snbc.bbpf.bus.system.constans.Constant;
import com.snbc.bbpf.bus.system.constans.NumberConstant;
import com.snbc.bbpf.bus.system.constans.OrgConstant;
import com.snbc.bbpf.bus.system.enums.BusTemplateCodeEnum;
import com.snbc.bbpf.bus.system.enums.UserLockStatusEnum;
import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.pool.ThreadPool;
import com.snbc.bbpf.bus.system.service.OrgService;
import com.snbc.bbpf.bus.system.service.RoleService;
import com.snbc.bbpf.bus.system.service.UserImportNewService;
import com.snbc.bbpf.bus.system.service.UserImportService;
import com.snbc.bbpf.bus.system.utils.AllOrg;
import com.snbc.bbpf.bus.system.utils.BossDES3Util;
import com.snbc.bbpf.bus.system.utils.CheckOrgPermission;
import com.snbc.bbpf.bus.system.utils.ConverterUtils;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.bus.system.utils.ExcelReadListener;
import com.snbc.bbpf.bus.system.utils.ImportUserUtil;
import com.snbc.bbpf.bus.system.utils.ResultUtil;
import com.snbc.bbpf.bus.system.utils.SendMsgUtil;
import com.snbc.bbpf.commons.strings.security.parse.MobilePhoneParse;
import com.snbc.bbpf.component.captcha.model.common.NumConstant;
import com.snbc.bbpf.component.dataauth.utils.DataAuthCurrentUser;
import com.snbc.bbpf.component.dataauth.utils.DataRuleHelper;
import com.snbc.bbpf.system.db.common.dto.RoleNamesDto;
import com.snbc.bbpf.system.db.common.dto.UserDto;
import com.snbc.bbpf.system.db.common.dto.UserExportDtoNew;
import com.snbc.bbpf.system.db.common.dto.UserImportDtoNew;
import com.snbc.bbpf.system.db.common.entity.ExportFile;
import com.snbc.bbpf.system.db.common.entity.ExportFileOrg;
import com.snbc.bbpf.system.db.common.entity.Message;
import com.snbc.bbpf.system.db.common.entity.Org;
import com.snbc.bbpf.system.db.common.entity.User;
import com.snbc.bbpf.system.db.common.entity.UserOrg;
import com.snbc.bbpf.system.db.common.mapper.ExportFileMapper;
import com.snbc.bbpf.system.db.common.mapper.ExportFileOrgMapper;
import com.snbc.bbpf.system.db.common.mapper.RoleMapper;
import com.snbc.bbpf.system.db.common.mapper.UserMapper;
import com.snbc.bbpf.system.db.common.mapper.UserOrgMapper;
import com.snbc.bbpf.system.db.common.model.RoleRelationUser;
import com.snbc.bbpf.system.db.common.vo.OrgVo;
import com.snbc.component.filestorage.args.UploadArg;
import com.snbc.component.filestorage.args.UploadResult;
import com.snbc.component.filestorage.fileutil.FileUpAndDownload;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * @ClassName: UserImportServiceImpl
 * @Description: 用户新增用户和导入用户处理
 * @module: si-bbpf-system
 * @Author: wangsong
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Service
@Slf4j
public class UserImportNewServiceImpl implements UserImportNewService {
    private static final String TYPEEXCLEX=".xlsx";
    private static final String TYPEEXCLE=".xls";
    public static final int SUCCESS = 2;
    public static final int FAIL = 3;
    public static final int GENFILE = 1;
    public static final int MAXIMPORTNUM = 1000;
    @Autowired
    private UserMapper userMapper;
    /**
     * 用户操作数据库层，查询判断和插入数据集合
     */
    @Autowired
    private UserOrgMapper userOrgMapper;
    /**
     * 权限mapper
     */
    @Autowired
    private RoleMapper roleMapper;
    @Autowired
    private CheckOrgPermission checkOrgPermission;

    /**
     * 事务
     */
    @Autowired
    private PlatformTransactionManager platformTransactionManager;
    @Autowired
    private TransactionDefinition transactionDefinition;
    /**
     * 旧用户service
     */
    @Autowired
    private UserImportService userImportService;
    /**
     * 部门service
     */
    @Autowired
    private OrgService orgService;
    /**
     * 权限service
     */
    @Autowired
    private RoleService roleService;
    /**
     * redis缓存
     */
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private AllOrg allOrg;
    @Autowired
    private ExportFileMapper exportFileMapper;
    @Autowired
    private ExportFileOrgMapper exportFileOrgMapper;
    //文件上传下载
    @Autowired
    private FileUpAndDownload fileUpAndDownload;
    @Value("${bbpf.exportFile.uploadPath}")
    private String exportFilePath;
    /**
     * 导入部门用户信息
     * @param file
     * @param response
     * @return
     * @throws Exception
     */
    @Override
    public void importUserNew(MultipartFile file,HttpServletResponse response) throws Exception {
        long startTime = System.currentTimeMillis();
        log.debug("importUserNew startTime:{}", startTime);
        // 验证 是否为登录用户
        String createUserId= CurrentUser.getUserId();
        ErrorMessage.USER_INVALID.assertNotNull(createUserId);
        // 验证文件不能为空
        ErrorMessage.USER_FILE_INVALID.assertNotNull(file);
        ErrorMessage.USER_FILE_INVALID.assertNotNull(file.getOriginalFilename());
        // 验证文件后缀格式只能是excle
        ErrorMessage.USER_FILE_TYPE_INVALID.assertEquals(file.getOriginalFilename().toLowerCase(Locale.getDefault()).endsWith(TYPEEXCLE)
                ||file.getOriginalFilename().toLowerCase(Locale.getDefault()).endsWith(TYPEEXCLEX),true);
        // 文件转对象
        ExcelReadListener<UserImportDtoNew> excelReadListener = new ExcelReadListener<>();
        // 从第12行开始读 后续模板从第三行读取
        EasyExcelFactory.read(file.getInputStream(), UserImportDtoNew.class, excelReadListener).sheet().headRowNumber(NumberConstant.NO_TWO).doRead();
        List<UserImportDtoNew> list = excelReadListener.getList();

        log.info("importUserNew excelReadList:{},readExcelEndUseTime:{}", list.size(), System.currentTimeMillis()- startTime);
        ErrorMessage.OUT_OF_NUM.assertEquals(list.size() > NumConstant.ZERO && list.size() <= MAXIMPORTNUM, true);

        //设置子线程共享
        ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        RequestContextHolder.setRequestAttributes(sra, true);
        String userName = CurrentUser.getUserName();
        List<OrgVo> orgIdPathListDataRule = allOrg.getDataRuleOrg(CurrentUser.getUserId());
        String url = CurrentUser.getRequestUrl();
        ThreadPool.execute(()-> {
            // 线程中 变量缺失，重新赋值
            DataRuleHelper.setRequestUrl(url);
            CurrentUser.setUserId(createUserId);
            DataAuthCurrentUser.setUserId(createUserId);
            importUserTask(createUserId,userName,list,orgIdPathListDataRule);
        });
    }

    /**
     *
     * @param currentUserId 当前登录人
     * @param userName  当前登录人名称
     * @param list  导入用户的列表
     * @param orgIdPathListDataRule 当前人具有数据权限的组织机构
     */
    private void importUserTask(String currentUserId,String userName, List<UserImportDtoNew> list,List<OrgVo> orgIdPathListDataRule) {
        List<UserImportDtoNew> errorList = new ArrayList<>();
        //获取组织机构数据
        Map<String,String > orgMap = userImportService.initAllOrgLst();
        Map<String,String > roleMap = getAllRoleNameMap();
        //循环excel数据
        for(UserImportDtoNew importDto: list){
            handleImportUserRow(currentUserId, errorList, orgMap, roleMap, importDto);
        }
        String[] userIdArr = {currentUserId};
        String fileName=formatDate("yyyyMMddHHmmss")+"-用户列表导入结果.xlsx";
        if(errorList.isEmpty()){
            Message message= Message.builder().receiveNos(userIdArr)
                    .templateCode(BusTemplateCodeEnum.USER_IMPORT_MSG.getName())
                    .msgTitle(BusTemplateCodeEnum.USER_IMPORT_MSG.getTitle()).build();
            SendMsgUtil.sendSysMessage(message);
        }else{
            String exportId=UUID.randomUUID().toString();
            ExportFile exportFile=ExportFile
                    .builder()
                    .createUser(userName)
                    .createUserId(currentUserId)
                    .exportId(exportId)
                    .status(GENFILE)
                    .fileName(fileName)
                    .createTime(LocalDateTime.now())
                    .build();
            exportFileMapper.insertExportFile(exportFile);

            insertExportFileOrg(orgIdPathListDataRule, exportId);
            //生成excel
            try(OutputStream out = new ByteArrayOutputStream()) {
                EasyExcelFactory.write(out, UserImportDtoNew.class).sheet("用户信息").doWrite(errorList);
                byte[] fileBytes = ((ByteArrayOutputStream) out).toByteArray();
                InputStream stream = new ByteArrayInputStream(fileBytes);
                //第四步上传到又拍云
                UploadArg fileArg = (new UploadArg.UploadArgBuilder(exportFilePath + "exportfile/" + fileName,
                        stream, exportId, fileBytes.length)).override(true).build();
                UploadResult result = fileUpAndDownload.upload(fileArg);
                if (result.getCode().equals(ResultUtil.success().getCode())) {
                    exportFile.setUrl(result.getFileUrl());
                    exportFile.setStatus(SUCCESS);
                } else {
                    exportFile.setStatus(FAIL);
                }
                //第五步更新文件导出表
                exportFileMapper.updateExportFile(exportFile);
                Message message = Message.builder().receiveNos(userIdArr)
                        .templateCode(BusTemplateCodeEnum.USER_IMPORT_FAIL_MSG.getName())
                        .templateParamJsonArr(convertTemplateParamJsonArr(fileName))
                        .msgTitle(BusTemplateCodeEnum.USER_IMPORT_FAIL_MSG.getTitle()).build();
                SendMsgUtil.sendSysMessage(message);
            }catch (Exception e) {
                log.warn("export file error:",e);
            }
        }
    }

    /**
     *
     * @param orgList 当前人数据权限的组织机构
     * @param exportId
     */
    private void insertExportFileOrg(List<OrgVo> orgList, String exportId) {
        if(CollectionUtils.isEmpty(orgList)){
            log.warn("insertExportFile orgList is null");
            return;
        }
        List<ExportFileOrg> exportFileOrgList = Lists.newArrayList();
        orgList.forEach(org -> {
            ExportFileOrg exportFileOrg = ExportFileOrg.builder().exportId(exportId).orgId(org.getOrgId()).id(UUID.randomUUID().toString()).build();
            exportFileOrgList.add(exportFileOrg);
        });
        exportFileOrgMapper.insert(exportFileOrgList);
    }

    /**
     * 处理从excel导入的数据
     * @param createUserId
     * @param errorList
     * @param orgMap
     * @param roleMap
     * @param importDto
     */
    private void handleImportUserRow(String createUserId,
                                     List<UserImportDtoNew> errorList, Map<String, String> orgMap,
                                     Map<String, String> roleMap, UserImportDtoNew importDto) {
        TransactionStatus transactionStatus = platformTransactionManager.getTransaction(transactionDefinition);
        try{
            //检查输入的用户数据
            if (ckeckImportUserDtoNew(importDto)) {
                long checkDataEnd = System.currentTimeMillis();
                //操作用户
                String userId = saveOrUpdateUser(importDto, createUserId);
                long saveUserDataEnd = System.currentTimeMillis();
                log.debug("saveOrUpdateUser userId:{},saveUserDataEndUseTime",saveUserDataEnd-checkDataEnd);
                //操作组织机构
                List<String> orgList = saveOrUpdateOrg(importDto, orgMap,userId,createUserId);
                // 权限校验
                checkOrgPermission.checkOrg(createUserId, orgList);

                long saveOrgDataEnd = System.currentTimeMillis();
                log.debug("orgList size:{},saveOrgDataEndUseTime={}",orgList.size(),saveOrgDataEnd-saveUserDataEnd);
                //操作用户组织机构
                bindingOrg(userId,orgList);
                //操作用户角色相关
                bindingRole(importDto, roleMap,userId);
                long saveBindingDataEnd = System.currentTimeMillis();
                log.debug("importUserNew saveBindingDataEndUseTime={}",saveBindingDataEnd-saveOrgDataEnd);
            } else {
                log.warn("UserImportDtoNew error:{}", importDto.toString());
                errorList.add(importDto);
            }
            platformTransactionManager.commit(transactionStatus);
        }catch(BusinessException e){
            log.error("importUserNew BusinessException params={},code={}", importDto.toString(),e);
            platformTransactionManager.rollback(transactionStatus);
            importDto.setErrorMsg(e.getMessage());
            errorList.add(importDto);
        }catch (Exception e){
            log.error("importUserNew Exception params={}", importDto.toString(),e);
            platformTransactionManager.rollback(transactionStatus);
            importDto.setErrorMsg("导入数据发生异常"+e.getMessage());
            errorList.add(importDto);
        }
    }

    /**
     * 绑定权限
     * @param importDto
     * @param roleMap
     * @param userId
     */
    private void bindingRole(UserImportDtoNew importDto,Map<String,String > roleMap,String userId){
        try{
            if(StringUtils.isNotBlank(importDto.getBelongRoleNames())){
                List<String> roleIds = new ArrayList<>();
                for(String roleName:importDto.getBelongRoleNames().split(",")){
                    String roleId = roleMap.get(roleName);
                    if(StringUtils.isNotBlank(roleId)){
                        roleIds.add(roleId);
                    }
                }
                log.info("bindingRole roleIds:{}",roleIds.size());
                if(!CollectionUtils.isEmpty(roleIds)){
                    RoleRelationUser roleRelationUser = new RoleRelationUser();
                    roleRelationUser.setRoleIds(roleIds.toArray(new String[0]));
                    roleRelationUser.setUserIds(new String[]{userId});
                    roleRelationUser.setStatus("1");
                    log.info("roleService roleRelationUser：{}",roleRelationUser.toString());
                    roleService.roleRelationUser(roleRelationUser);
                }
            }
        }catch (Exception e){
            throw new BusinessException(ErrorMessage.PERMISSION_BIND_USER_ERROR.getMessage(),ErrorMessage.PERMISSION_BIND_USER_ERROR.getCode(),e);
        }
    }

    /**
     * 绑定部门
     * @param userId
     * @param orgList
     */
    private void bindingOrg(String userId,List<String> orgList){
        try{
            int del = userOrgMapper.deleteByUserId(userId);
            log.info("bindingOrg deleteByUserId:{}",del);
            for(String orgId : orgList){
                userOrgMapper.insertOrUpdate(UserOrg.builder().id(UUID.randomUUID().toString()).userId(userId).orgId(orgId).build());
            }
            //删除缓存
            redisTemplate.delete(Constant.USER_DATA_AUTH_ORG_AND_SUB_ORG);
            redisTemplate.delete(Constant.USER_DATA_AUTH_ORG);
            // 删除 ：组织机构数据权限
            redisTemplate.delete(Constant.ORG_DATA_AUTH_ORG_AND_SUB_ORG);
            redisTemplate.delete(Constant.ORG_DATA_AUTH_ORG_ORG);
            redisTemplate.delete(Constant.ORG_DATA_AUTH_ORG_AND_CREATE_ORG);
        }catch (Exception e){
            throw new BusinessException(ErrorMessage.ORG_BIND_USER_ERROR.getMessage(),ErrorMessage.ORG_BIND_USER_ERROR.getCode(),e);
        }
    }

    /**
     * 保存更新部门信息
     *
     * @param importDto
     * @param orgMap
     * @param userId
     * @param createUserId
     * @return
     * @throws BusinessException
     * @throws Exception
     */
    private List<String> saveOrUpdateOrg(UserImportDtoNew importDto, Map<String,String > orgMap, String userId, String createUserId) throws BusinessException {
        String[] orgRemarks = StringUtils.isNotBlank(importDto.getOrgRemarks()) ? importDto.getOrgRemarks().split(";") : new String[0];
        String[] orgPaths = importDto.getBelongOrgNames().split(";");
        String[] isManagers = StringUtils.isNotBlank(importDto.getIsManagers()) ? importDto.getIsManagers().split(";") : new String[0];
        //设为部门主管的map key为orgid value为1则是 为后续绑定组织数据
        List<String> orgList = new ArrayList<>();
        for (int i = 0; i < orgPaths.length; i++) {
            //判断部门描述在此次循环坐标中是否有对应的描述 有则赋值 没有置空防止数据覆盖
            String remark = "";
            if (orgRemarks.length - 1 >= i && StringUtils.isNotBlank(orgRemarks[i])) {
                remark = orgRemarks[i];
            } else {
                remark = "";
            }
            //判断是否设为管理员在此次循环坐标中是否为是 是则赋值 否则判断以前ID是否为该用户 是则清空
            String isManager = "";
            if (isManagers.length - 1 >= i) {
                isManager = isManagers[i];
            }
            String[] orgNames = orgPaths[i].split("/");
            //首次循环第一个组织机构的父级为0 后面更新此值
            String parentId = OrgConstant.ORG_PARNENT_ID;
            StringBuilder orgPath = new StringBuilder("组织机构");
            orgList.addAll(orgNames(orgPath, orgNames, remark, isManager, orgMap, userId, parentId,importDto,createUserId));
        }
        return orgList;
    }

    /**
     * 组装内部循环org
     *
     * @param orgPath
     * @param orgNames
     * @param remark
     * @param isManager
     * @param orgMap
     * @param userId
     * @param parentId
     * @param importDto
     * @param createUserId
     */
    private  List<String> orgNames(StringBuilder orgPath, String[] orgNames, String remark, String isManager,
                                   Map<String,String > orgMap, String userId, String parentId, UserImportDtoNew importDto, String createUserId) {
        log.info("orgNames params orgPath:{},remark:{},isManager:{},userId:{},parentId:{}", orgPath, remark, isManager, userId, parentId);
        List<String> orgList = new ArrayList<>();
        for (int j = 0; j < orgNames.length; j++) {
            orgPath.append("/" + orgNames[j]);
            String orgId = orgMap.get(orgPath.toString());
            //找到对应部门并且部门描述不为空 则进行更新操作
            log.debug("orgNames[j] orgId:{},j:{},orgNames.length-1:{},remark:{}", orgId, j,
                    orgNames.length - 1, remark);
            if (StringUtils.isNotBlank(orgId)) {
                updateOrg(orgNames, remark, isManager, userId, j, orgId, importDto,createUserId);
                //没有匹配到org 新增操作
            } else {
                orgId = addOrg(orgNames, remark, isManager, userId, parentId, j, importDto.getPhone(),createUserId);
                //插入新增的组织机构到全量中 确保后面不会重复新增
                orgMap.put(orgPath.toString(), orgId);
            }
            if (j == orgNames.length - 1) {
                orgList.add(orgId);
                log.debug("orgList add orgId:{}", orgId);
            }
            //更新parentId为下次循环准备
            parentId = orgId;
        }
        return orgList;
    }

    private String addOrg(String[] orgNames, String remark, String isManager, String userId, String parentId, int j, String phone, String createUserId) {
        OrgVo orgVo;
        String orgId;
        orgVo = new OrgVo();
        orgVo.setParentId(parentId);
        orgVo.setOrgName(orgNames[j]);
        if (j == orgNames.length - 1) {
            if (StringUtils.isNotBlank(remark)) {
                orgVo.setOrgDesc(remark);
            }
            if ("是".equalsIgnoreCase(isManager)) {
                // 不用加主管手机号 orgService.addOrg方法会加手机号
                orgVo.setSupervisor(userId);
            }
        }
        orgId = orgService.addOrg(orgVo,createUserId);
        return orgId;
    }

    private void updateOrg(String[] orgNames, String remark, String isManager, String userId, int j, String orgId, UserImportDtoNew importDto, String createUserId) {
        OrgVo orgVo;
        if (j == orgNames.length - 1) {
            orgVo = orgService.getOrg(orgId);
            if (setRemarkAdminManager(orgVo, isManager, userId, remark, importDto.getPhone())) {
                log.debug("updateOrg orgVo:{}", JSON.toJSONString(orgVo));
                orgService.updateOrg(orgVo, importDto.getUserName(),createUserId);
            }
        }
    }

    private boolean setRemarkAdminManager(OrgVo orgVo, String isManager, String userId, String remark, String phone) {
        boolean result = false;
        if (StringUtils.isNotBlank(remark)) {
            orgVo.setOrgDesc(remark);
            result = true;
        }
        if ("是".equalsIgnoreCase(isManager)) {
            orgVo.setSupervisor(userId);
            //这里不用加密 orgService.updateOrg会加密手机号
            orgVo.setSupervisorPhone(phone);
            result = true;
        } else {
            log.warn("no isManager type");
        }
        return result;
    }

    /**
     * 保存更新用户信息
     * @param importDto
     * @param createUserId
     * @return
     * @throws BusinessException
     * @throws Exception
     */
    private String saveOrUpdateUser(UserImportDtoNew importDto,String createUserId){
        //加密之后根据手机号查询
        User user = userMapper.selectByPhone(BossDES3Util.encrypt(importDto.getPhone()));
        UserDto userDto = new UserDto();
        if(null != user){
            userDto = ConverterUtils.getSimpleConverObject(user,UserDto.class);
        }
        userDto.setUserName(importDto.getUserName());
        userDto.setJobNumber(importDto.getJobNumber());
        userDto.setHasLock(UserLockStatusEnum.getStatus(importDto.getHasLockName()));
        userDto.setPhone(importDto.getPhone());
        userDto.setEmail(importDto.getEmail());
        userDto.setCreateUserId(createUserId);
        log.info("saveOrUpdateUser Dto :{}",JSON.toJSONString(userDto));
        return userImportService.saveUser(userDto);
    }

    /**
     * 校验基础信息
     * @param importDto
     * @return
     * @throws Exception
     */
    private static StringBuilder checkBasicData (UserImportDtoNew importDto){
        StringBuilder errorMsg = new StringBuilder();
        //姓名校验
        if(StringUtils.isBlank(importDto.getUserName())){
            errorMsg.append(",姓名不能为空");
        }else {
            if (StringUtils.isBlank(ImportUserUtil.match(importDto.getUserName(), "[\\s\\S]{1,20}"))) {
                errorMsg.append(",姓名最大长度为20位");
            }
            if (importDto.getUserName().contains("%") || importDto.getUserName().contains("％")) {
                errorMsg.append(",姓名不能带有%");
            }
        }
        //工号校验
        if(StringUtils.isBlank(importDto.getJobNumber()) && !importDto.getJobNumber().matches("^[A-Za-z0-9-]+$")){
            errorMsg.append(",工号不能为空且必须由数字、字母或中划线组成");
        }
        if(StringUtils.isBlank(ImportUserUtil.match(importDto.getJobNumber(), "[\\s\\S]{1,20}"))){
            errorMsg.append(",工号最大长度为20位");
        }
        return errorMsg;
    }
    /**
     * 校验基础信息
     * @param importDto
     * @return
     * @throws Exception
     */
    private static StringBuilder checkBasicData2 (UserImportDtoNew importDto){
        StringBuilder errorMsg = new StringBuilder();
        //手机号校验
        if(StringUtils.isBlank(importDto.getPhone())){
            errorMsg.append(",手机号码不能为空");
        }
        if (StringUtils.isBlank(ImportUserUtil.match(importDto.getPhone(), "[0-9\\-]{1,16}"))) {
            errorMsg.append(",手机号码长度为16位内的数字");
        }
        //邮箱校验
        if (StringUtils.isNotBlank(importDto.getEmail())){
            if(importDto.getEmail().length()> NumberConstant.NO_SIXTY_FOUR) {
                errorMsg.append(",邮箱地址最多64位");
            }
            if(StringUtils.isBlank(ImportUserUtil.match(importDto.getEmail(), CommonConstant.USER_EMAIL_MATCH))){
                errorMsg.append(",邮箱格式错误");
            }
        }
        return errorMsg;
    }

    /**
     * 校验部门信息
     * @param importDto
     * @return
     * @throws Exception
     */
    private StringBuilder checkOrgData (UserImportDtoNew importDto) {
        StringBuilder errorMsg = new StringBuilder();
        //部门校验
        checkBelongOrgNames(importDto, errorMsg);
        //部门描述校验
        checkOrgRemarks(importDto, errorMsg);
        //部门主管校验
        checkIsManagers(importDto, errorMsg);
        //状态校验
        errorMsg.append(checkHasLock(importDto));
        return errorMsg;
    }

    /**
     * 部门主管校验
     * @param importDto
     * @param errorMsg
     */
    private void checkIsManagers(UserImportDtoNew importDto, StringBuilder errorMsg) {
        if(StringUtils.isNotBlank(importDto.getIsManagers())){
            for(String isManager : importDto.getIsManagers().split(";")){
                if(StringUtils.isBlank(isManager) || !"是否".contains(isManager)){
                    errorMsg.append(",是否为部门主管应为：是/否");
                    break;
                }
            }
        }
    }

    /**
     * 部门描述校验
     * @param importDto
     * @param errorMsg
     */
    private void checkOrgRemarks(UserImportDtoNew importDto, StringBuilder errorMsg) {
        if(StringUtils.isNotBlank(importDto.getOrgRemarks())){
            for(String remark : importDto.getOrgRemarks().split(";")){
                if(remark.length()> NumberConstant.NO_TWOH){
                    errorMsg.append(",部门描述最长200字");
                    break;
                }
            }
        }
    }

    /**
     * 部门校验
     * @param importDto
     * @param errorMsg
     */
    private void checkBelongOrgNames(UserImportDtoNew importDto, StringBuilder errorMsg) {
        if(StringUtils.isBlank(importDto.getBelongOrgNames())){
            errorMsg.append(",所属部门不能为空");
        }else{
            for(String orgPath : importDto.getBelongOrgNames().split(";")){
                for(String orgName:orgPath.split("/")){
                    if(orgName.length()> NumberConstant.NO_TWENTY){
                        errorMsg.append(",部门名称最长20位");
                        break;
                    }
                }
            }
        }
    }

    /**
     * 校验用户状态
     * @param importDto
     * @return
     */
    private static StringBuilder checkHasLock(UserImportDtoNew importDto){
        StringBuilder errorMsg = new StringBuilder();
        //状态校验
        if(StringUtils.isBlank(importDto.getHasLockName())){
            errorMsg.append(",用户状态不能为空");
        }else{
            Integer hasLock=UserLockStatusEnum.getStatus(importDto.getHasLockName());
            if (hasLock==null){
                errorMsg.append(",用户状态应为：启用或禁用");
            }
        }
        return errorMsg;
    }
    /**
     * 导入用户校验
     * @param importDto
     * @return
     */
    private boolean ckeckImportUserDtoNew (UserImportDtoNew importDto) {
        StringBuilder result = checkBasicData(importDto).append(checkBasicData2(importDto)).append(checkOrgData(importDto));
        if(StringUtils.isNotBlank(result.toString())){
            importDto.setErrorMsg(result.toString().substring(NumberConstant.NO_ONE));
            return false;
        }
        return true;
    }

    /**
     * 获取所有权限map key权限名 value 权限ID
     * @return
     */
    private Map<String,String> getAllRoleNameMap() {
        List<Map<String,String>> roleList = roleMapper.getAllRoleList(null);
        Map<String,String> roleNameMap=  new HashMap<>();
        if(!CollectionUtils.isEmpty(roleList)) {
            for(Map<String,String> map : roleList){
                roleNameMap.put(map.get("roleName"),map.get("roleId"));
            }
        }
        return roleNameMap;
    }

    @Override
    public void exportUserNew(String orgId) {
        exportUserFile(orgId,null);
    }

    @Override
    public void exportPartUserNew(String orgId, String userIds)  {
        exportUserFile(orgId,userIds);
    }

    /**
     *导出文件
     * @param orgId  前端选中的组织机构
     * @param userIds  前端选中的用户
     */
    private void exportUserFile(String orgId, String userIds)  {
        String userId=CurrentUser.getUserId();
        String userName=CurrentUser.getUserName();
        List<OrgVo> orgIdPathListDataRule = allOrg.getDataRuleOrg(CurrentUser.getUserId());
        String url = CurrentUser.getRequestUrl();
        ThreadPool.execute(()->{
            // 线程中 变量缺失，重新赋值
            DataRuleHelper.setRequestUrl(url);
            CurrentUser.setUserId(userId);
            DataAuthCurrentUser.setUserId(userId);
            //第一步插入文件导出表
            String exportId=UUID.randomUUID().toString();
            String fileName=formatDate("yyyyMMddHHmmss")+"-用户数据.xlsx";
            ExportFile exportFile=ExportFile
                    .builder()
                    .createUser(userName)
                    .createUserId(userId)
                    .exportId(exportId)
                    .status(GENFILE)
                    .fileName(fileName)
                    .createTime(LocalDateTime.now())
                    .build();
            exportFileMapper.insertExportFile(exportFile);

            insertExportFileOrg(orgIdPathListDataRule, exportId);

            //第二步根据条件得到导出用户数据
            List<UserExportDtoNew> userInfo = exportUsers(orgId, userIds,userId,orgIdPathListDataRule);
            //第三步生成excel
            genExcelFile(userId, exportId, fileName, exportFile, userInfo);
        });
    }

    private void genExcelFile(String userId, String exportId, String fileName, ExportFile exportFile, List<UserExportDtoNew> userInfo) {
        try(OutputStream out = new ByteArrayOutputStream()) {
            EasyExcelFactory.write(out,UserExportDtoNew.class).sheet("用户信息")
                    .doWrite(userInfo);
            byte[] fileBytes=((ByteArrayOutputStream)out).toByteArray();
            InputStream stream=new ByteArrayInputStream(fileBytes);
            //第四步上传到又拍云
            UploadArg fileArg = (new UploadArg.UploadArgBuilder(exportFilePath + "exportfile/" + fileName,
                    stream, exportId,fileBytes.length)).override(true).build();
            UploadResult result = fileUpAndDownload.upload(fileArg);
            if(result.getCode().equals(ResultUtil.success().getCode())){
                exportFile.setUrl(result.getFileUrl());
                exportFile.setStatus(SUCCESS);
            }else {
                exportFile.setStatus(FAIL);
            }
            //第五步更新文件导出表
            exportFileMapper.updateExportFile(exportFile);
            String[] userIdArr = {userId};
            Message message=Message.builder().receiveNos(userIdArr)
                    .templateCode(BusTemplateCodeEnum.DATA_EXPORT_MSG.getName())
                    .templateParamJsonArr(convertTemplateParamJsonArr(fileName))
                    .msgTitle(BusTemplateCodeEnum.DATA_EXPORT_MSG.getTitle()).build();
            SendMsgUtil.sendSysMessage(message);
        } catch (Exception e) {
            log.warn("export file error:",e);
        }
    }

    /**
     * 转换模板参数
     * @param fileName
     * @return
     */
    private static String[] convertTemplateParamJsonArr(String fileName){
        Map<String,String> templateParamMap=new HashMap<>();
        templateParamMap.put("fileName", fileName);
        return new String[]{new Gson().toJson(templateParamMap)};
    }

    private static String formatDate(String dateFormat) {
        DateTimeFormatter dateTimeFormatter=DateTimeFormatter.ofPattern(dateFormat);
        return LocalDateTime.now().format(dateTimeFormatter);

    }

    /**
     *
     * @param orgId 页面所选中的组织机构
     * @param userIds  选中的用户：部分导出时用到
     * @param currentUser  当前登录的人
     * @param orgIdPathListDataRule  当前人的数据权限的组织机构
     * @return
     */
    private List<UserExportDtoNew> exportUsers(String orgId, String userIds,String currentUser,List<OrgVo> orgIdPathListDataRule) {
        List<String> userIdList = new ArrayList<>();
        List<UserExportDtoNew> userExportDtoList;
        if (StringUtils.isNotBlank(userIds)) {
            userIdList = Arrays.asList(userIds.split(Pattern.quote(",")));
            // 用户不为空，则导出选中的用户
            userExportDtoList = userMapper.queryExportUserListByUserId(userIdList);
        } else {
            boolean isAdmin = allOrg.isAdmin(currentUser);
            // 是超管
            if(isAdmin){
                List<String> orgPathList = allOrg.processAdmin(orgId);
                //超管：组织机构及下级下的用户
                userExportDtoList = userMapper.queryExportUserListByOrgPath(orgPathList);
            }else {
                //true 前端的orgId是所属组织机构的上级
                boolean isSub = allOrg.isSub(orgIdPathListDataRule, orgId);
                Org orgExsit = allOrg.getOrgByOrgId(orgId);
                //点击的是前端 是 数据权限的组织机构的上级
                if(isSub){
                    List<String> orgIdList = orgIdPathListDataRule.stream().map(OrgVo::getOrgId).collect(Collectors.toList());
                    userExportDtoList = userMapper.queryExportUserListByOrgPathByOrgId(orgIdList);
                }else {
                    //如果点击的是末节点
                    List<Org> childList = allOrg.queryOrgListByParentId(orgExsit.getParentId());
                    if(CollectionUtils.isEmpty(childList)){
                        userExportDtoList = userMapper.queryExportUserListByOrgPathByOrgId(Collections.singletonList(orgId));
                    }else {
                        //查询当前orgId的下级，
                        List<String> orgIdList = allOrg.processGeneralUser(orgIdPathListDataRule, orgExsit.getOrgPath());
                        userExportDtoList = userMapper.queryExportUserListByOrgPathByOrgId(orgIdList);
                    }
                }
            }
        }
        // 数据为空时 返回null，如果不返回null会导出后excle打不开。
        if (CollectionUtils.isEmpty(userExportDtoList)) {
            return new ArrayList<>();
        }
        List<UserExportDtoNew> userExportNewDtoList = installUserExportDtoNew(userExportDtoList,userIdList);
        log.info("Number of exported data items：{}", userExportDtoList.size());
        return userExportNewDtoList;
    }

    /**
     * 组装导出数据
     * @param userExportDtoList
     * @param userIdList
     * @return
     */
    private List<UserExportDtoNew> installUserExportDtoNew(List<UserExportDtoNew> userExportDtoList, List<String> userIdList){
        List<RoleNamesDto> roleNames = roleMapper.selectRoleNamesByUserId(userIdList);
        Map<String, String> userIdRoleNamesMap = roleNames.stream()
                .collect(Collectors.toMap(RoleNamesDto::getUserId,
                        roleNamesDto -> (roleNamesDto.getRoleNames() != null ? roleNamesDto.getRoleNames() : "")));
        for (UserExportDtoNew u : userExportDtoList) {
            //用户导出电话脱敏
            u.setPhone(setPhone(u));
            // 转换状态
            u.setHasLock(UserLockStatusEnum.getStatusName(Integer.valueOf(u.getHasLock())));
            u.setRoleNames(userIdRoleNamesMap.get(u.getUserId()));
        }
        return userExportDtoList;
    }

    private static String setPhone(UserExportDtoNew u) {
        MobilePhoneParse parsePhone = new MobilePhoneParse();
        return parsePhone.parseString(BossDES3Util.decrypt(u.getPhone()));
    }
}
