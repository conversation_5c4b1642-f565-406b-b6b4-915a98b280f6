/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;


/**
 * @ClassName: BusLog
 * @Description: bus log entity
 * @module: bbpf-bus-system
 * @Author: liuyi
 * @date: 2021/5/21
 * copyright 2020 barm Inc. All rights reserver
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Log {

    /** 日志ID */
    private String logId;

    /** 日志时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /** 操作员  */
    private String userId;

    /** 用户名 */
    private String userName;

    /** 组织机构ID */
    private String orgId;

    /** 组织机构名称 */
    private String orgName;

    /** 登录IP */
    private String ip;

    /** 操作模块 */
    private String logTarget;

    /** 操作类型 */
    private String logType;

    /** 创建人组织机构id */
    private String createOrgId;

    /** 中文日志内容 */
    private String zhContent;
    /** 英文日志内容 */
    private String enContent;
    /** 请求来源 */
    private String requestSource;

    /** */
    private String remark;

}
