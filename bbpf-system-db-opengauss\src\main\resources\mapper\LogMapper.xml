<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snbc.bbpf.system.db.common.mapper.LogMapper" >
  <resultMap id="BaseResultMap" type="com.snbc.bbpf.system.db.common.entity.Log" >
    <constructor >
      <idArg column="log_id" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="create_time" jdbcType="TIMESTAMP" javaType="java.time.LocalDateTime" />
      <arg column="user_id" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="user_name" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="org_id" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="org_name" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="ip" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="log_target" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="log_type" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="create_org_id" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="zh_content" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="en_content" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="request_source" jdbcType="VARCHAR" javaType="java.lang.String" />
      <arg column="remark" jdbcType="VARCHAR" javaType="java.lang.String" />
    </constructor>
  </resultMap>
  <sql id="Base_Column_List" >
    log_id, create_time, user_id, user_name, org_id, org_name, ip, log_target, log_type,
    create_org_id, zh_content, remark, en_content,request_source
  </sql>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>

  <select id="selectByExample" resultType="com.snbc.bbpf.system.db.common.dto.LogDto" parameterType="java.util.List" >
    select
    create_time as logTime, user_name as userName, ip, log_target as logTarget, log_type as logType,
    remark,request_source as requestSource,
    <choose>
      <when test="international != null and international !='' and international == 'zh'">
        zh_content as logContent
      </when>
      <otherwise>
        en_content as logContent
      </otherwise>
    </choose>
    from t_log
    where log_id in
    <foreach collection="logIdList" index="index" item="logId" open="(" separator="," close=")">
      #{logId,jdbcType=VARCHAR}
    </foreach>
    order by create_time desc;
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from t_log
    where log_id = #{logId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from t_log
    where log_id = #{logId,jdbcType=VARCHAR}
  </delete>

  <insert id="insert" parameterType="com.snbc.bbpf.system.db.common.entity.Log" >
    insert into t_log (log_id, create_time, user_id,
                       user_name, org_id, org_name,
                       ip, log_target, log_type,
                       create_org_id, zh_content, en_content,request_source, remark)
    values (#{logId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{userId,jdbcType=VARCHAR},
            #{userName,jdbcType=VARCHAR}, #{orgId,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR},
            #{ip,jdbcType=VARCHAR}, #{logTarget,jdbcType=VARCHAR}, #{logType,jdbcType=VARCHAR},
            #{createOrgId,jdbcType=VARCHAR}, #{zhContent,jdbcType=VARCHAR}, #{enContent,jdbcType=VARCHAR}
             , #{requestSource,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.snbc.bbpf.system.db.common.entity.Log" >
    insert into t_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="logId != null" >
        log_id,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="userId != null" >
        user_id,
      </if>
      <if test="userName != null" >
        user_name,
      </if>
      <if test="orgId != null" >
        org_id,
      </if>
      <if test="orgName != null" >
        org_name,
      </if>
      <if test="ip != null" >
        ip,
      </if>
      <if test="logTarget != null" >
        log_target,
      </if>
      <if test="logType != null" >
        log_type,
      </if>
      <if test="createOrgId != null" >
        create_org_id,
      </if>
      <if test="zhContent != null" >
        zh_content,
      </if>
      <if test="enContent != null" >
        en_content,
      </if>
      <if test="requestSource != null" >
        request_source,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="logId != null" >
        #{logId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="userName != null" >
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null" >
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null" >
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="ip != null" >
        #{ip,jdbcType=VARCHAR},
      </if>
      <if test="logTarget != null" >
        #{logTarget,jdbcType=VARCHAR},
      </if>
      <if test="logType != null" >
        #{logType,jdbcType=VARCHAR},
      </if>
      <if test="createOrgId != null" >
        #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="zhContent != null" >
        #{zhContent,jdbcType=VARCHAR},
      </if>
      <if test="enContent != null" >
        #{enContent,jdbcType=VARCHAR},
      </if>
      <if test="requestSource != null" >
        #{requestSource,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="selectExportLogs" resultType="com.snbc.bbpf.system.db.common.vo.ExportLogVo" parameterType="com.snbc.bbpf.system.db.common.vo.LogQuery" >
    select
    create_time as createTime,user_name as userName,ip,log_target as logTarget,log_type as logType,
    zh_content as zhContent,en_content as enContent,request_source as requestSource,remark
    from t_log where 1=1
    <if test="depId != null and depId !=''" >
      AND org_id like concat('%',  #{depId,jdbcType=VARCHAR},'%')
    </if>
    <if test="ip != null and ip !=''" >
      AND ip like concat('%',  #{ip,jdbcType=VARCHAR},'%')
    </if>
    <if test="endTime != null and endTime !=''" >
      AND create_time &lt;= #{endTime,jdbcType=VARCHAR}
    </if>
    <if test="startTime != null and startTime !=''" >
      AND create_time &gt;= #{startTime,jdbcType=VARCHAR}
    </if>
    <if test="logTarget != null and logTarget !=''" >
      AND log_target like concat('%', #{logTarget,jdbcType=VARCHAR},'%')
    </if>
    <if test="logType != null and logType !=''" >
      AND log_type like concat('%', #{logType,jdbcType=VARCHAR},'%')
    </if>
    <if test="userName != null and userName !=''" >
      AND user_name like concat('%', #{userName,jdbcType=VARCHAR},'%')
    </if>
    order by create_time desc
  </select>
  <select id="selelctBusLogIds" resultType="java.lang.String">
    select
    log_Id
    from t_log where 1=1
    <if test="depId != null and depId !=''" >
      AND org_id like concat('%',  #{depId,jdbcType=VARCHAR},'%')
    </if>
    <if test="endTime != null and endTime !=''" >
      AND create_time &lt;= #{endTime,jdbcType=VARCHAR}
    </if>
    <if test="startTime != null and startTime !=''" >
      AND create_time &gt;= #{startTime,jdbcType=VARCHAR}
    </if>
    <if test="logTarget != null and logTarget !=''" >
      AND log_target like concat('%', #{logTarget,jdbcType=VARCHAR},'%')
    </if>
    <if test="logType != null and logType !=''" >
      AND log_type like concat('%', #{logType,jdbcType=VARCHAR},'%')
    </if>
    <if test="userName != null and userName !=''" >
      AND user_name like concat('%', #{userName,jdbcType=VARCHAR},'%')
    </if>
    <if test="ip != null and ip !=''" >
      AND ip like concat('%', #{ip,jdbcType=VARCHAR},'%')
    </if>
    order by create_time desc
  </select>

  <select id="clearLogByOpenGauss" parameterType="java.lang.String" resultType="java.lang.Object">
    call public.p_clear_log(#{yesterday,jdbcType=VARCHAR},@res);
  </select>

  <select id="retentionDays" resultType="com.snbc.bbpf.system.db.common.dto.RetentionInfoDto">
    SELECT
      table_name AS tableName,
      retention_day AS retentionDay,
      extable_name AS extableName
    FROM
      t_table_part_master
    WHERE
      is_ex = 1;
  </select>

  <select id="selectClearPartition" resultType="java.lang.String">
    SELECT
    listagg (A .relname, ',') WITHIN GROUP (ORDER BY A .relname ASC) AS gg
    FROM
    pg_catalog.pg_partition A
    JOIN pg_catalog.pg_class b ON A .parentid = b.oid
    WHERE
    b.relname = #{tableName}
    AND A .parttype = 'p'
    AND A .relname <![CDATA[ < ]]> #{partitionDate}
    AND A .relname NOT IN (
    SELECT
    partition_name
    FROM
    t_part_drop_slave T
    WHERE
    T . TABLE_NAME = b.relname
    AND T .partition_name = A .relname
    )
    GROUP BY
    b.relname
  </select>

  <select id="performingPartitionCleanup" resultType="java.lang.Object">
    call public.p_ex_partition (
        #{tableName},#{partition},#{extableName},#{yesterday});
  </select>
</mapper>
