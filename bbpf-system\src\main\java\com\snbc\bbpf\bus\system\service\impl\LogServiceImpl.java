/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.snbc.bbpf.bus.system.constans.NumberConstant;
import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.mapstruct.ClassConvertorMapper;
import com.snbc.bbpf.bus.system.service.LogService;
import com.snbc.bbpf.bus.system.utils.DateUtil;
import com.snbc.bbpf.system.db.common.dto.LogDto;
import com.snbc.bbpf.system.db.common.dto.RetentionInfoDto;
import com.snbc.bbpf.system.db.common.entity.User;
import com.snbc.bbpf.system.db.common.mapper.LogMapper;
import com.snbc.bbpf.system.db.common.mapper.UserMapper;
import com.snbc.bbpf.system.db.common.vo.BusLogVo;
import com.snbc.bbpf.system.db.common.vo.ExportLogVo;
import com.snbc.bbpf.system.db.common.vo.LogQuery;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * @ClassName: BusLogServiceImpl
 * @Description: 提供日志的查询，导出功能
 * @module: bbpf-bus-system
 * @Author: liuyi
 * @date: 2021/5/21
 * copyright 2020 barm Inc. All rights reserver
 */
@Service
public class LogServiceImpl implements LogService {
    @Autowired
    private LogMapper logMapper;
    @Autowired
    private UserMapper userMapper;
    @Value("${bbpf.system.log.timeoutDays:30}")
    private long timeoutDays;


    /**
     * @param logQuery query json object,...........
     * @description: query bus log,...........................
     * @return: void
     * @author: liuyi
     * @time: 2021/6/7 16:11
     */
    @Override
    public PageInfo getLogList(LogQuery logQuery) {
        PageMethod.startPage(logQuery.getPageNum(), logQuery.getPageSize());
        List<String> logIdList = logMapper.selelctBusLogIds(logQuery);
        PageInfo<String> page1 = new PageInfo<>(logIdList);
        if (!logIdList.isEmpty()) {
            List<LogDto> logList = logMapper.selectByExample(logIdList, logQuery.getInternational());
            PageInfo<LogDto> page2 = new PageInfo<>(logList);
            return converPage(page1, page2);
        } else {
            return converPage(page1, new PageInfo());
        }
    }

    private PageInfo converPage(PageInfo converBeforePage, PageInfo converAfterPage) {
        converAfterPage.setStartRow(converBeforePage.getStartRow());
        converAfterPage.setEndRow(converBeforePage.getEndRow());
        converAfterPage.setPageSize(converBeforePage.getPageSize());
        converAfterPage.setPageNum(converBeforePage.getPageNum());
        converAfterPage.setIsLastPage(converBeforePage.isIsLastPage());
        converAfterPage.setIsFirstPage(converBeforePage.isIsFirstPage());
        converAfterPage.setTotal(converBeforePage.getTotal());
        converAfterPage.setSize(converBeforePage.getSize());
        converAfterPage.setHasNextPage(converBeforePage.isHasNextPage());
        converAfterPage.setNextPage(converBeforePage.getNextPage());
        converAfterPage.setPrePage(converBeforePage.getPrePage());
        converAfterPage.setPages(converBeforePage.getPages());
        converAfterPage.setNavigateFirstPage(converBeforePage.getNavigateFirstPage());
        converAfterPage.setNavigateLastPage(converBeforePage.getNavigateLastPage());
        converAfterPage.setNavigatepageNums(converBeforePage.getNavigatepageNums());
        return converAfterPage;
    }

    /**
     * @param logQuery bus log query json object ....................
     * @description: export log to excel..................
     * @return: void
     * @author: liuyi
     * @time: 2021/6/7 16:11
     */
    @Override
    public List<ExportLogVo> exportLogs(LogQuery logQuery) {
        //前端时间不为空
        if (StringUtils.isNotBlank(logQuery.getStartTime()) &&
                StringUtils.isNotBlank(logQuery.getEndTime())) {
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime startTime = LocalDateTime.parse(logQuery.getStartTime(), df);
            LocalDateTime endTime = LocalDateTime.parse(logQuery.getEndTime(), df);
            int monthDiff = DateUtil.calculateDateDiff(startTime, endTime);
            if (monthDiff > timeoutDays) {
                throw new BusinessException(String.valueOf(timeoutDays), ErrorMessage.LOG_EXPORT_TIMES_ERROR.getCode());
            }
        } else {
            //为空设置默认时间
            logQuery.setStartTime(DateUtil.getExpirationTime(-timeoutDays));
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            logQuery.setEndTime(df.format(LocalDate.now()).concat(" 23:59:59"));
        }
        return logMapper.selectExportLogs(logQuery);
    }

    @Override
    public void addLogsByFeign(BusLogVo busLogVo) {
        //获取当前登录人信息
        User user = userMapper.selectByPrimaryKey(busLogVo.getUserId());
        busLogVo.setUserName(user.getUserName());
        logMapper.insert(ClassConvertorMapper.INSTANCE.covertLog(busLogVo));
    }

    @Override
    public void retentionDays() {
        RetentionInfoDto retentionInfoDto = logMapper.retentionDays();
        int retentionDay = retentionInfoDto.getRetentionDay() != null ? retentionInfoDto.getRetentionDay() : NumberConstant.NO_ONE_K_2O;
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String partitionDate = "p_" + dateTimeFormatter.format(LocalDateTime.now().minusDays(retentionDay));
        String databasePartition = logMapper.selectClearPartition(retentionInfoDto.getTableName(), partitionDate);
        if (StringUtils.isNotBlank(databasePartition)) {
            String[] databasePartitionList = databasePartition.split(",");
            DateTimeFormatter dateTimeFormatter1 = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String yesterday = dateTimeFormatter1.format(LocalDateTime.now().minusDays(1));
            for (String dbPartition : databasePartitionList) {
                logMapper.performingPartitionCleanup(retentionInfoDto.getTableName(), dbPartition,
                        retentionInfoDto.getExtableName(), yesterday);
            }
        }
    }
}
