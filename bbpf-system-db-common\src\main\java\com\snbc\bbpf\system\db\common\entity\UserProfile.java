/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 用户配置对象 t_user_profile
 *
 * <AUTHOR>
 * @date 2022-12-05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserProfile {

    /** 主键id */
    private String profileId;
    /** 用户id */
    private String userId;
    private Integer moduleType;
    private String tenantCode;
    /** 配置项代码 */
    private String profileCode;
    //视图名称
    private String profileName;
    /** 配置项内容，json格式 */
    private String profileContent;
    /** 创建时间 */
    private LocalDateTime createTime;
    private LocalDateTime updateTime;


}
