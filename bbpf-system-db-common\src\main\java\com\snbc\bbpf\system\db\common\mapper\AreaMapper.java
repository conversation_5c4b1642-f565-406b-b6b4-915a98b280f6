package com.snbc.bbpf.system.db.common.mapper;

import com.snbc.bbpf.system.db.common.dto.AreaDto;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: AreaMapper
 * @Description: 省市区mapper
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/12/30
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Mapper
public interface AreaMapper {
    List<AreaDto> getArea(String areaCode);

    List<Map<String, String>> getAllArea();
}
