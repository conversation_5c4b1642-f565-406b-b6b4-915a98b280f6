/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.constans;

/**
 * @ClassName: Constant
 * 常量定义类
 * @module: bbpf-bus-system
 * @Author: yangweipeng
 * @date: 2021/5/18
 * 版权所有 山东新北洋信息技术股份有限公司
 * 受到法律的保护，任何公司或个人，未经授权不得擅自拷贝。
 */
public class Constant {
    public static final String PERMISSION_CACHE_CATEGORY = "permission_Cache_Data";
    public static final String ROLE_RESOURCE_PERMISSION = "role-resource-permission";
    public static final String USER_DATA_AUTH_ORG = "user-dataauth-org";
    public static final String USER_DATA_AUTH_ORG_AND_SUB_ORG = "user-dataauth-organdsuborg";
    /**
     * 组织机构数据权限：当前人组织机构及下级
     * */
    public static final String ORG_DATA_AUTH_ORG_AND_SUB_ORG = "org-data-auth-org-and-sub-org";
    /**
     * 组织机构数据权限：当前人组织机构
     * */
    public static final String ORG_DATA_AUTH_ORG_ORG = "org-data-auth-org";
    /**
     * 组织机构数据权限：当前人组织机构及创建人的组织机构
     * */
    public static final String ORG_DATA_AUTH_ORG_AND_CREATE_ORG = "org-data-auth-org-and-create-org";
    public static final String ALL_RESOURCE_PERMISSION = "all-resource-permission_";
    public static final String CACHE_KEY_GENERATOR_NAME = "cacheKeyGenerator";
    /**
     * 用户-角色对应关系
     */
    public static final String USERID_ROLE_ID_MAPPING = "userid-role-id-mapping_";
    public static final String INNER = "inner";
    public static final String PREV = "prev";
    public static final String NEXT = "next";
}
