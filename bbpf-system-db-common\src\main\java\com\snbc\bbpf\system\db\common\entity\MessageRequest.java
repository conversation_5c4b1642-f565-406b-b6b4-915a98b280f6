/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @ClassName:      MessageRequest.java
 * @Description:    发送短信消息
 * @module:
 * @Author:         wangsong
 * @date:           2021/5/25 18:38
 * copyright 2020 SNBC. All rights reserver
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MessageRequest{

	/**
	 * 模板ID
	 */
	private String templateId;

	/**
	 * 短信手机号（多个逗号分隔）
	 */
	private String telphones;
	
	/**
	 * 邮箱账号（多个逗号分隔）
	 */
	private String emails;
	
	/**
	 * 内容参数   json格式
	 */
	private Map<String,Object> data;
	
	/**
	 * 微信使用
	 */
	private String openId;
	/**
	 * 微信 使用
	 */
	private String url;
}
