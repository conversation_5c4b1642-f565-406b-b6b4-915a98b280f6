package com.snbc.bbpf.bus.system.job;

import com.snbc.bbpf.system.db.common.entity.ExportFile;
import com.snbc.bbpf.system.db.common.mapper.ExportFileMapper;
import com.snbc.component.filestorage.fileutil.FileCommonUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class FileCleanupJob {

    /**
     * 导出文件mapper
     */
    private ExportFileMapper exportFileMapper;
    /**
     * 文件处理类
     */
    @Autowired
    private FileCommonUtil fileCommonUtil;
    /**
     * setter注入
     *
     * @param exportFileMapper
     */
    @Autowired
    private void setExportFileMapper(ExportFileMapper exportFileMapper) {

        this.exportFileMapper = exportFileMapper;
    }

    /**
     * 定时任务业务,删除过期文档
     */
    @XxlJob("fileCleanupHandler")
    public void fileCleanupHandler() {
        log.info("begin fileCleanup job");
        /**
         * 获取所有过期文件
         */
        List<ExportFile> list= exportFileMapper.getAllExpireFiles();
        if(!list.isEmpty()){
            //循环处理文件删除
            list.stream().forEach(item->{
                if(item.getUrl()!=null&&!"".equals(item.getUrl())){
                    fileCommonUtil.delete(item.getUrl());
                }
                exportFileMapper.deleteExportFileByExportId(item.getExportId());
            });
        }
        log.info("fileCleanup finish");
    }

}
