/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service.impl;

import com.snbc.bbpf.bus.system.constans.CommonConstant;
import com.snbc.bbpf.bus.system.constans.NumberConstant;
import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.bus.system.service.UserExportService;
import com.snbc.bbpf.bus.system.utils.BossDES3Util;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.system.db.common.entity.User;
import com.snbc.bbpf.system.db.common.mapper.OrgMapper;
import com.snbc.bbpf.system.db.common.mapper.UserMapper;
import com.snbc.bbpf.system.db.common.vo.OrgDirectorVo;
import com.snbc.bbpf.system.db.common.vo.TenantClerkVo;
import com.snbc.component.filestorage.args.DownloadArg;
import com.snbc.component.filestorage.fileutil.FileUpAndDownload;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: UserExportServiceImpl
 * @Description: 用户导出业务处理, 因为SONR问题要超过25类引用进行分开
 * @module: si-bbpf-system
 * @Author: Liangjb
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Slf4j
@Service
public class UserExportServiceImpl implements UserExportService {
    private static final String CONTENTDISPOSITION = "Content-Disposition";
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private OrgMapper orgMapper;
    @Value("${importuser.template.path}")
    private String importUserTemplatePath;
    //文件上传下载
    @Autowired
    private FileUpAndDownload fileUpAndDownload;


    /***
     * @Description: 根据组织机构ID 查询该机构下的人员列表
     * @Author: jiafei
     * @param :         orgId
     * @CreateDate: 2021/6/8 10:24
     * @UpdateDate: 2021/6/8 10:24
     * @return :        java.util.List<com.snbc.bbpf.system.db.common.vo.UserVo>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public List<OrgDirectorVo> queryUserList(String orgId) throws BusinessException {
        List<String> orgIds = new ArrayList<>();
        orgIds.add(orgId);
        //根据组织结构查询所有用户
        List<User> userList = userMapper.selectUserByOrgId(orgIds, NumberConstant.NO_ONE);
        List<OrgDirectorVo> orgDirectorVoList = new ArrayList<>();
        userList.forEach(user -> {
            // 手机号先解密
            String phone = BossDES3Util.decrypt(user.getPhone());
            orgDirectorVoList.add(OrgDirectorVo.builder()
                    .phone(phone)
                    .userName(user.getUserName())
                    .userId(user.getUserId()).build());
        });
        return orgDirectorVoList;
    }

    @Override
    public List<TenantClerkVo> userAllList(String orgId) throws BusinessException {
        //查所有本机构及下属orgId
        List<String> orgIds = orgMapper.selectOrgIdsByParentId(orgId);
        //查询启用的所有用户的id name
        List<TenantClerkVo> tenantClerkVos = userMapper.selectUserIdAndName(orgIds, NumberConstant.NO_ONE);
        for (TenantClerkVo tenantClerkVo : tenantClerkVos) {
            //手机号先解密
            tenantClerkVo.setPhone(BossDES3Util.decrypt(tenantClerkVo.getPhone()));
        }
        return tenantClerkVos;
    }

    @Override
    public void downloadUserTemplate(HttpServletRequest request, HttpServletResponse response)
            throws IOException {
        response.setContentType("application/force-download");
        String fileName = "导入用户模板.xlsx";
        response.addHeader(CONTENTDISPOSITION, "attachment;fileName="
                + new String(fileName.getBytes(CommonConstant.CHARSET_UTF8), CommonConstant.CHARSET_ISO8859));
        ServletOutputStream outputStream = response.getOutputStream();
        //下载模板
        DownloadArg downloadArg = (new DownloadArg.DownloadArgBuilder(importUserTemplatePath, CurrentUser.getUserId())).outputStream(outputStream).build();
        fileUpAndDownload.download(downloadArg);
    }
}
