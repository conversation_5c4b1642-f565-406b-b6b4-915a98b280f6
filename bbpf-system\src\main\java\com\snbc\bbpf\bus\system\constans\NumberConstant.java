/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.constans;

/**
 * 
 * @ClassName:NumberConstans
 * @Description:数字常量
 * <AUTHOR>
 * @date 2021年5月18日
 */
public final class NumberConstant {
	public static final int NO_ZERO = 0;
	public static final int F_NO_ONE = -1;
	public static final int NO_ONE = 1;
	public static final int NO_TWO = 2;
	public static final int NO_THREE = 3;
	public static final int NO_FOUR = 4;
	public static final int NO_FIVE = 5;
	public static final int NO_SIX = 6;
	public static final int NO_SEVEN = 7;
	public static final int NO_EIGHT = 8;
	public static final int NO_NINE = 9;
	public static final int NO_TEN = 10;
	public static final int NO_ELEVEN = 11;
	public static final int NO_TWELEVE = 12;
	public static final int NO_THIRTEEN = 13;
	public static final int NO_FOURTEEN = 14;
	public static final int NO_FIFTEEN = 15;
	public static final int NO_SIXTEEN = 16;
	public static final int NO_SEVENTEEN = 17;
	public static final int NO_EIGHTEEN = 18;
	public static final int NO_NINETEEN = 19;
	public static final int NO_TWENTY = 20;
	public static final int NO_TWENTY_ONE = 21;
	public static final int NO_TWENTY_TWO = 22;
	public static final int NO_TWENTY_THREE = 23;
	public static final int NO_TWENTY_FOUR = 24;
	public static final int NO_TWENTY_FIVE = 25;
	public static final int NO_TWENTY_SIX = 26;
	public static final int NO_TWENTY_SEVEN = 27;
	public static final int NO_TWENTY_EIGHT = 28;
	public static final int NO_TWENTY_NINE = 29;
	public static final int NO_THIRTY = 30;
	public static final int NO_THIRTY_TWO = 32;
	public static final int NO_FORTY = 40;
	public static final int NO_FORTY_ONE = 41;
	public static final int NO_FORTY_TWO = 42;
	public static final int NO_FORTY_EIGHT = 48;
	public static final int NO_FIFTY = 50;
	public static final int NO_FIFTY_SEVEN = 57;
	public static final int NO_FIFTY_NINE = 59;
	public static final int NO_SIXTY = 60;
	public static final long NOL_SIXTY = 60L;
	public static final int NO_SIXTY_FIVE = 65;
	public static final int NO_SIXTY_FOUR = 64;
	public static final int NO_EIGHTY = 80;
	public static final int NO_NINETY = 90;
	public static final int NO_NINETY_ONE = 91;
	public static final int NO_NINETY_FIVE = 95;
	public static final int NO_NINETY_SIX = 96;
	public static final int NO_NINETY_SEVEN = 97;
	public static final int NO_NINETY_NINE = 99;
	public static final int NO_ONEH = 100;
	public static final int NO_ONEH_ONE = 101;
	public static final int NO_ONEH_NINE = 109;
	public static final int NO_ONEH_TWENTY_THREE = 123;
	public static final int NO_ONEH_TWENTY_EIGHT = 128;
	public static final int NO_ONEH_THIRTY = 130;
	public static final int NO_ONEH_SIXTY_EIGHT = 168;
	public static final int NO_ONEH_EIGHTY = 180;
	public static final int NO_TWOH = 200;
	public static final int NO_TWOH_FIFTY_THREE = 253;
	public static final int NO_TWOH_FIFTY_FIVE = 255;
	public static final int NO_TWOH_FIFTY_SIX = 256;
	public static final int NO_THREEH_FORTY = 340;
	public static final int NO_FOUR_FORTY_THREE = 443;
	public static final int NO_FIVEH = 500;
	public static final int NO_SIXH = 600;
	public static final int NO_NINEH_NINETY_NINE = 999;
	public static final int NO_ONE_K = 1000;
	public static final int NO_ONE_K_OO = 1101;
	public static final int NO_ONE_K_2O = 1200;
	public static final long NOL_ONE_K = 1000L;
	public static final int NO_ONE_K_TF = 1024;
	public static final int NO_ONE_K_FS = 1056;
	public static final int NO_TWO_K_FORTYE = 2048;
	public static final int NO_ONE_K_NINEH = 1900;
	public static final int NO_THREE_K_SIXH = 3600;
	public static final int NO_THREE_K = 3000;
	public static final long NOL_THREE_K = 3000L;
	public static final int NO_FOUR_K = 4000;
	public static final int NO_FOUR_K_NS = 4096;
	public static final long NOL_FIVE_K = 5000L;
	public static final int NO_FIVE_K_FOURH = 5400;
	public static final int NO_FIVE_K_SIXH_ST = 5672;
	public static final int NO_SIX_K_SIXH = 6600;
	public static final long NOL_EIGHT_K_TWOH = 7200L;
	public static final int NO_EIGHT_K = 8000;
	public static final int NO_EIGHT_K_NINETY_SIX = 8096;
	public static final int NO_EIGHT_K_ONEH_NT = 8192;
	public static final int NO_EIGHT_K_EHEE = 8888;
    public static final int NO_NINE_K_NHNN = 9999;
    // 10000
	public static final int NO_TEN_K = 10000;
	// 20000
	public static final int NO_TWENTY_K = 20000;
	// 25000
	public static final int NO_TWENTY_FIVE_K = 25000;
	// 30000
	public static final int NO_THIRTY_K = 30000;
	// 60000
	public static final int NO_SIXTY_K = 60000;
	// 900000
	public static final int NO_NINEH_K = 900_000;
	// 200_000
	public static final int NO_TWOH_K = 200_000;
	// 604_800
	public static final int NO_SIXH_FOUR_KEH = 604_800;
	// 360万
	public static final int NO_THREEM_SIXH = 3_600_000;
	public static final long NOL_THREEM_SIXH = 3_600_000L;
	// 一天的秒数
	public static final int NO_EIGHT_SIXM_FOURHK = 86_400_000;
	/**
	 * wjc1 增加
	 *该类都是静态变量，
	 * 不需要公有的构造方法
	 *1、声明一个私有的构造方法
	 *2、将类声明成final
	 * 不能有空方法
	 * 所以throw IllegalStateException
	 */
	private NumberConstant() {
		throw new IllegalStateException("NumberConstant class");
	}
}
