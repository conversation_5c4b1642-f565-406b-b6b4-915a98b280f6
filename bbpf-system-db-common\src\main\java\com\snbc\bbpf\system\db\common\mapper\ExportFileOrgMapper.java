/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.mapper;

import com.snbc.bbpf.system.db.common.entity.ExportFileOrg;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName: ExportFileOrgMapper
 * 用户组织机构mapper
 * @module: si-bbpf-system
 * @Author: wjc1
 * @date: 2021/5/19 18:19
 */
@Mapper
public interface ExportFileOrgMapper {

    int insert(@Param("exportFileOrgList") List<ExportFileOrg> exportFileOrgList);

    void delete(@Param("exportId") String exportId);
}
