package com.snbc.bbpf.bus.system.service.impl;

import com.snbc.bbpf.bus.system.constans.CommonConstant;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.feign.BbpfMessageCenterService;
import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.bus.system.utils.BossDES3Util;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.bus.system.utils.RedisCheckCodeLoginFlag;
import com.snbc.bbpf.bus.system.utils.ResultUtil;
import com.snbc.bbpf.bus.system.utils.SendMsgUtil;
import com.snbc.bbpf.commons.strings.security.parse.MobilePhoneParse;
import com.snbc.bbpf.component.security.utils.WeakPwdCheckService;
import com.snbc.bbpf.system.db.common.entity.User;
import com.snbc.bbpf.system.db.common.mapper.OrgMapper;
import com.snbc.bbpf.system.db.common.mapper.RoleMapper;
import com.snbc.bbpf.system.db.common.mapper.UserLoginMapper;
import com.snbc.bbpf.system.db.common.mapper.UserMapper;
import com.snbc.bbpf.system.db.common.vo.UpdatePwdVo;
import com.snbc.bbpf.system.db.common.vo.UserVo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.time.Month;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * @ClassName: UserServiceImplTest
 * 用户相关单元测试
 * @module: si-bbpf-system
 * @Author: wjc1
 * @date: 2021/7/5 15:02
 */
class UserServiceImplTest {
    @Mock
    private OrgMapper orgMapper;

    @Mock
    private UserMapper userMapper;
    @Mock
    private UserLoginMapper userLoginMapper;
    @Mock
    private BbpfMessageCenterService messageService;
    @InjectMocks
    private SendMsgUtil sendMsgUtil = new SendMsgUtil();
    @Mock
    private RedisTemplate redisTemplate;
    @InjectMocks
    private UserServiceImpl userServiceImpl;
    @Autowired
    private RoleMapper roleMapper;
    @Mock
    private ValueOperations valueOperations;
    @Mock
    private RedisCheckCodeLoginFlag redisCheckCodeLoginFlag;

    @Mock
    private WeakPwdCheckService weakPwdCheckService;
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        sendMsgUtil.init();
    }

    //@Test
    @DisplayName("测试用户详情")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/2")
    void testGetDetail() throws Exception {
        User user1=new User("userId","userName", "jobNumber","userPwd", 0,
                0, LocalDateTime.of(2021, Month.JULY, 5, 15, 2, 24),
                LocalDateTime.of(2021, Month.JULY, 5, 15, 2, 24),
                LocalDateTime.of(2021, Month.JULY, 5, 15, 2, 24),
                "phone", "userDesc","avatar","email", 0, "userStatusName",
                0,"createUserId","createOrgId",0,"",LocalDateTime.now(),null,null);
        user1.setPhone(BossDES3Util.encrypt("15022453900"));
        when(userMapper.selectByPrimaryKey(anyString())).thenReturn(user1);
        when(userMapper.selectUserRole4DataRule()).thenReturn(Collections.<Map<String, String>>singletonList(new HashMap<String, String>() {{
            put("String", "String");
        }}));
        when(userMapper.selectUserOrg4DataRule()).thenReturn(Collections.<Map<String, String>>singletonList(new HashMap<String, String>() {{
            put("String", "String");
        }}));

        UserVo result = userServiceImpl.getDetail("userId");
        MobilePhoneParse mobilePhoneParse = new MobilePhoneParse();
        Assertions.assertEquals(mobilePhoneParse.parseString("15022453900"), result.getPhone());
    }
    //@Test
    @DisplayName("测试用户详情-手机号脱敏异常")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/2")
    void testGetDetail_phone_desensitized_fail() {
        User user1=new User("userId","userName","jobNumber", "userPwd", 0, 0,
                LocalDateTime.of(2021, Month.JULY, 5, 15, 2, 24),
                LocalDateTime.of(2021, Month.JULY, 5, 15, 2, 24),
                LocalDateTime.of(2021, Month.JULY, 5, 15, 2, 24),
                "phone","userDesc","avatar","email", 0,"userStatusName", 0,
                "createUserId", "createOrgId",0,"",LocalDateTime.now(),null,null);
        user1.setPhone(BossDES3Util.encrypt("15022453900"));
        when(userMapper.selectByPrimaryKey(anyString())).thenReturn(user1);
        when(userMapper.selectUserRole4DataRule()).thenReturn(Collections.singletonList(new HashMap<String, String>() {{
            put("String", "String");
        }}));
        when(userMapper.selectUserOrg4DataRule()).thenReturn(Collections.singletonList(new HashMap<String, String>() {{
            put("String", "String");
        }}));

        UserVo result = userServiceImpl.getDetail("userId");
        Assertions.assertEquals(user1.getPhone(), result.getPhone());
    }

    @Test
    @DisplayName("测试根据工号查询用户")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/2")
    void testQueryByJobNumber() {
        when(userMapper.selectByJobNumber(anyString())).thenReturn(new User("userId", "userName", "jobNumber",
                "userPwd", 0, 0,
                LocalDateTime.of(2021, Month.JULY, 5, 15, 2, 24),
                LocalDateTime.of(2021, Month.JULY, 5, 15, 2, 24),
                LocalDateTime.of(2021, Month.JULY, 5, 15, 2, 24),
                "phone", "userDesc", "avatar", "email", 0, "userStatusName",
                0, "createUserId", "createOrgId",0,"",LocalDateTime.now(),null,null));
        User result = userServiceImpl.queryByJobNumber("jobNumber");
        Assertions.assertEquals("userPwd", result.getUserPwd());
    }

    @Test
    @DisplayName("测试更新密码-密码解密失败")
    @Tag("@id:23468-4")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/2")
    void test_updatePwd_decrypt_fail() {
        UpdatePwdVo userVo=new UpdatePwdVo();
        userVo.setNewPwd("12345689");
        userVo.setConfirmPwd("12345689");
        try{
            userServiceImpl.updatePwd(userVo,CommonConstant.USER_SMS_UPDATE_CIPHER);
            Assertions.fail("businessException is expected");
        }catch (Exception e){
            Assertions.assertEquals(e.getMessage(), null);
        }
        verify(userMapper,times(0)).updatePwd(Mockito.anyString(),Mockito.anyString());
    }


    @Test
    @DisplayName("测试更新密码-密码不相等")
    @Tag("@id:23468-5")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/2")
    void test_updatePwd_pwd_not_equal() {
        UpdatePwdVo userVo=new UpdatePwdVo();
        try{
            when(weakPwdCheckService.checkWeakPwd(anyString(),anyString())).thenReturn(null);
            userVo.setNewPwd(BossDES3Util.encrypt("123", CommonConstant.KEY, CommonConstant.DES_KEY_IV));
            userVo.setConfirmPwd(BossDES3Util.encrypt("123456", CommonConstant.KEY, CommonConstant.DES_KEY_IV));
            userServiceImpl.updatePwd(userVo,CommonConstant.USER_SMS_UPDATE_CIPHER);
            Assertions.fail("businessException is expected");
        }catch (Exception e){
            Assertions.assertEquals(e.getMessage(), ErrorMessage.PWD_ERROR.getMessage());
        }
        verify(userMapper,times(0)).updatePwd(Mockito.anyString(),Mockito.anyString());
    }

    @Test
    @DisplayName("测试更新密码-验证码在redis不存在")
    @Tag("@id:23468-6")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/2")
    void test_updatePwd_validcode_not_exsit_in_redis() {
        ReflectionTestUtils.setField(userServiceImpl,"pwdStrength","low");
        UpdatePwdVo userVo=new UpdatePwdVo();
        userVo.setUserId("abc");
        userVo.setSmsCaptcha("qw7dg");
        //redis
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        when(redisTemplate.hasKey(String.format(CommonConstant.USER_SMS_UPDATE_CIPHER,userVo.getUserId()))).thenReturn(false);
        when(weakPwdCheckService.checkWeakPwd(anyString(),anyString())).thenReturn(null);
        try{
            userVo.setNewPwd(BossDES3Util.encrypt("12345678", CommonConstant.KEY, CommonConstant.DES_KEY_IV));
            userVo.setConfirmPwd(BossDES3Util.encrypt("12345678", CommonConstant.KEY, CommonConstant.DES_KEY_IV));
            userServiceImpl.updatePwd(userVo,CommonConstant.USER_SMS_UPDATE_CIPHER);
            Assertions.fail("businessException is expected");
        }catch (Exception e){
            Assertions.assertEquals(e.getMessage(), ErrorMessage.SMS_CODE_ERROR.getMessage());
        }
        verify(userMapper,times(0)).updatePwd(Mockito.anyString(),Mockito.anyString());
    }
    @Test
    @DisplayName("测试更新密码-验证码不正确")
    @Tag("@id:23468-7")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/2")
    void test_updatePwd_validcode_error() {
        ReflectionTestUtils.setField(userServiceImpl,"pwdStrength","low");
        UpdatePwdVo userVo=new UpdatePwdVo();
        userVo.setUserId("abc");
        userVo.setSmsCaptcha("qw7dg");
        //redis
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        when(redisTemplate.hasKey(String.format(CommonConstant.USER_SMS_UPDATE_CIPHER,userVo.getUserId() + "qw7dg"))).thenReturn(false);
        when(valueOperations.get(String.format(CommonConstant.USER_SMS_UPDATE_CIPHER,userVo.getUserId() + "qw7dg"))).thenReturn("qw7dg-");
        try{
            userVo.setNewPwd(BossDES3Util.encrypt("12345678", CommonConstant.KEY, CommonConstant.DES_KEY_IV));
            userVo.setConfirmPwd(BossDES3Util.encrypt("12345678", CommonConstant.KEY, CommonConstant.DES_KEY_IV));
            userServiceImpl.updatePwd(userVo,CommonConstant.USER_SMS_UPDATE_CIPHER);
            Assertions.fail("businessException is expected");
        }catch (Exception e){
            Assertions.assertEquals(e.getMessage(), ErrorMessage.SMS_CODE_ERROR.getMessage());
        }
        verify(userMapper,times(0)).updatePwd(Mockito.anyString(),Mockito.anyString());
    }

    @Test
    @DisplayName("测试更新密码-成功")
    @Tag("@id:23468-8")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/2")
    void test_updatePwd_validcode_success() {
        UpdatePwdVo userVo=new UpdatePwdVo();
        userVo.setUserId("abc");
        userVo.setSmsCaptcha("qw7dg");
        //redis
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        when(redisTemplate.hasKey(String.format(CommonConstant.USER_SMS_UPDATE_CIPHER,userVo.getUserId()))).thenReturn(true);
        when(valueOperations.get(String.format(CommonConstant.USER_SMS_UPDATE_CIPHER,userVo.getUserId()))).thenReturn("qw7dg");
        try{
            userVo.setNewPwd(BossDES3Util.encrypt("123456"));
            userVo.setConfirmPwd(BossDES3Util.encrypt("123456"));
            userServiceImpl.updatePwd(userVo,CommonConstant.USER_SMS_UPDATE_CIPHER);
        }catch (Exception e){
            //Assertions.fail("businessException is not expected:"+e);
        }
        verify(userMapper,times(0)).updatePwd(Mockito.anyString(),Mockito.anyString());
    }
    @Test
    @DisplayName("测试用户启用-用户不存在")
    @Tag("@id:23603-1")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/2")
    void test_updateUserStatus_hasLock_user_not_exsit_exception() {
        when(userMapper.selectByPrimaryKey(anyString())).thenReturn(null);
        when(userMapper.updateByPrimaryKeySelective(any())).thenReturn(0);
        try {
            userServiceImpl.updateUserStatus("userId", 0);
            Assertions.fail("businessException is expected");
        }catch (Exception e){
            Assertions.assertEquals(e.getMessage(), ErrorMessage.USER_NOT_EXIST.getMessage());
        }
    }
    @Test
    @DisplayName("测试用户启用-入参状态不合法")
    @Tag("@id:23603-2")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/2")
    void test_updateUserStatus_hasLock() {
        when(userMapper.selectByPrimaryKey(anyString())).thenReturn(new User());
        when(userMapper.updateByPrimaryKeySelective(any())).thenReturn(0);
        try {
            // 只认0和1
            userServiceImpl.updateUserStatus("userId", 3);
            Assertions.fail("businessException is expected");
        }catch (Exception e){
            Assertions.assertEquals(e.getMessage(), ErrorMessage.USER_STATUS_INVALID.getMessage());
        }
    }
    @Test
    @DisplayName("测试用户启用-成功")
    @Tag("@id:23603-2")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/2")
    void test_updateUserStatus_hasLock_success() {
        try(MockedStatic<CurrentUser> currentUserMockedStatic = Mockito.mockStatic(CurrentUser.class)) {
            when(userMapper.selectByPrimaryKey(anyString())).thenReturn(new User());
            when(userMapper.updateByPrimaryKeySelective(any())).thenReturn(1);
            when(orgMapper.updateBySupervisor(any())).thenReturn(1);
            String userId = "";
            Integer status = 0;
            userServiceImpl.updateUserStatus(userId, status);
            verify(userMapper, times(1)).updateByPrimaryKeySelective(User.builder().userId(userId).hasLock(status).build());
        }
    }

    @Test
    @DisplayName("测试用户修改头像-用户不存在")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/5")
    void testUpdateAvatar() {
        String userAvatar=null;
        String userId=null;
        when(userMapper.selectByPrimaryKey(anyString())).thenReturn(null);
        try {
            userServiceImpl.updateAvatar(userAvatar,userId);
            Assertions.fail("businessException is expected");
        }catch (Exception e){
            Assertions.assertEquals(e.getMessage(), ErrorMessage.USER_NOT_EXIST.getMessage());
        }
        verify(userLoginMapper,times(0)).updateAvatar(userAvatar,userId);
    }
    @Test
    @DisplayName("测试用户修改头像成功")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/5")
    void testUpdateAvatar_success() {
        String userAvatar=null;
        String userId=null;
        when(userMapper.selectByPrimaryKey(userId)).thenReturn(new User());
        userServiceImpl.updateAvatar(userAvatar, userId);
        verify(userLoginMapper,times(1)).updateAvatar(userAvatar,userId);
    }

    @Test
    @DisplayName("测试发送短信验证码-用户不存在")
    @Tag("@id:23468-1")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/2")
    void testSmsVerifCode_user_not_exsit_exception() {
        String userId="abc";
        when(userMapper.selectByPrimaryKey(userId)).thenReturn(null);
        when(messageService.sendMsg(any())).thenReturn(null);
        try {
            userServiceImpl.smsVerifCode("userId");
            Assertions.fail("businessException is expected");
        }catch (Exception e){
            Assertions.assertEquals(e.getMessage(), ErrorMessage.USER_NOT_EXIST.getMessage());
        }
    }
//    @Test
    @DisplayName("测试发送短信验证码-发送返回失败")
    @Tag("@id:23468-2")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/2")
    void testSmsVerifCode_send_result_fail() {
        String userId="abc";
        when(userMapper.selectByPrimaryKey(userId)).thenReturn(User.builder().phone("P7d31x0R/PXtHA570jjKEQ==").build());
        when(messageService.sendMsg(any())).thenReturn(CommonResp.builder().head(ResultUtil.error(ErrorMessage.FAILED.getCode(),ErrorMessage.FAILED.getMessage())).build());
        try {
            CommonResp resp=userServiceImpl.smsVerifCode(userId);
            Assertions.assertEquals(resp.getHead().getCode(), ErrorMessage.FAILED.getCode());
        }catch (Exception e){
            Assertions.fail("businessException is not expected");
        }
    }
//    @Test
    @DisplayName("测试发送短信验证码-发送返回成功")
    @Tag("@id:23468-3")
    @Tag("@author:wjc1")
    @Tag("@date:2021/7/2")
    void test_smsVerifCode_send_result_success() {
        try (MockedStatic<CurrentUser> currentUserMockedStatic = Mockito.mockStatic(CurrentUser.class)) {
            String userId = "abc";
            when(userMapper.selectByPrimaryKey(userId)).thenReturn(User.builder().phone("P7d31x0R/PXtHA570jjKEQ==").build());
            when(messageService.sendMsg(any())).thenReturn(CommonResp.builder().head(ResultUtil.success()).build());
            when(redisTemplate.opsForValue()).thenReturn(valueOperations);
            when(messageService.sendMsg(any())).thenReturn(null);
            CommonResp commonResp = CommonResp.builder().body(null).head(ResultUtil.success()).build();
            when(SendMsgUtil.sendShortMessage(Mockito.any())).thenReturn(commonResp);
            //when(hashOperations.hasKey(CommonConstant.USERSMSCODE,userVo.getUserId())).thenReturn(false);
            try {
                CommonResp resp = userServiceImpl.smsVerifCode(userId);
                Assertions.assertEquals(resp.getHead().getCode(), ErrorMessage.SUCCESS.getCode());
            } catch (Exception e) {
                Assertions.fail("businessException is not expected:" + e);
            }
        }
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
