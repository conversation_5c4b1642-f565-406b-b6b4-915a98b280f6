/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @ClassName: UpdateUserPwdVo
 * @Description: 忘记密码修改
 * @module: si-bbpf-system
 * @Author: wangsong
 * @date: 2021/5/18
 *        Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all
 *        rights reserved.
 */
@Data
public class UpdateUserPwdVo extends UpdatePwdVo {
    @NotBlank(message = "手机号码不能为空")
    private String phone;
}
