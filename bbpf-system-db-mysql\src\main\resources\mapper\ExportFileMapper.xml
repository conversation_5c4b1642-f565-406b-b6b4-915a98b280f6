<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snbc.bbpf.system.db.common.mapper.ExportFileMapper">
    
    <resultMap type="com.snbc.bbpf.system.db.common.entity.ExportFile" id="ExportFileResult">
        <result property="exportId"    column="export_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="status"    column="status"    />
        <result property="createUser"    column="create_user"    />
        <result property="createTime"    column="create_time"    />
        <result property="url"    column="url"    />
        <result property="createUserId"    column="create_user_id"    />
    </resultMap>

    <sql id="selectExportFileVo">
        select export_id, file_name, status, create_user, create_time, url,create_user_id from t_export_file
    </sql>

    <select id="selectExportFileList" parameterType="com.snbc.bbpf.system.db.common.vo.ExportFileQuery" resultMap="ExportFileResult">
        select DISTINCT (t1.export_id), t1.file_name, t1.status, t1.create_user, t1.create_time, t1.url,t1.create_user_id
        from t_export_file t1
        left join t_export_file_org t2 on t1.export_id = t2.export_id
        <where>
            <if test="fileName != null  and fileName != ''"> and t1.file_name like concat('%', #{fileName}, '%')</if>
            <if test="createUser != null  and createUser != ''"> and t1.create_user like concat('%', #{createUser}, '%') </if>
            <if test="endTime != null and endTime !=''" >
                AND t1.create_time &lt;= #{endTime,jdbcType=VARCHAR}
            </if>
            <if test="beginTime != null and beginTime !=''" >
                AND t1.create_time &gt;= #{beginTime,jdbcType=VARCHAR}
            </if>
        </where>
        order by t1.create_time desc
    </select>
    <select id="getAllExpireFiles"  resultMap="ExportFileResult">
        <include refid="selectExportFileVo"/>
        where DATEDIFF(now(),create_time)>=(select param_value from t_param where param_code='FileDeleteDay')
    </select>
    <select id="selectExportFileByExportId" parameterType="String" resultMap="ExportFileResult">
        <include refid="selectExportFileVo"/>
        where export_id = #{exportId}
    </select>
        
    <insert id="insertExportFile" parameterType="com.snbc.bbpf.system.db.common.entity.ExportFile">
        insert into t_export_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="exportId != null">export_id,</if>
            <if test="fileName != null">file_name,</if>
            <if test="status != null">status,</if>
            <if test="createUser != null">create_user,</if>
            <if test="createTime != null">create_time,</if>
            <if test="url != null">url,</if>
            <if test="createUserId != null">create_user_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="exportId != null">#{exportId},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="status != null">#{status},</if>
            <if test="createUser != null">#{createUser},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="url != null">#{url},</if>
            <if test="createUserId != null">#{createUserId},</if>
         </trim>
    </insert>

    <update id="updateExportFile" parameterType="com.snbc.bbpf.system.db.common.entity.ExportFile">
        update t_export_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createUser != null">create_user = #{createUser},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="url != null">url = #{url},</if>
        </trim>
        where export_id = #{exportId}
    </update>

    <delete id="deleteExportFileByExportId" parameterType="String">
        delete from t_export_file where export_id = #{exportId}
    </delete>

    <delete id="deleteExportFileByExportIds" parameterType="String">
        delete from t_export_file where export_id in 
        <foreach item="exportId" collection="array" open="(" separator="," close=")">
            #{exportId}
        </foreach>
    </delete>
</mapper>