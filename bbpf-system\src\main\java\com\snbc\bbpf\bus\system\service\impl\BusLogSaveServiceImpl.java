/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service.impl;

import com.snbc.bbpf.bus.system.properties.BusLogTaskPropertie;
import com.snbc.bbpf.bus.system.queue.BusLogQueue;
import com.snbc.bbpf.buslog.entity.BusLog;
import com.snbc.bbpf.buslog.service.BusLogService;
import com.snbc.bbpf.system.db.common.entity.Log;
import com.snbc.bbpf.system.db.common.mapper.LogMapper;
import org.apache.commons.codec.CharEncoding;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URLDecoder;
import java.time.LocalDateTime;
import java.util.UUID;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName: BusLogSaveServiceImpl
 * @Description: 日志存储接口实现
 * @module: si-bbpf-system
 * @Author: jiafei
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
public class BusLogSaveServiceImpl implements BusLogService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BusLogSaveServiceImpl.class);

    private LogMapper busLogMapper;
    private BusLogTaskPropertie busLogTaskPropertie;

    public BusLogSaveServiceImpl(LogMapper busLogMapper, BusLogTaskPropertie busLogTaskPropertie) {
        this.busLogMapper = busLogMapper;
        this.busLogTaskPropertie = busLogTaskPropertie;
    }

    /***
     * @Description: 插入日志
     * @Author: jiafei
     * @param :     busLog
     * @CreateDate: 2021/5/25 17:15
     * @UpdateDate: 2021/5/25 17:15
     * @return :        int
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public void insertLog(BusLog busLog) {
        //存储到租户数据库中
        BusLogQueue.getInstance().addLog(busLog);
    }

    /***
     * @Description:    记录操作日志
     * @Author:         WangSong
     * @return:         void
     * @CreateDate:     2022/11/9 20:00
     * @UpdateDate:     2022/11/9 20:00
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    public void startTask() {
        /**
         * 线程主要方法
         */
        Runnable customerRunnable = new Runnable() {
            @Override
            public void run() {
                while (!BusLogQueue.getInstance().isEmpty()) {
                    try {
                        BusLog busLog = BusLogQueue.getInstance().pollLog();
                        if (busLog != null) {
                            busLogMapper.insert(Log.builder().logId(UUID.randomUUID().toString()).logType(busLog.getLogType())
                                    .logTarget(busLog.getLogTarget()).createTime(LocalDateTime.now()).enContent(busLog.getEnContent())
                                    .requestSource(busLog.getRequestSource()).userId(busLog.getUserId()).ip(busLog.getIp())
                                    .userName(busLog.getUserName() != null ?  URLDecoder.decode(busLog.getUserName(), CharEncoding.UTF_8)
                                            : busLog.getUserName())
                                    .zhContent(busLog.getZhContent()).requestSource(busLog.getRequestSource()).build());
                        }
                    } catch (Exception e) {
                        LOGGER.error("Consuming log failed:", e);
                    }
                }
            }
        };
        // 启用线程
        ScheduledExecutorService service = Executors.newScheduledThreadPool(busLogTaskPropertie.getPool());
        for (int i = 0; i < busLogTaskPropertie.getWorkers(); i++) {
            service.scheduleAtFixedRate(customerRunnable, 0, busLogTaskPropertie.getDelay(), TimeUnit.SECONDS);
        }
        // 循环从队列中读取信息
        LOGGER.debug("The log storage thread pool is running。。。。");
        new Thread(customerRunnable).start();
    }
}
