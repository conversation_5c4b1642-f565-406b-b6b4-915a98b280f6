/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: RoleUserPage
 * @Description: 角色管理用户分页列表
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/5/20
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RoleUserPage {
    private String userId;
    private String userName;
    private String phone;
    @JsonInclude(value = JsonInclude.Include.CUSTOM)
    private String orgNames;
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    private Integer isLock;
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    private String jobNumber;
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    private String avatar;
}
