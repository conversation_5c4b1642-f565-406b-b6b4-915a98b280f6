package com.snbc.bbpf.grafana;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * BBPF Grafana代理服务启动类
 * 
 * 功能说明：
 * 1. 用户认证和权限控制
 * 2. Grafana请求代理和转发
 * 3. 动态权限变量注入
 * 4. 数据权限过滤
 * 5. 安全审计和日志记录
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableAsync
@EnableScheduling
public class GrafanaProxyApplication {

    public static void main(String[] args) {

        SpringApplication.run(GrafanaProxyApplication.class, args);
        System.out.println("\n=== BBPF Grafana代理服务启动成功 ===");
        System.out.println("访问地址: http://localhost:8080");
        System.out.println("健康检查: http://localhost:8080/actuator/health");
        System.out.println("================================\n");
    }
}