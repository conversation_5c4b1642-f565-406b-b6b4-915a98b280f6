package com.snbc.bbpf.system.db.common.vo;

import com.snbc.bbpf.buslog.annotations.DiffKey;
import com.snbc.bbpf.buslog.annotations.UnionDisplayKey;
import com.snbc.bbpf.buslog.annotations.convert.DictFormat;

/**
 * @ClassName: ThirdUnbind
 * @Description: 三方登录状态
 * @module: SI-bbpf-umis
 * @Author: wangsong
 * @date: 2022/11/18
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
public class ThirdUnbind {

    @UnionDisplayKey
    @DiffKey(name = "绑定类型",enName = "Bind Type")
    @DictFormat(dictJson = "[{'enName':'WeChat','zhName':'微信','value':'41'}," +
            "{'enName':'DingDing','zhName':'钉钉','value':'42'}," +
            "{'enName':'Ali','zhName':'阿里','value':'43'}]")
    private String bindType;
}
