/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * @ClassName: AdjustDepartmentDto
 * 调整部门
 * @module: si-bbpf-system
 * @Author: wjc1
 * @date: 2021/5/20 19:43
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class AdjustDepartmentDto {
    /**调整前的组织机构*/
    @NotBlank
    private String oldOrgId;
    /**调整部门的用户，多个用逗号分隔*/
    @NotBlank
    private String userIds;
    /**调整后的组织机构，多个用逗号分隔*/
    @NotBlank
    private String newOrgIds;
}
