/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service;

import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.system.db.common.dto.WeakPasswordDto;
import com.snbc.bbpf.system.db.common.vo.WeakPasswordVo;

import java.util.List;

/**
 * 弱密码管理Service接口
 *
 * <AUTHOR>
 * @date 2023-01-16
 */
public interface IWeakPasswordService {
    /**
     * 查询弱密码管理
     *
     * @param id 弱密码管理主键
     * @return 弱密码管理
     */
    WeakPasswordVo selectWeakPasswordById(String id);

    /**
     * 分页查询弱密码管理列表
     *
     * @param weakPasswordDto 弱密码管理
     * @param pageSize 分页大小
     * @param pageNum 分页数
     * @return 弱密码管理集合
     */
    PageInfo<WeakPasswordVo> selectWeakPasswordList(WeakPasswordDto weakPasswordDto, int pageSize, int pageNum);

    /**
     * 查询弱密码管理列表
     *
     * @param weakPasswordDto 弱密码管理
     * @return 弱密码管理集合
     */
    List<WeakPasswordVo> exportWeakPasswordList(WeakPasswordDto weakPasswordDto);
    /**
     * 新增弱密码管理
     *
     * @param weakPasswordDto 弱密码管理
     * @return 受影响结果数
     */
    int insertWeakPassword(WeakPasswordDto weakPasswordDto);

    /**
     * 修改弱密码管理
     *
     * @param weakPasswordDto 弱密码管理
     * @return 受影响结果数
     */
    int updateWeakPassword(WeakPasswordDto weakPasswordDto);

    /**
     * 批量删除弱密码管理
     *
     * @param ids 需要删除的弱密码管理主键集合
     * @return 受影响结果数
     */
    int deleteWeakPasswordByIds(String[] ids);

    /**
     * 删除弱密码管理信息
     *
     * @param id 弱密码管理主键
     * @return 受影响结果数
     */
    int deleteWeakPasswordById(String id);
}
