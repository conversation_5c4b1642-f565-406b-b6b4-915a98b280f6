<mxfile host="65bd71144e">
    <diagram name="BBPF-Grafana代理模块架构图" id="bbpf-grafana-proxy">
        <mxGraphModel dx="1386" dy="765" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="title" value="BBPF Grafana代理模块" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;fontStyle=1;fontColor=#2F5597;" parent="1" vertex="1">
                    <mxGeometry x="270" y="30" width="600" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="client-layer" value="客户端层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1F5FE;strokeColor=#01579B;fontSize=14;fontStyle=1;verticalAlign=top;" parent="1" vertex="1">
                    <mxGeometry x="100" y="90" width="970" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="web-browser" value="Web浏览器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBDEFB;strokeColor=#1976D2;" parent="1" vertex="1">
                    <mxGeometry x="200" y="110" width="120" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="mobile-app" value="移动应用" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBDEFB;strokeColor=#1976D2;" parent="1" vertex="1">
                    <mxGeometry x="420" y="110" width="120" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="api-client" value="API客户端" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBDEFB;strokeColor=#1976D2;" parent="1" vertex="1">
                    <mxGeometry x="670" y="110" width="120" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="proxy-module" value="BBPF Grafana代理模块" style="rounded=1;whiteSpace=wrap;fillColor=#F3E5F5;strokeColor=#7B1FA2;fontSize=16;fontStyle=1;align=center;html=1;labelPosition=center;verticalLabelPosition=middle;verticalAlign=top;" parent="1" vertex="1">
                    <mxGeometry x="100" y="200" width="970" height="290" as="geometry"/>
                </mxCell>
                <mxCell id="token-auth-module" value="Token验证" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F5E8;strokeColor=#2E7D32;fontSize=14;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="150" y="240" width="250" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="proxy-interface-module" value="Grafana接口代理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF3E0;strokeColor=#F57C00;fontSize=14;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="450" y="240" width="250" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="sql-interceptor-module" value="SQL拦截" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#1976D2;fontSize=14;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="750" y="240" width="250" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="variable-injection-module" value="动态模版变量注入" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FCE4EC;strokeColor=#C2185B;fontSize=14;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="150" y="370" width="250" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="share-control-module" value="分享控制" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F1F8E9;strokeColor=#689F38;fontSize=14;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="450" y="370" width="240" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="grafana-instance" value="Grafana实例" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFEBEE;strokeColor=#D32F2F;fontSize=16;fontStyle=1;verticalAlign=top;" parent="1" vertex="1">
                    <mxGeometry x="100" y="530" width="970" height="110" as="geometry"/>
                </mxCell>
                <mxCell id="grafana-dashboard" value="仪表盘" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFCDD2;strokeColor=#D32F2F;" parent="1" vertex="1">
                    <mxGeometry x="200" y="560" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="grafana-api" value="API接口" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFCDD2;strokeColor=#D32F2F;" parent="1" vertex="1">
                    <mxGeometry x="400" y="560" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="grafana-datasource" value="数据源" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFCDD2;strokeColor=#D32F2F;" parent="1" vertex="1">
                    <mxGeometry x="600" y="560" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="grafana-query" value="查询引擎" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFCDD2;strokeColor=#D32F2F;" parent="1" vertex="1">
                    <mxGeometry x="800" y="560" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="external-systems" value="外部系统" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF8E1;strokeColor=#F57F17;fontSize=16;fontStyle=1;verticalAlign=top;" parent="1" vertex="1">
                    <mxGeometry x="1130" y="180" width="220" height="460" as="geometry"/>
                </mxCell>
                <mxCell id="bbpf-system" value="BPF权限系统" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF9C4;strokeColor=#F9A825;" parent="1" vertex="1">
                    <mxGeometry x="1180" y="240" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="external-auth-service" value="外部认证服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF9C4;strokeColor=#F9A825;" parent="1" vertex="1">
                    <mxGeometry x="1180" y="365" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="redis-cache" value="Redis缓存" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF9C4;strokeColor=#F9A825;" parent="1" vertex="1">
                    <mxGeometry x="1180" y="490" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="" style="shape=doubleArrow;direction=south;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="230" y="160" width="30" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="" style="shape=doubleArrow;direction=south;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="465" y="160" width="30" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="" style="shape=doubleArrow;direction=south;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="715" y="160" width="30" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="" style="shape=doubleArrow;direction=south;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="240" y="490" width="30" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="" style="shape=doubleArrow;direction=south;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="540" y="490" width="30" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="" style="shape=doubleArrow;direction=south;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="790" y="490" width="30" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="11" value="" style="shape=doubleArrow;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="1070" y="340" width="60" height="30" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>