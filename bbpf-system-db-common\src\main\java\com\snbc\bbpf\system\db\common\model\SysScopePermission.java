/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: SysScopePermission
 * @Description: 前端传递的数据权限
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/5/19
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SysScopePermission {
    //权限id
    private String permissionId;
    //数据权限ids
    private String[] dataScopeIds;
}
