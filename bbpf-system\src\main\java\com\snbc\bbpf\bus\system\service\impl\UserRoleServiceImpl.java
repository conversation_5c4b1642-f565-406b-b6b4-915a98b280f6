/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service.impl;

import com.snbc.bbpf.bus.system.service.UserRoleService;
import com.snbc.bbpf.system.db.common.entity.UserRole;
import com.snbc.bbpf.system.db.common.mapper.UserRoleMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClassName: UserRoleServiceImpl
 * 用户角色接口
 * @module: si-bbpf-system
 * @Author: wjc1
 * @date: 2021/5/19 18:45
 */
@Service
public class UserRoleServiceImpl implements UserRoleService {
    @Autowired
    private UserRoleMapper userRoleMapper;
    @Override
    public int insert(UserRole userRole) {
        return userRoleMapper.insert(userRole);
    }
}
