# BBPF Grafana代理服务 - 问题排查指南

## 当前问题分析

### 1. 命令执行超时问题
- **现象**: Maven命令和Java命令都出现超时错误
- **可能原因**:
  - 网络连接问题导致Maven依赖下载失败
  - 系统环境配置问题
  - 防火墙或代理设置阻止了网络访问

### 2. 解决方案

#### 方案一：使用提供的批处理脚本
```bash
# 双击运行或在命令行执行
run.bat
```

#### 方案二：手动执行Maven命令
```bash
# 1. 清理并编译
mvn clean compile

# 2. 运行应用
mvn spring-boot:run -Dspring-boot.run.profiles=local
```

#### 方案三：检查环境配置
1. **验证Java环境**:
   ```bash
   java -version
   javac -version
   ```

2. **验证Maven环境**:
   ```bash
   mvn -version
   ```

3. **检查网络连接**:
   ```bash
   ping repo1.maven.org
   ```

#### 方案四：配置Maven镜像（如果网络问题）
在 `~/.m2/settings.xml` 中添加阿里云镜像：
```xml
<mirrors>
  <mirror>
    <id>aliyun</id>
    <mirrorOf>central</mirrorOf>
    <name>Aliyun Central</name>
    <url>https://maven.aliyun.com/repository/central</url>
  </mirror>
</mirrors>
```

## 项目状态

### ✅ 已完成的修改
1. **配置文件整合**: 所有配置已迁移到 `application-local.properties`
2. **主启动类**: 创建了 `GrafanaProxyApplication.java`
3. **代码结构**: 清理了重复文件
4. **配置格式**: 修复了properties文件格式问题

### 📁 项目结构
```
bbpf-grafana-proxy/
├── src/main/java/com/snbc/bbpf/grafana/
│   ├── GrafanaProxyApplication.java          # 主启动类
│   └── proxy/
│       ├── client/                          # Grafana API客户端
│       ├── config/                          # 配置类
│       ├── controller/                      # 控制器
│       ├── dto/                            # 数据传输对象
│       ├── filter/                         # 过滤器
│       ├── handler/                        # WebSocket处理器
│       ├── interceptor/                    # 拦截器
│       ├── service/                        # 服务层
│       └── utils/                          # 工具类
├── src/main/resources/
│   ├── application-local.properties        # 本地配置文件
│   ├── application.properties              # 默认配置
│   └── log4j2.xml                         # 日志配置
├── pom.xml                                 # Maven配置
└── run.bat                                 # 运行脚本
```

### 🔧 核心功能
1. **用户认证**: JWT令牌验证
2. **权限控制**: 基于BBPF权限系统的访问控制
3. **请求代理**: Grafana API请求转发
4. **动态变量**: 权限相关的Grafana变量注入
5. **缓存机制**: Redis缓存提高性能
6. **WebSocket支持**: 实时数据推送

## 下一步操作建议

1. **首先尝试**: 运行 `run.bat` 脚本
2. **如果失败**: 检查Java和Maven环境
3. **网络问题**: 配置Maven镜像
4. **依赖问题**: 手动下载依赖或使用IDE导入项目

## 联系支持

如果问题仍然存在，请提供以下信息：
- Java版本 (`java -version`)
- Maven版本 (`mvn -version`)
- 网络连接状态
- 具体错误信息

---

**注意**: 当前使用模拟权限数据，实际部署时需要配置真实的BBPF权限API地址。