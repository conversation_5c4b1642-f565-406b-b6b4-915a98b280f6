/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.gson.Gson;
import com.snbc.bbpf.bus.system.constans.CommonConstant;
import com.snbc.bbpf.bus.system.constans.Constant;
import com.snbc.bbpf.bus.system.constans.NumberConstant;
import com.snbc.bbpf.bus.system.enums.BusTemplateCodeEnum;
import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.bus.system.service.UserOptService;
import com.snbc.bbpf.bus.system.utils.AllOrg;
import com.snbc.bbpf.bus.system.utils.BossDES3Util;
import com.snbc.bbpf.bus.system.utils.CheckOrgPermission;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.bus.system.utils.SendMsgUtil;
import com.snbc.bbpf.commons.strings.security.parse.MobilePhoneParse;
import com.snbc.bbpf.system.db.common.dto.AdjustDepartmentDto;
import com.snbc.bbpf.system.db.common.dto.OrgNamesDto;
import com.snbc.bbpf.system.db.common.dto.RoleNamesDto;
import com.snbc.bbpf.system.db.common.entity.Message;
import com.snbc.bbpf.system.db.common.entity.Org;
import com.snbc.bbpf.system.db.common.entity.User;
import com.snbc.bbpf.system.db.common.entity.UserOrg;
import com.snbc.bbpf.system.db.common.mapper.OrgMapper;
import com.snbc.bbpf.system.db.common.mapper.RoleMapper;
import com.snbc.bbpf.system.db.common.mapper.UserMapper;
import com.snbc.bbpf.system.db.common.mapper.UserOrgMapper;
import com.snbc.bbpf.system.db.common.vo.OrgSupervisorVo;
import com.snbc.bbpf.system.db.common.vo.OrgUserPageVo;
import com.snbc.bbpf.system.db.common.vo.OrgVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @ClassName: UserOptServiceImpl
 * @Description: 用户业务处理
 * @module: si-bbpf-system
 * @Author: wangsong
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Service
@Slf4j
public class UserOptServiceImpl implements UserOptService {
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private UserOrgMapper userOrgMapper;
    @Autowired
    private OrgMapper orgMapper;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private AllOrg allOrg;
    @Autowired
    private RoleMapper roleMapper;
    @Autowired
    private CheckOrgPermission checkOrgPermission;
    /***
     * @Description: 调整部门
     * @Author: WJC
     * 只针对当前部门的ID进行移除，新增目前选择的新部门
     * @param :         adjustDepartmentDto
     * @CreateDate: 2021/5/15 17:18
     * @UpdateDate: 2021/5/15 17:18
     * @return :        com.snbc.bbpf.system.db.common.dto.UserDto
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    @Transactional(rollbackFor = BusinessException.class)
    public void adjustDepartment(AdjustDepartmentDto adjustDepartmentDto) {
        //由 只能调整到本部门及下属部门
        //wjc 20230621 当时登录人，校验选中的组织机构是否是本人数据权限配置的组织机构
        checkOrgPermission.checkOrg(CurrentUser.getUserId(),
                Arrays.asList(adjustDepartmentDto.getNewOrgIds().split(Pattern.quote(CommonConstant.JWT_FILTER_CHAR)).clone()));

        String[] userIdArr = adjustDepartmentDto.getUserIds().split(Pattern.quote(CommonConstant.JWT_FILTER_CHAR));
        //20220107查询原组织机构,并转成map
        List<OrgNamesDto> oldOrgList = orgMapper.selectOrgNamesByUserId(Arrays.asList(userIdArr));
        Map<String, String> oldOrgMap = oldOrgList.stream().collect(Collectors.toMap(OrgNamesDto::getUserId, OrgNamesDto::getOrgNames));
        //20220107查询原部门主管,并转成map
        List<OrgSupervisorVo> oldOrgIdList = orgMapper.selectSupervisorByUserId(Arrays.asList(userIdArr));
        Map<String, String> oldOrgIdMap = oldOrgIdList.stream().collect(Collectors.toMap(OrgSupervisorVo::getUserId, OrgSupervisorVo::getOrgIds));

        for (String uId : userIdArr) {
            // 将该组织机构和人员解绑：判断是否成功
            if (!String.valueOf(NumberConstant.NO_ZERO).equals(adjustDepartmentDto.getOldOrgId())) {
                //当用户移除掉该部门的时候的时候，如果该用户为该这部门的负责人则需要移除掉
                userOrgMapper.deleteByOrgIdUserId(adjustDepartmentDto.getOldOrgId(), uId);
            }
            String[] newOrgIdsArr = adjustDepartmentDto.getNewOrgIds().split(Pattern.quote(CommonConstant.JWT_FILTER_CHAR));
            //先删除用户下的所有组织机构，以新勾选的组织机构为准
            userOrgMapper.deleteByUserId(uId);
            for (String orgId : newOrgIdsArr) {
                userOrgMapper.insertOrUpdate(UserOrg.builder().id(UUID.randomUUID().toString()).userId(uId).orgId(orgId).build());
            }
        }
        //20220107查询现在的组织机构,并转map
        List<OrgNamesDto> newOrgList = orgMapper.selectOrgNamesByUserId(Arrays.asList(userIdArr));
        Map<String, String> newOrgMap = newOrgList.stream().collect(Collectors.toMap(OrgNamesDto::getUserId, OrgNamesDto::getOrgNames));
        //20220107查询原部门主管,并转成map
        List<OrgSupervisorVo> newOrgIdList = orgMapper.selectSupervisorByUserId(Arrays.asList(userIdArr));
        Map<String, String> newOrgIdMap = newOrgIdList.stream().collect(Collectors.toMap(OrgSupervisorVo::getUserId, OrgSupervisorVo::getOrgIds));

        //删除缓存
        redisTemplate.delete(Constant.USER_DATA_AUTH_ORG_AND_SUB_ORG);
        redisTemplate.delete(Constant.USER_DATA_AUTH_ORG);
        // 删除 ：组织机构数据权限
        redisTemplate.delete(Constant.ORG_DATA_AUTH_ORG_AND_SUB_ORG);
        redisTemplate.delete(Constant.ORG_DATA_AUTH_ORG_ORG);
        redisTemplate.delete(Constant.ORG_DATA_AUTH_ORG_AND_CREATE_ORG);
        //  消息通知开始，20220107wjc开启线程处理，待优化
        String sendId=CurrentUser.getUserId();
        new Thread(()-> {
            for (String uId : userIdArr) {
                // 1、系统通知，短信,发送给 被调整部门的用户
                extracted(oldOrgMap, newOrgMap, uId,sendId);
                //2、被调整部门的用户变更前部门负责人，系统和短信
                extractedSupervisor(oldOrgIdMap, uId, "调离",sendId);
                //3、被调整部门的用户变更后部门负责人，系统和短信
                extractedSupervisor(newOrgIdMap, uId, "调入",sendId);
            }
        }).start();
    }

    /**
     * wjc1 20220107
     * 通知给被调整部门的用户
     *
     * @param oldOrgMap 调整前的部门
     * @param newOrgMap 调整后的部门
     * @param uId       被调整部门的用户id
     */
    private void extracted(Map<String, String> oldOrgMap, Map<String, String> newOrgMap, String uId,String sendId) {
        User user = userMapper.selectByPrimaryKey(uId);
        String phone = BossDES3Util.decrypt(user.getPhone());
        String[] receivePhoneArr = {phone};
        String[] receiveUserIdArr = {uId};
        Message message=Message.builder().receiveNos(receiveUserIdArr).receiverPhone(receivePhoneArr)
                .templateCode(BusTemplateCodeEnum.ADJUST_DEPARTMENT1.getName())
                .templateParamJsonArr(convertTemplateParamJsonArr(user.getUserName(),oldOrgMap.get(uId),newOrgMap.get(uId)))
                .msgTitle(BusTemplateCodeEnum.ADJUST_DEPARTMENT1.getTitle()).senderId(sendId).build();
        SendMsgUtil.sendShortMessage(message);
        //发送系统通知
        String[] userIdArr = {uId};
        message.setReceiveNos(userIdArr);
        SendMsgUtil.sendSysMessage(message);
    }

    /**
     * wjc1 20220107
     * 通知给主管的消息
     *
     * @param orgIdMap 部门id
     * @param uId      被调整部门的用户id
     * @param flag     调离；调入
     */
    private void extractedSupervisor(Map<String, String> orgIdMap, String uId, String flag,String sendId) {
        //短信
        String orgId = orgIdMap.get(uId);
        //原组织机构没有。所以没有主管，直接return
        if(StringUtils.isBlank(orgId)){
            return;
        }
        String[] orgIdArr = orgId.split(Pattern.quote(CommonConstant.JWT_FILTER_CHAR));
        User user = userMapper.selectByPrimaryKey(uId);
        //主管手机号
        for (String id : orgIdArr) {
            Org orgExsit = orgMapper.selectByPrimaryKey(id);
            User userSupervisor = userMapper.selectByPrimaryKey(orgExsit.getSupervisor());
            if (userSupervisor!=null) {
                String phone = BossDES3Util.decrypt(orgExsit.getSupervisorPhone());
                String[] receivePhoneArr = {phone};
                String[] receiveUserIdArr = {userSupervisor.getUserId()};
                Message message=Message.builder().receiveNos(receiveUserIdArr).receiverPhone(receivePhoneArr)
                        .templateCode(BusTemplateCodeEnum.ADJUST_DEPARTMENT2.getName())
                        .templateParamJsonArr(convertTemplateParamJsonArr(user.getUserName(),userSupervisor.getUserName(),orgExsit.getOrgName(),flag))
                        .msgTitle(BusTemplateCodeEnum.ADJUST_DEPARTMENT2.getTitle()).senderId(sendId).build();
                SendMsgUtil.sendShortMessage(message);
                //发送系统通知
                String[] userIdArr = {userSupervisor.getUserId()};
                message.setReceiveNos(userIdArr);
                SendMsgUtil.sendSysMessage(message);
            }
        }
    }
    /**
     * 转换模板参数 通知给主管
     * @param userName
     * @param supervisorUserName
     * @param adjustOrgName
     * @param flag
     * @return
     */
    private static String[] convertTemplateParamJsonArr(String userName,String supervisorUserName,String adjustOrgName,String flag){
        Map<String,String> templateParamMap=new HashMap<>();
        templateParamMap.put("userName", userName);
        templateParamMap.put("supervisorUserName", supervisorUserName);
        templateParamMap.put("adjustOrgName", adjustOrgName);
        templateParamMap.put("flag", flag);
        return new String[]{new Gson().toJson(templateParamMap)};
    }
    /**
     * 转换模板参数
    * @param userName
     * @param oldOrgName
     * @param newOrgName
     * @return
     */
    private static String[] convertTemplateParamJsonArr(String userName,String oldOrgName,String newOrgName){
        Map<String,String> templateParamMap=new HashMap<>();
        templateParamMap.put("userName", userName);
        templateParamMap.put("oldOrgName", oldOrgName);
        if(StringUtils.isBlank(oldOrgName)){
            templateParamMap.put("oldOrgName", "无");
        }
        templateParamMap.put("newOrgName", newOrgName);
        return new String[]{new Gson().toJson(templateParamMap)};
    }
    /***
     * @Description: 查询用户分页
     * 手机号得全匹配进行精确查询，并且能查用户相关的数字
     * @Author: WJC
     * @param :         orgId
     * @param :         pageNum
     * @param :         pageSize
     * @param :         queryParam
     * @CreateDate: 2021/5/15 17:18
     * @UpdateDate: 2021/5/15 17:18
     * @return :        com.snbc.bbpf.system.db.common.dto.UserDto
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public PageInfo<OrgUserPageVo> queryUserListPage(String orgId, Integer pageNum, Integer pageSize, String queryParam) {
        // 查本组织及下属组织机构的id
        // queryParam 是完整手机号，则先加密再查询,
        String phoneParam = null;
        if (StringUtils.isNumeric(queryParam)) {
            phoneParam = BossDES3Util.encrypt(queryParam);
        }
        List<OrgUserPageVo> userList =  null;
        boolean isAdmin = allOrg.isAdmin(CurrentUser.getUserId());
        // 是超管
        if(isAdmin){
            List<String> orgPathList = allOrg.processAdmin(orgId);
            //超管：组织机构及下级下的用户
            PageMethod.startPage(pageNum, pageSize);
            userList = userMapper.queryUserListPage(orgPathList, queryParam, phoneParam);
        }else {
            // 数据权限的组织机构
            List<OrgVo> orgIdPathListDataRule = allOrg.getDataRuleOrg(CurrentUser.getUserId());
            //true 前端的orgId是所属组织机构的上级
            boolean isSub = allOrg.isSub(orgIdPathListDataRule, orgId);
            Org orgExsit = orgMapper.selectByPrimaryKey(orgId);
            //点击的是前端 是 数据权限的组织机构的上级
            if(isSub){
                List<String> orgIdList = orgIdPathListDataRule.stream().map(OrgVo::getOrgId).collect(Collectors.toList());
                // 分页设置，需要紧挨着sql，否则分页无效
                PageMethod.startPage(pageNum, pageSize);
                userList = userMapper.queryUserListPageByOrgId(orgIdList,queryParam, phoneParam);
            }else {
                //如果点击的是末节点
                List<Org> childList = orgMapper.queryOrgListByParentId(orgExsit.getParentId());
                if(CollectionUtils.isEmpty(childList)){
                    PageMethod.startPage(pageNum, pageSize);
                    userList = userMapper.queryUserListPageByOrgId(Collections.singletonList(orgId),queryParam, phoneParam);
                }else {
                    //查询当前orgId的下级，
                    List<String> orgIdList = allOrg.processGeneralUser(orgIdPathListDataRule, orgExsit.getOrgPath());
                    PageMethod.startPage(pageNum, pageSize);
                    userList = userMapper.queryUserListPageByOrgId(orgIdList,queryParam, phoneParam);
                }
            }
        }
        matchOrgAndRole(userList);
        return new PageInfo<>(userList);
    }

    private void matchOrgAndRole(List<OrgUserPageVo> userList) {
        List<String> userIdList = userList.stream().map(OrgUserPageVo::getUserId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(userIdList)) {
            //查询用户的所有组织机构
            List<OrgNamesDto> orgNamesDtoList = orgMapper.selectOrgNamesByUserId(userIdList);
            Map<String, String> orgNameMap = orgNamesDtoList.stream()
                    .collect(Collectors.toMap(OrgNamesDto::getUserId, OrgNamesDto::getOrgNames, (k1, k2) -> k1));
            //查询用户的所有角色
            List<RoleNamesDto> roleNamesDtoList = roleMapper.selectRoleNamesByUserId(userIdList);
            Map<String, String> roleNameMap = roleNamesDtoList.stream()
                    .collect(Collectors.toMap(RoleNamesDto::getUserId,
                            roleNamesDto -> (roleNamesDto.getRoleNames() != null ? roleNamesDto.getRoleNames() : "")));
            MobilePhoneParse parsePhone = new MobilePhoneParse();
            userList.forEach(userVo -> {
                //添加用户的组织机构
                userVo.setOrgNames(orgNameMap.get(userVo.getUserId()));
                //添加用户的角色
                userVo.setRoleNames(roleNameMap.get(userVo.getUserId()));
                userVo.setPhone(parsePhone.parseString(BossDES3Util.decrypt(userVo.getPhone())));
            });
        }
    }
}
