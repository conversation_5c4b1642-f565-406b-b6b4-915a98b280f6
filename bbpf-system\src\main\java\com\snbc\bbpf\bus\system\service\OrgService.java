/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service;

import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.system.db.common.vo.NoticeOrgVo;
import com.snbc.bbpf.system.db.common.vo.OrgSort;
import com.snbc.bbpf.system.db.common.vo.OrgVo;

import java.util.List;

/**
 * @ClassName: UserService
 * @Description: 用户业务处理
 * @module: si-bbpf-system
 * @Author: wangsong
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
public interface OrgService {

    /**
     * 查询组织机构树
     *
     * @param orgId
     * @param orgLevel
     * @return
     */
    List<OrgVo> getOrgTree(String orgId, String orgLevel);

    /**
     * @param : orgId
     * @param : orgLevel
     * @return :        java.util.List<com.snbc.bbpf.system.db.common.vo.OrgVo>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     * @Description: 查询组织机构list
     * @Author: jiafei
     * @CreateDate: 2021/6/1 11:24
     * @UpdateDate: 2021/6/1 11:24
     */
    List<OrgVo> getOrgListById(String orgId, String orgLevel) throws BusinessException;

    /**
     * 获取组织机构信息
     *
     * @param orgId
     * @return
     */
    OrgVo getOrg(String orgId) throws BusinessException;

    /**
     * 新增组织机构
     *
     * @param orgVo
     * @param createUserId
     * @throws Exception
     */
    String addOrg(OrgVo orgVo, String createUserId) throws BusinessException;

    /**
     * 修改组织机构
     *
     * @param orgVo
     * @param userName
     * @param userId
     * @throws Exception
     */
    void updateOrg(OrgVo orgVo, String userName, String userId) throws BusinessException;

    /**
     * 删除组织机构
     *
     * @param orgId
     * @return
     * @throws Exception
     */
    void delOrg(String orgId) throws BusinessException;

    /**
     * 组织机构拖拽排序
     *
     * @param orgSort
     * @return
     * @throws Exception
     */
    List<OrgVo> orgSort(OrgSort orgSort) throws BusinessException;

    List<OrgVo> orgTree(String userId);

    List<NoticeOrgVo> getNoticeOrgName(List<String> orgIdList);
}
