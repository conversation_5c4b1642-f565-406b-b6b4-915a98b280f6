/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.constans;

import java.nio.charset.StandardCharsets;

/**
 * @ClassName: Constant
 * @Description: 常量类
 * @module: si-bbpf-system
 * @Author: wangsong
 * @date: 2021/5/18
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
public final class CommonConstant {
    public static final String KEY = "YWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4";
    public static final String DES_KEY = "iLe34(mWs7$9Kfepeuq#znfb";
    public static final String KEY_IV = "snbc-bbpf";
    /**默认密码*/
    public static final String DEFAULT_P_NAME = "snbc-1234";
    public static final String DES_KEY_IV = "snbcbbpf";
    /**UTF_8*/
    public static final String CHARSET_UTF8 = StandardCharsets.UTF_8.name();
    public static final String CHARSET_ISO8859 = StandardCharsets.ISO_8859_1.name();
    /**
     * 用户登录状态
     */
    public static final String USER_LOGIN_STATUS = "user-login-status";
    /**权限路径默认分隔符号：/ */
    public static final String PERMISSION_FILTER_CHAR = "/";
    /**默认的魔法数字1*/
    public static final Integer ONE = 1;
    /**默认的魔法数字6*/
    public static final Integer NO_SIX = 6;
    /**默认的字符串3*/
    public static final String THREE = "3";
    /**默认的魔法数字0*/
    public static final Integer ZERO = 0;
    /**默认的魔法数字8*/
    public static final Integer EIGHT = 8;
    /**默认的链接字符：分号*/
    public static final String JOINER = ":";
    /**默认的链接字符：逗号*/
    public static final String JWT_FILTER_CHAR =",";
    /**默认的链接字符：RSA*/
    public static final String JWT_RSA ="RSA";

    /**公共属性常量：用户id userId*/
    public static final String COM_HEAD_USERID = "userId";
    /**公共属性常量：用户名 userName*/
    public static final String COM_HEAD_USERNAME = "userName";
    /**公共属性常量系统类型：sysType*/
    public static final String CURRENT_SYS_TYPE = "sysType";
    /**redis 用户短信验证码 key值*/
    public static final String USER_SMS_UPDATE_CIPHER = "smsUpdatePwd:%s";
    /**redis 用户短信验证码登录 key值*/
    public static final String SMS_LOGIN = "smsLogin:%s";
    /**redis 用户忘记密码手机验证码 key值*/
    public static final String SMS_FORGOT_CIPHER = "smsForgotPwd:%s";
    /**redis 用户忘记密码手机验证码 key值*/
    public static final String UPDATE_USER_PHONE = "smsUpdatePhone:%s";
    /**用户的EMAIL检测*/
    public static final String USER_EMAIL_MATCH = "([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((\\.[a-zA-Z0-9_-]{2,3}){1,2}){1,64}";
    /**用户汉子检测*/
    public static final String USER_REGEXP_MATCH = "^[\\u4E00-\\u9FFF]+$";

    public static final String ROOT_ORG_ID = "0";
    public static final String ACCOUNT_LOGIN_TYPE = "account";
    /**系统登录标识*/
    public static final String SMS_LOGIN_TYPE = "sms";
    /**LDAP登录标识*/
    public static final String LDAP_LOGIN_TYPE = "ldap";
    /**二维码登录标识*/
    public static final String QRCODE_LOGIN_TYPE = "qrcode";

    private CommonConstant() {
        throw new IllegalStateException("CommonConstant class");
    }
}
