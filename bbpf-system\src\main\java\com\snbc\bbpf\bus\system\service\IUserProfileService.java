/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service;

import com.github.pagehelper.PageInfo;
import com.snbc.bbpf.system.db.common.dto.UserProfileDto;
import com.snbc.bbpf.system.db.common.vo.UserProfileVo;
import com.snbc.bbpf.system.db.common.vo.ViewVo;

import java.util.List;
import java.util.Map;

/**
 * 用户配置Service接口
 *
 * <AUTHOR>
 * @date 2022-12-05
 */
public interface IUserProfileService {

    /**
     * 查询用户配置
     *
     * @param profileCode 用户配置编码
     * @return 用户配置
     */
    UserProfileVo getUserProfileByProfileCode(String profileCode);


    /**
     * 分页查询用户配置列表
     *
     * @param userProfileDto 用户配置
     * @param pageSize 分页大小
     * @param pageNum 分页数
     * @return 用户配置集合
     */
    PageInfo<UserProfileVo> selectUserProfileList(UserProfileDto userProfileDto, int pageSize, int pageNum);

    /**
     * 查询用户配置列表
     *
     * @param userProfileDto 用户配置
     * @return 用户配置集合
     */
    List<UserProfileVo> exportUserProfileList(UserProfileDto userProfileDto);
    /**
     * 新增用户配置
     *
     * @param userProfileDto 用户配置
     * @return 受影响结果数
     */
    int insertUserProfile(UserProfileDto userProfileDto);

    /**
     * 保存用户配置
     *
     * @param userProfileDto 用户配置
     * @return 受影响结果数
     */
    int saveUserProfile(UserProfileDto userProfileDto);
    /**
     * 修改用户配置
     *
     * @param userProfileDto 用户配置
     * @return 受影响结果数
     */
    int updateUserProfile(UserProfileDto userProfileDto);

    /**
     * 批量删除用户配置
     *
     * @param profileIds 需要删除的用户配置主键集合
     * @return 受影响结果数
     */
    int deleteUserProfileByprofileIds(String[] profileIds);

    /**
     * 删除用户配置信息
     *
     * @param profileId 用户配置主键
     * @return 受影响结果数
     */
    int deleteUserProfileByprofileId(String profileId);

    void saveOrUpdateView(ViewVo userProfileDto);

    /**
     * 获取视图
     * @param userId
     * @param moduleType
     * @param tenantCode
     * @return
     */
    List<Map<String, Object>> getMyView(String userId, Integer moduleType, String tenantCode);
}
