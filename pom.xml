<?xml version="1.0" encoding="UTF-8"?>
<!-- 此 XML 文件是一个 Maven 的项目对象模型（POM）文件，用于管理多模块项目的构建和依赖等信息 -->
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<!-- POM 文件的模型版本，通常为 4.0.0 -->
	<modelVersion>4.0.0</modelVersion>
	<!-- 该项目继承自父项目，使用父项目的部分配置信息 -->
	<parent>
		<!-- 父项目的组 ID，通常代表组织或公司的标识符 -->
		<groupId>com.snbc.bbpf</groupId>
		<!-- 父项目的 artifact ID，用于区分父项目中的不同模块 -->
		<artifactId>bbpf-parent</artifactId>
		<!-- 父项目的版本号 -->
		<version>2.0.12</version>
		<!-- 父项目 POM 文件的相对路径，这里为空表示 Maven 将根据默认的父查找规则查找 -->
		<relativePath />
	</parent>
	<!-- 本项目的 artifact ID，用于在组织内唯一标识本项目 -->
	<artifactId>bbpf-system</artifactId>
	<!-- 本项目的版本号 -->
	<version>2.0.0</version>
	<!-- 项目的打包类型，这里为 pom，表示这是一个父项目，主要用于管理子模块 -->
	<packaging>pom</packaging>
	<!-- 本项目的名称 -->
	<name>bbpf-system</name>
	<!-- 本项目的描述信息，简单介绍项目的用途 -->
	<description>project for bbpf-system</description>
	<!-- 项目的模块列表，列出了该父项目所包含的子模块 -->
	<modules>
		<!-- 子模块的名称，这些子模块将作为该项目的一部分被构建和管理 -->
		<module>bbpf-system-db-common</module>
		<module>bbpf-system-db-mysql</module>
		<module>bbpf-system-db-opengauss</module>
		<module>bbpf-system</module>
		<module>bbpf-grafana-proxy</module>
	</modules>
	<!-- 项目的属性配置，可在整个 POM 文件中使用这些属性 -->
	<properties>
		<!-- Java 版本属性，指定为 1.8，可在编译等操作中使用 -->
		<java.version>1.8</java.version>
		<!-- 项目的版本属性，可在依赖管理中使用，方便版本的统一管理 -->
		<project.version>2.0.0</project.version>
	</properties>
	<!-- 依赖管理部分，用于集中管理子模块的依赖版本，确保子模块使用相同的依赖版本 -->
	<dependencyManagement>
		<!-- 依赖列表，为子模块统一管理依赖信息 -->
		<dependencies>
			<!-- 依赖的组 ID -->
			<dependency>
				<groupId>com.snbc.bbpf</groupId>
				<artifactId>bbpf-system-db-common</artifactId>
				<!-- 依赖的版本使用 project.version 属性的值 -->
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.snbc.bbpf</groupId>
				<artifactId>bbpf-system-db-mysql</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.snbc.bbpf</groupId>
				<artifactId>bbpf-system-db-opengauss</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.snbc.bbpf</groupId>
				<artifactId>bbpf-grafana-proxy</artifactId>
				<version>${project.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>
</project>