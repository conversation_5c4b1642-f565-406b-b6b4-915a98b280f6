/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.snbc.bbpf.commons.strings.security.annotation.Desensitized;
import com.snbc.bbpf.commons.strings.security.annotation.SensitiveType;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * @ClassName: UserRequest
 * 导入用户请求实体类
 * @module: si-bbpf-system
 * @Author: wjc1
 * @date: 2021/5/19 14:07
 */
@Data
public class UserImportDto {
    @ExcelProperty(value = "姓名", index = 0)
    @NotBlank(message = "用户名不能为空")
    @Length(max=20, message="用户名最大长度为20")
    private String userName;

    @ExcelProperty(value = "工号", index = 1)
    @NotBlank(message = "用户工号不能为空")
    @Length(max=10, message="用户工号最大长度为10")
    private String jobNumber;

    @ExcelProperty(value = "手机号码", index = 2)
    @NotBlank(message = "用户手机号码不能为空")
    @Length(min=11, max=11, message="手机号长度为11")
    @Desensitized(type = SensitiveType.MOBILE_PHONE)
    private String phone;

    @ExcelProperty(value = "邮箱", index = 3)
    @Length(min=1, max=30, message="邮箱最大长度为30")
    private String email;
    /**
     * 所属组织机构id
     * */
    @ExcelProperty(value = "部门", index = 4)
    @NotBlank(message = "所属组织机构不能为空")
    private String belongOrgNames;
    /**
     * 所属角色id
     * */
    @ExcelProperty(value = "角色", index = 5)
    private String belongRoleNames;
    @ExcelProperty(value = "状态", index = 6)
    private String hasLockName;
    /**
     * 导入报错的错误信息
     * */
    @ExcelProperty(value = "错误信息", index = 7)
    private String errorMsg;
    // 上面属性顺便不能变，与导入的excle相对应的
    private Integer hasLock ;
    private String userId;
    private String belongRoleIds;
    private String belongOrgIds;
    /**
     * 是否导入新用户，1为新用户，0为老用户
     * */
    private boolean hasNewUser;
}
