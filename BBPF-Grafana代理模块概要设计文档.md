# BBPF Grafana代理模块概要设计文档

## 文档信息

- **文档名称**: BBPF Grafana代理模块概要设计文档
- **版本**: V1.0.0
- **编写日期**: 2025年1月
- **编写人**: BBPF开发团队
- **审核人**: 技术架构师

## 1. 项目概述

### 1.1 项目背景

BBPF Grafana代理模块是BBPF系统与Grafana监控平台之间的安全代理服务，旨在为企业级数据可视化提供统一的认证、授权和数据权限控制机制。该模块通过代理模式实现了对Grafana访问的完全控制，确保数据安全和合规性。

### 1.2 设计目标

- **安全性**: 提供完整的JWT认证和基于BBPF权限系统的访问控制
- **透明性**: 对用户完全透明的代理服务，无需修改Grafana配置
- **可扩展性**: 支持多种认证方式和权限模型的扩展
- **高性能**: 基于Redis的缓存机制和连接池优化
- **可监控**: 全面的监控指标和健康检查机制

### 1.3 技术架构

```
┌─────────────────────────────────────────────────────────────────┐
│                    BBPF Grafana代理模块架构图                      │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────────┐ │
│  │   单点登录模块    │ │  Grafana接口代理  │ │    SQL拦截模块      │ │
│  │                │ │     模块         │ │                    │ │
│  │ • JWT认证       │ │ • HTTP代理       │ │ • 查询拦截          │ │
│  │ • 用户身份验证   │ │ • 请求转发       │ │ • 动态权限过滤      │ │
│  │ • Token管理     │ │ • 响应处理       │ │ • SQL安全检查      │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────────┘ │
│                                                                 │
│  ┌─────────────────┐ ┌─────────────────┐                       │
│  │ 动态模版变量注入  │ │   分享控制模块    │                       │
│  │     模块        │ │                │                       │
│  │ • 变量动态注入   │ │ • 分享功能控制   │                       │
│  │ • 权限变量管理   │ │ • 导出权限验证   │                       │
│  │ • 模板变量缓存   │ │ • 安全策略执行   │                       │
│  └─────────────────┘ └─────────────────┘                       │
└─────────────────────────────────────────────────────────────────┘
```

## 6. 功能模块详细设计

### 6.1 Token验证模块

#### 6.1.1 本模块的功能描述及结构组成

**功能描述**:
Token验证模块负责根据部署模式提供灵活的身份认证功能。在微服务模式下，由于网关已完成认证，该模块可配置为禁用状态；在单体模式下，该模块通过调用配置的外部认证接口实现Token验证，确保只有经过认证的用户才能访问Grafana代理服务。

**结构组成**:

- **Token验证过滤器** (`TokenAuthenticationFilter`): 根据配置决定是否启用认证拦截
- **外部认证服务** (`ExternalAuthService`): 调用配置的外部认证接口验证Token
- **认证配置服务** (`AuthConfigService`): 管理认证模式和外部接口配置
- **Token工具类** (`TokenUtil`): 处理Token的解析和验证

#### 6.1.2 程序设计

**核心类设计**:

```java
// Token验证过滤器
@Component
public class TokenAuthenticationFilter extends OncePerRequestFilter {
    // 白名单路径配置
    private static final List<String> WHITELIST_PATHS = Arrays.asList(
        "/health", "/actuator", "/public", "/static"
    );
  
    // 用户上下文属性
    public static final String USER_ID_ATTRIBUTE = "bbpf.userId";
    public static final String USERNAME_ATTRIBUTE = "bbpf.username";
  
    @Autowired
    private AuthConfigService authConfigService;
  
    @Autowired
    private ExternalAuthService externalAuthService;
  
    @Override
    protected void doFilterInternal(HttpServletRequest request, 
                                  HttpServletResponse response, 
                                  FilterChain filterChain);
  
    private boolean isAuthenticationEnabled();
    private boolean isWhitelistPath(String path);
}

// 外部认证服务
@Service
public class ExternalAuthService {
  
    @Autowired
    private AuthConfigService authConfigService;
  
    @Autowired
    private RestTemplate restTemplate;
  
    public UserAuthResult validateToken(String token);
    public boolean isTokenValid(String token);
    private String callExternalAuthApi(String token);
}

// 认证配置服务
@Service
public class AuthConfigService {
  
    @Value("${bbpf.auth.enabled:true}")
    private boolean authEnabled;
  
    @Value("${bbpf.auth.mode:STANDALONE}")
    private String authMode;
  
    @Value("${bbpf.auth.external.url:}")
    private String externalAuthUrl;
  
    public boolean isAuthenticationEnabled();
    public String getAuthMode(); // MICROSERVICE, STANDALONE
    public String getExternalAuthUrl();
    public Map<String, String> getAuthHeaders();
    public int getAuthTimeout();
}

// Token工具类
@Component
public class TokenUtil {
    public String extractToken(HttpServletRequest request);
    public boolean isTokenFormatValid(String token);
    public Map<String, Object> parseTokenClaims(String token);
}
```

**类图设计**:

```mermaid
classDiagram
    class TokenAuthenticationFilter {
        -List~String~ WHITELIST_PATHS
        -AuthConfigService authConfigService
        -ExternalAuthService externalAuthService
        -TokenUtil tokenUtil
        +doFilterInternal(request, response, filterChain)
        +isAuthenticationEnabled() boolean
        +isWhitelistPath(path) boolean
        +handleAuthenticationFailure(response, message)
    }
  
    class ExternalAuthService {
        -RestTemplate restTemplate
        -AuthConfigService authConfigService
        +validateToken(token) UserAuthResult
        +isTokenValid(token) boolean
        -callExternalAuthApi(token) String
    }
  
    class AuthConfigService {
        -boolean authEnabled
        -String authMode
        -String externalAuthUrl
        +isAuthenticationEnabled() boolean
        +getAuthMode() String
        +getExternalAuthUrl() String
        +getAuthHeaders() Map
        +getAuthTimeout() int
    }
  
    class TokenUtil {
        +extractToken(request) String
        +isTokenFormatValid(token) boolean
        +parseTokenClaims(token) Map
    }
  
    class AuthConfig {
        -boolean enabled
        -String authMode
        -String externalAuthUrl
        -Map~String,String~ authHeaders
        -int authTimeout
    }
  
    class UserAuthResult {
         -boolean valid
         -String userId
         -String username
         -String message
         -long timestamp
     }
   
     TokenAuthenticationFilter --> AuthConfigService
     TokenAuthenticationFilter --> ExternalAuthService
     TokenAuthenticationFilter --> TokenUtil
     ExternalAuthService --> AuthConfigService
     ExternalAuthService --> UserAuthResult
     AuthConfigService --> AuthConfig
```

 **类图说明**:

- `TokenAuthenticationFilter`: 核心认证过滤器，根据配置决定是否启用认证功能
- `AuthConfigService`: 认证配置管理服务，控制认证模式和外部接口配置
- `ExternalAuthService`: 外部认证服务，负责调用配置的认证接口
- `TokenUtil`: Token处理工具类，提供Token解析和验证功能
- `AuthConfig`: 认证配置数据结构，包含所有认证相关配置
- `UserAuthResult`: 认证结果数据结构，包含认证状态和用户信息

**认证流程（基于配置驱动）**:

1. 请求到达Token认证过滤器
2. 检查认证模式配置（微服务模式直接放行）
3. 检查请求路径是否在白名单中
4. 提取Authorization头中的Token
5. 调用配置的外部认证接口验证Token
6. 解析认证结果并设置用户上下文
7. 继续处理请求或返回认证失败

#### 6.1.3 本模块业务流和数据流图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Filter as TokenAuthenticationFilter
    participant ConfigService as AuthConfigService
    participant AuthService as ExternalAuthService
    participant ExternalAPI as 外部认证接口
    participant Controller as 目标Controller
  
    Client->>Filter: HTTP请求
    Filter->>ConfigService: 检查认证模式
    alt 微服务模式
        ConfigService->>Filter: 认证已禁用
        Filter->>Controller: 直接放行
    else 单体模式
        ConfigService->>Filter: 认证已启用
        Filter->>Filter: 检查白名单路径
        alt 白名单路径
            Filter->>Controller: 直接放行
        else 需要认证
            Filter->>Filter: 提取Token
            alt Token不存在
                Filter->>Client: 401未授权错误
            else Token存在
                Filter->>AuthService: validateToken(token)
                AuthService->>ConfigService: 获取外部接口配置
                ConfigService->>AuthService: 返回接口配置
                AuthService->>ExternalAPI: 调用认证接口
                alt 验证失败
                    ExternalAPI->>AuthService: 认证失败
                    AuthService->>Filter: UserAuthResult(失败)
                    Filter->>Client: 认证失败错误
                else 验证成功
                    ExternalAPI->>AuthService: 用户信息
                    AuthService->>Filter: UserAuthResult(成功)
                    Filter->>Filter: 设置用户上下文
                    Filter->>Controller: 继续过滤器链
                end
            end
        end
    end
```

**业务流程图说明**:

- **参与者角色**: 客户端发起请求，Token认证过滤器进行拦截，认证配置服务提供配置信息，外部认证服务调用配置的认证接口
- **交互流程**: 根据部署模式决定是否启用认证，支持微服务模式的认证旁路和单体模式的Token验证
- **数据流特点**: 配置驱动的认证流程，支持灵活的外部接口集成，实现透明的认证代理

#### 6.1.4 数据结构

**认证配置数据结构**:

```java
public class AuthConfig {
    private boolean enabled;                    // 是否启用认证
    private String authMode;                    // 认证模式：MICROSERVICE, STANDALONE
    private String externalAuthUrl;             // 外部认证接口URL
    private Map<String, String> authHeaders;    // 认证请求头
    private int authTimeout;                    // 认证超时时间（毫秒）
    private List<String> whitelistPaths;        // 白名单路径
}
```

**用户认证结果数据结构**:

```java
public class UserAuthResult {
    private boolean valid;          // 认证是否有效
    private String userId;          // 用户ID
    private String username;        // 用户名
    private String message;         // 认证消息
    private long timestamp;         // 认证时间戳
    private Map<String, Object> attributes; // 扩展属性
}
```

**外部认证接口请求结构**:

```json
{
  "token": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "requestId": "req_12345",
  "timestamp": 1640995200
}
```

**外部认证接口响应结构**:

```json
{
  "success": true,
  "userId": "user123",
  "username": "张三",
  "message": "认证成功",
  "data": {
    "roles": ["admin", "user"],
    "permissions": ["read", "write"]
  }
}
```

#### 6.1.5 界面描述

Token验证模块主要处理后端认证逻辑，不涉及直接的用户界面。但会影响以下界面行为：

- **配置管理界面**: 管理员可配置认证模式、外部接口地址等参数
- **Grafana仪表盘**: 通过代理访问，根据配置决定是否进行认证验证
- **错误页面**: 认证失败时显示401未授权页面，包含具体的错误信息
- **监控界面**: 显示认证成功率、响应时间等指标

#### 6.1.6 出错和异常处理

**异常类型及处理策略**:

| 异常类型         | 处理策略                   | 返回状态码 |
| ---------------- | -------------------------- | ---------- |
| Token缺失        | 返回认证失败信息           | 401        |
| Token格式错误    | 返回格式错误信息           | 400        |
| 外部接口调用失败 | 记录日志，返回认证服务异常 | 503        |
| 外部接口超时     | 记录日志，返回超时错误     | 504        |
| 配置错误         | 记录日志，返回配置错误     | 500        |
| 认证被禁用       | 直接放行，记录日志         | 200        |
| 系统异常         | 记录日志，返回系统错误     | 500        |

**错误处理代码示例**:

```java
private void handleAuthenticationFailure(HttpServletResponse response, 
                                       String message, int statusCode) {
    response.setStatus(statusCode);
    response.setContentType("application/json;charset=UTF-8");
  
    Map<String, Object> errorResponse = new HashMap<>();
    errorResponse.put("error", message);
    errorResponse.put("timestamp", System.currentTimeMillis());
    errorResponse.put("status", statusCode);
    errorResponse.put("authMode", authConfigService.getAuthMode());
    errorResponse.put("requestId", UUID.randomUUID().toString());
  
    try {
        response.getWriter().write(objectMapper.writeValueAsString(errorResponse));
        logger.warn("Authentication failed: {} - Status: {} - Mode: {}", 
                   message, statusCode, authConfigService.getAuthMode());
    } catch (IOException e) {
        logger.error("Error writing authentication failure response", e);
    }
}

// 外部接口调用异常处理
private void handleExternalApiException(Exception e, String token) {
    if (e instanceof SocketTimeoutException) {
        logger.error("External auth API timeout for token: {}", 
                    token.substring(0, Math.min(token.length(), 10)) + "...");
        throw new AuthenticationException("认证服务超时");
    } else if (e instanceof ConnectException) {
        logger.error("External auth API connection failed: {}", e.getMessage());
        throw new AuthenticationException("认证服务不可用");
    } else {
        logger.error("External auth API call failed", e);
        throw new AuthenticationException("认证服务异常");
    }
}
```

#### 6.1.7 安全保密

**安全措施**:

1. **配置安全**: 外部认证接口地址和密钥通过环境变量或加密配置文件管理
2. **传输安全**: 与外部认证接口通信强制使用HTTPS协议
3. **Token保护**: 在日志中对Token进行脱敏处理，避免泄露
4. **接口鉴权**: 外部认证接口支持API Key或证书认证
5. **访问控制**: 基于IP白名单限制外部接口访问
6. **审计日志**: 记录所有认证相关的操作和异常日志

**安全配置**:

```properties
# Token验证配置
bbpf.auth.enabled=true
bbpf.auth.mode=STANDALONE
bbpf.auth.external.url=${EXTERNAL_AUTH_URL}
bbpf.auth.external.apiKey=${EXTERNAL_AUTH_API_KEY}
bbpf.auth.external.timeout=5000
bbpf.auth.whitelist.paths=/health,/actuator,/public
bbpf.auth.security.logTokenLength=10
```

#### 6.1.8 本模块的性能指标

**性能要求**:

- **认证响应时间**: < 200ms（包含外部接口调用）
- **配置加载时间**: < 10ms
- **并发认证能力**: > 500 TPS
- **内存使用**: < 50MB
- **CPU使用率**: < 5%

**性能优化策略**:

1. **配置缓存**: 将认证配置缓存到内存，避免重复读取
2. **连接池**: 使用连接池管理与外部认证接口的HTTP连接
3. **超时控制**: 设置合理的外部接口调用超时时间
4. **熔断机制**: 外部接口异常时启用熔断保护
5. **异步日志**: 认证日志采用异步写入方式

#### 6.1.9 本模块用到的开源软件

| 软件名称          | 版本   | 用途           | 许可证     |
| ----------------- | ------ | -------------- | ---------- |
| Spring Security   | 5.7.x  | 安全框架       | Apache 2.0 |
| Apache HttpClient | 4.5.x  | HTTP客户端     | Apache 2.0 |
| Jackson           | 2.13.x | JSON序列化     | Apache 2.0 |
| Spring Boot       | 2.7.x  | 应用框架       | Apache 2.0 |
| Logback           | 1.2.x  | 日志框架       | EPL 1.0    |
| Hystrix           | 1.5.x  | 熔断器（可选） | Apache 2.0 |

---

### 6.2 Grafana接口代理模块

#### 6.2.1 本模块的功能描述及结构组成

**功能描述**:
Grafana接口代理模块负责接收客户端请求并转发到Grafana服务器，同时处理请求和响应的修改、权限验证、错误处理等功能。该模块是整个代理服务的核心，实现了透明的HTTP代理功能。

**结构组成**:

- **代理控制器** (`GrafanaProxyController`): 处理所有代理请求的入口
- **代理服务** (`GrafanaProxyService`): 核心代理逻辑实现
- **静态资源控制器** (`StaticResourceController`): 处理静态资源请求
- **Grafana API客户端** (`GrafanaApiClient`): 与Grafana API交互的客户端

#### 6.2.2 程序设计

**核心类设计**:

```java
// 代理控制器
@RestController
@RequestMapping("/")
public class GrafanaProxyController {
  
    @GetMapping("/d/{dashboardId}/")
    public void accessDashboard(@PathVariable String dashboardId,
                               HttpServletRequest request,
                               HttpServletResponse response);
  
    @RequestMapping(value = "/api/**", method = {GET, POST, PUT, DELETE})
    public void handleApiRequest(HttpServletRequest request, 
                               HttpServletResponse response);
  
    @PostMapping("/export/{resourceId}")
    public void handleDataExport(@PathVariable String resourceId,
                                HttpServletRequest request,
                                HttpServletResponse response);
}

// 代理服务实现
@Service
public class GrafanaProxyServiceImpl implements GrafanaProxyService {
  
    public boolean proxyRequest(HttpServletRequest request, 
                              HttpServletResponse response,
                              String targetPath, String userId);
  
    private HttpRequestBase createProxyRequest(HttpServletRequest request, 
                                             String targetUrl, String userId);
}
```

**类图设计**:

```mermaid
classDiagram
    class GrafanaProxyController {
        -GrafanaProxyService proxyService
        -PermissionService permissionService
        -ProxyStatistics statistics
        +accessDashboard(dashboardId, request, response)
        +handleApiRequest(request, response)
        +handleDataExport(resourceId, request, response)
        +handleStaticResource(request, response)
    }
  
    class GrafanaProxyService {
        <<interface>>
        +proxyRequest(request, response, targetPath, userId) boolean
        +buildTargetUrl(request, targetPath) String
        +modifyRequestHeaders(request, userId) Map
        +processResponse(response, userId) String
    }
  
    class GrafanaProxyServiceImpl {
        -CloseableHttpClient httpClient
        -GrafanaAuthService authService
        -SqlInterceptorService sqlInterceptor
        -DynamicTemplateVariableService variableService
        +proxyRequest(request, response, targetPath, userId) boolean
        +createProxyRequest(request, targetUrl, userId) HttpRequestBase
        +copyResponseHeaders(grafanaResponse, response)
        +handleProxyError(exception, response)
    }
  
    class StaticResourceController {
        -ResourceLoader resourceLoader
        -GrafanaProxyConfig config
        +handleStaticResource(request, response)
        +getResourcePath(requestPath) String
        +setResponseHeaders(response, resource)
    }
  
    class GrafanaApiClient {
        -RestTemplate restTemplate
        -GrafanaProxyConfig config
        -GrafanaAuthService authService
        +getDashboard(dashboardId, userId) DashboardDto
        +getDataSource(dataSourceId, userId) DataSourceDto
        +executeQuery(query, userId) QueryResultDto
        +createApiKey(userId) String
    }
  
    class ProxyStatistics {
        -AtomicLong totalRequests
        -AtomicLong successfulRequests
        -AtomicLong failedRequests
        -Timer responseTime
        +recordRequest(method, path, duration)
        +getStatistics() StatisticsDto
        +reset()
    }
  
    class ProxyRequestConfig {
        -String targetUrl
        -String method
        -Map headers
        -String body
        -int timeout
        -int retryCount
    }
  
    class ProxyResponse {
        -int statusCode
        -Map headers
        -String body
        -String contentType
        -long responseTime
    }
  
    GrafanaProxyController --> GrafanaProxyService
    GrafanaProxyController --> ProxyStatistics
    GrafanaProxyServiceImpl ..|> GrafanaProxyService
    GrafanaProxyServiceImpl --> GrafanaApiClient
    GrafanaProxyServiceImpl --> ProxyRequestConfig
    GrafanaProxyServiceImpl --> ProxyResponse
    StaticResourceController --> GrafanaProxyConfig
  
  

```

**代理处理流程**:

1. 接收客户端HTTP请求
2. 验证用户身份和权限
3. 构建目标Grafana URL
4. 创建代理请求对象
5. 修改请求头（添加认证信息）
6. 执行HTTP请求到Grafana
7. 处理Grafana响应
8. 修改响应内容（如需要）
9. 返回响应给客户端

#### 6.2.3 本模块业务流和数据流图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as GrafanaProxyController
    participant PermService as PermissionService
    participant ProxyService as GrafanaProxyService
    participant ApiClient as GrafanaApiClient
    participant Grafana as Grafana实例
  
    Client->>Controller: HTTP请求
    Controller->>Controller: 获取用户信息
    Controller->>PermService: checkAccess(user, resource)
    alt 权限验证失败
        PermService->>Controller: 权限不足
        Controller->>Client: 403禁止访问
    else 权限验证成功
        PermService->>Controller: 权限验证通过
        Controller->>ProxyService: proxyRequest(request)
        ProxyService->>ProxyService: 解析请求路径和参数
        ProxyService->>ProxyService: 构建目标Grafana URL
        ProxyService->>ApiClient: sendRequest(config)
        ApiClient->>Grafana: HTTP请求
        alt Grafana响应成功
            Grafana->>ApiClient: 成功响应
            ApiClient->>ProxyService: 响应数据
            ProxyService->>ProxyService: 更新统计信息
        else Grafana响应失败
            Grafana->>ApiClient: 错误响应
            ApiClient->>ProxyService: 错误信息
            ProxyService->>ProxyService: 记录错误日志
        end
        ProxyService->>Controller: ProxyResponse
        Controller->>Client: 返回响应
    end
```

#### 6.2.4 数据结构

**代理请求配置**:

```java
public class ProxyRequestConfig {
    private String targetUrl;           // 目标URL
    private String method;              // HTTP方法
    private Map<String, String> headers; // 请求头
    private String body;                // 请求体
    private int timeout;                // 超时时间
    private int retryCount;             // 重试次数
}
```

**代理响应数据**:

```java
public class ProxyResponse {
    private int statusCode;             // 状态码
    private Map<String, String> headers; // 响应头
    private String body;                // 响应体
    private String contentType;         // 内容类型
    private long responseTime;          // 响应时间
}
```

**统计信息数据结构**:

```java
public class ProxyStatistics {
    private AtomicLong totalRequests;     // 总请求数
    private AtomicLong successfulRequests; // 成功请求数
    private AtomicLong failedRequests;    // 失败请求数
    private AtomicLong deniedRequests;    // 拒绝请求数
    private double successRate;           // 成功率
    private long averageResponseTime;     // 平均响应时间
}
```

#### 6.2.5 界面描述

代理模块主要处理后端请求转发，不直接涉及用户界面，但影响以下界面的访问：

- **Grafana仪表盘页面**: 通过代理访问，URL格式为 `/d/{dashboardId}/`
- **Grafana API接口**: 所有以 `/api/` 开头的接口请求
- **静态资源**: CSS、JS、图片等静态文件的代理访问
- **数据导出页面**: 支持仪表盘和面板的数据导出功能

#### 6.2.6 出错和异常处理

**异常类型及处理策略**:

| 异常类型          | 处理策略               | 返回状态码 | 重试策略     |
| ----------------- | ---------------------- | ---------- | ------------ |
| 连接超时          | 记录日志，返回超时错误 | 504        | 最多重试3次  |
| Grafana服务不可用 | 返回服务不可用         | 503        | 指数退避重试 |
| 权限不足          | 返回权限错误信息       | 403        | 不重试       |
| 请求格式错误      | 返回格式错误信息       | 400        | 不重试       |
| 内部服务错误      | 记录详细日志           | 500        | 不重试       |

**错误处理实现**:

```java
public class ProxyErrorHandler {
  
    public void handleProxyError(Exception e, HttpServletResponse response) {
        if (e instanceof ConnectTimeoutException) {
            handleTimeoutError(response);
        } else if (e instanceof HttpHostConnectException) {
            handleConnectionError(response);
        } else {
            handleGenericError(e, response);
        }
    }
  
    private void handleTimeoutError(HttpServletResponse response) {
        response.setStatus(HttpStatus.GATEWAY_TIMEOUT.value());
        writeErrorResponse(response, "Grafana服务响应超时");
    }
}
```

#### 6.2.7 安全保密

**安全措施**:

1. **请求头过滤**: 移除敏感的客户端请求头
2. **响应头清理**: 清理可能泄露服务器信息的响应头
3. **URL验证**: 验证目标URL的合法性，防止SSRF攻击
4. **内容过滤**: 对响应内容进行安全检查
5. **访问日志**: 记录所有代理请求的详细日志

**安全配置**:

```properties
# 安全配置
bbpf.grafana.proxy.enable-request-logging=true
bbpf.grafana.proxy.filter-sensitive-headers=true
bbpf.grafana.proxy.validate-target-urls=true
```

#### 6.2.8 本模块的性能指标

**性能要求**:

- **代理响应时间**: < 200ms（不含Grafana处理时间）
- **并发处理能力**: > 500 TPS
- **内存使用**: < 200MB
- **连接池大小**: 最大100个连接
- **超时设置**: 连接超时5s，读取超时30s

**性能监控指标**:

```java
@Component
public class ProxyMetrics {
    private final Counter totalRequests;
    private final Timer responseTime;
    private final Gauge activeConnections;
  
    public void recordRequest(String method, String path, long duration) {
        totalRequests.increment(Tags.of("method", method, "path", path));
        responseTime.record(duration, TimeUnit.MILLISECONDS);
    }
}
```

#### 6.2.9 本模块用到的开源软件

| 软件名称             | 版本   | 用途       | 许可证     |
| -------------------- | ------ | ---------- | ---------- |
| Apache HttpClient    | 4.5.x  | HTTP客户端 | Apache 2.0 |
| Spring Web           | 5.3.x  | Web框架    | Apache 2.0 |
| Jackson              | 2.13.x | JSON处理   | Apache 2.0 |
| Micrometer           | 1.9.x  | 性能监控   | Apache 2.0 |
| Spring Boot Actuator | 2.7.x  | 健康检查   | Apache 2.0 |

---

### 6.3 SQL拦截模块

#### 6.3.1 本模块的功能描述及结构组成

**功能描述**:
SQL拦截模块负责拦截和修改Grafana数据查询请求中的SQL语句，根据用户权限动态添加数据过滤条件，实现细粒度的数据级权限控制。该模块支持多种数据源类型，能够智能识别SQL语句并注入权限过滤条件。

#### 6.3.2 程序设计

**核心类设计**:

```java
// SQL拦截服务
@Service
public class SqlInterceptorService {
  
    // 拦截请求的主入口方法
    public String interceptRequest(String requestBody, String userId);
  
    // 检查是否需要拦截请求
    public boolean shouldInterceptRequest(String apiPath);
  
    // 拦截并修改请求
    public String interceptAndModifyRequest(String requestBody, String userId, String apiPath);
  
    // 修改SQL语句
    private String modifySqlWithPermissionFilter(String originalSql, String permissionFilter);
  
    // 提取表名
    private Set<String> extractTableNames(String sql);
  
    // 构建权限过滤条件
    private String buildPermissionFilter(UserPermissionDto userPermission, String sql);
}

// SQL拦截配置
@Configuration
@ConfigurationProperties(prefix = "bbpf.sql.interceptor")
public class SqlInterceptorConfig {
    private boolean enabled = true;
    private List<String> interceptPatterns;
    private Map<String, TablePermissionMapping> tablePermissionMapping;
    private boolean verboseLogging = false;
}
```

**类图设计**:

```mermaid
classDiagram
    class SqlInterceptorService {
        -SqlInterceptorConfig config
        -PermissionService permissionService
        -SqlParser sqlParser
        -RedisTemplate redisTemplate
        +interceptRequest(requestBody, userId) String
        +shouldInterceptRequest(apiPath) boolean
        +interceptAndModifyRequest(requestBody, userId, apiPath) String
        -modifySqlWithPermissionFilter(originalSql, permissionFilter) String
        -extractTableNames(sql) Set~String~
        -buildPermissionFilter(userPermission, sql) String
    }
  
    class SqlInterceptorConfig {
        -boolean enabled
        -List~String~ interceptPatterns
        -Map~String,TablePermissionMapping~ tablePermissionMapping
        -boolean verboseLogging
        +isEnabled() boolean
        +getInterceptPatterns() List~String~
        +getTablePermissionMapping() Map
    }
  
    class SqlParser {
        -JSqlParser parser
        +parseStatement(sql) Statement
        +extractTableNames(statement) Set~String~
        +modifyWhereClause(statement, filter) Statement
        +statementToString(statement) String
        +validateSqlSyntax(sql) boolean
    }
  
    class PermissionFilter {
        -PermissionService permissionService
        +generateUserFilter(userId, tableName) String
        +generateOrgFilter(userId, tableName) String
        +generateCustomFilter(userId, tableName) String
        +combineFilters(filters) String
    }
  
    class TablePermissionMapping {
        -List~String~ userFields
        -List~String~ orgFields
        -String customFilter
        -boolean allowFullAccess
        +getUserFields() List~String~
        +getOrgFields() List~String~
        +getCustomFilter() String
        +isAllowFullAccess() boolean
    }
  
    class SqlParseResult {
        -String originalSql
        -String modifiedSql
        -Set~String~ tableNames
        -String whereClause
        -boolean hasWhereClause
        -List~String~ appliedFilters
    }
  
    class InterceptorStatistics {
        -AtomicLong totalInterceptions
        -AtomicLong successfulInterceptions
        -AtomicLong failedInterceptions
        -Map~String,Long~ tableAccessCount
        +recordInterception(tableName, success)
        +getStatistics() StatisticsDto
    }
  
    SqlInterceptorService --> SqlInterceptorConfig
    SqlInterceptorService --> SqlParser
    SqlInterceptorService --> PermissionFilter
    SqlInterceptorService --> SqlParseResult
    SqlInterceptorService --> InterceptorStatistics
    SqlInterceptorConfig --> TablePermissionMapping
    PermissionFilter --> TablePermissionMapping
```

**类图说明**:

- **SqlInterceptorService**: SQL拦截核心服务类，负责拦截Grafana数据查询请求，解析SQL语句并根据用户权限动态注入数据过滤条件。提供请求拦截、SQL修改、权限过滤等核心功能。

- **SqlInterceptorConfig**: SQL拦截器配置类，管理拦截器的启用状态、拦截路径模式、表权限映射关系等配置信息。支持动态配置更新和详细的日志控制。

- **SqlParser**: SQL解析器类，使用JSqlParser库解析SQL语句，提取表名信息，修改WHERE子句，验证SQL语法。负责SQL语句的结构化分析和安全的SQL重写。

- **PermissionFilter**: 权限过滤器类，根据用户权限生成数据过滤条件。支持用户级、组织级和自定义过滤规则，能够组合多种过滤条件形成最终的权限控制语句。

- **TablePermissionMapping**: 表权限映射配置类，定义每个数据表的权限控制规则，包括用户字段、组织字段、自定义过滤条件和完全访问权限设置。

- **SqlParseResult**: SQL解析结果类，封装SQL解析和修改的结果信息，包括原始SQL、修改后SQL、涉及的表名、WHERE子句和应用的过滤条件等。

- **InterceptorStatistics**: 拦截器统计类，记录SQL拦截操作的统计信息，包括拦截总数、成功次数、失败次数和各表的访问统计，用于性能监控和审计分析。


#### 6.3.3 本模块业务流和数据流图

```mermaid
sequenceDiagram
    participant Request as 请求
    participant Interceptor as SqlInterceptorService
    participant Parser as SqlParser
    participant Filter as PermissionFilter
    participant Config as TablePermissionMapping
    participant Statistics as InterceptorStatistics
  
    Request->>Interceptor: 进入拦截器
    Interceptor->>Interceptor: 检查路径匹配模式
    alt 不需要拦截
        Interceptor->>Request: 返回原始请求
    else 需要拦截
        Interceptor->>Interceptor: 提取SQL语句
        Interceptor->>Parser: parseQuery(sql)
        Parser->>Parser: 解析SQL结构
        Parser->>Interceptor: 返回表名列表
  
        Interceptor->>Filter: getUserPermissions(user)
        Filter->>Config: 查询表权限配置
        Config->>Filter: 返回权限配置
        Filter->>Interceptor: 返回用户权限
  
        alt 无表访问权限
            Interceptor->>Request: 权限拒绝错误
        else 有表访问权限
            Interceptor->>Filter: 生成过滤条件
            Filter->>Interceptor: 返回WHERE条件
            Interceptor->>Parser: injectWhereCondition(sql, condition)
            Parser->>Interceptor: 返回修改后SQL
  
            Interceptor->>Statistics: recordInterception()
            Interceptor->>Interceptor: 重新构建请求体
            Interceptor->>Request: 返回修改后请求
        end
    end
  
  
```

**模块内部类交互说明**:

1. **SqlInterceptorService**: SQL拦截核心服务，负责拦截和处理SQL请求
2. **SqlParser**: SQL解析器，负责解析和修改SQL语句
3. **PermissionFilter**: 权限过滤器，获取用户权限并生成过滤条件
4. **TablePermissionMapping**: 表权限映射配置，定义表级别的权限规则
5. **SqlParseResult**: SQL解析结果对象，包含解析后的SQL信息
6. **InterceptorStatistics**: 拦截器统计服务，记录拦截操作统计信息
7. **SqlInterceptorConfig**: SQL拦截器配置，控制拦截行为和规则

#### 6.3.4 数据结构

**SQL拦截配置结构**:

```java
public class SqlInterceptorConfig {
    private boolean enabled;                    // 是否启用拦截
    private List<String> interceptPatterns;     // 拦截路径模式
    private Map<String, TablePermissionMapping> tablePermissionMapping; // 表权限映射
  
    public static class TablePermissionMapping {
        private List<String> userFields;         // 用户字段列表
        private List<String> orgFields;          // 组织字段列表
        private String customFilter;             // 自定义过滤条件
        private boolean allowFullAccess;         // 是否允许完全访问
    }
}
```

**SQL解析结果结构**:

```java
public class SqlParseResult {
    private String originalSql;                 // 原始SQL
    private String modifiedSql;                 // 修改后SQL
    private Set<String> tableNames;             // 涉及的表名
    private String whereClause;                 // WHERE子句
    private boolean hasWhereClause;             // 是否已有WHERE子句
    private List<String> injectedConditions;    // 注入的条件
}
```

**权限过滤条件结构**:

```java
public class PermissionFilter {
    private String userId;                      // 用户ID
    private String orgId;                       // 组织ID
    private String dataFilter;                  // 数据过滤条件
    private Map<String, String> tableFilters;   // 表级过滤条件
    private boolean isAdmin;                    // 是否管理员
}
```

#### 6.3.5 界面描述

SQL拦截模块在后台运行，对用户透明，不涉及直接的用户界面。但会影响以下数据展示：

- **仪表盘图表**: 显示经过权限过滤的数据
- **数据表格**: 只显示用户有权限查看的数据行
- **统计图表**: 统计结果基于过滤后的数据
- **导出数据**: 导出的数据也会经过权限过滤

#### 6.3.6 出错和异常处理

**异常类型及处理策略**:

| 异常类型         | 处理策略              | 降级方案             |
| ---------------- | --------------------- | -------------------- |
| SQL解析失败      | 记录日志，使用原始SQL | 允许查询但记录警告   |
| 权限获取失败     | 拒绝查询请求          | 返回空结果集         |
| 过滤条件生成失败 | 使用默认严格过滤      | 只返回用户自己的数据 |
| JSON解析异常     | 返回原始请求          | 跳过拦截处理         |
| 配置错误         | 使用默认配置          | 启用最严格的权限控制 |

**异常处理实现**:

```java
public class SqlInterceptorErrorHandler {
  
    public String handleSqlParseError(String originalSql, Exception e) {
        logger.error("SQL解析失败: {}", e.getMessage());
  
        // 记录到监控系统
        meterRegistry.counter("sql.parse.error").increment();
  
        // 返回拒绝访问的SQL
        return "SELECT 'Access Denied' as error WHERE 1=0";
    }
  
    public String handlePermissionError(String userId, Exception e) {
        logger.error("获取用户{}权限失败: {}", userId, e.getMessage());
  
        // 使用最严格的权限控制
        return "user_id = '" + userId + "'";
    }
}
```

#### 6.3.7 安全保密

**安全措施**:

1. **SQL注入防护**: 使用参数化查询，防止SQL注入攻击
2. **权限条件验证**: 验证注入的权限条件的安全性
3. **敏感信息过滤**: 过滤可能泄露敏感信息的SQL语句
4. **访问日志**: 记录所有SQL拦截和修改的详细日志
5. **白名单机制**: 支持配置不需要拦截的安全SQL模式

**安全配置示例**:

```properties
# SQL拦截安全配置
bbpf.sql.interceptor.enable-sql-injection-check=true
bbpf.sql.interceptor.blocked-keywords=DROP,DELETE,UPDATE,INSERT,TRUNCATE
bbpf.sql.interceptor.max-sql-length=10000
bbpf.sql.interceptor.enable-audit-log=true
```

#### 6.3.8 本模块的性能指标

**性能要求**:

- **SQL解析时间**: < 50ms
- **权限条件生成时间**: < 30ms
- **总拦截处理时间**: < 100ms
- **内存使用**: < 50MB
- **缓存命中率**: > 80%

**性能优化策略**:

1. **SQL解析缓存**: 缓存已解析的SQL模式
2. **权限条件缓存**: 缓存用户的权限过滤条件
3. **正则表达式优化**: 优化SQL解析的正则表达式
4. **异步处理**: 非关键的日志记录采用异步处理

#### 6.3.9 本模块用到的开源软件

| 软件名称    | 版本   | 用途     | 许可证     |
| ----------- | ------ | -------- | ---------- |
| Jackson     | 2.13.x | JSON解析 | Apache 2.0 |
| Spring Boot | 2.7.x  | 框架支持 | Apache 2.0 |
| SLF4J       | 1.7.x  | 日志记录 | MIT        |
| Micrometer  | 1.9.x  | 性能监控 | Apache 2.0 |
| Caffeine    | 3.1.x  | 本地缓存 | Apache 2.0 |

---

### 6.4 动态模版变量注入模块

#### 6.4.1 本模块的功能描述及结构组成

**功能描述**:
动态模版变量注入模块通过拦截Grafana仪表盘的JSON响应，动态向其templating列表添加用户权限相关的变量，实现高通用性、低侵入性的权限控制机制。该模块相比SQL拦截方案具有更好的通用性和维护性。

#### 6.4.2 程序设计

**核心类设计**:

```java
// 动态模板变量服务
@Service
public class DynamicTemplateVariableService {
  
    // 注入用户权限变量到仪表盘JSON
    public String injectUserPermissionVariables(String dashboardJson, String userId);
  
    // 创建权限变量定义
    private ObjectNode createPermissionVariable(String name, Object value, String type);
  
    // 验证变量安全性
    private boolean isVariableValueSafe(String variableName, String variableValue);
}

// Grafana变量服务
@Service
public class GrafanaVariableService {
  
    // 为用户生成Grafana变量映射
    public Map<String, Object> generateUserVariables(String userId);
  
    // 生成数据权限相关的变量
    private void generateDataPermissionVariables(Map<String, Object> variables, 
                                               UserPermissionDto userPermission);
  
    // 转换为Grafana格式
    public Map<String, Object> convertToGrafanaFormat(Map<String, Object> variables);
}
```

**类图设计**:

```mermaid
classDiagram
    class DynamicTemplateVariableService {
        -GrafanaVariableService variableService
        -PermissionService permissionService
        -ObjectMapper objectMapper
        -RedisTemplate redisTemplate
        +injectUserPermissionVariables(dashboardJson, userId) String
        +processTemplatingSection(templating, userId) JsonNode
        -createPermissionVariable(name, value, type) ObjectNode
        -isVariableValueSafe(variableName, variableValue) boolean
        -validateVariableInjection(variables) boolean
    }
  
    class GrafanaVariableService {
        -GrafanaApiClient apiClient
        -PermissionService permissionService
        -VariableCache variableCache
        +generateUserVariables(userId) Map~String,Object~
        +generateUserInfoVariables(userId) Map~String,Object~
        +generateOrgPermissionVariables(userId) Map~String,Object~
        +parseDataFilterToVariables(dataFilter) Map~String,Object~
        -generateDataPermissionVariables(variables, userPermission)
        +convertToGrafanaFormat(variables) Map~String,Object~
        +createVariableValue(name, value, type) VariableDefinition
    }
  
    class DashboardResponseInterceptor {
        -DynamicTemplateVariableService variableService
        +preHandle(request, response, handler) boolean
        +afterCompletion(request, response, handler, ex)
        +processResponse(responseBody, userId) String
        -isJsonResponse(response) boolean
        -isDashboardApi(requestPath) boolean
        -getResponseContentType(response) String
    }
  
    class VariableCache {
        -RedisTemplate redisTemplate
        -long cacheExpiration
        +getCachedVariables(userId) Map~String,Object~
        +cacheVariables(userId, variables)
        +invalidateUserCache(userId)
        +getCacheKey(userId) String
    }
  
    class VariableDefinition {
        -String name
        -String type
        -String label
        -String description
        -int hide
        -Object query
        -VariableValue current
        -List~VariableValue~ options
        +toGrafanaJson() JsonNode
    }
  
    class VariableValue {
        -String text
        -Object value
        -boolean selected
        +getText() String
        +getValue() Object
        +isSelected() boolean
    }
  
    class PermissionVariableGenerator {
        -PermissionService permissionService
        +generateUserIdVariable(userId) VariableDefinition
        +generateOrgIdVariable(userId) VariableDefinition
        +generateRoleVariable(userId) VariableDefinition
        +generateDataFilterVariable(userId, tableName) VariableDefinition
        -createConstantVariable(name, value) VariableDefinition
        -createQueryVariable(name, query) VariableDefinition
    }
  
    class VariableInjectionStatistics {
        -AtomicLong totalInjections
        -AtomicLong successfulInjections
        -AtomicLong failedInjections
        -Map~String,Long~ variableTypeCount
        +recordInjection(variableType, success)
        +getStatistics() StatisticsDto
    }
  
    DynamicTemplateVariableService --> GrafanaVariableService
    DynamicTemplateVariableService --> VariableDefinition
    DynamicTemplateVariableService --> VariableInjectionStatistics
    GrafanaVariableService --> VariableCache
    GrafanaVariableService --> PermissionVariableGenerator
    GrafanaVariableService --> VariableDefinition
    DashboardResponseInterceptor --> DynamicTemplateVariableService
    VariableDefinition --> VariableValue
    PermissionVariableGenerator --> VariableDefinition
```

**类图说明**:

- **DynamicTemplateVariableService**: 动态模板变量服务的核心类，负责拦截Grafana仪表盘JSON响应并注入用户权限相关的变量。主要功能包括解析仪表盘JSON、创建权限变量、验证变量安全性等。
- **GrafanaVariableService**: Grafana变量服务类，负责生成符合Grafana格式的用户权限变量。包括用户信息变量、组织权限变量、数据过滤变量等的生成和格式转换。
- **DashboardResponseInterceptor**: 仪表盘响应拦截器，用于拦截Grafana仪表盘API的响应，识别需要注入变量的响应并调用变量注入服务进行处理。
- **VariableCache**: 变量缓存管理类，负责缓存用户的权限变量定义，提高变量注入的性能，减少重复计算。
- **VariableDefinition**: 变量定义数据类，表示一个Grafana模板变量的完整定义，包括名称、类型、标签、查询条件、当前值和选项列表等。
- **VariableValue**: 变量值数据类，表示变量的具体值，包括显示文本、实际值和是否选中状态。
- **PermissionVariableGenerator**: 权限变量生成器，专门负责根据用户权限信息生成各种类型的权限变量，如用户ID变量、角色变量、数据过滤变量等。
- **VariableInjectionStatistics**: 变量注入统计类，用于记录和统计变量注入的成功率、失败次数、变量类型分布等监控指标。

#### 6.4.3 本模块业务流和数据流图

```mermaid
sequenceDiagram
    participant Response as 仪表盘响应
    participant Interceptor as DashboardResponseInterceptor
    participant VariableService as DynamicTemplateVariableService
    participant Generator as PermissionVariableGenerator
    participant Cache as VariableCache
    participant GrafanaService as GrafanaVariableService
    participant Statistics as VariableInjectionStatistics
  
    Response->>Interceptor: 进入响应拦截器
    Interceptor->>Interceptor: 检查是否为仪表盘API
    alt 非仪表盘配置响应
        Interceptor->>Response: 返回原始响应
    else 仪表盘配置响应
        Interceptor->>Interceptor: 解析JSON配置
        Interceptor->>VariableService: injectVariables(dashboard, user)
        VariableService->>VariableService: 获取用户信息
        VariableService->>Generator: generateVariables(user)
  
        Generator->>Generator: 获取用户权限信息
        Generator->>Cache: 查询变量缓存
        alt 缓存命中
            Cache->>Generator: 返回缓存变量
        else 缓存未命中
            Generator->>Generator: 生成新权限变量
            Generator->>Cache: 缓存变量定义
        end
  
        Generator->>VariableService: 返回变量定义列表
        VariableService->>GrafanaService: createGrafanaVariable(definition)
        GrafanaService->>VariableService: 返回Grafana格式变量
  
        VariableService->>VariableService: 注入到templating.list
        VariableService->>Statistics: recordInjection()
        VariableService->>Interceptor: 返回修改后配置
        Interceptor->>Response: 返回响应给客户端
    end
```

**时序图说明**:

该时序图展示了动态模板变量注入模块的完整工作流程，主要包括以下几个阶段：

1. **响应拦截阶段**: DashboardResponseInterceptor拦截Grafana仪表盘API的响应，检查是否为需要注入变量的仪表盘配置响应。
2. **变量生成阶段**: 如果是仪表盘配置响应，则调用DynamicTemplateVariableService进行变量注入处理，首先获取当前用户信息，然后调用PermissionVariableGenerator生成权限相关的变量。
3. **缓存处理阶段**: PermissionVariableGenerator首先查询VariableCache中是否有该用户的缓存变量，如果缓存命中则直接返回，否则生成新的权限变量并缓存。
4. **格式转换阶段**: 将生成的变量定义通过GrafanaVariableService转换为符合Grafana格式的变量对象。
5. **变量注入阶段**: DynamicTemplateVariableService将转换后的变量注入到仪表盘JSON的templating.list中，同时记录注入统计信息。
6. **响应返回阶段**: 将修改后的仪表盘配置返回给客户端，完成整个变量注入流程。

**关键交互说明**:

- **缓存优化**: 通过VariableCache提高性能，避免重复生成相同用户的权限变量
- **统计监控**: 通过VariableInjectionStatistics记录注入成功率和性能指标
- **错误处理**: 当变量注入失败时，返回原始响应，确保仪表盘正常显示
- **透明代理**: 整个过程对用户完全透明，不影响Grafana的正常使用体验

#### 6.4.4 数据结构

**Grafana变量定义结构**:

```json
{
  "name": "current_user_id",
  "type": "constant",
  "label": "当前用户ID",
  "description": "用于数据权限过滤的当前用户ID",
  "hide": 2,
  "query": "user123",
  "current": {
    "text": "user123",
    "value": "user123"
  },
  "options": [
    {
      "text": "user123",
      "value": "user123",
      "selected": true
    }
  ]
}
```

**权限变量映射结构**:

```java
public class PermissionVariableMapping {
    private String userId;                      // 用户ID
    private String userRole;                    // 用户角色
    private String orgId;                       // 组织ID
    private String dataFilter;                  // 数据过滤条件
    private Map<String, String> customVariables; // 自定义变量
    private long cacheExpireTime;               // 缓存过期时间
}
```

**变量注入配置结构**:

```java
public class VariableInjectionConfig {
    private boolean enabled;                    // 是否启用变量注入
    private List<String> targetApiPatterns;     // 目标API模式
    private Map<String, VariableTemplate> variableTemplates; // 变量模板
    private int cacheExpirationSeconds;         // 缓存过期时间
  
    public static class VariableTemplate {
        private String name;                    // 变量名
        private String type;                    // 变量类型
        private String label;                   // 显示标签
        private int hide;                       // 隐藏级别
        private String valueExpression;         // 值表达式
    }
}
```

#### 6.4.5 界面描述

动态模版变量注入模块主要在后台运行，但会影响Grafana仪表盘的以下界面元素：

- **变量下拉框**: 注入的权限变量会出现在仪表盘的变量列表中（通常隐藏）
- **查询编辑器**: 用户可以在查询中使用注入的权限变量（如 `$current_user_id`）
- **仪表盘设置**: 在仪表盘设置的变量页面可以看到注入的变量
- **数据面板**: 面板查询会自动使用权限变量进行数据过滤

#### 6.4.6 出错和异常处理

**异常类型及处理策略**:

| 异常类型     | 处理策略               | 降级方案             |
| ------------ | ---------------------- | -------------------- |
| JSON解析失败 | 返回原始响应           | 跳过变量注入         |
| 权限获取失败 | 注入默认严格变量       | 限制为用户自己的数据 |
| 变量注入失败 | 记录错误，返回原始响应 | 不影响仪表盘正常显示 |
| 缓存异常     | 直接计算变量值         | 性能降低但功能正常   |
| 配置错误     | 使用默认配置           | 注入基本的权限变量   |

**异常处理实现**:

```java
public class VariableInjectionErrorHandler {
  
    public String handleJsonParseError(String originalJson, Exception e) {
        logger.error("仪表盘JSON解析失败: {}", e.getMessage());
  
        // 记录监控指标
        meterRegistry.counter("variable.injection.json.parse.error").increment();
  
        // 返回原始JSON，不影响仪表盘显示
        return originalJson;
    }
  
    public Map<String, Object> handlePermissionError(String userId, Exception e) {
        logger.error("获取用户{}权限变量失败: {}", userId, e.getMessage());
  
        // 返回默认的严格权限变量
        Map<String, Object> defaultVariables = new HashMap<>();
        defaultVariables.put("current_user_id", userId);
        defaultVariables.put("data_filter", "user_id = '" + userId + "'");
  
        return defaultVariables;
    }
}
```

#### 6.4.7 安全保密

**安全措施**:

1. **变量值验证**: 验证注入的变量值不包含恶意代码
2. **权限变量隐藏**: 将权限相关变量设置为隐藏，用户不可见
3. **变量名白名单**: 只允许注入预定义的安全变量名
4. **值长度限制**: 限制变量值的最大长度，防止注入攻击
5. **审计日志**: 记录所有变量注入操作的详细日志

**安全配置**:

```properties
# 变量注入安全配置
bbpf.variable.injection.enable-value-validation=true
bbpf.variable.injection.max-variable-value-length=1000
bbpf.variable.injection.allowed-variable-names=current_user_id,user_role,org_id,data_filter
bbpf.variable.injection.enable-audit-log=true
```

#### 6.4.8 本模块的性能指标

**性能要求**:

- **变量注入时间**: < 50ms
- **JSON解析时间**: < 30ms
- **权限变量生成时间**: < 20ms
- **缓存命中率**: > 90%
- **内存使用**: < 30MB

**性能优化策略**:

1. **变量缓存**: 缓存用户的权限变量，减少重复计算
2. **JSON流式处理**: 使用流式JSON处理，减少内存占用
3. **异步注入**: 对于非关键变量，采用异步注入
4. **批量处理**: 支持批量处理多个仪表盘的变量注入

#### 6.4.9 本模块用到的开源软件

| 软件名称          | 版本   | 用途      | 许可证     |
| ----------------- | ------ | --------- | ---------- |
| Jackson           | 2.13.x | JSON处理  | Apache 2.0 |
| Spring Boot       | 2.7.x  | 框架支持  | Apache 2.0 |
| Caffeine          | 3.1.x  | 本地缓存  | Apache 2.0 |
| SLF4J             | 1.7.x  | 日志记录  | MIT        |
| Spring Data Redis | 2.7.x  | Redis缓存 | Apache 2.0 |

---

### 6.5 分享控制模块

#### 6.5.1 本模块的功能描述及结构组成

**功能描述**:
分享控制模块通过配置控制Grafana界面中分享和导出按钮的显示。代理服务根据配置修改返回给前端的页面内容，隐藏或禁用相关功能按钮，实现简单有效的分享控制。

**结构组成**:

- **分享配置服务** (`ShareConfigService`): 管理分享功能的开关配置
- **响应修改器** (`ResponseModifier`): 修改Grafana页面响应内容

#### 6.5.2 程序设计

**核心类设计**:

```java
// 分享配置服务
@Service
public class ShareConfigService {
  
    // 检查是否启用分享功能
    public boolean isShareEnabled();
  
    // 检查是否启用快照功能
    public boolean isSnapshotEnabled();
  
    // 检查是否启用导出功能
    public boolean isExportEnabled();
  
    // 检查是否启用面板导出
    public boolean isPanelExportEnabled();
}

// 响应修改器
@Component
public class ResponseModifier {
  
    @Autowired
    private ShareConfigService shareConfigService;
  
    // 修改Grafana页面响应，隐藏分享按钮
    public String modifyDashboardResponse(String originalResponse);
  
    // 修改API响应，移除分享相关配置
    public String modifyApiResponse(String originalResponse);
  
    // 注入JavaScript代码隐藏UI元素
    public String injectHideScript(String htmlContent);
}
```

**类图设计**:

```mermaid
classDiagram
    class ShareConfigService {
        -boolean shareEnabled
        -boolean snapshotEnabled
        -boolean exportEnabled
        -boolean panelExportEnabled
        +isShareEnabled() boolean
        +isSnapshotEnabled() boolean
        +isExportEnabled() boolean
        +isPanelExportEnabled() boolean
        +loadConfig() void
        +updateConfig(config) void
    }
  
    class ResponseModifier {
        -ShareConfigService shareConfigService
        -Pattern shareButtonPattern
        -Pattern exportButtonPattern
        -Pattern snapshotButtonPattern
        +modifyDashboardResponse(response) String
        +modifyApiResponse(response) String
        +injectHideScript(htmlContent) String
        -hideShareButtons(content) String
        -hideExportButtons(content) String
        -removeShareConfig(apiResponse) String
    }
  
    class ShareConfig {
        -boolean shareEnabled
        -boolean snapshotEnabled
        -boolean exportEnabled
        -boolean panelExportEnabled
        -List~String~ hiddenElements
        +isFeatureEnabled(feature) boolean
        +getHiddenElements() List~String~
    }
  
    ResponseModifier --> ShareConfigService
    ShareConfigService --> ShareConfig
```

**类图说明**:

- **ShareConfigService**: 分享配置服务类，负责管理分享功能的各种开关配置。该服务从配置文件或数据库中加载配置信息，并提供配置查询和更新功能。通过布尔值控制分享、快照、导出等功能的启用状态。
- **ResponseModifier**: 响应修改器类，是分享控制的核心执行组件。依赖ShareConfigService获取配置状态，使用正则表达式模式匹配页面中的分享相关按钮和元素，根据配置动态修改Grafana返回的HTML内容和API响应。
- **ShareConfig**: 分享配置数据类，封装了所有分享控制相关的配置项。包含各功能模块的启用状态和需要隐藏的UI元素列表，为配置服务提供数据结构支持。

**类关系说明**:

- ResponseModifier依赖ShareConfigService获取实时配置状态
- ShareConfigService使用ShareConfig作为配置数据载体
- 整体采用依赖注入模式，便于配置的动态更新和测试

**分享控制流程**:

1. 代理服务拦截Grafana页面请求
2. 检查分享功能配置状态
3. 根据配置修改页面响应内容
4. 隐藏或禁用相关分享按钮和功能

#### 6.5.3 本模块业务流和数据流图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Proxy as 代理服务
    participant ConfigService as ShareConfigService
    participant Modifier as ResponseModifier
    participant Grafana as Grafana服务
  
    Client->>Proxy: 请求Grafana页面
    Proxy->>Grafana: 转发请求
    Grafana->>Proxy: 返回页面内容
    Proxy->>ConfigService: 检查分享配置
    ConfigService->>Proxy: 返回配置状态
    Proxy->>Modifier: 修改页面响应
    Modifier->>Modifier: 隐藏分享按钮
    Modifier->>Proxy: 返回修改后内容
    Proxy->>Client: 返回处理后页面
```

**业务流程图说明**:

**参与者角色**:

- **客户端(Client)**: 用户浏览器，发起对Grafana页面的访问请求
- **代理服务(Proxy)**: BBPF代理服务，作为中间层拦截和处理所有请求
- **ShareConfigService**: 分享配置服务，提供分享功能的配置查询
- **ResponseModifier**: 响应修改器，负责根据配置修改页面内容
- **Grafana服务**: 原始Grafana服务，提供基础的监控页面和数据

**交互流程详解**:

1. **请求拦截阶段**: 客户端访问Grafana页面时，代理服务首先拦截请求，然后透明转发给后端Grafana服务，确保基础功能正常运行。
2. **内容获取阶段**: Grafana服务处理请求并返回完整的页面内容，包含所有原始的分享按钮、导出菜单等UI元素。
3. **配置检查阶段**: 代理服务调用ShareConfigService查询当前的分享控制配置，获取各功能模块（分享、快照、导出等）的启用状态。
4. **内容修改阶段**: 根据配置状态，ResponseModifier对页面内容进行处理，通过正则表达式匹配和替换，隐藏或禁用相应的UI元素。
5. **响应返回阶段**: 将修改后的页面内容返回给客户端，用户看到的是经过分享控制处理的界面。

**数据流特点**:

- 配置驱动：所有修改行为基于配置文件，支持动态调整
- 透明代理：对Grafana服务无侵入，保持原有功能完整性
- 实时生效：配置变更可立即反映到用户界面上

#### 6.5.4 数据结构

**分享控制配置结构**:

```java
public class ShareConfig {
    private boolean shareEnabled;                // 是否启用分享功能
    private boolean snapshotEnabled;             // 是否启用快照功能
    private boolean exportEnabled;               // 是否启用导出功能
    private boolean panelExportEnabled;          // 是否启用面板导出
    private List<String> hiddenElements;         // 需要隐藏的UI元素列表
}
```

**响应修改配置**:

```java
public class ResponseModifyConfig {
    private Map<String, String> elementSelectors;  // UI元素选择器映射
    private List<String> hideScripts;              // 隐藏脚本列表
    private Map<String, String> apiModifyRules;    // API响应修改规则
}
```

#### 6.5.5 界面描述

分享控制模块通过配置控制Grafana界面中的分享相关功能显示：

- **分享按钮**: 根据配置隐藏或显示分享按钮
- **导出菜单**: 根据配置隐藏或显示导出菜单项
- **快照功能**: 根据配置隐藏或显示快照相关按钮
- **面板导出**: 根据配置隐藏或显示面板导出选项

#### 6.5.6 出错和异常处理

**异常类型及处理策略**:

| 异常类型     | 处理策略     | 返回状态码 | 用户提示               |
| ------------ | ------------ | ---------- | ---------------------- |
| 配置加载失败 | 使用默认配置 | 200        | 使用默认分享控制设置   |
| 响应修改失败 | 返回原始响应 | 200        | 返回未修改的页面内容   |
| 系统异常     | 记录错误日志 | 500        | "系统异常，请稍后重试" |

**异常处理实现**:

```java
public class ShareConfigErrorHandler {
  
    @ExceptionHandler(ConfigLoadException.class)
    public void handleConfigLoadError(ConfigLoadException e) {
        // 记录错误日志
        logger.error("分享配置加载失败，使用默认配置: {}", e.getMessage());
  
        // 使用默认配置
        shareConfigService.loadDefaultConfig();
    }
  
    @ExceptionHandler(ResponseModifyException.class)
    public String handleResponseModifyError(ResponseModifyException e, String originalResponse) {
        // 记录错误日志
        logger.warn("响应修改失败，返回原始内容: {}", e.getMessage());
  
        // 返回原始响应
        return originalResponse;
    }
}
```

#### 6.5.7 安全保密

**安全措施**:

1. **配置安全**: 分享控制配置文件的访问权限控制
2. **响应安全**: 确保页面修改过程中不泄露敏感信息
3. **日志记录**: 记录配置变更和异常情况

**安全配置**:

```properties
# 分享控制配置
bbpf.share.config.share-enabled=false
bbpf.share.config.snapshot-enabled=false
bbpf.share.config.export-enabled=false
bbpf.share.config.panel-export-enabled=false
```

#### 6.5.8 本模块的性能指标

**性能要求**:

- **配置加载时间**: < 50ms
- **页面响应修改时间**: < 100ms
- **内存使用**: < 10MB

**性能监控指标**:

```java
@Component
public class ShareConfigMetrics {
    private final Timer configLoadTime;
    private final Timer responseModifyTime;
    private final Counter configLoadFailures;
  
    public void recordConfigLoad(long duration) {
        configLoadTime.record(duration, TimeUnit.MILLISECONDS);
    }
  
    public void recordResponseModify(long duration) {
        responseModifyTime.record(duration, TimeUnit.MILLISECONDS);
    }
  
    public void recordConfigLoadFailure() {
        configLoadFailures.increment();
    }
}
```

#### 6.5.9 本模块用到的开源软件

| 软件名称        | 版本   | 用途       | 许可证     |
| --------------- | ------ | ---------- | ---------- |
| Spring Security | 5.7.x  | 权限控制   | Apache 2.0 |
| Spring Boot     | 2.7.x  | 框架支持   | Apache 2.0 |
| Jackson         | 2.13.x | JSON处理   | Apache 2.0 |
| SLF4J           | 1.7.x  | 日志记录   | MIT        |
| Micrometer      | 1.9.x  | 性能监控   | Apache 2.0 |
| Spring Data JPA | 2.7.x  | 数据持久化 | Apache 2.0 |

---

## 7. 五视图架构设计

### 7.1 逻辑视图

逻辑视图描述了BBPF Grafana代理模块的功能组织和模块间的逻辑关系。

```mermaid
graph TB
    subgraph "表示层"
        A[Web界面] --> B[REST API]
        C[移动端] --> B
        D[第三方集成] --> B
    end
  
    subgraph "业务逻辑层"
        B --> E[Token验证模块]
        B --> F[Grafana接口代理模块]
        B --> G[SQL拦截模块]
        B --> H[动态模板变量注入模块]
        B --> I[分享控制模块]
    end
  
    subgraph "数据访问层"
        E --> J[认证服务接口]
        F --> K[Grafana API]
        G --> L[数据源连接池]
        H --> M[权限缓存]
        I --> N[配置存储]
    end
  
    subgraph "基础设施层"
        J --> O[Redis缓存]
        K --> O
        L --> P[数据库连接]
        M --> O
        N --> Q[配置中心]
    end
```

**模块职责说明**:

- **Token验证模块**: 负责用户身份认证和权限验证
- **Grafana接口代理模块**: 核心代理功能，处理HTTP请求转发
- **SQL拦截模块**: 实现数据权限控制和SQL安全检查
- **动态模板变量注入模块**: 动态注入权限相关的模板变量
- **分享控制模块**: 控制Grafana的分享和导出功能

### 7.2 开发视图

开发视图展示了系统的软件包结构和组件依赖关系。

```mermaid
graph LR
    subgraph "bbpf-grafana-proxy"
        subgraph "controller"
            A1[GrafanaProxyController]
            A2[StaticResourceController]
            A3[HealthCheckController]
        end
    
        subgraph "service"
            B1[TokenAuthenticationService]
            B2[GrafanaProxyService]
            B3[SqlInterceptorService]
            B4[DynamicTemplateVariableService]
            B5[ShareControlService]
        end
    
        subgraph "filter"
            C1[TokenAuthenticationFilter]
            C2[SqlInterceptorFilter]
            C3[ResponseModifyFilter]
        end
    
        subgraph "config"
            D1[AuthConfig]
            D2[ProxyConfig]
            D3[CacheConfig]
            D4[SecurityConfig]
        end
    
        subgraph "util"
            E1[TokenUtil]
            E2[HttpUtil]
            E3[SqlParserUtil]
            E4[CacheUtil]
        end
    end
  
    A1 --> B2
    A2 --> B5
    B1 --> C1
    B2 --> C2
    B3 --> E3
    B4 --> E4
```

**包结构说明**:

- **controller**: Web控制器层，处理HTTP请求
- **service**: 业务服务层，实现核心业务逻辑
- **filter**: 过滤器层，实现请求拦截和处理
- **config**: 配置类，管理系统配置信息
- **util**: 工具类，提供通用功能支持

### 7.3 进程视图

进程视图描述了系统运行时的进程结构和并发处理机制。

```mermaid
graph TB
    subgraph "负载均衡器"
        LB[Nginx/HAProxy]
    end
  
    subgraph "代理服务集群"
        P1[代理实例1:8080]
        P2[代理实例2:8080]
        P3[代理实例3:8080]
        PN[代理实例N:8080]
    end
  
    subgraph "缓存集群"
        R1[Redis Master]
        R2[Redis Slave1]
        R3[Redis Slave2]
    end
  
    subgraph "后端服务"
        G1[Grafana实例1:3000]
        G2[Grafana实例2:3000]
        DB[(数据库)]
    end
  
    LB --> P1
    LB --> P2
    LB --> P3
    LB --> PN
  
    P1 --> R1
    P2 --> R1
    P3 --> R1
    PN --> R1
  
    R1 --> R2
    R1 --> R3
  
    P1 --> G1
    P2 --> G2
    P3 --> G1
    PN --> G2
  
    G1 --> DB
    G2 --> DB
```

**进程特性**:

- **水平扩展**: 支持多实例部署，通过负载均衡器分发请求
- **高可用**: Redis主从复制，Grafana多实例部署
- **并发处理**: 每个代理实例支持1000+并发连接
- **故障恢复**: 实例故障时自动切换到健康实例

### 7.4 物理视图

物理视图展示了系统的部署架构和硬件资源分配。

```mermaid
graph TB
    subgraph "DMZ区域"
        subgraph "负载均衡层"
            LB1[负载均衡器1<br/>2C4G]
            LB2[负载均衡器2<br/>2C4G]
        end
    end
  
    subgraph "应用区域"
        subgraph "代理服务层"
            APP1[代理服务器1<br/>4C8G]
            APP2[代理服务器2<br/>4C8G]
            APP3[代理服务器3<br/>4C8G]
        end
    
        subgraph "缓存层"
            CACHE1[Redis服务器1<br/>2C4G]
            CACHE2[Redis服务器2<br/>2C4G]
            CACHE3[Redis服务器3<br/>2C4G]
        end
    end
  
    subgraph "数据区域"
        subgraph "监控服务层"
            GRAF1[Grafana服务器1<br/>4C8G]
            GRAF2[Grafana服务器2<br/>4C8G]
        end
    
        subgraph "数据存储层"
            DB1[(主数据库<br/>8C16G)]
            DB2[(从数据库<br/>8C16G)]
        end
    end
  
    LB1 -.-> LB2
    LB1 --> APP1
    LB1 --> APP2
    LB2 --> APP2
    LB2 --> APP3
  
    APP1 --> CACHE1
    APP2 --> CACHE2
    APP3 --> CACHE3
  
    CACHE1 -.-> CACHE2
    CACHE2 -.-> CACHE3
  
    APP1 --> GRAF1
    APP2 --> GRAF2
    APP3 --> GRAF1
  
    GRAF1 --> DB1
    GRAF2 --> DB1
    DB1 -.-> DB2
```

**部署特点**:

- **网络隔离**: DMZ、应用、数据三层网络隔离
- **资源配置**: 根据负载特点合理分配CPU和内存资源
- **高可用**: 关键组件双机热备，数据库主从复制
- **安全防护**: 防火墙、入侵检测、访问控制

### 7.5 数据视图

数据视图描述了系统的数据模型、存储结构和缓存设计。

#### 7.5.1 核心数据模型

```mermaid
erDiagram
    USER {
        string user_id PK
        string username
        string email
        datetime created_at
        datetime updated_at
    }
  
    PERMISSION {
        string permission_id PK
        string user_id FK
        string resource_type
        string resource_id
        string action
        datetime created_at
        datetime expires_at
    }
  
    DASHBOARD_ACCESS {
        string access_id PK
        string user_id FK
        string dashboard_id
        string access_level
        datetime created_at
        datetime last_access
    }
  
    SQL_FILTER_RULE {
        string rule_id PK
        string user_id FK
        string datasource_id
        string filter_condition
        boolean is_active
        datetime created_at
    }
  
    TEMPLATE_VARIABLE {
        string variable_id PK
        string user_id FK
        string variable_name
        string variable_value
        string variable_type
        datetime created_at
        datetime expires_at
    }
  
    USER ||--o{ PERMISSION : has
    USER ||--o{ DASHBOARD_ACCESS : has
    USER ||--o{ SQL_FILTER_RULE : has
    USER ||--o{ TEMPLATE_VARIABLE : has
```

#### 7.5.2 缓存设计

系统采用Redis作为分布式缓存，以下是详细的缓存设计表格：

| 缓存类型                  | 缓存Key格式                                            | 数据类型 | TTL(秒) | Value大小限制 | 用途说明                               | 更新策略          | 命中率要求 |
| ------------------------- | ------------------------------------------------------ | -------- | ------- | ------------- | -------------------------------------- | ----------------- | ---------- |
| **用户认证缓存**    | `bbpf:auth-service:s:token:{tokenHash}`              | String   | 1800    | <1KB          | 存储用户认证信息，避免重复调用认证接口 | 被动过期          | >90%       |
| **用户权限缓存**    | `bbpf:auth-service:h:perm:{userId}`                  | Hash     | 3600    | <10KB         | 缓存用户权限列表，提高权限检查性能     | 主动更新+防击穿   | >85%       |
| **仪表盘访问权限**  | `bbpf:proxy-service:s:dash_access:{userId}:{dashId}` | String   | 7200    | <512B         | 缓存用户对特定仪表盘的访问权限         | 主动更新          | >80%       |
| **SQL过滤规则**     | `bbpf:sql-service:l:filter:{userId}:{dsId}`          | List     | 3600    | <5KB          | 缓存用户的SQL过滤规则，提高查询性能    | 主动更新+防雪崩   | >75%       |
| **模板变量缓存**    | `bbpf:template-service:s:var:{userId}:{varName}`     | String   | 1800    | <2KB          | 缓存动态模板变量值                     | 被动过期+随机TTL  | >70%       |
| **数据源连接信息**  | `bbpf:datasource-service:h:conn:{dsId}`              | Hash     | 86400   | <3KB          | 缓存数据源连接配置信息                 | 主动更新          | >95%       |
| **Grafana API响应** | `bbpf:proxy-service:s:api:{apiPath}:{paramHash}`     | String   | 300     | <50KB         | 缓存Grafana API响应，减少后端压力      | 被动过期+压缩存储 | >60%       |
| **用户会话信息**    | `bbpf:session-service:h:user:{sessionId}`            | Hash     | 7200    | <2KB          | 存储用户会话状态和上下文信息           | 被动过期          | >85%       |
| **配置信息缓存**    | `bbpf:config-service:h:config:{configType}`          | Hash     | 3600    | <5KB          | 缓存系统配置信息，减少配置读取         | 主动更新          | >98%       |
| **统计计数器**      | `bbpf:metrics-service:s:stats:{metric}:{timeWindow}` | String   | 3600    | <100B         | 存储访问统计和性能指标                 | 定时更新          | >50%       |
| **IP访问限制**      | `bbpf:security-service:s:limit_ip:{ipAddress}`       | String   | 3600    | <200B         | 存储IP访问频率限制信息                 | 被动过期          | >70%       |
| **错误重试计数**    | `bbpf:retry-service:s:retry:{operation}:{key}`       | String   | 1800    | <100B         | 记录操作重试次数，实现熔断机制         | 被动过期          | >60%       |

#### 7.5.3 缓存策略说明

**Key命名规范**:

- **格式**: `项目名称:服务模块:数据结构:业务key`
- **项目前缀**: `bbpf` (BBPF框架标识)
- **服务模块**: 明确标识所属服务模块，如 `auth-service`、`proxy-service`等
- **数据结构缩写**: `s`(string)、`h`(hash)、`l`(list)、`u`(set)、`zu`(zset)
- **长度限制**: Key总长度不超过100个字符
- **唯一性保证**: 通过命名空间前缀确保全局Redis库中的唯一性

**Value规范**:

- **大小限制**: 单个Value不超过1MB，设计阶段已考虑压缩存储
- **数据精简**: 仅存储应用必要数据，移除未使用的属性和对象
- **序列化**: 使用JSON格式存储复杂对象，确保跨语言兼容性
- **压缩策略**: 对于大于10KB的数据启用GZIP压缩

**TTL设置规范**:

- **强制要求**: 所有缓存Key必须设置TTL，避免内存泄漏
- **最大限制**: TTL不超过30天(2592000秒)
- **随机化**: 对于批量数据，TTL增加随机偏移(±10%)，防止缓存雪崩
- **分级设置**:
  - 热点数据: 300-1800秒
  - 常用数据: 1800-7200秒
  - 配置数据: 3600-86400秒

**更新策略**:

- **被动过期**: 设置TTL，到期自动删除，适用于时效性要求不高的数据
- **主动更新**: 数据变更时主动删除或更新缓存，适用于一致性要求高的数据
- **定时更新**: 定时任务批量更新缓存，适用于统计类数据
- **防击穿**: 对于热点数据使用互斥锁，避免缓存失效时大量请求穿透到数据库
- **防雪崩**: 设置随机TTL，避免大量Key同时失效
- **防穿透**: 对于不存在的数据缓存空值，TTL设置较短(60-300秒)

**数据一致性保障**:

- **最终一致性**: 大部分缓存采用最终一致性模型，允许短暂的数据不一致
- **强一致性**: 关键业务数据(如权限信息)采用Cache-Aside模式，确保数据一致性
- **故障处理**: Redis故障时自动降级到数据库查询，确保业务连续性
- **超时处理**: Redis访问超时(>100ms)时直接查询数据库，避免用户等待

**缓存层级**:

1. **L1缓存**: 应用内存缓存，存储热点数据，TTL较短(60-300秒)
2. **L2缓存**: Redis分布式缓存，存储共享数据，TTL较长(300-86400秒)
3. **L3缓存**: 数据库查询结果缓存，减少数据库压力

**缓存监控指标**:

- **命中率要求**: 不同类型缓存有不同的命中率要求(见上表)
- **响应时间**: 缓存查询响应时间 < 10ms
- **内存使用**: Redis内存使用率 < 80%
- **网络延迟**: 应用到Redis网络延迟 < 5ms
- **未设置TTL监控**: 运维日常巡检未设置TTL的Key
- **僵尸Key监控**: 监控命中率极低(0次或1次)的Key，及时清理

#### 7.5.4 缓存设计实现示例

**缓存Key生成工具类**:

```java
@Component
public class CacheKeyGenerator {
    private static final String PROJECT_PREFIX = "bbpf";
    private static final int MAX_KEY_LENGTH = 100;
  
    public String generateKey(String service, String dataType, String... keyParts) {
        StringBuilder keyBuilder = new StringBuilder(PROJECT_PREFIX)
            .append(":")
            .append(service)
            .append(":")
            .append(getDataTypeAbbr(dataType))
            .append(":");
        
        for (String part : keyParts) {
            keyBuilder.append(part).append(":");
        }
    
        String key = keyBuilder.toString();
        if (key.endsWith(":")) {
            key = key.substring(0, key.length() - 1);
        }
    
        if (key.length() > MAX_KEY_LENGTH) {
            throw new IllegalArgumentException("Cache key length exceeds maximum: " + key.length());
        }
    
        return key;
    }
  
    private String getDataTypeAbbr(String dataType) {
        switch (dataType.toLowerCase()) {
            case "string": return "s";
            case "hash": return "h";
            case "list": return "l";
            case "set": return "u";
            case "zset": return "zu";
            default: return "s";
        }
    }
}
```

**缓存服务实现**:

```java
@Service
public class BbpfCacheService {
  
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
  
    @Autowired
    private CacheKeyGenerator keyGenerator;
  
    private static final int TTL_RANDOM_OFFSET_PERCENT = 10;
  
    public void setUserPermission(String userId, Object permissions) {
        String key = keyGenerator.generateKey("auth-service", "hash", "perm", userId);
        int ttl = addRandomOffset(3600); // 1小时 + 随机偏移
    
        // 检查Value大小
        String jsonValue = JSON.toJSONString(permissions);
        if (jsonValue.length() > 10 * 1024) { // 10KB限制
            log.warn("Cache value size exceeds limit: {} bytes for key: {}", jsonValue.length(), key);
        }
    
        redisTemplate.opsForValue().set(key, permissions, ttl, TimeUnit.SECONDS);
        log.info("Cached user permission. key={}, ttl={}", key, ttl);
    }
  
    public Object getUserPermission(String userId) {
        String key = keyGenerator.generateKey("auth-service", "hash", "perm", userId);
        Object result = redisTemplate.opsForValue().get(key);
    
        // 记录命中率
        if (result != null) {
            cacheMetrics.recordHit("user_permission");
        } else {
            cacheMetrics.recordMiss("user_permission");
        }
    
        return result;
    }
  
    public void setWithFallback(String key, Object value, int ttl, Supplier<Object> fallbackSupplier) {
        try {
            redisTemplate.opsForValue().set(key, value, ttl, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("Redis cache failed, using fallback. key={}, error={}", key, e.getMessage());
            // Redis故障时的降级处理
            return fallbackSupplier.get();
        }
    }
  
    private int addRandomOffset(int baseTtl) {
        int offset = (int) (baseTtl * TTL_RANDOM_OFFSET_PERCENT / 100.0);
        return baseTtl + new Random().nextInt(offset * 2) - offset;
    }
}
```

**缓存监控配置**:

```java
@Component
public class CacheMetrics {
    private final Counter cacheHitCounter;
    private final Counter cacheMissCounter;
    private final Timer cacheResponseTimer;
  
    public CacheMetrics(MeterRegistry meterRegistry) {
        this.cacheHitCounter = Counter.builder("bbpf.cache.hit")
            .description("Cache hit count")
            .tag("cache_type", "redis")
            .register(meterRegistry);
        
        this.cacheMissCounter = Counter.builder("bbpf.cache.miss")
            .description("Cache miss count")
            .tag("cache_type", "redis")
            .register(meterRegistry);
        
        this.cacheResponseTimer = Timer.builder("bbpf.cache.response.time")
            .description("Cache response time")
            .register(meterRegistry);
    }
  
    public void recordHit(String cacheType) {
        cacheHitCounter.increment(Tags.of("cache_name", cacheType));
    }
  
    public void recordMiss(String cacheType) {
        cacheMissCounter.increment(Tags.of("cache_name", cacheType));
    }
}
```

### 7.6 场景视图

场景视图通过具体的使用场景展示系统的运行流程和交互过程。

#### 7.6.1 用户登录访问场景

```mermaid
sequenceDiagram
    participant U as 用户
    participant LB as 负载均衡器
    participant P as 代理服务
    participant R as Redis缓存
    participant A as 认证服务
    participant G as Grafana
  
    U->>LB: 1. 访问仪表盘URL
    LB->>P: 2. 转发请求
    P->>P: 3. 提取Token
    P->>R: 4. 查询认证缓存
    alt 缓存命中
        R->>P: 5a. 返回用户信息
    else 缓存未命中
        P->>A: 5b. 调用认证接口
        A->>P: 6b. 返回认证结果
        P->>R: 7b. 更新认证缓存
    end
    P->>R: 8. 查询权限缓存
    R->>P: 9. 返回权限信息
    P->>G: 10. 转发到Grafana
    G->>P: 11. 返回页面内容
    P->>P: 12. 注入模板变量
    P->>U: 13. 返回最终页面
```

#### 7.6.2 数据查询权限控制场景

```mermaid
sequenceDiagram
    participant U as 用户
    participant P as 代理服务
    participant S as SQL拦截器
    participant R as Redis缓存
    participant D as 数据源
  
    U->>P: 1. 发起数据查询请求
    P->>S: 2. 拦截SQL查询
    S->>R: 3. 获取用户权限规则
    R->>S: 4. 返回过滤规则
    S->>S: 5. 解析并修改SQL
    S->>D: 6. 执行修改后的SQL
    D->>S: 7. 返回查询结果
    S->>S: 8. 过滤敏感数据
    S->>P: 9. 返回安全结果
    P->>U: 10. 返回最终数据
```

#### 7.6.3 系统故障恢复场景

```mermaid
sequenceDiagram
    participant M as 监控系统
    participant LB as 负载均衡器
    participant P1 as 代理实例1
    participant P2 as 代理实例2
    participant R as Redis集群
  
    M->>P1: 1. 健康检查
    P1-->>M: 2. 响应超时/失败
    M->>LB: 3. 标记实例1不健康
    LB->>LB: 4. 移除实例1
    M->>P1: 5. 尝试重启服务
    P1->>R: 6. 重新连接缓存
    R->>P1: 7. 连接成功
    P1->>M: 8. 健康检查通过
    M->>LB: 9. 恢复实例1
    LB->>LB: 10. 重新加入负载均衡
```

---

## 8. 总体技术架构

### 8.1 系统架构图

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           BBPF Grafana代理模块总体架构                          │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐  │
│  │   前端应用   │    │   移动应用   │    │   第三方系统  │    │   管理后台   │  │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘  │
│           │                 │                 │                 │          │
│           └─────────────────┼─────────────────┼─────────────────┘          │
│                             │                 │                            │
│  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │                      负载均衡器 (Nginx/HAProxy)                      │   │
│  └─────────────────────────────────────────────────────────────────────┘   │
│                             │                                               │
│  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │                    BBPF Grafana代理服务集群                          │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  │   │
│  │  │  代理实例1   │  │  代理实例2   │  │  代理实例3   │  │  代理实例N   │  │   │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘  │   │
│  └─────────────────────────────────────────────────────────────────────┘   │
│                             │                                               │
│  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │                        中间件层                                      │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  │   │
│  │  │ Redis集群   │  │ 消息队列     │  │ 配置中心     │  │ 服务注册     │  │   │
│  │  │ (缓存)      │  │ (RabbitMQ)  │  │ (Nacos)     │  │ (Eureka)    │  │   │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘  │   │
│  └─────────────────────────────────────────────────────────────────────┘   │
│                             │                                               │
│  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │                        后端服务层                                     │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  │   │
│  │  │ BBPF权限API │  │ Grafana服务  │  │ 监控服务     │  │ 日志服务     │  │   │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘  │   │
│  └─────────────────────────────────────────────────────────────────────┘   │
│                             │                                               │
│  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │                        数据存储层                                     │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  │   │
│  │  │ MySQL主库   │  │ MySQL从库   │  │ InfluxDB    │  │ Elasticsearch│  │   │
│  │  │ (业务数据)   │  │ (读取优化)   │  │ (时序数据)   │  │ (日志搜索)   │  │   │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘  │   │
│  └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 8.2 技术栈总览

| 层次                 | 技术组件             | 版本   | 用途说明            |
| -------------------- | -------------------- | ------ | ------------------- |
| **应用框架**   | Spring Boot          | 2.7.18 | 微服务应用框架      |
| **安全框架**   | Spring Security      | 5.7.x  | 认证授权框架        |
| **Web框架**    | Spring Web MVC       | 5.3.x  | Web请求处理         |
| **缓存**       | Redis                | 6.x    | 分布式缓存          |
| **HTTP客户端** | Apache HttpClient    | 4.5.x  | HTTP请求代理        |
| **JSON处理**   | Jackson              | 2.13.x | JSON序列化/反序列化 |
| **JWT处理**    | JJWT                 | 0.11.x | JWT Token处理       |
| **监控**       | Micrometer           | 1.9.x  | 应用监控指标        |
| **健康检查**   | Spring Boot Actuator | 2.7.x  | 应用健康检查        |
| **WebSocket**  | Spring WebSocket     | 5.3.x  | 实时通信支持        |
| **测试框架**   | JUnit 5              | 5.8.x  | 单元测试            |
| **Mock框架**   | Mockito              | 4.6.x  | 测试Mock            |
| **构建工具**   | Maven                | 3.8.x  | 项目构建管理        |

### 8.3 部署架构

#### 8.3.1 开发环境

```yaml
# 开发环境配置
environment: development
instances: 1
resources:
  cpu: 2 cores
  memory: 4GB
  storage: 20GB
services:
  - bbpf-grafana-proxy:8080
  - redis:6379
  - grafana:3000
```

#### 8.3.2 测试环境

```yaml
# 测试环境配置
environment: testing
instances: 2
resources:
  cpu: 4 cores
  memory: 8GB
  storage: 50GB
services:
  - bbpf-grafana-proxy:8080 (2 instances)
  - redis-cluster (3 nodes)
  - grafana:3000
  - nginx:80 (load balancer)
```

#### 8.3.3 生产环境

```yaml
# 生产环境配置
environment: production
instances: 4+
resources:
  cpu: 8 cores
  memory: 16GB
  storage: 100GB
services:
  - bbpf-grafana-proxy:8080 (4+ instances)
  - redis-cluster (6 nodes)
  - grafana:3000 (HA setup)
  - nginx:80/443 (load balancer + SSL)
  - monitoring: prometheus + grafana
```

## 9. 总结

### 9.1 项目特色

1. **安全性**: 完整的JWT认证和基于BBPF权限系统的访问控制体系
2. **透明性**: 对用户完全透明的代理服务，无需修改Grafana配置
3. **高性能**: 基于Redis的多级缓存机制和连接池优化
4. **可扩展性**: 模块化设计，支持水平扩展和功能扩展
5. **可维护性**: 清晰的代码结构和完善的文档体系

### 9.2 技术亮点

1. **动态模板变量注入**: 创新的权限控制方案，通用性强、侵入性低
2. **智能SQL拦截**: 支持多种数据源的SQL权限过滤
3. **细粒度权限控制**: 支持仪表盘、数据源、导出等多维度权限控制
4. **完整的审计体系**: 全面的操作审计和安全追踪
5. **高可用架构**: 支持集群部署和故障自动恢复

### 9.3 应用价值

该代理模块不仅解决了Grafana与BBPF系统集成的技术问题，更重要的是建立了一套完整的企业级数据可视化安全访问体系，为企业的数据安全和合规性提供了有力保障。通过统一的认证授权机制，实现了数据访问的精细化控制，有效防止了数据泄露和非授权访问，提升了整体系统的安全性和可管理性。

---

**文档版本**: V1.0.0
**最后更新**: 2025年1月
**文档状态**: 正式版
