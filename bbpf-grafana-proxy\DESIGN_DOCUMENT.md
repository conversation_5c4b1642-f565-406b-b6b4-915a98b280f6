# BBPF Grafana代理模块设计文档

## 1. 项目概述

### 1.1 项目简介
BBPF Grafana代理服务是一个基于Spring Boot的微服务，作为BBPF系统和Grafana之间的安全代理层。该服务实现了用户认证、权限控制、请求转发、数据权限过滤等核心功能，确保只有授权用户才能访问相应的Grafana资源。

### 1.2 核心价值
- **安全性**: 提供JWT认证和基于BBPF权限系统的访问控制
- **透明性**: 对用户透明的代理服务，无需修改Grafana配置
- **可扩展性**: 支持多种认证方式和权限模型
- **高性能**: 基于Redis的缓存机制和连接池优化
- **可监控**: 完整的监控指标和健康检查机制

### 1.3 技术栈
- **核心框架**: Spring Boot 2.7.18
- **安全框架**: Spring Security + JWT
- **缓存**: Redis (Jedis客户端)
- **HTTP客户端**: Apache HttpClient
- **序列化**: Jackson
- **WebSocket**: Spring WebSocket
- **监控**: Spring Boot Actuator + Micrometer
- **测试**: JUnit 5 + Mockito
- **构建工具**: Maven 3.x
- **运行环境**: Java 8+

## 2. 系统架构

### 2.1 整体架构图
```
┌─────────────────┐    ┌─────────────────────┐    ┌─────────────────┐
│   BBPF前端      │───▶│  BBPF Grafana Proxy │───▶│    Grafana      │
│                 │    │                     │    │                 │
└─────────────────┘    └─────────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │  BBPF权限API    │
                       │                 │
                       └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   Redis缓存     │
                       │                 │
                       └─────────────────┘
```

### 2.2 核心组件架构
```
┌─────────────────────────────────────────────────────────────────┐
│                        BBPF Grafana Proxy                      │
├─────────────────────────────────────────────────────────────────┤
│  Controller Layer                                               │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ GrafanaProxy    │  │ StaticResource  │  │ Health Check    │ │
│  │ Controller      │  │ Controller      │  │ Controller      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  Security Layer                                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ JWT             │  │ Permission      │  │ CORS            │ │
│  │ Authentication  │  │ Filter          │  │ Configuration   │ │
│  │ Filter          │  │                 │  │                 │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  Service Layer                                                  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ GrafanaProxy    │  │ Permission      │  │ GrafanaAuth     │ │
│  │ Service         │  │ Service         │  │ Service         │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  Infrastructure Layer                                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ Redis Cache     │  │ HTTP Client     │  │ Configuration   │ │
│  │ Manager         │  │ Pool            │  │ Management      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 2.3 请求处理流程
```
用户请求 ──▶ JWT认证过滤器 ──▶ 权限验证 ──▶ 请求代理 ──▶ Grafana
    │              │              │           │
    │              ▼              ▼           ▼
    │         用户身份提取    权限缓存查询   请求转发
    │              │              │           │
    │              ▼              ▼           ▼
    └─────── 认证失败返回 ── 权限不足返回 ── 响应处理
```

## 3. 核心功能模块

### 3.1 认证模块

#### 3.1.1 JWT认证过滤器 (JwtAuthenticationFilter)
**功能描述**: 负责提取和验证JWT Token，设置用户上下文信息

**核心特性**:
- JWT Token提取和验证
- 用户身份信息解析
- 安全上下文设置
- 白名单路径支持
- 认证失败处理

**关键方法**:
```java
// JWT Token验证
public boolean validateToken(String token)

// 用户信息提取
public UserDetails extractUserDetails(String token)

// 设置认证上下文
public void setAuthenticationContext(UserDetails userDetails)
```

#### 3.1.2 Grafana认证服务 (GrafanaAuthService)
**功能描述**: 管理与Grafana的认证交互

**核心特性**:
- API Token管理
- Session认证
- 认证信息缓存
- 自动重新认证

### 3.2 权限控制模块

#### 3.2.1 权限服务 (PermissionService)
**功能描述**: 提供完整的权限验证和管理功能

**核心特性**:
- 用户权限获取
- 资源访问权限验证
- 权限缓存管理
- 多级权限检查

**权限类型**:
- **仪表盘权限**: 控制用户对特定仪表盘的访问
- **文件夹权限**: 控制用户对仪表盘文件夹的访问
- **数据源权限**: 控制用户对数据源的访问
- **操作权限**: 控制用户的操作类型(查看/编辑/导出/管理)

**关键方法**:
```java
// 获取用户权限
UserPermissionDto getUserPermissions(String userId)

// 验证仪表盘访问权限
boolean hasAccessToDashboard(String userId, String dashboardId)

// 验证数据源访问权限
boolean hasAccessToDataSource(String userId, String dataSourceId)

// 验证操作权限
boolean hasOperationPermission(String userId, String operation)
```

#### 3.2.2 权限缓存机制
**缓存策略**:
- Redis分布式缓存
- 可配置的过期时间(默认30分钟)
- 缓存预热和更新机制
- 缓存失效处理

### 3.3 代理服务模块

#### 3.3.1 Grafana代理服务 (GrafanaProxyService)
**功能描述**: 核心代理服务，处理所有到Grafana的请求转发

**核心特性**:
- HTTP请求转发
- 请求头修改和适配
- 响应内容处理
- URL重写
- 错误处理和重试

**请求处理流程**:
1. 权限验证
2. 请求头修改
3. URL重写
4. 请求转发
5. 响应处理
6. 内容修改

**关键方法**:
```java
// 代理请求处理
ResponseEntity<String> proxyRequest(HttpServletRequest request, String path)

// 权限验证
boolean hasAccessPermission(String userId, String path, String method)

// 请求头修改
void modifyRequestHeaders(HttpHeaders headers, String userId)

// 响应内容处理
String modifyResponseContent(String content, String contentType)
```

#### 3.3.2 静态资源代理 (StaticResourceController)
**功能描述**: 专门处理Grafana的静态资源请求

**支持的资源类型**:
- CSS样式文件
- JavaScript脚本
- 图片资源
- 字体文件
- 其他静态资源

### 3.4 数据权限过滤模块

#### 3.4.1 SQL拦截器
**功能描述**: 拦截和修改SQL查询，实现数据级权限控制

**核心特性**:
- SQL语句拦截
- 权限条件注入
- 表级权限控制
- 字段级权限过滤

**配置示例**:
```properties
# 需要拦截的API路径
bbpf.sql.interceptor.intercept-patterns[0]=/api/ds/query
bbpf.sql.interceptor.intercept-patterns[1]=/api/datasources/proxy/.*

# 表级权限配置
bbpf.sql.interceptor.table-permission-mapping.t_log.user-fields[0]=user_id
bbpf.sql.interceptor.table-permission-mapping.t_user.user-fields[0]=create_user_id
```

### 3.5 监控和健康检查模块

#### 3.5.1 健康检查服务
**检查项目**:
- 应用程序状态
- Grafana连接状态
- BBPF权限服务状态
- Redis连接状态

#### 3.5.2 统计信息服务
**统计指标**:
- 总请求数
- 成功请求数
- 失败请求数
- 拒绝请求数
- 成功率
- 响应时间

## 4. 配置管理

### 4.1 核心配置类 (GrafanaProxyConfig)
**配置项分类**:

#### 4.1.1 基础服务配置
```properties
# Grafana服务配置
bbpf.grafana.proxy.grafana-base-url=http://localhost:3000
bbpf.grafana.proxy.proxy-base-url=http://localhost:8080

# BBPF权限API配置
bbpf.grafana.proxy.bbpf-permission-api-url=https://api.bbpf.com/permission
bbpf.grafana.proxy.bbpf-api-timeout=5000
```

#### 4.1.2 认证配置
```properties
# JWT配置
bbpf.system.security.jwt.expiration=1
bbpf.system.security.jwt.privateKey=<RSA私钥>
bbpf.system.security.jwt.publicKey=<RSA公钥>

# Grafana认证配置
bbpf.grafana.proxy.grafana-admin-username=admin
bbpf.grafana.proxy.grafana-admin-password=123456
bbpf.grafana.proxy.grafana-api-token=<API Token>
```

#### 4.1.3 缓存配置
```properties
# 权限缓存配置
bbpf.grafana.proxy.enable-permission-cache=true
bbpf.grafana.proxy.permission-cache-expiration-seconds=1800

# Redis配置
spring.redis.host=127.0.0.1
spring.redis.port=6379
spring.redis.timeout=3000ms
```

#### 4.1.4 性能配置
```properties
# 代理性能配置
bbpf.grafana.proxy.proxy-timeout=30000
bbpf.grafana.proxy.max-concurrent-requests=100
bbpf.grafana.proxy.enable-request-logging=true
```

#### 4.1.5 WebSocket配置
```properties
# WebSocket支持
spring.websocket.enabled=true
spring.websocket.session-timeout=300000
bbpf.grafana.proxy.websocket-connection-timeout=60000
```

### 4.2 环境配置
**支持的环境**:
- `local`: 本地开发环境
- `dev`: 开发环境
- `test`: 测试环境
- `prod`: 生产环境

## 5. 安全设计

### 5.1 认证机制

#### 5.1.1 JWT认证流程
```
1. 用户请求携带JWT Token
2. 提取Authorization头中的Token
3. 验证Token签名和有效期
4. 解析用户身份信息
5. 设置Spring Security上下文
6. 继续请求处理
```

#### 5.1.2 Token格式
```
Authorization: Bearer <JWT_TOKEN>
```

### 5.2 权限验证机制

#### 5.2.1 多级权限检查
```
1. 用户身份验证
2. 资源访问权限检查
3. 操作权限验证
4. 数据权限过滤
```

#### 5.2.2 权限模型
```json
{
  "userId": "user123",
  "username": "john.doe",
  "accessibleDashboardIds": ["dashboard-1", "dashboard-2"],
  "accessibleFolderIds": ["folder-1", "folder-2"],
  "accessibleDataSourceIds": ["datasource-1", "datasource-2"],
  "permissions": ["view", "edit", "export"],
  "dataFilter": "department='IT'",
  "expirationTime": 1703123456789
}
```

### 5.3 安全防护措施

#### 5.3.1 CORS配置
```properties
# 允许的源
bbpf.grafana.proxy.allowed-origins=http://localhost:3000,https://grafana.company.com
```

#### 5.3.2 请求头安全
- 自动添加安全响应头
- 过滤敏感请求头
- 防止XSS攻击

#### 5.3.3 数据权限过滤
- SQL注入防护
- 数据行级权限控制
- 敏感字段过滤

## 6. 性能优化

### 6.1 缓存策略

#### 6.1.1 权限缓存
- **缓存键**: `permission:user:{userId}`
- **过期时间**: 30分钟(可配置)
- **更新策略**: 被动更新 + 定时刷新

#### 6.1.2 认证缓存
- **缓存键**: `auth:grafana:session`
- **过期时间**: 24小时
- **更新策略**: 自动续期

### 6.2 连接池优化

#### 6.2.1 HTTP连接池
```java
// 连接池配置
PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
connectionManager.setMaxTotal(200);  // 最大连接数
connectionManager.setDefaultMaxPerRoute(50);  // 每个路由最大连接数
```

#### 6.2.2 Redis连接池
```properties
# Jedis连接池配置
spring.redis.jedis.pool.max-active=200
spring.redis.jedis.pool.max-idle=10
spring.redis.jedis.pool.min-idle=0
spring.redis.jedis.pool.max-wait=-1
```

### 6.3 异步处理
- 非阻塞请求处理
- 异步权限验证
- 后台缓存预热

## 7. 监控和运维

### 7.1 监控指标

#### 7.1.1 业务指标
- 请求总数
- 成功率
- 响应时间
- 权限拒绝率
- 缓存命中率

#### 7.1.2 技术指标
- JVM内存使用
- GC频率和时间
- 线程池状态
- 连接池状态
- Redis连接状态

### 7.2 健康检查

#### 7.2.1 检查端点
```
GET /actuator/health
```

#### 7.2.2 检查项目
- 应用程序状态
- Grafana连接
- BBPF权限服务连接
- Redis连接
- 磁盘空间
- 内存使用

### 7.3 日志管理

#### 7.3.1 日志级别
- `ERROR`: 系统错误和异常
- `WARN`: 警告信息
- `INFO`: 重要业务信息
- `DEBUG`: 调试信息

#### 7.3.2 关键日志
```
# 认证成功
Authentication successful for user: {username} (userId: {userId})

# 权限验证失败
Access denied for user {userId} to resource {resourceId}

# 代理请求
Proxying request: {method} {path} for user: {userId}

# 健康检查
Grafana health check result: {status}, response time: {time}ms
```

## 8. 部署架构

### 8.1 单机部署
```
┌─────────────────┐
│   Load Balancer │
└─────────────────┘
         │
         ▼
┌─────────────────┐
│ BBPF Grafana    │
│ Proxy Instance  │
└─────────────────┘
         │
    ┌────┴────┐
    ▼         ▼
┌───────┐ ┌───────┐
│Grafana│ │ Redis │
└───────┘ └───────┘
```

### 8.2 集群部署
```
┌─────────────────┐
│   Load Balancer │
└─────────────────┘
         │
    ┌────┼────┐
    ▼    ▼    ▼
┌───────┐┌───────┐┌───────┐
│Proxy-1││Proxy-2││Proxy-3│
└───────┘└───────┘└───────┘
    │        │        │
    └────────┼────────┘
             ▼
    ┌─────────────────┐
    │ Redis Cluster   │
    └─────────────────┘
             │
             ▼
    ┌─────────────────┐
    │ Grafana Cluster │
    └─────────────────┘
```

### 8.3 Docker部署

#### 8.3.1 Dockerfile
```dockerfile
FROM openjdk:8-jre-alpine
VOLUME /tmp
COPY target/bbpf-grafana-proxy-*.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java","-jar","/app.jar"]
```

#### 8.3.2 Docker Compose
```yaml
version: '3.8'
services:
  bbpf-grafana-proxy:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - REDIS_HOST=redis
      - GRAFANA_BASE_URL=http://grafana:3000
    depends_on:
      - redis
      - grafana
  
  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
  
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
```

## 9. 故障处理

### 9.1 常见问题

#### 9.1.1 认证问题
**问题**: JWT认证失败
**排查步骤**:
1. 检查JWT密钥配置
2. 验证Token格式和有效期
3. 查看认证过滤器日志
4. 检查时钟同步

**解决方案**:
- 更新JWT密钥配置
- 重新生成Token
- 同步服务器时钟

#### 9.1.2 权限问题
**问题**: 权限验证失败
**排查步骤**:
1. 检查BBPF权限API连接
2. 验证用户权限配置
3. 查看权限服务日志
4. 检查缓存状态

**解决方案**:
- 修复权限API连接
- 更新用户权限配置
- 清理权限缓存

#### 9.1.3 连接问题
**问题**: Grafana连接失败
**排查步骤**:
1. 检查Grafana服务状态
2. 验证网络连接
3. 查看代理服务日志
4. 检查防火墙设置

**解决方案**:
- 重启Grafana服务
- 修复网络配置
- 调整防火墙规则

### 9.2 性能问题

#### 9.2.1 响应时间慢
**排查步骤**:
1. 检查系统资源使用
2. 分析慢查询日志
3. 检查缓存命中率
4. 监控网络延迟

**优化方案**:
- 增加缓存时间
- 优化数据库查询
- 扩展服务实例
- 使用CDN加速

#### 9.2.2 内存泄漏
**排查步骤**:
1. 监控JVM内存使用
2. 分析GC日志
3. 使用内存分析工具
4. 检查连接池配置

**解决方案**:
- 调整JVM参数
- 优化连接池配置
- 修复内存泄漏代码
- 定期重启服务

## 10. 扩展和定制

### 10.1 自定义认证方式

#### 10.1.1 实现自定义认证过滤器
```java
@Component
public class CustomAuthenticationFilter implements Filter {
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) {
        // 自定义认证逻辑
    }
}
```

#### 10.1.2 配置Security
```java
@Configuration
@EnableWebSecurity
public class CustomSecurityConfig extends WebSecurityConfigurerAdapter {
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.addFilterBefore(customAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);
    }
}
```

### 10.2 自定义权限模型

#### 10.2.1 扩展权限DTO
```java
public class ExtendedUserPermissionDto extends UserPermissionDto {
    private List<String> customPermissions;
    private Map<String, Object> additionalAttributes;
    // getter/setter
}
```

#### 10.2.2 实现自定义权限服务
```java
@Service
public class CustomPermissionService implements PermissionService {
    @Override
    public boolean hasCustomPermission(String userId, String resource, String action) {
        // 自定义权限验证逻辑
        return true;
    }
}
```

### 10.3 添加监控指标

#### 10.3.1 自定义指标
```java
@Component
public class CustomMetrics {
    private final Counter requestCounter;
    private final Timer responseTimer;
    
    public CustomMetrics(MeterRegistry meterRegistry) {
        this.requestCounter = Counter.builder("custom.requests.total")
            .description("Total number of custom requests")
            .register(meterRegistry);
        
        this.responseTimer = Timer.builder("custom.response.time")
            .description("Custom response time")
            .register(meterRegistry);
    }
}
```

## 11. 最佳实践

### 11.1 安全最佳实践

1. **密钥管理**
   - 使用环境变量存储敏感信息
   - 定期轮换JWT密钥
   - 使用强密码策略

2. **权限控制**
   - 实施最小权限原则
   - 定期审计用户权限
   - 记录所有权限变更

3. **网络安全**
   - 使用HTTPS传输
   - 配置防火墙规则
   - 启用网络监控

### 11.2 性能最佳实践

1. **缓存策略**
   - 合理设置缓存过期时间
   - 监控缓存命中率
   - 实施缓存预热

2. **连接管理**
   - 优化连接池配置
   - 监控连接使用情况
   - 实施连接超时控制

3. **资源优化**
   - 定期监控系统资源
   - 优化JVM参数
   - 实施负载均衡

### 11.3 运维最佳实践

1. **监控告警**
   - 设置关键指标告警
   - 建立故障响应流程
   - 定期检查系统健康

2. **日志管理**
   - 结构化日志输出
   - 集中化日志收集
   - 定期清理历史日志

3. **备份恢复**
   - 定期备份配置文件
   - 测试恢复流程
   - 建立灾难恢复计划

## 12. 总结

BBPF Grafana代理模块是一个功能完整、架构清晰的企业级代理服务。通过分层架构设计，实现了认证、授权、代理、监控等核心功能，为BBPF系统和Grafana之间提供了安全、高效的桥梁。

### 12.1 核心优势

1. **安全性**: 完整的JWT认证和权限控制体系
2. **性能**: 基于Redis的缓存机制和连接池优化
3. **可扩展性**: 模块化设计，支持自定义扩展
4. **可维护性**: 清晰的代码结构和完善的文档
5. **可监控性**: 全面的监控指标和健康检查

### 12.2 技术亮点

1. **透明代理**: 对用户完全透明的代理服务
2. **数据权限**: 细粒度的数据级权限控制
3. **缓存优化**: 多层次的缓存策略
4. **WebSocket支持**: 完整的WebSocket代理功能
5. **SQL拦截**: 智能的SQL权限过滤机制

### 12.3 应用价值

该代理模块不仅解决了Grafana与BBPF系统集成的技术问题，更重要的是建立了一套完整的企业级数据可视化安全访问体系，为企业的数据安全和合规性提供了有力保障。