package com.snbc.bbpf.grafana.proxy.service;

import com.snbc.bbpf.grafana.proxy.config.GrafanaProxyConfig;
import com.snbc.bbpf.grafana.proxy.interceptor.DashboardResponseInterceptor;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.*;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.InputStreamEntity;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Grafana代理服务实现类
 * 
 * 实现Grafana代理服务接口，提供具体的代理功能：
 * 1. HTTP请求转发到Grafana
 * 2. 权限验证和访问控制
 * 3. 请求/响应内容修改
 * 4. 统计信息收集
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2024-12-13
 */
@Service
public class GrafanaProxyServiceImpl implements GrafanaProxyService {

	private static final Logger logger = LoggerFactory.getLogger(GrafanaProxyServiceImpl.class);

	private static final String GRAFANA_HEALTH_CACHE_KEY = "bbpf:grafana:health";
	private static final String PROXY_STATS_KEY = "bbpf:grafana:proxy:stats";

	// 需要权限验证的路径模式
	private static final Pattern DASHBOARD_PATTERN = Pattern.compile("/d/([^/]+)");
	private static final Pattern API_DASHBOARD_PATTERN = Pattern.compile("/api/dashboards/uid/([^/]+)");
	private static final Pattern API_DATASOURCE_PATTERN = Pattern.compile("/api/datasources/([^/]+)");
	private static final Pattern EXPORT_PATTERN = Pattern.compile("/api/dashboard-snapshots|/render");

	// 统计计数器
	private final AtomicLong totalRequests = new AtomicLong(0);
	private final AtomicLong successfulRequests = new AtomicLong(0);
	private final AtomicLong failedRequests = new AtomicLong(0);
	private final AtomicLong deniedRequests = new AtomicLong(0);

	@Autowired
	private GrafanaProxyConfig config;

	@Autowired
	private PermissionService permissionService;

	@Autowired
	private RedisTemplate<String, Object> redisTemplate;

	@Autowired
	private GrafanaAuthService grafanaAuthService;

	@Autowired
	private SqlInterceptorService sqlInterceptorService;

	@Autowired
	private DashboardResponseInterceptor dashboardResponseInterceptor;

	private final CloseableHttpClient httpClient = HttpClients.createDefault();

	@Override
	public boolean proxyRequest(HttpServletRequest request, HttpServletResponse response,
			String targetPath, String userId) {
		totalRequests.incrementAndGet();

		// 设置当前API路径到线程本地存储，供SQL拦截服务使用
		SqlInterceptorService.setCurrentApiPath(targetPath);
		
		try {
			// 验证权限
			/*
			 * if (!hasAccessPermission(userId, targetPath, request.getMethod())) {
			 * deniedRequests.incrementAndGet();
			 * response.setStatus(HttpServletResponse.SC_FORBIDDEN);
			 * response.getWriter().write("{\"error\":\"Access denied\"}");
			 * return false;
			 * }
			 */
			// 构建目标URL
			String targetUrl = buildTargetUrl(targetPath, request.getQueryString());

			// 创建代理请求
			HttpRequestBase proxyRequest = createProxyRequest(request, targetUrl, userId);

			// 执行请求
			try (CloseableHttpResponse grafanaResponse = httpClient.execute(proxyRequest)) {
				// 复制响应状态
				response.setStatus(grafanaResponse.getStatusLine().getStatusCode());

				// 复制响应头
				copyResponseHeaders(grafanaResponse, response);

				// 复制响应体
				HttpEntity entity = grafanaResponse.getEntity();
				if (entity != null) {
					String contentType = entity.getContentType() != null ? entity.getContentType().getValue()
							: "application/octet-stream";

					if (contentType.contains("text/") || contentType.contains("application/json")) {
						// 文本内容，可能需要修改 - 指定UTF-8编码避免中文乱码
						String content = EntityUtils.toString(entity, "UTF-8");
						String modifiedContent = modifyResponseContent(content, userId, targetPath);
						// 设置响应编码为UTF-8
						response.setCharacterEncoding("UTF-8");
						response.getWriter().write(modifiedContent);
					} else {
						// 二进制内容，直接复制
						try (java.io.InputStream inputStream = entity.getContent();
								java.io.OutputStream outputStream = response.getOutputStream()) {
							byte[] buffer = new byte[8192];
							int bytesRead;
							while ((bytesRead = inputStream.read(buffer)) != -1) {
								outputStream.write(buffer, 0, bytesRead);
							}
						}
					}
				}

				successfulRequests.incrementAndGet();
				return true;
			}
		} catch (Exception e) {
			logger.error("Error proxying request to Grafana: {}", e.getMessage(), e);
			failedRequests.incrementAndGet();

			try {
				response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
				response.getWriter().write("{\"error\":\"Proxy error\"}");
			} catch (IOException ioException) {
				logger.error("Error writing error response: {}", ioException.getMessage());
			}
			return false;
		} finally {
			// 清理线程本地存储
			SqlInterceptorService.clearCurrentApiPath();
		}
	}

	@Override
	public boolean hasAccessPermission(String userId, String requestPath, String httpMethod) {
		// 检查仪表盘访问权限
		Matcher dashboardMatcher = DASHBOARD_PATTERN.matcher(requestPath);
		if (dashboardMatcher.find()) {
			String dashboardId = dashboardMatcher.group(1);
			return permissionService.canAccessDashboard(userId, dashboardId);
		}

		// 检查API仪表盘访问权限
		Matcher apiDashboardMatcher = API_DASHBOARD_PATTERN.matcher(requestPath);
		if (apiDashboardMatcher.find()) {
			String dashboardId = apiDashboardMatcher.group(1);
			return permissionService.canAccessDashboard(userId, dashboardId);
		}

		// 检查数据源访问权限
		Matcher datasourceMatcher = API_DATASOURCE_PATTERN.matcher(requestPath);
		if (datasourceMatcher.find()) {
			String datasourceId = datasourceMatcher.group(1);
			return permissionService.canAccessDataSource(userId, datasourceId);
		}

		// 检查导出权限
		if (EXPORT_PATTERN.matcher(requestPath).find()) {
			return permissionService.hasPermission(userId, "export");
		}

		// 检查写操作权限
		if ("POST".equals(httpMethod) || "PUT".equals(httpMethod) || "DELETE".equals(httpMethod)) {
			return permissionService.hasPermission(userId, "edit");
		}

		// 默认允许读操作
		return permissionService.hasPermission(userId, "view");
	}

	@Override
	public String buildTargetUrl(String targetPath, String queryString) {
    StringBuilder url = new StringBuilder(config.getGrafanaBaseUrl());
    
    // 特殊处理静态资源路径
    if (targetPath.startsWith("/public/")) {
        // 确保静态资源路径正确
        url.append(targetPath);
    } else {
        if (!targetPath.startsWith("/")) {
            url.append("/");
        }
        url.append(targetPath);
    }
    
    if (queryString != null && !queryString.isEmpty()) {
        url.append("?").append(queryString);
    }
    
    return url.toString();
}

	@Override
	public boolean handleDashboardAccess(HttpServletRequest request, HttpServletResponse response,
			String dashboardId, String userId) {
		/*if (!permissionService.canAccessDashboard(userId, dashboardId)) {
			try {
				response.setStatus(HttpServletResponse.SC_FORBIDDEN);
				response.getWriter().write("{\"error\":\"Dashboard access denied\"}");
			} catch (IOException e) {
				logger.error("Error writing dashboard access denied response: {}", e.getMessage());
			}
			return false;
		}*/
		
		return proxyRequest(request, response, request.getRequestURI(), userId);
	}

	@Override
	public boolean handleApiRequest(HttpServletRequest request, HttpServletResponse response,
			String apiPath, String userId) {
		return proxyRequest(request, response, apiPath, userId);
	}

	@Override
	public boolean handleDataExport(HttpServletRequest request, HttpServletResponse response,
			String resourceId, String userId) {
		
		return proxyRequest(request, response, request.getRequestURI(), userId);
	}

	@Override
	public Map<String, String> modifyRequestHeaders(HttpServletRequest request, String userId, String username) {
		Map<String, String> headers = new HashMap<>();

		// 复制原始请求头（排除某些头）
		Enumeration<String> headerNames = request.getHeaderNames();
		while (headerNames.hasMoreElements()) {
			String headerName = headerNames.nextElement();
			if (!shouldSkipHeader(headerName)) {
				headers.put(headerName, request.getHeader(headerName));
			}
		}

		// 添加Grafana认证头
		String authToken = grafanaAuthService.getAuthToken(userId);
		if (config.isEnableVerboseLogging()) {
			logger.info("Generated auth token for user {}: {}", userId, authToken != null ? authToken.substring(0, Math.min(20, authToken.length())) + "..." : "null");
		}
		if (authToken != null) {
			headers.put("Authorization", authToken);
			if (config.isEnableVerboseLogging()) {
				logger.info("Added Grafana authentication header for user: {}, header value: {}", userId, authToken.substring(0, Math.min(20, authToken.length())) + "...");
			}
		} else {
			logger.warn("No valid Grafana authentication token available for user: {}", userId);
			// 尝试使用Basic Auth作为后备
			String basicAuth = grafanaAuthService.getBasicAuthHeader();
			if (basicAuth != null) {
				headers.put("Authorization", basicAuth);
				if (config.isEnableVerboseLogging()) {
					logger.info("Using Basic Auth as fallback for user: {}, header value: {}", userId, basicAuth.substring(0, Math.min(20, basicAuth.length())) + "...");
				}
			} else {
				logger.error("No authentication method available for user: {}", userId);
			}
		}

		// 如果启用Auth Proxy模式，添加Auth Proxy头
		if (config.isEnableAuthProxy()) {
			headers.put(config.getAuthProxyHeaderName(), username);
			headers.put("X-WEBAUTH-NAME", username);
			headers.put("X-WEBAUTH-EMAIL", username + "@bbpf.com");
			if (config.isEnableVerboseLogging()) {
				logger.info("Added Auth Proxy headers for user: {}", username);
			}
		}

		// 添加自定义头
		headers.put("X-BBPF-USER-ID", userId);
		headers.put("X-BBPF-PROXY", "true");

		// 添加更多浏览器兼容性请求头
        headers.put("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8");
        headers.put("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
        headers.put("Accept-Encoding", "gzip, deflate, br");
        headers.put("Cache-Control", "no-cache");
        headers.put("Pragma", "no-cache");
        
        // 修正 Origin 和 Referer 头，使用实际的代理域名
        headers.put("Origin", config.getProxyBaseUrl());
        headers.put("Referer", config.getProxyBaseUrl());

        // 添加Chrome浏览器User-Agent以确保Grafana兼容性
        headers.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
        
        // 添加必要的代理头信息
        headers.put("X-Forwarded-Proto", "http");
        headers.put("X-Forwarded-Host", "g.xinbeiyang.info");

		return headers;
	}

	@Override
	public String modifyResponseContent(String originalContent, String userId, String requestPath) {
		if (originalContent == null || originalContent.isEmpty()) {
			return originalContent;
		}

		String modifiedContent = originalContent;

		// 替换Grafana内部URL为代理URL
        String grafanaBaseUrl = config.getGrafanaBaseUrl();
        String proxyBaseUrl = config.getProxyBaseUrl();

        if (grafanaBaseUrl != null && proxyBaseUrl != null) {
            // 替换各种可能的URL格式
            modifiedContent = modifiedContent.replace(grafanaBaseUrl, proxyBaseUrl);
            modifiedContent = modifiedContent.replace("http://localhost:3000", proxyBaseUrl);
            modifiedContent = modifiedContent.replace("//localhost:3000", "//g.xinbeiyang.info");
            
            // 处理相对路径的静态资源
            modifiedContent = modifiedContent.replace("\"/public/", "\"" + proxyBaseUrl + "/public/");
            modifiedContent = modifiedContent.replace("\"/static/", "\"" + proxyBaseUrl + "/static/");
            modifiedContent = modifiedContent.replace("\"/assets/", "\"" + proxyBaseUrl + "/assets/");
            modifiedContent = modifiedContent.replace("\"/app/", "\"" + proxyBaseUrl + "/app/");
            
            // 处理API路径
            modifiedContent = modifiedContent.replace("\"/api/", "\"" + proxyBaseUrl + "/api/");
            
            // 处理根路径引用
            modifiedContent = modifiedContent.replaceAll("\"(/[^/][^\"]*)\""  , "\"" + proxyBaseUrl + "$1\"");
        }

		// 动态注入模板变量（核心功能）
		// 使用响应拦截器处理仪表盘JSON，注入用户权限相关的模板变量
		if (userId != null && requestPath != null) {
			try {
				modifiedContent = dashboardResponseInterceptor.processResponse(modifiedContent, userId, requestPath);
				logger.debug("[代理服务] 已处理动态模板变量注入: {} for user: {}", requestPath, userId);
			} catch (Exception e) {
				logger.error("[代理服务] 动态模板变量注入失败: {}", e.getMessage(), e);
				// 注入失败时继续使用原始内容，不影响正常功能
			}
		}

		// 添加权限相关的JavaScript代码（如果是HTML页面）
		if (modifiedContent.contains("<html") && modifiedContent.contains("</html>")) {
			String permissionScript = generatePermissionScript(userId);
			modifiedContent = modifiedContent.replace("</head>", permissionScript + "</head>");
		}

		return modifiedContent;
	}

	@Override
	public boolean isGrafanaAvailable() {
		// 检查缓存中的健康状态
		Boolean cachedHealth = (Boolean) redisTemplate.opsForValue().get(GRAFANA_HEALTH_CACHE_KEY);
		if (cachedHealth != null) {
			return cachedHealth;
		}

		// 实际检查Grafana健康状态
		boolean isAvailable = checkGrafanaHealth();

		// 缓存健康状态（短时间缓存）
		redisTemplate.opsForValue().set(GRAFANA_HEALTH_CACHE_KEY, isAvailable, 30, TimeUnit.SECONDS);

		return isAvailable;
	}

	@Override
	public Map<String, Object> getProxyStatistics() {
		Map<String, Object> stats = new HashMap<>();
		stats.put("totalRequests", totalRequests.get());
		stats.put("successfulRequests", successfulRequests.get());
		stats.put("failedRequests", failedRequests.get());
		stats.put("deniedRequests", deniedRequests.get());
		stats.put("successRate", calculateSuccessRate());
		stats.put("grafanaAvailable", isGrafanaAvailable());
		stats.put("permissionServiceAvailable", permissionService.isServiceAvailable());

		return stats;
	}

	/**
	 * 创建代理请求
	 */
	private HttpRequestBase createProxyRequest(HttpServletRequest request, String targetUrl, String userId)
			throws IOException {
		String method = request.getMethod();
		HttpRequestBase proxyRequest;

		switch (method.toUpperCase()) {
			case "GET":
				proxyRequest = new HttpGet(targetUrl);
				break;
			case "POST":
				HttpPost post = new HttpPost(targetUrl);
				String requestBody = readRequestBody(request);
				if (requestBody != null && !requestBody.isEmpty()) {
					// 使用SQL拦截服务处理请求体
					String interceptedBody = sqlInterceptorService.interceptRequest(requestBody, userId);
					StringEntity entity = new StringEntity(interceptedBody, ContentType.APPLICATION_JSON);
					post.setEntity(entity);
				}
				proxyRequest = post;
				break;
			case "PUT":
				HttpPut put = new HttpPut(targetUrl);
				if (request.getContentLength() > 0) {
					put.setEntity(new InputStreamEntity(request.getInputStream()));
				}
				proxyRequest = put;
				break;
			case "DELETE":
				proxyRequest = new HttpDelete(targetUrl);
				break;
			default:
				throw new UnsupportedOperationException("HTTP method not supported: " + method);
		}

		// 设置请求头
		Map<String, String> headers = modifyRequestHeaders(request, userId, getUsernameFromUserId(userId));
		for (Map.Entry<String, String> header : headers.entrySet()) {
			proxyRequest.setHeader(header.getKey(), header.getValue());
		}

		return proxyRequest;
	}

	/**
	 * 复制响应头
	 */
	private void copyResponseHeaders(CloseableHttpResponse grafanaResponse, HttpServletResponse response) {
		for (Header header : grafanaResponse.getAllHeaders()) {
			String headerName = header.getName();
			if (!shouldSkipResponseHeader(headerName)) {
				response.setHeader(headerName, header.getValue());
			}
		}
		// 确保设置正确的编码类型
		if (response.getContentType() != null && 
			(response.getContentType().contains("text/") || response.getContentType().contains("application/json"))) {
			response.setCharacterEncoding("UTF-8");
		}
	}

	/**
	 * 检查是否应该跳过请求头
	 */
	private boolean shouldSkipHeader(String headerName) {
		String lowerName = headerName.toLowerCase();
		return lowerName.equals("host") ||
				lowerName.equals("content-length") ||
				lowerName.equals("authorization") ||
				lowerName.startsWith("x-webauth-");
	}

	/**
	 * 检查是否应该跳过响应头
	 */
	private boolean shouldSkipResponseHeader(String headerName) {
        String lowerName = headerName.toLowerCase();
        // 保留更多响应头以确保静态资源正确加载
        // 只跳过可能导致问题的头信息
        return lowerName.equals("transfer-encoding") || 
               lowerName.equals("connection") ||
               lowerName.equals("server");
    }

	/**
	 * 检查Grafana健康状态
	 */
	private boolean checkGrafanaHealth() {
		String healthUrl = config.getGrafanaBaseUrl() + "/api/health";

		HttpGet request = new HttpGet(healthUrl);
		// 添加Chrome浏览器User-Agent
		request.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
		try (CloseableHttpResponse response = httpClient.execute(request)) {
			return response.getStatusLine().getStatusCode() == 200;
		} catch (Exception e) {
			logger.warn("Grafana health check failed: {}", e.getMessage());
			return false;
		}
	}

	/**
	 * 生成权限相关的JavaScript代码
	 * 包含禁用分析功能的脚本
	 */
	private String generatePermissionScript(String userId) {
		StringBuilder script = new StringBuilder();
		script.append("<script>\n")
				.append("window.bbpfUserId = '").append(userId).append("';\n")
				.append("window.bbpfProxyEnabled = true;\n");
		
		// 只有在配置启用时才添加禁用分析功能的代码
		if (config.isDisableAnalysisFeatures()) {
			script.append("\n")
					.append("// 添加CSS样式来隐藏分析相关的按钮和链接\n")
					.append("var style = document.createElement('style');\n")
					.append("style.textContent = `\n")
					.append("  /* 隐藏Explore按钮 */\n")
					.append("  [data-testid=\"nav-item-explore\"],\n")
					.append("  a[href*=\"/explore\"],\n")
					.append("  [aria-label*=\"Explore\"],\n")
					.append("  .navbar-nav a[href*=\"explore\"] {\n")
					.append("    display: none !important;\n")
					.append("  }\n")
					.append("  \n")
					.append("  /* 隐藏Inspect按钮和菜单 */\n")
					.append("  [data-testid*=\"inspect\"],\n")
					.append("  [aria-label*=\"Inspect\"],\n")
					.append("  .dropdown-item[href*=\"inspect\"],\n")
					.append("  .panel-menu-item[data-testid*=\"inspect\"] {\n")
					.append("    display: none !important;\n")
					.append("  }\n")
					.append("  \n")
					.append("  /* 隐藏面板菜单中的分析选项 */\n")
					.append("  .panel-menu .dropdown-menu .dropdown-item:has([data-testid*=\"inspect\"]),\n")
					.append("  .panel-menu .dropdown-menu .dropdown-item:has([aria-label*=\"Inspect\"]) {\n")
					.append("    display: none !important;\n")
					.append("  }\n")
					.append("`;\n")
					.append("document.head.appendChild(style);\n")
					.append("\n")
					.append("// 拦截点击事件，阻止访问分析功能\n")
					.append("document.addEventListener('click', function(e) {\n")
					.append("  var target = e.target;\n")
					.append("  var href = target.href || target.closest('a')?.href || '';\n")
					.append("  var testId = target.getAttribute('data-testid') || '';\n")
					.append("  var ariaLabel = target.getAttribute('aria-label') || '';\n")
					.append("  \n")
					.append("  if (href.includes('/explore') || \n")
					.append("      href.includes('inspect') ||\n")
					.append("      testId.includes('explore') ||\n")
					.append("      testId.includes('inspect') ||\n")
					.append("      ariaLabel.includes('Explore') ||\n")
					.append("      ariaLabel.includes('Inspect')) {\n")
					.append("    e.preventDefault();\n")
					.append("    e.stopPropagation();\n")
					.append("    console.log('分析功能已被禁用');\n")
					.append("    return false;\n")
					.append("  }\n")
					.append("}, true);\n")
					.append("\n")
					.append("// 监听DOM变化，动态隐藏新添加的分析元素\n")
					.append("var observer = new MutationObserver(function(mutations) {\n")
					.append("  mutations.forEach(function(mutation) {\n")
					.append("    mutation.addedNodes.forEach(function(node) {\n")
					.append("      if (node.nodeType === 1) {\n")
					.append("        // 检查新添加的元素是否包含分析相关内容\n")
					.append("        var elements = node.querySelectorAll ? \n")
					.append("          node.querySelectorAll('[data-testid*=\"explore\"], [data-testid*=\"inspect\"], [aria-label*=\"Explore\"], [aria-label*=\"Inspect\"], a[href*=\"/explore\"], a[href*=\"inspect\"]') : [];\n")
					.append("        elements.forEach(function(el) {\n")
					.append("          el.style.display = 'none';\n")
					.append("        });\n")
					.append("      }\n")
					.append("    });\n")
					.append("  });\n")
					.append("});\n")
					.append("\n")
					.append("observer.observe(document.body, {\n")
					.append("  childList: true,\n")
					.append("  subtree: true\n")
					.append("});\n");
		}
		
		script.append("</script>");
		return script.toString();
	}

	/**
	 * 从用户ID获取用户名（简化实现）
	 */
	private String getUsernameFromUserId(String userId) {
		// 这里可以调用用户服务获取真实用户名
		// 暂时使用用户ID作为用户名
		return userId;
	}

	/**
	 * 计算成功率
	 */
	private double calculateSuccessRate() {
		long total = totalRequests.get();
		if (total == 0) {
			return 0.0;
		}
		return (double) successfulRequests.get() / total * 100;
	}

	@Override
	public byte[] getPanelAsPng(String userId, String dashboardUid, int panelId, long from, long to, int width, int height, HttpServletRequest request) {
		// 权限检查：用户是否有权访问此仪表盘
		if (!permissionService.canAccessDashboard(userId, dashboardUid)) {
			logger.warn("User {} denied access to export panel {} from dashboard {}", userId, panelId, dashboardUid);
			return null;
		}

		// 构建指向 Grafana render API 的 URL
		StringBuilder renderUrl = new StringBuilder(config.getGrafanaBaseUrl());
		renderUrl.append("/render/d-solo/").append(dashboardUid);
		renderUrl.append("?orgId=1"); // 通常 orgId 是 1
		renderUrl.append("&panelId=").append(panelId);
		renderUrl.append("&from=").append(from);
		renderUrl.append("&to=").append(to);
		renderUrl.append("&width=").append(width);
		renderUrl.append("&height=").append(height);
		renderUrl.append("&tz=Asia/Shanghai"); // 可根据需要设置时区

		// 附加所有其他的查询参数，特别是模板变量 (var-VARNAME=value)
		request.getParameterMap().forEach((key, values) -> {
			if (key.startsWith("var-")) {
				renderUrl.append("&").append(key).append("=").append(values[0]);
			}
		});

		String finalUrl = renderUrl.toString();
		logger.info("Requesting panel render from URL: {}", finalUrl);

		// 创建请求
		HttpGet renderRequest = new HttpGet(finalUrl);

		// 添加Chrome浏览器User-Agent
		renderRequest.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");

		// 添加认证头
		String authToken = grafanaAuthService.getAuthToken(userId);
		if (authToken != null) {
			renderRequest.setHeader("Authorization", authToken);
		} else {
			logger.warn("No Grafana auth token available for user {}, proceeding without it.", userId);
		}

		// 执行请求
		try (CloseableHttpResponse response = httpClient.execute(renderRequest)) {
			int statusCode = response.getStatusLine().getStatusCode();
			if (statusCode == 200) {
				HttpEntity entity = response.getEntity();
				return EntityUtils.toByteArray(entity);
			} else {
				logger.error("Grafana render API returned status code: {} for URL: {}", statusCode, finalUrl);
				return null;
			}
		} catch (IOException e) {
			logger.error("Failed to call Grafana render API: {}", e.getMessage(), e);
			return null;
		}
	}

	private String readRequestBody(HttpServletRequest request) throws IOException {
	    StringBuilder buffer = new StringBuilder();
	    try (BufferedReader reader = request.getReader()) {
	        String line;
	        while ((line = reader.readLine()) != null) {
	            buffer.append(line);
	        }
	    }
	    return buffer.toString();
	}

}