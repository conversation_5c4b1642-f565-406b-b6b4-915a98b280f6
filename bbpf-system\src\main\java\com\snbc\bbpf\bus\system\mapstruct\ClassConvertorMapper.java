package com.snbc.bbpf.bus.system.mapstruct;


import com.snbc.bbpf.system.db.common.dto.ExportFileDto;
import com.snbc.bbpf.system.db.common.dto.NavigationDto;
import com.snbc.bbpf.system.db.common.dto.UserDto;
import com.snbc.bbpf.system.db.common.dto.UserProfileDto;
import com.snbc.bbpf.system.db.common.dto.WeakPasswordDto;
import com.snbc.bbpf.system.db.common.entity.ExportFile;
import com.snbc.bbpf.system.db.common.entity.Log;
import com.snbc.bbpf.system.db.common.entity.Navigation;
import com.snbc.bbpf.system.db.common.entity.Org;
import com.snbc.bbpf.system.db.common.entity.Param;
import com.snbc.bbpf.system.db.common.entity.Permission;
import com.snbc.bbpf.system.db.common.entity.PermissionNode;
import com.snbc.bbpf.system.db.common.entity.Role;
import com.snbc.bbpf.system.db.common.entity.User;
import com.snbc.bbpf.system.db.common.entity.UserProfile;
import com.snbc.bbpf.system.db.common.entity.WeakPassword;
import com.snbc.bbpf.system.db.common.entity.login.ReturnUser;
import com.snbc.bbpf.system.db.common.vo.BusLogVo;
import com.snbc.bbpf.system.db.common.vo.ExportFileVo;
import com.snbc.bbpf.system.db.common.vo.NavigationVo;
import com.snbc.bbpf.system.db.common.vo.OrgVo;
import com.snbc.bbpf.system.db.common.vo.PermissionStatusVo;
import com.snbc.bbpf.system.db.common.vo.PermissionVo;
import com.snbc.bbpf.system.db.common.vo.RoleDetail;
import com.snbc.bbpf.system.db.common.vo.UserProfileVo;
import com.snbc.bbpf.system.db.common.vo.UserVo;
import com.snbc.bbpf.system.db.common.vo.WeakPasswordVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * @ClassName: ClassConvertorMapper
 * @Description: 类之间转换
 * @module: SI-tcums-manage-service
 * @Author: wangsong
 * @date: 2023/7/21
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Mapper(componentModel = "spring",unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ClassConvertorMapper {
    /**
     * 实例
     */
    ClassConvertorMapper INSTANCE = Mappers.getMapper(ClassConvertorMapper.class);

    default String mapLocalDateTimeToString(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return dtf.format(localDateTime);
    }

    /**
     * 导出文件转换
     * @param userProfile
     * @return
     */
    UserProfile covertUserProfile(UserProfileDto userProfile);

    UserProfileVo covertUserProfileVo(UserProfile userProfile);
    List<UserProfileVo> covertUserProfileVoList(List<UserProfile> userProfileList);

    UserVo covertUserVo(User user);
    User covertUser(UserDto user);

    WeakPasswordVo covertWeakPasswordVo(WeakPassword weakPassword);

    WeakPassword covertWeakPassword(WeakPasswordDto weakPasswordDto);
    List<WeakPasswordVo> coverWeakPasswordVoList(List<WeakPassword> weakPasswordList);

    /**
     * 将User类转成ReturnUser
     */
    ReturnUser userConvertReturnUser(User source);
    /**
     * 将Permission类转成PermissionNode
     */
    @Mapping(source = "permissionImage", target = "permissionIcon")
    PermissionNode convertPermissionNode(Permission source);

    Permission convertPermission(PermissionStatusVo source);

    @Mappings({@Mapping(source = "permissionImage", target = "permissionIcon"),
            @Mapping(source = "permissionDesc", target = "remarks")})
    PermissionVo convertPermissionVo(Permission permission);

    @Mappings({@Mapping(source = "permissionIcon", target = "permissionImage"),
            @Mapping(source = "remarks", target = "permissionDesc")})
    Permission convertPermission(PermissionVo source);

    ExportFileVo covertExportFileVo(ExportFile exportFile);
    List<ExportFileVo> covertExportFileVoList(List<ExportFile> exportFileList);

    ExportFile covertExportFile(ExportFileDto exportFileDto);

    Log covertLog(BusLogVo busLogVo);

    NavigationVo covertNavigationVo(Navigation navigation);
    Navigation covertNavigation(NavigationDto navigationDto);

    List<NavigationVo> covertNavigationList(List<Navigation> navigationList);

    OrgVo covertOrgVo(Org org);
    Org covertOrg(OrgVo orgVo);

    List<OrgVo> covertOrgVoList(List<Org> orgList);

    Param covertParam(Param param);
    RoleDetail covertRoleDetail(Role role);

}
