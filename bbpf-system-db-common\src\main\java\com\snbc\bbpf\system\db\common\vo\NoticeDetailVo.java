package com.snbc.bbpf.system.db.common.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName: NoticeDetailVo
 * @Description: 消息公告详情vo
 * @module: SI-bbpf-message-center-opt
 * @Author: wangsong
 * @date: 2023/1/5
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NoticeDetailVo{
	private String msgContent;
	private String msgTitle;
	private String msgModel;
	private List<String> fileUrl;
	private List<OrgsItem> orgs;
	private List<UsersItem> users;
}
