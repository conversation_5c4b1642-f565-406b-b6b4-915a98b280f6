/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.vo;

import lombok.Builder;
import lombok.Data;


/**
 * @ClassName: QrcodeVo
 * 用户绑定详情VO
 * @module: si-bbpf-system
 * @Author: liangjb
 * @date: 2021/5/19 14:07
 */
@Data
@Builder
public class UserBindVo {


    /**
     * 获得用户登录绑定详情 WX 为微信，DD 为钉钉
     * */
    private String bindName;


    /**
     * 绑定状态 0为未绑定，1绑定
     * */
    private Integer bindState;


}
