/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.mapper;

import com.snbc.bbpf.system.db.common.entity.Navigation;
import com.snbc.bbpf.system.db.common.entity.NavigationPermission;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 顶部导航功能Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-27
 */
@Mapper
public interface NavigationMapper {
    /**
     * 查询顶部导航功能
     *
     * @param navigationId 顶部导航功能主键
     * @return 顶部导航功能
     */
    Navigation selectNavigationByNavigationId(String navigationId);

    /**
     * 查询顶部导航功能
     *
     * @param navigationCode 顶部导航功能代码
     * @return 顶部导航功能
     */
    Navigation selectNavigationByNavigationCode(String navigationCode);
    /**
     * 保存导航菜单关系
     * @param list
     * @return
     */
    int saveNavigationPermission(List<NavigationPermission> list);

    /**
     * 删除关系
     * @param navigationPermission
     * @return
     */
    int deleteRelationship(String navigationPermission);
    /**
     * 查询顶部导航功能列表
     *
     * @param navigation 顶部导航功能
     * @return 顶部导航功能集合
     */
    List<Navigation> selectNavigationList(Navigation navigation);

    /**
     * 查询关系列表
     * @param navigationId
     * @return
     */
    List<String> selectRelationship(String navigationId);
    /**
     * 新增顶部导航功能
     *
     * @param navigation 顶部导航功能
     * @return 受影响行数
     */
    int insertNavigation(Navigation navigation);

    /**
     * 修改顶部导航功能
     *
     * @param navigation 顶部导航功能
     * @return 受影响行数
     */
    int updateNavigation(Navigation navigation);

    /**
     * 删除顶部导航功能
     *
     * @param navigationId 顶部导航功能主键
     * @return 受影响行数
     */
    int deleteNavigationByNavigationId(String navigationId);

    /**
     * 批量删除顶部导航功能
     *
     * @param navigationIds 需要删除的数据主键集合
     * @return 受影响行数
     */
    int deleteNavigationByNavigationIds(String[] navigationIds);
}
