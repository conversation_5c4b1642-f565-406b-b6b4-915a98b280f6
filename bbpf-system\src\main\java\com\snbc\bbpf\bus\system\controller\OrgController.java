/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.controller;

import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.bus.system.service.OrgService;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.bus.system.utils.ResultUtil;
import com.snbc.bbpf.buslog.annotations.Buslog;
import com.snbc.bbpf.buslog.annotations.OperationType;
import com.snbc.bbpf.system.db.common.vo.OrgSort;
import com.snbc.bbpf.system.db.common.vo.OrgVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @ClassName: OrgController
 * @Description: 组织机构控制器
 * @module: si-bbpf-system
 * @Author: jiafei
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@RestController
@RequestMapping("/console/org")
public class OrgController {
    //组织机构业务层
    @Autowired
    private OrgService orgService;

    /***
     * @Description: 新增组织机构
     * @Author: jiafei
     * @param :         orgVo
     * @CreateDate: 2021/5/19 13:21
     * @UpdateDate: 2021/5/19 13:21
     * @return :        com.snbc.bbpf.component.config.CommonResp<java.lang.String>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @PostMapping(value = "/addOrg", consumes = {"application/json"})
    @Buslog(target = "org", opration = "addOrg", zh = "在【${parentOrgName}】下新增部门【${orgName}】",
            en = "Newly add department [${parentName}] under [${orgName}]")
    public CommonResp<Object> addOrg(@RequestBody OrgVo orgVo) throws BusinessException {
        orgService.addOrg(orgVo, CurrentUser.getUserId());
        return CommonResp.builder().head(ResultUtil.success()).build();
    }

    /***
     * @Description: 编辑组织机构
     * @Author: jiafei
     * @param :         orgVo
     * @CreateDate: 2021/5/19 13:22
     * @UpdateDate: 2021/5/19 13:22
     * @return :        com.snbc.bbpf.component.config.CommonResp<java.lang.String>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @PutMapping(value = "/updateOrg", consumes = {"application/json"})
    @Buslog(oprationType = OperationType.MODIFY,opration = "modifyOrg", target = "org",type = OrgVo.class )
    public CommonResp<Object> updateOrg(@RequestBody OrgVo orgVo) throws BusinessException {
        ErrorMessage.ORG_ID_IS_NULL.assertNotNull(orgVo.getOrgId());
        orgService.updateOrg(orgVo, orgVo.getSupervisorName(), CurrentUser.getUserId());
        return CommonResp.builder().head(ResultUtil.success()).build();
    }

    /***
     * @Description: 获取组织机构信息
     * @Author: jiafei
     * @param :         orgId
     * @CreateDate: 2021/5/20 15:48
     * @UpdateDate: 2021/5/20 15:48
     * @return :        com.snbc.bbpf.component.config.CommonResp<com.snbc.bbpf.system.db.common.vo.OrgVo>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @GetMapping(value = "/getOrg")
    public CommonResp<OrgVo> getOrg(@RequestParam String orgId) throws BusinessException {
        return CommonResp.<OrgVo>builder().head(ResultUtil.success()).body(orgService.getOrg(orgId)).build();
    }

    /***
     * @Description: 删除组织机构及下属组织机构
     * @Author: jiafei
     * @param :         orgId 当前组织机构ID
     * @CreateDate: 2021/5/19 13:27
     * @UpdateDate: 2021/5/19 13:27
     * @return :        com.snbc.bbpf.component.config.CommonResp<java.lang.String>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @DeleteMapping(value = "/delOrg")
    @Buslog(target = "org", opration = "delOrg", zh = "删除组织机构【${orgName}】", en = "Delete organization [${orgName}]")
    public CommonResp<Object> delOrg(@RequestParam String orgId) throws BusinessException {
        orgService.delOrg(orgId);
        return CommonResp.builder().head(ResultUtil.success()).build();
    }

    /**
     * wjc1 20230616 更新，左侧组织机构树数据由数据权限控制
     * @param : orgLevel 返回的组织机构层级 0：全部 1：根节点下一级 2：根节点下两级
     * @param : orgId  当前节点ID 最父级为空
     * @return :        com.snbc.bbpf.component.config.CommonResp<java.util.List<com.snbc.bbpf.system.db.common.vo.OrgVo>>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     * @Description: 组织机构树
     * @Author: jiafei
     * @CreateDate: 2021/5/18 15:38
     * @UpdateDate: 2021/5/18 15:38
     */
    @GetMapping("/getOrgTree")
    public CommonResp<List<OrgVo>> getOrgTree(){
        return CommonResp.<List<OrgVo>>builder().head(ResultUtil.success()).body(orgService.orgTree(CurrentUser.getUserId())).build();
    }

    /***
     * @Description: 组织机构拖拽排序
     * @Author: jiafei
     * @param :         orgSort
     * @CreateDate: 2021/5/18 18:07
     * @UpdateDate: 2021/5/18 18:07
     * @return :        com.snbc.bbpf.component.config.CommonResp<java.util.List<com.snbc.bbpf.system.db.common.vo.OrgVo>>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @PutMapping("/orgSort")
    @Buslog(target = "org", opration = "rankOrg", zh = "部门排序：已更新", en = "Department sort: updated")
    public CommonResp<List<OrgVo>> orgSort(@Valid @RequestBody OrgSort orgSort) {
        return CommonResp.<List<OrgVo>>builder().head(ResultUtil.success()).body(orgService.orgSort(orgSort)).build();
    }
}
