package com.snbc.bbpf.bus.system.controller;


import com.snbc.bbpf.bus.system.config.WeixinConfig;
import com.snbc.bbpf.bus.system.utils.WeiXinServiceUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 *  负责微信三方回调接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-04
 */
@Controller
@Slf4j
@RequestMapping("/console/third/")
public class WxBackLoginController {

    /**
     * 微信配置类
     */
    @Autowired
    private WeixinConfig weixinConfig;
    /**
     * 用户绑定登录实现
     */
    @Autowired
    private WeiXinServiceUtil weiXinServiceUtil;
    /**
     * 微信回调函数
     * @param code
     * @param state
     * @param response
     * @return
     * @throws Exception
     */
    @GetMapping("wxCallback")
    public void wxCallback(String code, String state, HttpServletResponse response) throws Exception{
        String url=weixinConfig.getGetErrorUrl();
        try {
            log.info("wxCallback start  ,code={},state={}",code,state);
            url=weiXinServiceUtil.dealCallback(code,state);
        } catch (Exception ex){
            log.error("wxCallback e={} ,code={},state={}",ex,code,state);
        }
        response.sendRedirect(url);
    }

}
