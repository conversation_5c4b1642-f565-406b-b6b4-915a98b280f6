# Grafana代理认证配置指南

## 问题描述

当调用Grafana API接口时出现 `401 Unauthorized` 错误，错误信息如下：
```json
{
  "extra": null,
  "message": "Unauthorized",
  "messageId": "auth.unauthorized",
  "statusCode": 401,
  "traceID": ""
}
```

这表明Grafana需要有效的认证信息才能访问API接口。

## 解决方案

### 方案1：使用Grafana API Token（推荐）

#### 1.1 创建Service Account Token

1. 登录Grafana管理界面
2. 进入 `Administration` -> `Service accounts`
3. 点击 `Add service account`
4. 填写服务账户信息：
   - Name: `bbpf-proxy-service`
   - Display name: `BBPF Proxy Service`
   - Role: `Admin` 或 `Editor`（根据需要的权限）
5. 创建完成后，点击服务账户进入详情页
6. 点击 `Add service account token`
7. 设置Token名称和过期时间
8. 复制生成的Token（格式：`glsa_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx_xxxxxxxx`）

#### 1.2 配置API Token

在应用配置文件中添加：

```yaml
bbpf:
  grafana:
    proxy:
      grafana-api-token: "glsa_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx_xxxxxxxx"
      enable-verbose-logging: true
```

或通过环境变量：
```bash
BBPF_GRAFANA_PROXY_GRAFANA_API_TOKEN=glsa_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx_xxxxxxxx
```

### 方案2：使用管理员用户名密码

如果无法创建API Token，可以使用管理员账户：

```yaml
bbpf:
  grafana:
    proxy:
      grafana-admin-username: "admin"
      grafana-admin-password: "your-admin-password"
      enable-verbose-logging: true
```

### 方案3：启用Auth Proxy模式

#### 3.1 配置Grafana Auth Proxy

在Grafana配置文件 `grafana.ini` 中添加：

```ini
[auth.proxy]
enabled = true
header_name = X-WEBAUTH-USER
header_property = username
auto_sign_up = true
sync_ttl = 60
whitelist = 127.0.0.1,::1,localhost,your-proxy-server-ip
headers = Name:X-WEBAUTH-NAME Email:X-WEBAUTH-EMAIL
enable_login_token = false
```

#### 3.2 配置代理服务

```yaml
bbpf:
  grafana:
    proxy:
      enable-auth-proxy: true
      auth-proxy-header-name: "X-WEBAUTH-USER"
      auto-sign-up: true
      # 仍需要配置API Token或管理员账户用于代理服务本身的认证
      grafana-api-token: "your-api-token"
```

## 配置优先级

认证方式的优先级顺序：
1. **API Token**（最高优先级）
2. **管理员用户名密码**
3. **Session Token**（通过登录获取）

## 调试步骤

### 1. 启用详细日志

```yaml
bbpf:
  grafana:
    proxy:
      enable-verbose-logging: true
```

### 2. 检查日志输出

启用详细日志后，查看以下关键日志：

```
# 认证Token获取
[INFO] Using Grafana API Token for authentication
[INFO] Added Grafana authentication header for user: {userId}

# Auth Proxy模式
[INFO] Added Auth Proxy headers for user: {username}

# 错误情况
[WARN] No valid Grafana authentication token available for user: {userId}
[WARN] Using Basic Auth as fallback for user: {userId}
```

### 3. 测试认证

可以直接测试Grafana API：

```bash
# 使用API Token
curl -H "Authorization: Bearer glsa_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx_xxxxxxxx" \
     http://localhost:3000/api/user

# 使用Basic Auth
curl -u admin:password \
     http://localhost:3000/api/user
```

### 4. 常见问题排查

#### 4.1 Token无效
- 检查Token是否正确复制
- 确认Token未过期
- 验证Service Account权限

#### 4.2 Basic Auth失败
- 确认用户名密码正确
- 检查Grafana是否启用了基本认证

#### 4.3 Auth Proxy不工作
- 确认Grafana配置文件中启用了Auth Proxy
- 检查whitelist配置
- 验证请求头名称匹配

## 安全建议

1. **生产环境使用API Token**：比用户名密码更安全
2. **设置Token过期时间**：定期轮换Token
3. **使用环境变量**：避免在配置文件中硬编码敏感信息
4. **限制Token权限**：只授予必要的权限
5. **监控认证失败**：设置告警监控认证异常

## 环境变量配置示例

```bash
# API Token方式
export BBPF_GRAFANA_PROXY_GRAFANA_API_TOKEN="glsa_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx_xxxxxxxx"

# 用户名密码方式
export BBPF_GRAFANA_PROXY_GRAFANA_ADMIN_USERNAME="admin"
export BBPF_GRAFANA_PROXY_GRAFANA_ADMIN_PASSWORD="your-password"

# Auth Proxy配置
export BBPF_GRAFANA_PROXY_ENABLE_AUTH_PROXY="true"
export BBPF_GRAFANA_PROXY_AUTH_PROXY_HEADER_NAME="X-WEBAUTH-USER"

# 调试配置
export BBPF_GRAFANA_PROXY_ENABLE_VERBOSE_LOGGING="true"
```

## 验证配置

配置完成后，重启应用并观察日志输出，确认：

1. 认证方式正确加载
2. 请求头正确设置
3. Grafana API调用成功
4. 无401错误

如果仍有问题，请检查Grafana服务器日志和网络连接。