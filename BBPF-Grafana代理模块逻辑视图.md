# BBPF-Grafana代理模块逻辑视图

## 1. 逻辑架构概述

BBPF-Grafana代理模块采用分层架构设计，通过多个功能模块协同工作，实现对Grafana访问的安全代理和权限控制。

## 2. 逻辑分层架构

```mermaid
graph TB
    subgraph "客户端层"
        A[Web浏览器]
        B[移动应用]
        C[第三方系统]
    end
    
    subgraph "接入层"
        D[负载均衡器]
        E[SSL终端]
    end
    
    subgraph "代理服务层"
        F[Token验证模块]
        G[Grafana接口代理模块]
        H[SQL拦截模块]
        I[动态模版变量注入模块]
        J[分享控制模块]
    end
    
    subgraph "缓存层"
        K[Redis集群]
        L[本地缓存]
    end
    
    subgraph "外部服务层"
        M[BBPF权限服务]
        N[Grafana服务]
        O[认证服务]
    end
    
    subgraph "数据存储层"
        P[MySQL数据库]
        Q[InfluxDB时序数据库]
        R[配置文件]
    end
    
    A --> D
    B --> D
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I
    I --> J
    J --> N
    
    F --> K
    G --> K
    H --> K
    I --> K
    
    F --> O
    H --> M
    I --> M
    
    K --> P
    N --> Q
```

## 3. 核心功能模块逻辑视图

### 3.1 Token验证模块逻辑视图

```mermaid
graph LR
    subgraph "Token验证模块"
        A[TokenAuthenticationFilter] --> B[AuthConfigService]
        A --> C[ExternalAuthService]
        A --> D[TokenUtil]
        C --> E[外部认证接口]
        B --> F[认证配置]
    end
    
    subgraph "输入"
        G[HTTP请求]
        H[Authorization Header]
    end
    
    subgraph "输出"
        I[用户上下文]
        J[认证结果]
    end
    
    G --> A
    H --> A
    A --> I
    A --> J
```

**逻辑说明**:
- 根据部署模式（微服务/单体）决定是否启用认证
- 支持白名单路径直接放行
- 调用外部认证接口验证Token
- 设置用户上下文供后续模块使用

### 3.2 Grafana接口代理模块逻辑视图

```mermaid
graph TB
    subgraph "代理模块"
        A[GrafanaProxyController] --> B[GrafanaProxyService]
        B --> C[GrafanaApiClient]
        B --> D[StaticResourceController]
        B --> E[ProxyStatistics]
    end
    
    subgraph "请求处理流程"
        F[接收请求] --> G[权限验证]
        G --> H[构建目标URL]
        H --> I[修改请求头]
        I --> J[转发请求]
        J --> K[处理响应]
        K --> L[返回客户端]
    end
    
    subgraph "外部依赖"
        M[Grafana服务]
        N[权限服务]
    end
    
    A --> F
    C --> M
    G --> N
```

**逻辑说明**:
- 透明代理所有Grafana请求
- 支持仪表盘、API、静态资源的代理
- 集成权限验证和统计监控
- 提供错误处理和重试机制

### 3.3 SQL拦截模块逻辑视图

```mermaid
graph TB
    subgraph "SQL拦截模块"
        A[SqlInterceptorService] --> B[SqlParser]
        A --> C[PermissionFilter]
        A --> D[SqlInterceptorConfig]
        B --> E[SQL解析引擎]
        C --> F[权限条件生成器]
    end
    
    subgraph "处理流程"
        G[拦截查询请求] --> H[解析SQL语句]
        H --> I[获取用户权限]
        I --> J[生成过滤条件]
        J --> K[重写SQL语句]
        K --> L[执行修改后查询]
    end
    
    subgraph "权限数据源"
        M[用户权限缓存]
        N[表级权限配置]
        O[字段级权限配置]
    end
    
    A --> G
    I --> M
    I --> N
    I --> O
```

**逻辑说明**:
- 智能识别需要拦截的SQL查询
- 支持多种数据源类型的SQL解析
- 动态注入权限过滤条件
- 提供表级和字段级权限控制

### 3.4 动态模版变量注入模块逻辑视图

```mermaid
graph TB
    subgraph "变量注入模块"
        A[DynamicTemplateVariableService] --> B[GrafanaVariableService]
        A --> C[PermissionVariableGenerator]
        A --> D[VariableCache]
        B --> E[VariableDefinition]
        C --> F[权限变量生成器]
    end
    
    subgraph "注入流程"
        G[拦截仪表盘响应] --> H[解析JSON配置]
        H --> I[获取用户权限]
        I --> J[生成权限变量]
        J --> K[注入到templating.list]
        K --> L[返回修改后配置]
    end
    
    subgraph "变量类型"
        M[用户ID变量]
        N[组织ID变量]
        O[角色变量]
        P[数据过滤变量]
    end
    
    A --> G
    J --> M
    J --> N
    J --> O
    J --> P
```

**逻辑说明**:
- 动态注入权限相关的模板变量
- 支持多种变量类型（常量、查询、自定义）
- 变量缓存机制提升性能
- 对用户透明的权限控制

### 3.5 分享控制模块逻辑视图

```mermaid
graph LR
    subgraph "分享控制模块"
        A[ShareConfigService] --> B[ResponseModifier]
        A --> C[ShareConfig]
        B --> D[页面内容修改器]
        B --> E[API响应修改器]
    end
    
    subgraph "控制功能"
        F[分享按钮控制]
        G[导出功能控制]
        H[快照功能控制]
        I[面板导出控制]
    end
    
    subgraph "修改策略"
        J[隐藏UI元素]
        K[禁用功能按钮]
        L[移除API配置]
    end
    
    B --> F
    B --> G
    B --> H
    B --> I
    
    D --> J
    D --> K
    E --> L
```

**逻辑说明**:
- 基于配置控制分享功能的显示
- 通过页面内容修改实现功能控制
- 支持细粒度的功能开关
- 提供降级处理机制

## 4. 模块间交互逻辑

### 4.1 请求处理完整流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant TokenAuth as Token验证模块
    participant Proxy as 代理模块
    participant SqlInt as SQL拦截模块
    participant VarInj as 变量注入模块
    participant ShareCtrl as 分享控制模块
    participant Grafana as Grafana服务
    
    Client->>TokenAuth: 1. HTTP请求
    TokenAuth->>TokenAuth: 2. 验证Token
    alt 认证失败
        TokenAuth->>Client: 401 未授权
    else 认证成功
        TokenAuth->>Proxy: 3. 转发请求
        Proxy->>Proxy: 4. 权限检查
        alt 权限不足
            Proxy->>Client: 403 禁止访问
        else 权限通过
            alt SQL查询请求
                Proxy->>SqlInt: 5. SQL拦截处理
                SqlInt->>SqlInt: 6. 注入权限条件
                SqlInt->>Grafana: 7. 执行修改后查询
            else 仪表盘配置请求
                Proxy->>Grafana: 5. 转发请求
                Grafana->>VarInj: 6. 响应拦截
                VarInj->>VarInj: 7. 注入权限变量
                VarInj->>ShareCtrl: 8. 分享控制处理
                ShareCtrl->>ShareCtrl: 9. 修改页面内容
                ShareCtrl->>Client: 10. 返回处理后响应
            else 其他请求
                Proxy->>Grafana: 5. 直接转发
                Grafana->>Client: 6. 返回响应
            end
        end
    end
```

### 4.2 缓存交互逻辑

```mermaid
graph TB
    subgraph "缓存层次"
        A[本地缓存] --> B[Redis缓存]
        B --> C[数据库]
    end
    
    subgraph "缓存使用者"
        D[Token验证模块]
        E[SQL拦截模块]
        F[变量注入模块]
        G[分享控制模块]
    end
    
    subgraph "缓存内容"
        H[用户认证信息]
        I[权限数据]
        J[SQL拦截规则]
        K[模板变量]
        L[分享配置]
    end
    
    D --> A
    E --> A
    F --> A
    G --> A
    
    A --> H
    A --> I
    A --> J
    A --> K
    A --> L
```

## 5. 数据流逻辑

### 5.1 权限数据流

```mermaid
flowchart TD
    A[用户请求] --> B[Token验证]
    B --> C[提取用户信息]
    C --> D[查询权限缓存]
    D --> E{缓存命中?}
    E -->|是| F[使用缓存权限]
    E -->|否| G[调用权限服务]
    G --> H[缓存权限数据]
    H --> F
    F --> I[权限过滤]
    I --> J[SQL注入/变量注入]
    J --> K[返回过滤后数据]
```

### 5.2 配置数据流

```mermaid
flowchart TD
    A[配置文件] --> B[配置加载]
    B --> C[配置验证]
    C --> D[配置缓存]
    D --> E[模块配置更新]
    E --> F[功能开关控制]
    F --> G[运行时行为调整]
```

## 6. 错误处理逻辑

### 6.1 异常处理层次

```mermaid
graph TB
    subgraph "异常类型"
        A[认证异常]
        B[权限异常]
        C[代理异常]
        D[SQL异常]
        E[配置异常]
    end
    
    subgraph "处理策略"
        F[降级处理]
        G[重试机制]
        H[熔断保护]
        I[错误日志]
        J[监控告警]
    end
    
    subgraph "恢复机制"
        K[自动恢复]
        L[手动干预]
        M[配置回滚]
    end
    
    A --> F
    B --> F
    C --> G
    D --> H
    E --> M
    
    F --> I
    G --> I
    H --> I
    
    I --> J
    J --> K
    J --> L
```

## 7. 性能优化逻辑

### 7.1 性能优化策略

```mermaid
mindmap
  root((性能优化))
    缓存优化
      多级缓存
      缓存预热
      缓存更新策略
    连接优化
      连接池管理
      长连接复用
      超时控制
    处理优化
      异步处理
      批量操作
      流式处理
    资源优化
      内存管理
      CPU调度
      IO优化
```

### 7.2 监控指标逻辑

```mermaid
graph LR
    subgraph "业务指标"
        A[请求量]
        B[响应时间]
        C[成功率]
        D[错误率]
    end
    
    subgraph "系统指标"
        E[CPU使用率]
        F[内存使用率]
        G[网络IO]
        H[磁盘IO]
    end
    
    subgraph "应用指标"
        I[缓存命中率]
        J[连接池状态]
        K[线程池状态]
        L[GC状态]
    end
    
    subgraph "告警机制"
        M[阈值监控]
        N[趋势分析]
        O[异常检测]
    end
    
    A --> M
    B --> M
    E --> M
    I --> N
    C --> O
```

## 8. 安全控制逻辑

### 8.1 安全防护层次

```mermaid
graph TB
    subgraph "接入安全"
        A[SSL/TLS加密]
        B[IP白名单]
        C[请求频率限制]
    end
    
    subgraph "认证安全"
        D[Token验证]
        E[会话管理]
        F[权限验证]
    end
    
    subgraph "数据安全"
        G[SQL注入防护]
        H[数据脱敏]
        I[访问日志]
    end
    
    subgraph "传输安全"
        J[请求加密]
        K[响应加密]
        L[敏感信息过滤]
    end
    
    A --> D
    B --> D
    C --> D
    D --> G
    E --> G
    F --> G
    G --> J
    H --> J
    I --> J
```

## 9. 总结

BBPF-Grafana代理模块通过分层架构和模块化设计，实现了：

1. **安全性**: 多层次的安全防护机制
2. **可扩展性**: 模块化设计支持功能扩展
3. **高性能**: 多级缓存和连接优化
4. **可维护性**: 清晰的逻辑分层和错误处理
5. **可监控性**: 全面的监控指标和告警机制

该逻辑视图为系统的设计、开发、部署和运维提供了清晰的指导，确保系统能够满足企业级应用的各项要求。