package com.snbc.bbpf.system.db.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: DictValueDto
 * @Description: 字典value
 * @module: SI-bbpf-product-manage
 * @Author: wangsong
 * @date: 2021/11/18
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class DictValueDto {
    private String valueName;
    private int valueCode;
    private String typeCode;
}
