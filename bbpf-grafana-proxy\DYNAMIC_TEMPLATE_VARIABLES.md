# 动态模板变量实现方案

## 概述

本文档描述了BBPF Grafana代理系统中"动态注入模板变量"功能的实现。该方案通过在代理层拦截Grafana仪表盘的JSON响应，动态向其templating列表添加用户权限相关的变量，实现了高通用性、低侵入性的权限控制机制。

## 方案优势

### 相比"拦截并修改SQL"方案的优势：

| 对比维度 | 动态注入模板变量 (已实现) | 拦截并修改SQL查询 |
|---------|------------------------|------------------|
| **通用性** | 非常高 - 支持所有数据源类型 | 很低 - 需要为每种查询语言编写解析器 |
| **维护性** | 高 - 权限逻辑集中管理 | 低 - 与业务查询紧密耦合 |
| **侵入性** | 无侵入 - 对Grafana透明 | 高侵入 - 深度介入查询流程 |
| **安全性** | 高 - 利用Grafana变量机制 | 风险高 - 直接拼接SQL字符串 |
| **用户体验** | 友好 - 符合使用习惯 | 不透明 - 用户不知道SQL被修改 |

## 架构设计

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   用户请求       │───▶│   代理服务        │───▶│   Grafana       │
│                │    │                  │    │                │
│ GET /api/      │    │ 1. 转发请求       │    │ 返回仪表盘JSON   │
│ dashboards/uid │    │ 2. 拦截响应       │    │                │
│                │    │ 3. 注入变量       │    │                │
│                │    │ 4. 返回修改后JSON │    │                │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌──────────────────┐
                    │ 动态模板变量服务   │
                    │                  │
                    │ • 生成用户变量     │
                    │ • 注入到JSON      │
                    │ • 安全验证        │
                    └──────────────────┘
```

## 核心组件

### 1. DynamicTemplateVariableService

**职责**: 核心业务逻辑，负责生成和注入动态模板变量

**主要功能**:
- 根据用户权限生成变量映射
- 解析和修改仪表盘JSON
- 变量值安全验证
- 判断是否需要拦截响应

**生成的变量类型**:
```javascript
$_user_id          // 用户ID
$_org_id           // 组织ID  
$_dept_id          // 部门ID
$_tenant_id        // 租户ID
$_user_role        // 用户角色
$_data_filter      // 数据过滤条件
$_can_view_all     // 是否可查看所有数据
$_can_edit         // 是否可编辑
$_can_admin        // 是否为管理员
```

### 2. DashboardResponseInterceptor

**职责**: HTTP响应拦截器，集成到Spring MVC拦截器链

**主要功能**:
- 拦截仪表盘相关的API响应
- 调用动态模板变量服务处理响应
- 提供响应包装和处理能力
- 记录拦截统计信息

### 3. GrafanaProxyServiceImpl (已修改)

**职责**: 代理服务实现，集成动态变量注入功能

**修改内容**:
- 在`modifyResponseContent`方法中集成变量注入
- 添加错误处理和日志记录
- 确保注入失败时不影响正常功能

### 4. InterceptorConfig

**职责**: Spring MVC配置，注册拦截器

**配置内容**:
- 拦截路径: `/api/dashboards/**`, `/d/**`
- 排除路径: `/api/health`, `/public/**`, `/static/**`

## 实现细节

### 变量注入流程

1. **请求拦截**: 用户访问仪表盘API
2. **权限获取**: 从JWT token中提取用户ID
3. **响应拦截**: 拦截Grafana返回的JSON响应
4. **变量生成**: 根据用户权限生成动态变量
5. **JSON修改**: 将变量注入到templating.list中
6. **响应返回**: 返回修改后的JSON给客户端

### JSON结构修改示例

**原始JSON**:
```json
{
  "dashboard": {
    "templating": {
      "list": [
        {
          "name": "existing_var",
          "type": "query"
        }
      ]
    }
  }
}
```

**注入后JSON**:
```json
{
  "dashboard": {
    "templating": {
      "list": [
        {
          "name": "existing_var",
          "type": "query"
        },
        {
          "name": "_user_id",
          "type": "constant",
          "current": {
            "value": "12345",
            "text": "12345"
          },
          "hide": 2,
          "description": "当前用户ID (BBPF权限系统自动注入)"
        }
      ]
    }
  }
}
```

### 安全机制

1. **SQL注入防护**: 变量值经过严格的安全验证
2. **权限验证**: 只有经过JWT认证的用户才能获取变量
3. **变量隐藏**: 权限相关变量在UI中隐藏 (`hide: 2`)
4. **错误隔离**: 注入失败时不影响原有功能

## 使用方法

### 在仪表盘查询中使用变量

```sql
-- 查询当前用户的数据
SELECT * FROM user_data WHERE user_id = '$_user_id'

-- 查询当前组织的数据  
SELECT * FROM org_data WHERE org_id = '$_org_id'

-- 根据数据过滤条件查询
SELECT * FROM sensitive_data WHERE $_data_filter

-- 条件查询（基于权限）
SELECT * FROM all_data 
WHERE (
  CASE 
    WHEN '$_can_view_all' = 'true' THEN 1=1
    ELSE user_id = '$_user_id'
  END
)
```

### 测试接口

系统提供了完整的测试接口用于验证功能：

```bash
# 测试变量生成
GET /api/test/dynamic-variables/generate

# 测试变量注入
POST /api/test/dynamic-variables/inject

# 获取服务状态
GET /api/test/dynamic-variables/status

# 测试拦截判断
GET /api/test/dynamic-variables/should-intercept?apiPath=/api/dashboards/uid/test

# 获取示例仪表盘
GET /api/test/dynamic-variables/sample-dashboard
```

## 配置说明

### 应用配置

在`application.yml`中可以配置相关参数：

```yaml
bbpf:
  grafana:
    proxy:
      # 是否启用动态模板变量
      enable-dynamic-variables: true
      # 是否启用详细日志
      enable-verbose-logging: true
      # 变量缓存时间（秒）
      variable-cache-ttl: 300
```

### 权限配置

权限相关配置通过`PermissionService`管理：

```java
// 用户权限信息
UserPermission permission = permissionService.getUserPermission(userId);

// 数据过滤条件
String dataFilter = permission.getDataFilter();

// 权限标志
boolean canViewAll = permission.hasPermission("view_all");
```

## 监控和调试

### 日志记录

系统提供详细的日志记录：

```
[动态模板变量服务] 开始为用户生成权限变量: 12345
[动态模板变量服务] 成功生成 8 个动态变量
[仪表盘响应拦截器] 成功注入动态模板变量: /api/dashboards/uid/test for user: 12345
[代理服务] 已处理动态模板变量注入: /api/dashboards/uid/test for user: 12345
```

### 性能监控

- 变量生成时间
- JSON解析和修改时间
- 拦截成功率
- 错误统计

## 扩展性

### 添加新的变量类型

1. 在`DynamicTemplateVariableService.generateUserPermissionVariables()`中添加新变量
2. 在`PermissionService`中添加相应的权限获取方法
3. 更新测试用例

### 支持新的数据源

由于方案的高通用性，支持新数据源无需修改核心代码，只需：
1. 确保数据源支持Grafana变量机制
2. 在查询中使用相应的变量语法

## 故障排除

### 常见问题

1. **变量未注入**
   - 检查用户是否已认证
   - 检查API路径是否匹配拦截规则
   - 查看日志中的错误信息

2. **变量值为空**
   - 检查用户权限配置
   - 验证PermissionService是否正常工作

3. **JSON解析错误**
   - 检查原始JSON格式是否正确
   - 查看详细错误日志

### 调试步骤

1. 启用详细日志记录
2. 使用测试接口验证各个组件
3. 检查拦截器配置
4. 验证权限服务状态

## 总结

"动态注入模板变量"方案成功实现了以下目标：

✅ **高通用性**: 支持所有Grafana数据源  
✅ **低侵入性**: 对现有系统无影响  
✅ **高安全性**: 利用Grafana原生安全机制  
✅ **易维护性**: 权限逻辑集中管理  
✅ **用户友好**: 符合Grafana使用习惯  

该方案遵循了"高内聚、低耦合"的软件设计原则，为BBPF系统提供了一个健壮、安全、易扩展的权限控制解决方案。

---

**版本**: 2.0.0  
**作者**: BBPF Team  
**更新时间**: 2024-12-13