package com.snbc.bbpf.grafana.proxy.service;

import com.snbc.bbpf.grafana.proxy.config.SqlInterceptorConfig;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import java.util.List;
import java.util.Set;
import java.lang.reflect.Method;

/**
 * SQL拦截器服务测试类
 */
public class SqlInterceptorServiceTest {
    
    private SqlInterceptorConfig config;
    private SqlInterceptorService sqlInterceptorService;
    
    @BeforeEach
    void setUp() {
        config = new SqlInterceptorConfig();
        // 不需要完整的依赖注入，只测试extractTableNames方法
        sqlInterceptorService = new SqlInterceptorService();
    }
    
    @Test
    void testExtractTableNames() throws Exception {
        // 使用反射调用私有方法
        Method extractTableNamesMethod = SqlInterceptorService.class.getDeclaredMethod("extractTableNames", String.class);
        extractTableNamesMethod.setAccessible(true);
        
        // 测试带反引号和数据库名的SQL
        String sql1 = "SELECT org_name, org_code, org_id, supervisor FROM `bbpf-system-manager`.t_org LIMIT 50";
        @SuppressWarnings("unchecked")
        Set<String> result1 = (Set<String>) extractTableNamesMethod.invoke(sqlInterceptorService, sql1);
        assertTrue(result1.contains("t_org"), "应该提取出表名 t_org");
        
        // 测试普通表名
        String sql2 = "SELECT * FROM t_user WHERE user_id = '123'";
        @SuppressWarnings("unchecked")
        Set<String> result2 = (Set<String>) extractTableNamesMethod.invoke(sqlInterceptorService, sql2);
        assertTrue(result2.contains("t_user"), "应该提取出表名 t_user");
        
        // 测试多个表名
        String sql3 = "SELECT u.*, r.role_name FROM t_user u JOIN t_role r ON u.role_id = r.role_id";
        @SuppressWarnings("unchecked")
        Set<String> result3 = (Set<String>) extractTableNamesMethod.invoke(sqlInterceptorService, sql3);
        assertTrue(result3.contains("t_user"), "应该提取出表名 t_user");
        assertTrue(result3.contains("t_role"), "应该提取出表名 t_role");
        
        System.out.println("测试1结果: " + result1);
        System.out.println("测试2结果: " + result2);
        System.out.println("测试3结果: " + result3);
    }
}