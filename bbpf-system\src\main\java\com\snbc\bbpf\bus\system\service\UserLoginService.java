/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service;

import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.system.db.common.entity.PermissionNode;
import com.snbc.bbpf.system.db.common.entity.login.LoginUser;
import com.snbc.bbpf.system.db.common.entity.login.ReturnUser;

import java.util.List;

/**
 * @ClassName: UserLoginService
 * @Description: 用户登录业务处理
 * @module: si-bbpf-system
 * @Author: liangjb
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
public interface UserLoginService {

      /**
     * @Description: 验证用户
     * @Author:  liangjB
     * @param    loginUser 用户信息
       * @param    ip ip
     * @CreateDate: 2021/5/19 11:10
     * @UpdateDate: 2021/5/19 11:10
     */
    CommonResp<ReturnUser> verifyUserPwd(LoginUser loginUser, String requestSource, String ip) throws Exception;
    /**
     * @Description: 获取用户部门列表
     * @Author:  liangjB
     * @param    userId 用户编号
     * @CreateDate: 2021/5/19 11:10
     * @UpdateDate: 2021/5/19 11:10
     */
    List<PermissionNode> getUserPermissionsByUserId(String userId, String sysType,String langKey,String permissionType);

    /**
     * @Description: 获取用户部门列表
     * @Author:  liangjB
     * @param    userId 用户编号
     * @CreateDate: 2021/5/19 11:10
     * @UpdateDate: 2021/5/19 11:10
     */
    List<PermissionNode> getUserNavPermissionsByUserId(String userId, String sysType, String langKey,
                                                       String permissionType, String navId,
                                                       List<String> roleIds);
    /**
     * @Description: 验证用户
     * @Author:  liangjB
     * @param    userId 用户id
     * @param    tenantId 租户ID
     * @CreateDate: 2021/5/19 11:10
     * @UpdateDate: 2021/5/19 11:10
     */
    String getUserIdToken( String userId, String tenantId, String sysType, String ip);
}
