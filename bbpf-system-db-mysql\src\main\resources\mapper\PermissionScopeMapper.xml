<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snbc.bbpf.system.db.common.mapper.PermissionScopeMapper" >
  <resultMap id="BaseResultMap" type="com.snbc.bbpf.system.db.common.entity.PermissionScope">
      <id column="scope_id" jdbcType="VARCHAR" javaType="java.lang.String" property="scopeId"/>
      <result column="permission_id" jdbcType="VARCHAR" javaType="java.lang.String" property="permissionId"/>
      <result column="scope_name" jdbcType="VARCHAR" javaType="java.lang.String" property="scopeName"/>
      <result column="scope_code" jdbcType="VARCHAR" javaType="java.lang.String" property="scopeCode"/>
      <result column="scope_column" jdbcType="VARCHAR" javaType="java.lang.String" property="scopeColumn"/>
      <result column="scope_type" jdbcType="INTEGER" javaType="java.lang.Integer" property="scopeType"/>
      <result column="scope_field" jdbcType="VARCHAR" javaType="java.lang.String" property="scopeField"/>
      <result column="scope_class" jdbcType="VARCHAR" javaType="java.lang.String" property="scopeClass"/>
      <result column="remarks" jdbcType="VARCHAR" javaType="java.lang.String" property="remarks"/>
      <result column="create_time" jdbcType="TIMESTAMP" javaType="java.time.LocalDateTime" property="createTime"/>
      <result column="update_time" jdbcType="TIMESTAMP" javaType="java.time.LocalDateTime" property="updateTime"/>
      <result column="create_user_id" jdbcType="VARCHAR" javaType="java.lang.String" property="createUserId"/>
      <result column="scope_ext" jdbcType="VARCHAR" javaType="java.lang.String" property="scopeExt"/>
  </resultMap>
  <sql id="Base_Column_List" >
    scope_id, permission_id, scope_name, scope_code, scope_column, scope_type, scope_field, 
    scope_class, remarks, create_time, update_time, create_user_id,scope_ext
  </sql>
  <select id="getDataScopeByUserId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_permission_scope where scope_id in (
    select s.scope_id from  t_role_permission_scope s left join t_permission p  on p.permission_id=s.permission_id
    where p.routing_url=#{routingUrl,jdbcType=VARCHAR} and s.role_id in (select role_id from
    t_user_role where user_id=#{userId,jdbcType=VARCHAR})
    )
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from t_permission_scope
    where scope_id = #{scopeId,jdbcType=VARCHAR}
  </select>
  <select id="selectByScopeCode" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from t_permission_scope
    where scope_code = #{scopeCode,jdbcType=VARCHAR}
  </select>
  <insert id="insertSelective" parameterType="com.snbc.bbpf.system.db.common.entity.PermissionScope" >
    insert into t_permission_scope
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="scopeId != null" >
        scope_id,
      </if>
      <if test="permissionId != null" >
        permission_id,
      </if>
      <if test="scopeName != null" >
        scope_name,
      </if>
      <if test="scopeCode != null" >
        scope_code,
      </if>
      <if test="scopeColumn != null" >
        scope_column,
      </if>
      <if test="scopeType != null" >
        scope_type,
      </if>
      <if test="scopeField != null" >
        scope_field,
      </if>
      <if test="scopeClass != null" >
        scope_class,
      </if>
      <if test="remarks != null" >
        remarks,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="createUserId != null" >
        create_user_id,
      </if>
      <if test="scopeExt != null" >
        scope_ext,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="scopeId != null" >
        #{scopeId,jdbcType=VARCHAR},
      </if>
      <if test="permissionId != null" >
        #{permissionId,jdbcType=VARCHAR},
      </if>
      <if test="scopeName != null" >
        #{scopeName,jdbcType=VARCHAR},
      </if>
      <if test="scopeCode != null" >
        #{scopeCode,jdbcType=VARCHAR},
      </if>
      <if test="scopeColumn != null" >
        #{scopeColumn,jdbcType=VARCHAR},
      </if>
      <if test="scopeType != null" >
        #{scopeType,jdbcType=INTEGER},
      </if>
      <if test="scopeField != null" >
        #{scopeField,jdbcType=VARCHAR},
      </if>
      <if test="scopeClass != null" >
        #{scopeClass,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null" >
        #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null" >
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="scopeExt != null" >
        #{scopeExt,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.snbc.bbpf.system.db.common.entity.PermissionScope" >
    update t_permission_scope
    <set >
      <if test="permissionId != null" >
        permission_id = #{permissionId,jdbcType=VARCHAR},
      </if>
      <if test="scopeName != null" >
        scope_name = #{scopeName,jdbcType=VARCHAR},
      </if>
      <if test="scopeCode != null" >
        scope_code = #{scopeCode,jdbcType=VARCHAR},
      </if>
      <if test="scopeColumn != null" >
        scope_column = #{scopeColumn,jdbcType=VARCHAR},
      </if>
      <if test="scopeType != null" >
        scope_type = #{scopeType,jdbcType=INTEGER},
      </if>
      <if test="scopeField != null" >
        scope_field = #{scopeField,jdbcType=VARCHAR},
      </if>
      <if test="scopeClass != null" >
        scope_class = #{scopeClass,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null" >
        remarks = #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null" >
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="scopeExt != null" >
        scope_ext = #{scopeExt,jdbcType=VARCHAR},
      </if>
    </set>
    where scope_id = #{scopeId,jdbcType=VARCHAR}
  </update>
  <delete id="deleteByScopeIds" parameterType="java.lang.String" >
    delete from t_permission_scope
    where scope_id in
    <foreach collection="array" item="arr" index="no" open="("
             separator="," close=")">
      #{arr}
    </foreach>
  </delete>

  <select id="selectbyList" resultMap="BaseResultMap" parameterType="com.snbc.bbpf.system.db.common.entity.PermissionScope" >
    select
    <include refid="Base_Column_List" />
    from t_permission_scope
    <where>
      <if test="permissionId != null" >
        and permission_id = #{permissionId,jdbcType=VARCHAR}
      </if>
      <if test="scopeName != null" >
        and scope_name = #{scopeName,jdbcType=VARCHAR}
      </if>
      <if test="scopeCode != null" >
        and scope_code = #{scopeCode,jdbcType=VARCHAR}
      </if>
      <if test="scopeColumn != null" >
        and scope_column = #{scopeColumn,jdbcType=VARCHAR}
      </if>
      <if test="scopeType != null" >
        and scope_type = #{scopeType,jdbcType=INTEGER}
      </if>
      <if test="scopeField != null" >
        and scope_field = #{scopeField,jdbcType=VARCHAR}
      </if>
      <if test="scopeClass != null" >
        and scope_class = #{scopeClass,jdbcType=VARCHAR}
      </if>
      <if test="remarks != null" >
        and remarks = #{remarks,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null" >
        and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null" >
        and update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="createUserId != null" >
        and create_user_id = #{createUserId,jdbcType=VARCHAR}
      </if>
    </where>
     order by scope_type
  </select>
</mapper>