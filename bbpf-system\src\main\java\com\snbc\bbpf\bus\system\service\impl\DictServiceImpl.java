/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service.impl;

import com.snbc.bbpf.bus.system.constans.NumberConstant;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.bus.system.service.DictService;
import com.snbc.bbpf.bus.system.utils.ResultUtil;
import com.snbc.bbpf.system.db.common.dto.DictValueDto;
import com.snbc.bbpf.system.db.common.entity.DictType;
import com.snbc.bbpf.system.db.common.entity.DictValue;
import com.snbc.bbpf.system.db.common.mapper.DictTypeMapper;
import com.snbc.bbpf.system.db.common.mapper.DictValueMapper;
import com.snbc.bbpf.system.db.common.vo.DictTypeVo;
import com.snbc.bbpf.system.db.common.vo.DictValueVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * @ClassName: DictTypeServiceImpl
 * @Description: 字典管理service层
 * @module: bbpf-bus-system
 * @Author: liuyi
 * @date: 2021/5/18
 * copyright 2020 barm Inc. All rights reserver
 */
@Slf4j
@Service
public class DictServiceImpl implements DictService {

    @Autowired
    private DictTypeMapper dictTypeMapper;

    @Autowired
    private DictValueMapper dictValueMapper;

    /**
     * @description: 更新字典类型...........................................
     * @param dictType 字典类型json体
     * @param commonResp 返回响应json体
     * @return: void
     * @author: liuyi
     * @time: 2021/6/7 16:13
     */
    @Override
    public void updateDictType(DictType dictType, CommonResp<String> commonResp) {
        DictType origin = dictTypeMapper.selectByPrimaryKey(dictType.getId());
        List<DictType> list = dictTypeMapper.selectByExample(dictType.getTypeCode());
        int num;
        if (origin==null) {
            commonResp.setHead(ResultUtil.error(ErrorMessage.DICT_CODE_IS_NONE,dictType.getId()));
        } else if (!list.isEmpty() && !list.get(0).getId().equalsIgnoreCase(dictType.getId())) {
            commonResp.setHead(ResultUtil.error(ErrorMessage.DICT_CODE_EXIST,dictType.getTypeCode()));
        }else {
            num = dictTypeMapper.updateByPrimaryKeySelective(dictType);
            if (num == NumberConstant.NO_ONE) {
                commonResp.setHead(ResultUtil.success());
            }else {
                commonResp.setHead(ResultUtil.error(ErrorMessage.SQL_EXECUTE_FAILED));
            }
        }
    }

    /**
     * @description: 添加字典类型
     * @param dictTypeVo 字典类型json体
     * @param commonResp  返回响应json体
     * @return: void
     * @author: liuyi
     * @time: 2021/6/7 16:26
     */
    @Override
    public void addDictType(DictTypeVo dictTypeVo, CommonResp<String> commonResp) {
        int num;
        UUID uuid = UUID.randomUUID();
        DictType dictType = new DictType(uuid.toString(), dictTypeVo.getTypeCode(), dictTypeVo.getTypeName());
        List<DictType> list = dictTypeMapper.selectByExample(dictTypeVo.getTypeCode());
        if (!list.isEmpty()) {
            commonResp.setHead(ResultUtil.error(ErrorMessage.DICT_CODE_EXIST,dictTypeVo.getTypeCode()));
            return;
        } else {
            num = dictTypeMapper.insertSelective(dictType);
        }
        if (num == 1) {
            commonResp.setHead(ResultUtil.success());
        } else {
            commonResp.setHead(ResultUtil.error(ErrorMessage.SQL_EXECUTE_FAILED));
        }
    }


    /**
     * @description:  添加字典值
     * @param dictValueVo 字典值json体
     * @param commonResp  返回响应json体
     * @return: void
     * @author: liuyi
     * @time: 2021/6/7 16:28
     */
    @Override
    public void addDictValue(DictValueVo dictValueVo, CommonResp<String> commonResp) {
        List<DictType> list = dictTypeMapper.selectByExample(dictValueVo.getTypeCode());
        if (list.isEmpty()) {
            commonResp.setHead(ResultUtil.error(ErrorMessage.DICT_CODE_IS_NONE,dictValueVo.getTypeCode()));
            return;
        }
        if(StringUtils.isNotEmpty(dictValueVo.getParentId())){
            DictValue parent = dictValueMapper.selectByPrimaryKey(dictValueVo.getParentId());
            if(parent==null){
                commonResp.setHead(ResultUtil.error("parent id no exist",dictValueVo.getParentId()));
                return;
            }
        }
        if (dictValueMapper.selectOnlyValue(dictValueVo.getTypeCode(), dictValueVo.getValueCode()
                ,dictValueVo.getValueName(),"") >= NumberConstant.NO_ONE) {
            commonResp.setHead(ResultUtil.error(ErrorMessage.DICT_VALUE_EXIST,dictValueVo.getValueCode()));
        } else {
            DictValue dictValue = new DictValue(dictValueVo.getTypeCode(), dictValueVo.getValueName(), dictValueVo.getValueCode());
            dictValue.setValueId(UUID.randomUUID().toString());
            if (StringUtils.isNotEmpty(dictValueVo.getValueDesc())) {
                dictValue.setValueDesc(dictValueVo.getValueDesc());
            }
            if (StringUtils.isNotEmpty(dictValueVo.getParentId())) {
                dictValue.setParentId(dictValueVo.getParentId());
            }
            if (dictValueMapper.insertSelective(dictValue) == NumberConstant.NO_ONE) {
                commonResp.setHead(ResultUtil.success());
            }else {
                commonResp.setHead(ResultUtil.error(ErrorMessage.SQL_EXECUTE_FAILED));
            }
        }
    }

    /**
     * @description: 更新字典值..................
     * @param dictValueVo 字典值json体
     *                    TypeCode 唯一，采用id就行修改
     * @param commonResp http response json object
     * @return: void
     * @author: liuyi
     * @time: 2021/6/7 16:29
     */
    @Override
    public void updateDictValue(DictValueVo dictValueVo, CommonResp<String> commonResp) {
        List<DictType> list = dictTypeMapper.selectByExample(dictValueVo.getTypeCode());
        if (list.isEmpty()) {
            commonResp.setHead(ResultUtil.error(ErrorMessage.DICT_CODE_IS_NONE,dictValueVo.getTypeCode()));
            return;
        }
        //判断同类型下的值和名字是否唯一，
        if (dictValueMapper.selectOnlyValue(dictValueVo.getTypeCode(), dictValueVo.getValueCode()
                ,dictValueVo.getValueName(),dictValueVo.getValueId()) >= NumberConstant.NO_ONE) {
            commonResp.setHead(ResultUtil.error(ErrorMessage.DICT_VALUE_EXIST));
        } else {
            DictValue dictValue = new DictValue(dictValueVo.getTypeCode(), dictValueVo.getValueName(), dictValueVo.getValueCode());
            dictValue.setValueId(dictValueVo.getValueId());
            dictValue.setValueDesc(dictValueVo.getValueDesc());
            dictValue.setParentId(dictValueVo.getParentId());
            if(StringUtils.isNotEmpty(dictValueVo.getParentId())){
                DictValue parent = dictValueMapper.selectByPrimaryKey(dictValueVo.getParentId());
                if(parent==null){
                    //此处业务规则修改为删除直接删除并把父级菜单置空
                    dictValue.setParentId("");
                }
            }
            if (dictValueMapper.updateByPrimaryKeySelective(dictValue) == NumberConstant.NO_ONE) {
                commonResp.setHead(ResultUtil.success());
            } else {
                commonResp.setHead(ResultUtil.error(ErrorMessage.SQL_EXECUTE_FAILED));
            }
        }
    }

    /**
     * @description: delete json type by type code
     * @param typeCode type code
     * @param commonResp http response json object
     * @return: void
     * @author: liuyi
     * @time: 2021/6/7 16:30
     */
    @Override
    public void delDictType(String typeCode, CommonResp<String> commonResp) {
        int num;
        num = dictTypeMapper.deleteByTypeCode(typeCode);
        dictValueMapper.selectByExample(typeCode,null,null).forEach(dictValue ->
                dictValueMapper.deleteByPrimaryKey(dictValue.getValueId()));
        if (num < NumberConstant.NO_ONE) {
            commonResp.setHead(ResultUtil.error(ErrorMessage.SQL_EXECUTE_FAILED));
        } else {
            commonResp.setHead(ResultUtil.success());
            commonResp.setBody(MessageFormat.format(ErrorMessage.DELETE_COUNT.getMessage(),String.valueOf(num)));
        }
    }

    /**
     * @description:  delete dict value by value id
     * @param valueId dict value id
     * @param commonResp http response json object
     * @return: void
     * @author: liuyi
     * @time: 2021/6/7 16:31
     */
    @Override
    public void delDictValue(String valueId, CommonResp<String> commonResp) {
        int num = dictValueMapper.deleteByPrimaryKey(valueId);
        commonResp.setHead(ResultUtil.success());
        commonResp.setBody(MessageFormat.format(ErrorMessage.DELETE_COUNT.getMessage(),String.valueOf(num)));
    }

    /**
     * @description: query dict value by type code
     * @param typeCode
     * @param typeCode type code
     * @return: java.util.List<com.snbc.bbpf.system.db.common.vo.DictValueVo>
     * @author: liuyi
     * @time: 2021/6/7 16:32
     */
    @Override
    public List<DictValueVo> getDictValueList(String typeCode) {
        List<DictValue> list = dictValueMapper.selectByTypeLike(typeCode,null);
        return getDictValueVos(list);
    }

    /***
      * @Description:    根据typeCode，parentId获取value
      * @Author:         WangSong
      * @param :         typeCode
      * @param :         parentId
      * @return:         java.util.List<com.snbc.bbpf.system.db.common.vo.DictValueVo>
      * @CreateDate:     2022/11/25 11:30
      * @UpdateDate:     2022/11/25 11:30
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
    */
    @Override
    public List<DictValueVo> getDictValueByTypeCodeParentId(String typeCode, String parentId) {
        List<DictValue> list = dictValueMapper.selectByTypeLike(typeCode,parentId);
        return getDictValueVos(list);
    }

    /**
     * @description:  query dict value ........................
     * @param typeCode type code
     * @param typeValueName type value name
     * @return: java.util.List<com.snbc.bbpf.system.db.common.vo.DictValueVo>
     * @author: liuyi
     * @time: 2021/6/7 16:33
     */
    @Override
    public List<DictValueVo> getByTypeCodeAndValueName(String typeCode, String typeValueName) {
        List<DictValue> list = dictValueMapper.selectByExample(typeCode,null,typeValueName);
        return getDictValueVos(list);
    }

    /**
     * @description: query dict value,add dict parent description
     * @param list dict value list
     * @return: java.util.List<com.snbc.bbpf.system.db.common.vo.DictValueVo>
     * @author: liuyi
     * @time: 2021/6/7 16:35
     */
    private List<DictValueVo> getDictValueVos(List<DictValue> list) {
        List<DictValueVo> dictValueVos = new ArrayList<>();
        list.stream().forEach(dictValue -> {
            DictValue parent = null;
            if(StringUtils.isNotEmpty(dictValue.getParentId())) {
                parent = dictValueMapper.selectByPrimaryKey(dictValue.getParentId());
            }
            DictValueVo dictValueVo = new DictValueVo();
            dictValueVo.setValueId(dictValue.getValueId());
            dictValueVo.setTypeCode(dictValue.getTypeCode());
            dictValueVo.setValueCode(dictValue.getValueCode());
            dictValueVo.setValueName(dictValue.getValueName());
            dictValueVo.setValueDesc(dictValue.getValueDesc());
            dictValueVo.setParentId(dictValue.getParentId());
            if (parent!=null){
                dictValueVo.setParentValueCode(parent.getValueName());
            }
            dictValueVos.add(dictValueVo);
        });
        return dictValueVos;
    }

    /**
     * @description: query all dict type
     * @return: java.util.List<com.snbc.bbpf.system.db.common.entity.DictType>
     * @author: liuyi
     * @time: 2021/6/7 16:37
     */
    @Override
    public List<DictType> quertAllDictTypeCode() {
        return dictTypeMapper.quertAllDictType();
    }

    /**
     * @description: query dict type by like type name
     * @param typeName type name
     * @return: java.util.List<com.snbc.bbpf.system.db.common.entity.DictType>
     * @author: liuyi
     * @time: 2021/6/7 16:38
     */
    @Override
    public List<DictType> getDictTypeByName(String typeName) {
        return dictTypeMapper.queryDictTypeByName(typeName);
    }

    /**
     * @description:  you guess .................
     * @param typeCode you guess .........
     * @param valueCode you guess ...........
     * @return: java.util.List<com.snbc.bbpf.system.db.common.entity.DictValue>
     * @author: liuyi
     * @time: 2021/6/7 16:40
     */
    @Override
    public List<DictValue> selectByTypeCodeAndValueCode(String typeCode, String valueCode) {
        return dictValueMapper.selectByExample(typeCode,valueCode,null);
    }

    /**
     * @description: query dict value
     * @param valueIds value id list
     * @return: java.util.List<com.snbc.bbpf.system.db.common.entity.DictValue>
     * @author: liuyi
     * @time: 2021/6/7 16:39
     */
    @Override
    public List<DictValue> selectByValueIds(List<String> valueIds) {
        return dictValueMapper.selectByValueIds(valueIds);
    }

    /**
     * @description: query dict value
     * @param typeCode type code
     * @param valueCode value code
     * @return: com.snbc.bbpf.system.db.common.entity.DictValue
     * @author: liuyi
     * @time: 2021/6/7 16:38
     */
    @Override
    public DictValueVo getDictByTypeValueCode(String typeCode, String valueCode) {
        List<DictValue> list = dictValueMapper.selectByExample(typeCode, valueCode, null);
        if(list.isEmpty()){
            return null;
        }else if(list.size()==1){
            return getDictValueVos(list).get(0);
        }else {
            log.error("getDictByTypeValueCode has repeated data typeCode={},valueCode={}",typeCode,valueCode);
            return null;
        }
    }

    @Override
    public Map<String, List<DictValueDto>> getMultipleDictValues(List<String> dictTypeCodes) {
        List<DictValueDto> dictValueDtoList = dictValueMapper.getMultipleDictValues(dictTypeCodes);
        return dictValueDtoList.stream().collect(Collectors.groupingBy(DictValueDto::getTypeCode));
    }
}
