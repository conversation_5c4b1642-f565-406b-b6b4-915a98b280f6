/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.mapper;

import com.snbc.bbpf.system.db.common.entity.RoleOrg;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName: UserRoleMapper
 * 用户组织机构mapper
 * @module: si-bbpf-system
 * @Author: wjc1
 * @date: 2021/5/19 18:19
 */
@Mapper
public interface RoleOrgMapper {

    int insert(@Param("roleOrgList") List<RoleOrg> roleOrgList);
    /**
     * 根据用户id删除用户组织机构
     *
     * @param roleId 用户id
     */
    void deleteByRoleId(String roleId);

    /**
     * 根据orgId查询角色，验证能否删除组织机构
     * @param orgId
     */
    int selectRoleByOrgId(String orgId);
}
