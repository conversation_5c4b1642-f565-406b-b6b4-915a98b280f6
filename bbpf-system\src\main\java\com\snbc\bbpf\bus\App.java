/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.snbc.bbpf.component.gray.config.CustomLoadBalancerConfiguration;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.loadbalancer.annotation.LoadBalancerClients;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * @ClassName: App.java
 * @Description: 系统管理启动类
 * @module:
 * @Author: 山东新北洋信息技术股份有限公司
 * @date: 2021/5/11 13:21
 * copyright 2020 SNBC. All rights reserver
 */
@SpringBootApplication
@EnableApolloConfig
@EnableAsync
@EnableFeignClients(basePackages = "com.snbc.bbpf.*")
@MapperScan(basePackages = "com.snbc.bbpf.system.db.common.mapper")
@LoadBalancerClients(defaultConfiguration = CustomLoadBalancerConfiguration.class)
public class App {
    public static void main(String[] args) {
        SpringApplication.run(App.class, args);
    }
}
