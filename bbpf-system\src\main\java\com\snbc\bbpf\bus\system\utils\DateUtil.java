package com.snbc.bbpf.bus.system.utils;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @ClassName: DateUtil
 * @Description: 时间工具类
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/9/8
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
public class DateUtil {
    /***
      * @Description:    获取N天前的日期
      * @Author:         wangsong
      * @param :         number
      * @CreateDate:     2021/9/8 13:08
      * @UpdateDate:     2021/9/8 13:08
      * @return :        java.time.LocalDateTime
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    public static String getExpirationTime(long number) {
        return LocalDate.now().plusDays(number+1L).toString()+" 00:00:00";
    }

    /***
     * @Description:    计算两个时间点的天数差
     * @Author:         wangsong
     * @param :         dt1
     * @param :         dt2
     * @CreateDate:     2021/9/7 18:35
     * @UpdateDate:     2021/9/7 18:35
     * @return :        int
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    public static int calculateDateDiff(LocalDateTime dt1,LocalDateTime dt2){
        //计算时间差不算开始当天所以差值天数+1
        return (int)(dt2.toLocalDate().toEpochDay()
                - dt1.toLocalDate().toEpochDay()) + 1;
    }
}
