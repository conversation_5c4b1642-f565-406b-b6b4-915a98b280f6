/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service.impl;

import com.google.gson.Gson;
import com.snbc.bbpf.bus.system.constans.CommonConstant;
import com.snbc.bbpf.bus.system.constans.Constant;
import com.snbc.bbpf.bus.system.constans.NumberConstant;
import com.snbc.bbpf.bus.system.enums.UserLockStatusEnum;
import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.mapstruct.ClassConvertorMapper;
import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.bus.system.service.UserImportService;
import com.snbc.bbpf.bus.system.utils.BossDES3Util;
import com.snbc.bbpf.bus.system.utils.CheckOrgPermission;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.bus.system.utils.ImportUserUtil;
import com.snbc.bbpf.bus.system.utils.JasyptEncryptUtil;
import com.snbc.bbpf.bus.system.utils.RedisCheckCodeLoginFlag;
import com.snbc.bbpf.bus.system.utils.RedisIdGeneratorUtil;
import com.snbc.bbpf.bus.system.utils.SendMsgUtil;
import com.snbc.bbpf.system.db.common.dto.OrgItemDto;
import com.snbc.bbpf.system.db.common.dto.UserDto;
import com.snbc.bbpf.system.db.common.entity.User;
import com.snbc.bbpf.system.db.common.entity.UserOrg;
import com.snbc.bbpf.system.db.common.entity.UserRole;
import com.snbc.bbpf.system.db.common.mapper.OrgMapper;
import com.snbc.bbpf.system.db.common.mapper.UserMapper;
import com.snbc.bbpf.system.db.common.mapper.UserOptMapper;
import com.snbc.bbpf.system.db.common.mapper.UserOrgMapper;
import com.snbc.bbpf.system.db.common.mapper.UserRoleMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Pattern;

/**
 * @ClassName: UserImportServiceImpl
 * @Description: 用户新增用户和导入用户处理
 * @module: si-bbpf-system
 * @Author: wangsong
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class UserImportServiceImpl implements UserImportService {
    private static final String SPECIAL_SYMBOLS = "~!@#$%^&*()+=";
    private static final String NUM = "01234567";
    private static final String RANDOM = "random";
    private String randomPwd = "";
    @Autowired
    private UserMapper userMapper;
    /**
     * 用户操作数据库层，查询判断和插入数据集合
     */
    @Autowired
    private UserOptMapper userOptMapper;
    @Autowired
    private UserOrgMapper userOrgMapper;
    @Autowired
    private UserRoleMapper userRoleMapper;
    @Autowired
    private OrgMapper orgMapper;
    @Autowired
    private RedisCheckCodeLoginFlag redisCheckCodeLoginFlag;

    @Value("${bbpf.system.default.pwd}")
    private String defaultSecret;
    /**
     * default:默认密码  random:随机密码
     */
    @Value("${bbpf.system.generatepwd.type:random}")
    private String generateSecretType;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private CheckOrgPermission checkOrgPermission;
    /***
     * @Description:    更新用户信息
     * 工号重复判断，手机号唯一性，当修改或新增的时候对用户工号唯一性进行校验
     * 1，校验手机号，跟工号唯一性,
     * 2，将DTO转成PO
     * 3，插入/更新数据库
     * 4，更新用户的组织机构跟角色信息
     * @Author:         WJC
     * @param :         userId
     * @CreateDate:     2021/5/15 17:18
     * @UpdateDate:     2021/5/15 17:18
     * @UpdateUser:     LJB 问题太多进行重构
     * @return :        com.snbc.bbpf.system.db.common.dto.UserDto
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    @Transactional(rollbackFor = BusinessException.class)
    public String saveUser(UserDto userDto) {
        //20230620 wjc:超管角色不受任何限制,
        // getCreateUserId 当时登录人，校验选中的组织机构是否是本人数据权限配置的组织机构
        //对输入参数先进行判断
        if (StringUtils.isNotBlank(userDto.getBelongOrgIds())) {
            log.debug("user={}  belongOrgIds={}",userDto.getUserName(),userDto.getBelongOrgIds());
            //wjc 20230621 当时登录人，校验选中的组织机构是否是本人数据权限配置的组织机构
            checkOrgPermission.checkOrg(userDto.getCreateUserId(),
                    Arrays.asList(userDto.getBelongOrgIds().split(Pattern.quote(CommonConstant.JWT_FILTER_CHAR)).clone()));
        }
        // 公共校验
        extracted(userDto);
        // 邮箱唯一
        if (StringUtils.isNotBlank(userDto.getEmail())){
            // 邮箱不能重复 20230606
            ErrorMessage.USER_EMAIL_EXIST.assertEquals(userOptMapper.selectOnlyEmail(userDto.getEmail(), userDto.getUserId()),NumberConstant.NO_ZERO);
        }
        //判断手机号是否唯一，
        ErrorMessage.USER_PHONE_EXIST.assertEquals(userOptMapper.selectOnlyPhone(userDto.getPhone(),userDto.getUserId()),NumberConstant.NO_ZERO);
        //判断工号是否唯一，
        ErrorMessage.USER_JOB_NUMBER_EXIST.assertEquals(userOptMapper.selectOnlyJobNumber(userDto.getJobNumber(), userDto.getUserId()),NumberConstant.NO_ZERO);
        return userUpdateInfo(userDto);
    }

    @Override
    @Transactional(rollbackFor = BusinessException.class)
    public void updateUser(UserDto userDto) {
        extracted(userDto);
        //验证userId 是否存在用户
        User exsit=userMapper.selectByPrimaryKey(userDto.getUserId());
        ErrorMessage.USER_NOT_EXIST.assertNotNull(exsit);
        //修改手机号码
        if (!exsit.getPhone().equals(userDto.getPhone())){
            List<String> listRole=userRoleMapper.selectRoleByUserId(CurrentUser.getUserId());
            //判断是否超级管理员
            ErrorMessage.NO_PERMISSION_UPDATE.assertEquals(!listRole.isEmpty()&&listRole.contains("-1"),true);
            //判断手机号是否唯一，
            ErrorMessage.USER_PHONE_EXIST.assertEquals(userOptMapper.selectOnlyPhone(userDto.getPhone(),null),NumberConstant.NO_ZERO);

        }
        //判断工号是否唯一，
        if (!exsit.getJobNumber().equals(userDto.getJobNumber())){
            ErrorMessage.USER_JOB_NUMBER_EXIST.assertEquals(userOptMapper.selectOnlyJobNumber(userDto.getJobNumber(), null),NumberConstant.NO_ZERO);
        }
        // 邮箱唯一
        if (StringUtils.isNotBlank(userDto.getEmail())&&!userDto.getEmail().equals(exsit.getEmail())){
            // 邮箱不能重复 20230606
            ErrorMessage.USER_EMAIL_EXIST.assertEquals(userOptMapper.selectOnlyEmail(userDto.getEmail(), null),NumberConstant.NO_ZERO);
        }
        userUpdateInfo(userDto);
        //启用用户时解除登录锁定
        if (userDto.getHasLock() == NumberConstant.NO_ONE) {
            //解锁时清除用户登录失败五次的锁定状态
            userMapper.loginUnLock(userDto.getUserId());
        }
    }
    // 检验公共的入参
    private void extracted(UserDto userDto) {
        userDto.setPhone(BossDES3Util.encrypt(userDto.getPhone()));
        // 验证创建人是否存在
        ErrorMessage.USER_INVALID.assertNotNull(userMapper.selectByPrimaryKey(userDto.getCreateUserId()));
        // 验证 状态 是否为0 或1
        ErrorMessage.USER_STATUS_INVALID.assertNotNull(UserLockStatusEnum.getStatusName(userDto.getHasLock()));

        // 验证 邮箱长度及格式
        if (StringUtils.isNotBlank(userDto.getEmail())){
            ErrorMessage.USER_EMAIL_INVALID.assertNotNull(ImportUserUtil.match(userDto.getEmail(), CommonConstant.USER_EMAIL_MATCH));
            ErrorMessage.USER_EMAIL_LENGTH_INVALID.assertEquals(userDto.getEmail().length()>NumberConstant.NO_SIXTY_FIVE,false);
        }

    }

    /**
     * 用户初始化密码，现用户导入用户和新增用户
     * 字符串
     *
     * <AUTHOR>
     */
    private String userInitPd() {
        String password = JasyptEncryptUtil.desencrypt(defaultSecret, CommonConstant.KEY_IV);
        if (RANDOM.equals(generateSecretType)) {
            //生成8位随机字符+数字+特殊符号的密码
            StringBuilder randomCharacter = new StringBuilder(RandomStringUtils.randomAlphanumeric(NumberConstant.NO_SEVEN));
            String randomSymbol = RandomStringUtils.random(NumberConstant.NO_ONE, SPECIAL_SYMBOLS.toCharArray());
            int randomNum = Integer.parseInt(RandomStringUtils.random(NumberConstant.NO_ONE, NUM));
            password = randomPwd =  randomCharacter.insert(randomNum, randomSymbol).toString();
        }
        return JasyptEncryptUtil.encrypt(password, CommonConstant.KEY_IV);
    }

    /***
     * @Description:    将用户创建提取出来，
     * @Author:         liangjb
     * @param :         userDto  创建用户信息
     * @CreateDate:     2021/6/8 10:24
     * @UpdateDate:     2021/6/8 10:24
     * @return :        java.util.List<com.snbc.bbpf.system.db.common.vo.UserVo>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    private String userUpdateInfo(UserDto userDto) {
        //判断姓名是否重复
        ErrorMessage.USER_EXIST.assertEquals(userOptMapper.selectOnlyUserName(userDto.getUserName(), userDto.getUserId()), NumberConstant.NO_ZERO);
        // 请求入参转换
        User user = ClassConvertorMapper.INSTANCE.covertUser(userDto);
        String userId = user.getUserId();
        // userId 不为空，则为更新操作
        if (StringUtils.isNotBlank(userId)) {
            user.setUpdateTime(LocalDateTime.now());
            userMapper.updateByPrimaryKeySelective(user);
            if (UserLockStatusEnum.TLOCK.getStatus().equals(user.getHasLock())) {
                redisCheckCodeLoginFlag.delKey(CommonConstant.USER_LOGIN_STATUS,
                        CommonConstant.ZERO + CommonConstant.JOINER + userId);
            }
            //删除 t_user_org，t_user_role
            userOrgMapper.deleteByUserId(userId);
            userRoleMapper.deleteByUserId(userId);
        } else {
            user.setUserPwd(userInitPd());
            user.setCreateTime(LocalDateTime.now());
            user.setUpdateTime(LocalDateTime.now());
            user.setUpdatePwdTime(LocalDateTime.now());
            userId = RedisIdGeneratorUtil.nextId();
            user.setUserId(userId);
            user.setUserStatus(NumberConstant.NO_FIVE);
            user.setHasResign(NumberConstant.NO_ONE);
            userMapper.insertSelective(user);
        }
        // 保存到t_user_org
        insertUserOrg(userId, userDto.getBelongOrgIds());
        // 保存到t_user_role
        insertUserRole(userId, userDto.getBelongRoleIds());
        //下发密码
        sendPwdMsg(user);
        return userId;
    }

    /**
     * 下发短信-密码
     * @param user
     */
    private void sendPwdMsg(User user) {
        //下发随机密码
        if (!randomPwd.isEmpty() && RANDOM.equals(generateSecretType)) {
            Map<String,String> templateParamMap=new HashMap<>();
            templateParamMap.put("userName", user.getUserName());
            templateParamMap.put("jobNumber", user.getJobNumber());
            templateParamMap.put("secretCode", randomPwd);
            String templateParam = new Gson().toJson(templateParamMap);
            String phone = BossDES3Util.decrypt(user.getPhone());
            CommonResp commonResp = SendMsgUtil.sendGeneralMsg(phone, user.getUserId(), templateParam);
            if (!ErrorMessage.SUCCESS.getCode().equals(commonResp.getHead().getCode())){
                log.error("send Random Password Error,userId:{}",user.getUserId());
                throw new BusinessException(ErrorMessage.FAILED.getMessage(),ErrorMessage.FAILED.getCode());
            }
        }
    }

    /***
     * @Description:    获取用户角色信息并转成LIST
     * @Author:         liangjb
     * @param :         userId  创建用户信息
     * @param :         roleIds  插入用户角色信息
     * @param :         insert  是否插入数据库
     * @CreateDate:     2021/6/8 10:24
     * @UpdateDate:     2021/6/8 10:24
     * @return :        java.util.List<com.snbc.bbpf.system.db.common.vo.UserVo>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    private void insertUserRole(String userId, String roleIds) {
        List<UserRole> result = new ArrayList<>();
        if (StringUtils.isNotBlank(roleIds)) {
            List<String> roleIdList = Arrays.asList(roleIds.split(Pattern.quote(CommonConstant.JWT_FILTER_CHAR)));
            roleIdList.forEach(roleId -> {
                UserRole userRole = UserRole.builder().userId(userId).roleId(roleId)
                        .id(UUID.randomUUID().toString()).build();
                result.add(userRole);
            });
            userOptMapper.addUserRoles(result);
            //20230630 wjc 删除redis中用户-角色的关系
            String key = Constant.USERID_ROLE_ID_MAPPING+userId;
            if(Boolean.TRUE.equals(redisTemplate.hasKey(key))){
                redisTemplate.delete(key);
            }
        }
    }
    /***
     * @Description:    获取用户部门信息并转成LIST
     * @Author:         liangjb
     * @param :         userId  创建用户信息
     * @param :         roleIds  插入用户部门信息
     * @CreateDate:     2021/6/8 10:24
     * @UpdateDate:     2021/6/8 10:24
     * @return :        java.util.List<com.snbc.bbpf.system.db.common.vo.UserVo>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    private void insertUserOrg(String userId, String orgIds) {
        List<UserOrg> result = new ArrayList<>();
        if (StringUtils.isNotBlank(orgIds)) {
            //删除缓存
            redisTemplate.delete(Constant.USER_DATA_AUTH_ORG_AND_SUB_ORG);
            redisTemplate.delete(Constant.USER_DATA_AUTH_ORG);
            // 删除 ：组织机构数据权限
            redisTemplate.delete(Constant.ORG_DATA_AUTH_ORG_AND_SUB_ORG);
            redisTemplate.delete(Constant.ORG_DATA_AUTH_ORG_ORG);
            redisTemplate.delete(Constant.ORG_DATA_AUTH_ORG_AND_CREATE_ORG);
            List<String> orgIdList = Arrays.asList(orgIds.split(Pattern.quote(CommonConstant.JWT_FILTER_CHAR)));
            orgIdList.forEach(orgId -> {
                UserOrg userOrg = UserOrg.builder().userId(userId).orgId(orgId)
                        .id(UUID.randomUUID().toString()).build();
                result.add(userOrg);
            });
            userOptMapper.addUserOrgs(result);
        }
    }



    /***
     * @Description:    初始化组织机构列表，并映射成组织机构/组织机构的NAMEKE，
     * @Author:         liangjb
     * @param :         createUserId  创建用户ID
     * @param :         userDto  创建用户信息
     * @CreateDate:     2021/6/8 10:24
     * @UpdateDate:     2021/6/8 10:24
     * @return :        java.util.List<com.snbc.bbpf.system.db.common.vo.UserVo>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public Map<String,String> initAllOrgLst() {
        List <OrgItemDto> orgList = orgMapper.getAllEnableOrgItems();
        Map<String,String> orgNameKeyLst=  new HashMap<>();
        if(!CollectionUtils.isEmpty(orgList)) {
            Map<String,String> orgKeyNameLst=  new HashMap<>();
            orgList.forEach(o->{
                orgKeyNameLst.put(o.getOrgId(),o.getOrgName());
                String nameKey=getNameKey(o.getOrgIdPath(),orgKeyNameLst);
                //将路径的ID取出，只有不为空并且能匹配到中文名字表示有效，其它的抛出
                if(StringUtils.isNotBlank(nameKey)){
                    orgNameKeyLst.put(nameKey.substring(CommonConstant.ONE),o.getOrgId());
                }
            });
        }
        return orgNameKeyLst;
    }

    /***
     * @Description:    初始化组织机构列表，并映射成组织机构/组织机构的NAMEKE，
     * @Author:         liangjb
     * @param :         pathItem  路径映射
     * @param :         orgKeyNameLst  用户Name
     * @CreateDate:     2021/6/8 10:24
     * @UpdateDate:     2021/6/8 10:24
     * @return :        java.util.List<com.snbc.bbpf.system.db.common.vo.UserVo>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    private static String getNameKey(String pathItem,Map<String,String> orgKeyNameLst){
        String[] pathList= pathItem.split(CommonConstant.PERMISSION_FILTER_CHAR);
        //将路径的ID取出，只有不为空并且能匹配到中文名字表示有效，其它的抛出
        StringBuilder nameKey=new  StringBuilder() ;
        for(String item :pathList){
            if(!"".equals(item)){
                if(orgKeyNameLst.containsKey(item)){
                    nameKey.append(CommonConstant.PERMISSION_FILTER_CHAR).append(orgKeyNameLst.get(item));
                }else {
                    return "";
                }
            }
        }
        return nameKey.toString();
    }
}
