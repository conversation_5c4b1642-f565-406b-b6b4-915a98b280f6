/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.mapper;

import com.snbc.bbpf.system.db.common.entity.UserProfile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 用户配置Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-05
 */
@Mapper
public interface UserProfileMapper {

    /**
     * 查询用户配置
     *
     * @param proflieCode 用户配置编码
     * @return 用户配置
     */
    UserProfile selectUserProfileByProfileCode(@Param("profileCode") String proflieCode, @Param("userId") String userId);
    /**
     * 查询用户配置列表
     * 
     * @param userProfile 用户配置
     * @return 用户配置集合
     */
    List<UserProfile> selectUserProfileList(UserProfile userProfile);

    /**
     * 新增用户配置
     * 
     * @param userProfile 用户配置
     * @return 受影响行数
     */
    int insertUserProfile(UserProfile userProfile);

    /**
     * 修改用户配置
     * 
     * @param userProfile 用户配置
     * @return 受影响行数
     */
    int updateUserProfile(UserProfile userProfile);

    /**
     * 删除用户配置
     * 
     * @param profileId 用户配置主键
     * @return 受影响行数
     */
    int deleteUserProfileByprofileId(String profileId);

    /**
     * 批量删除用户配置
     * 
     * @param profileIds 需要删除的数据主键集合
     * @return 受影响行数
     */
    int deleteUserProfileByprofileIds(String[] profileIds);

    List<Map<String,Object>> getMyView(String userId, Integer moduleType, String tenantCode);

    int selectCountByProfileName(@Param(value = "profileName") String profileName,
                                 @Param(value = "userId") String userId,
                                 @Param(value = "id") String id);
}
