/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.config;

import com.snbc.bbpf.component.security.interceptor.mybatis.MybatisLikeSqlInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Configuration;

import java.util.List;
/**
 * sql注入拦截配置 MybatisLikeSqlInterceptorConfig
 *
 * <AUTHOR>
 * @date 2023-01-16
 */
@Configuration
@Slf4j
public class MybatisLikeSqlInterceptorConfig implements ApplicationRunner {

    @Autowired
    private List<SqlSessionFactory> sqlSessionFactoryList;
    public MybatisLikeSqlInterceptorConfig(){
        log.debug("init SqlLikeConfig");
    }

    private String addMybatisLikeSqlInterceptor() {
        //sqllike拦截器
        MybatisLikeSqlInterceptor interceptor = new MybatisLikeSqlInterceptor();
        for (SqlSessionFactory sqlSessionFactory : sqlSessionFactoryList) {
            org.apache.ibatis.session.Configuration configuration = sqlSessionFactory.getConfiguration();
            if (!containsInterceptor(configuration, interceptor)) {
                configuration.addInterceptor(interceptor);
                log.debug("addMybatisLikeSqlInterceptor success");
            }
        }
        return "interceptor";
    }

    /**
     * mysqllike 拦截器
     * @param configuration
     * @param interceptor
     * @return
     */
    private boolean containsInterceptor(org.apache.ibatis.session.Configuration configuration, Interceptor interceptor) {
        try {
            // getInterceptors since 3.2.2
            return configuration.getInterceptors().contains(interceptor);
        } catch (Exception e) {
            log.error("error:",e);
            return false;
        }
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        addMybatisLikeSqlInterceptor();
    }
}
