/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.constans;

/**
 * 消息模板
 */
public class MessageTemplateConstant {

    /**
     * 通知人：被调整部门的用户
     */
    public static final String ADJUST_DEPARTMENT1 = "尊敬的 userName 用户，您的所在部门由 oldOrgName 调整至 newOrgName ，请知晓。";
    /**
     * 通知人：被调整部门的用户变更前部门负责人， flag:调离  调入
     */
    public static final String ADJUST_DEPARTMENT2 = "尊敬的 supervisorUserName 用户， userName 成员已flag您的所在部门 adjustOrgName ，请知晓。";
    /**
     * 用户禁用：
     */
    public static final String USER_LOCK = "尊敬的 {0} 用户，您的 {1} 系统登录账号存在风险，已被禁用，如有疑问请拨打系统客服电话 ，请知晓。";
    /**
     * 修改密码：
     */
    public static final String CHANGE_SECRET = "您的短信验证码为：{0}，您正在进行修改密码操作，请勿将验证码泄露给他人。";

    /**
     * 短信登陆
     */
    public static final String USER_LOGIN = "您的验证码为：{0}，您正在使用短信验证码登录，请勿将验证码泄露给他人。";

    /**
     * 修改用户手机号
     */
    public static final String UPDATE_USER_PHONE = "您的验证码是：{0}，您正在修改手机号，请勿将验证码泄露给他人。";

    public static final String SMSCODE = "您的验证码是：{0}，验证码5分钟内有效，请不要把验证码泄露给其他人。";

    /**
     * 角色解绑用户
     */
    public static final String ROLEUNBINDUSER = "尊敬的 {0} 用户， 您的 BOSS管理平台 系统的 {1} 角色已被移除，相关权限已被回收，请知晓。";
    /**
     * 角色绑定用户
     */
    public static final String ROLERELATIONUSER = "尊敬的 {0} 用户，您已被添加 {1} 系统角色 ，可登录系统查看角色相关权限，请知晓。";

    /**
     * 随机密码
     */
    public static final String RANDOM_TEMP = "尊敬的 {0} 用户，您的系统登录账号已开通，账号：{1} 密码：{2} ，请勿泄露给他人。";

    /**
     * 用户禁用
     */
    public static final String DISABLEUSER = "尊敬的 {0} 用户，您的系统登录账号存在风险，已被禁用，请知晓。";

    /**
     * 用户启用
     */
    public static final String ENABLEUSER = "尊敬的 {0} 用户，您的系统登录账号 {1} 已恢复使用，请知晓。";

}
