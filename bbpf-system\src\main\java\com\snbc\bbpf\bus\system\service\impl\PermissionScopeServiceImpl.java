/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service.impl;

import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.bus.system.service.PermissionScopeService;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.system.db.common.entity.PermissionScope;
import com.snbc.bbpf.system.db.common.mapper.PermissionScopeMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * @ClassName: PermissionScopeServiceImpl
 * @Description: 数据权限业务处理实现
 * @module: si-bbpf-system
 * @Author: zhouzheng
 * @date: 2021/5/19
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Service
public class PermissionScopeServiceImpl implements PermissionScopeService {

    @Resource
    private PermissionScopeMapper permissionScopeMapper;
    /**
      * @Description:    新增数据权限逻辑实现
      * @Author:         zhouzheng
      * @param:  [permissionScope]
      * @CreateDate:     2021/5/25 10:34
      * @UpdateDate:     2021/5/25 10:34
      * @return: void
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public void addDataScope(PermissionScope permissionScope) throws BusinessException {
        permissionScope.setScopeId(UUID.randomUUID().toString());
        permissionScope.setCreateUserId(CurrentUser.getUserId());
        permissionScope.setCreateTime(LocalDateTime.now());
        permissionScope.setUpdateTime(LocalDateTime.now());
        PermissionScope permissionScope1=permissionScopeMapper.selectByScopeCode(permissionScope.getScopeCode());
        if(permissionScope1==null) {
            permissionScopeMapper.insertSelective(permissionScope);
        }else{
            throw new BusinessException("数据权限编码重复","991502");
        }
    }
    /**
      * @Description:    更新数据权限逻辑实现
      * @Author:         zhouzheng
      * @param:  [permissionScope]
      * @CreateDate:     2021/5/25 10:35
      * @UpdateDate:     2021/5/25 10:35
      * @return: void
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public void updateDataScope(PermissionScope permissionScope) throws BusinessException {
        permissionScope.setUpdateTime(LocalDateTime.now());
        PermissionScope permissionScope1=permissionScopeMapper.selectByScopeCode(permissionScope.getScopeCode());
        if(permissionScope1!=null&&!permissionScope.getScopeId().equals(permissionScope1.getScopeId())) {
            throw new BusinessException("数据权限编码重复","991502");
        }else {
            permissionScopeMapper.updateByPrimaryKeySelective(permissionScope);
        }
    }
    /**
      * @Description:    查询数据权限明细逻辑实现
      * @Author:         zhouzheng
      * @param:  [scopeId]
      * @CreateDate:     2021/5/25 10:35
      * @UpdateDate:     2021/5/25 10:35
      * @return: com.snbc.bbpf.system.db.common.entity.PermissionScope
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public PermissionScope getDataScopeDetail(String scopeId) throws BusinessException {
        return permissionScopeMapper.selectByPrimaryKey(scopeId);
    }
    /**
      * @Description:    批量删除数据权限逻辑实现
      * @Author:         zhouzheng
      * @param:  [scopeIds]
      * @CreateDate:     2021/5/25 10:36
      * @UpdateDate:     2021/5/25 10:36
      * @return: void
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public void delDataScope(String[] scopeIds) throws BusinessException {
        permissionScopeMapper.deleteByScopeIds(scopeIds);
    }
    /**
      * @Description:    查询数据权限列表逻辑实现
      * @Author:         zhouzheng
      * @param:  [permissionScope]
      * @CreateDate:     2021/5/25 10:36
      * @UpdateDate:     2021/5/25 10:36
      * @return: java.util.List<com.snbc.bbpf.system.db.common.entity.PermissionScope>
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public List<PermissionScope> getDataScopeList(PermissionScope permissionScope) throws BusinessException {
        return permissionScopeMapper.selectbyList(permissionScope);
    }


}
