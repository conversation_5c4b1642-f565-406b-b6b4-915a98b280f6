/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service;

import com.snbc.bbpf.system.db.common.vo.OrgDirectorVo;
import com.snbc.bbpf.system.db.common.vo.TenantClerkVo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * @ClassName: UserExportService
 * @Description: 用户导出业务处理，因之前的类超过34个引用，需要进行分离
 * @module: si-bbpf-system
 * @Author: LJB
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
public interface UserExportService {
    /**
     * 查询当前组织机构下的用户数据
     */
    List<OrgDirectorVo> queryUserList(String orgId);
    /**
     * 查询当前组织机构及子组织机构下的用户数据
     */
    List<TenantClerkVo> userAllList(String orgId);

     /**
     *下载导入模板
     */
    void downloadUserTemplate(HttpServletRequest request, HttpServletResponse response)throws IOException;

}
