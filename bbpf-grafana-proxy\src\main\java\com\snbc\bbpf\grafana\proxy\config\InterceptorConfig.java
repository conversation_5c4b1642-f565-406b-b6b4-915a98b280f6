package com.snbc.bbpf.grafana.proxy.config;

import com.snbc.bbpf.grafana.proxy.interceptor.DashboardResponseInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 拦截器配置类
 * 
 * 配置Spring MVC拦截器，包括：
 * 1. 仪表盘响应拦截器 - 用于动态注入模板变量
 * 2. 其他业务拦截器
 * 
 * 这个配置类是"动态注入模板变量"方案的重要组成部分，
 * 确保拦截器能够正确地在请求处理链中工作。
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2024-12-13
 */
@Configuration
public class InterceptorConfig implements WebMvcConfigurer {

    @Autowired
    private DashboardResponseInterceptor dashboardResponseInterceptor;

    /**
     * 添加拦截器到拦截器注册表
     * 
     * @param registry 拦截器注册表
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册仪表盘响应拦截器
        // 拦截所有Grafana API请求，特别是仪表盘相关的请求
        registry.addInterceptor(dashboardResponseInterceptor)
                .addPathPatterns(
                    "/api/dashboards/**",     // 仪表盘API
                    "/api/dashboard/**",      // 仪表盘API（备用路径）
                    "/d/**",                  // 仪表盘访问路径
                    "/dashboard/**"           // 仪表盘访问路径（备用）
                )
                .excludePathPatterns(
                    "/api/health",            // 健康检查
                    "/api/login",             // 登录接口
                    "/api/logout",            // 登出接口
                    "/public/**",             // 静态资源
                    "/static/**",             // 静态资源
                    "/assets/**"              // 静态资源
                );
        
        // 可以在这里添加其他拦截器
        // registry.addInterceptor(otherInterceptor).addPathPatterns("/**");
    }

    /**
     * 获取拦截器配置信息
     * 
     * @return 配置信息字符串
     */
    public String getInterceptorInfo() {
        return "DashboardResponseInterceptor已配置，用于动态注入模板变量";
    }

    /**
     * 检查拦截器是否已启用
     * 
     * @return true表示已启用，false表示未启用
     */
    public boolean isInterceptorEnabled() {
        return dashboardResponseInterceptor != null;
    }

    /**
     * 获取拦截器统计信息
     * 
     * @return 统计信息
     */
    public String getInterceptorStats() {
        if (dashboardResponseInterceptor != null) {
            return "仪表盘响应拦截器: 已启用";
        } else {
            return "仪表盘响应拦截器: 未启用";
        }
    }
}