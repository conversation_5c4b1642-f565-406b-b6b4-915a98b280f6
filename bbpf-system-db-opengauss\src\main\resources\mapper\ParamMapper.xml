<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snbc.bbpf.system.db.common.mapper.ParamMapper" >
  <resultMap id="BaseResultMap" type="com.snbc.bbpf.system.db.common.entity.Param" >
    <id column="param_id" property="paramId" jdbcType="VARCHAR" />
    <result column="param_name" property="paramName" jdbcType="VARCHAR" />
    <result column="param_code" property="paramCode" jdbcType="VARCHAR" />
    <result column="param_value" property="paramValue" jdbcType="VARCHAR" />
    <result column="param_type_code" property="paramTypeCode" jdbcType="VARCHAR" />
    <result column="param_type_name" property="paramTypeName" jdbcType="VARCHAR" />
    <result column="param_desc" property="paramDesc" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="create_org_id" property="createOrgId" jdbcType="VARCHAR" />
    <result column="create_user_id" property="createUserId" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    param_id, param_name, param_code, param_value, param_type_code, param_type_name, 
    param_desc, create_time, update_time,create_org_id,create_user_id
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from t_param
    where param_id = #{paramId,jdbcType=VARCHAR}
  </select>
  <select id="selectByParamCode" resultMap="BaseResultMap" parameterType="java.lang.String">
    select  <include refid="Base_Column_List" />
    from t_param
    where param_code = #{paramCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from t_param
    where param_id = #{paramId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.snbc.bbpf.system.db.common.entity.Param" >
    insert into t_param (param_id, param_name, param_code, 
      param_value, param_type_code, param_type_name, 
      param_desc, create_time, update_time,create_org_id,create_user_id
      )
    values (#{paramId,jdbcType=VARCHAR}, #{paramName,jdbcType=VARCHAR}, #{paramCode,jdbcType=VARCHAR},
      #{paramValue,jdbcType=VARCHAR}, #{paramTypeCode,jdbcType=VARCHAR}, #{paramTypeName,jdbcType=VARCHAR}, 
      #{paramDesc,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
      #{createOrgId,jdbcType=VARCHAR}, #{createUserId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.snbc.bbpf.system.db.common.entity.Param" >
    insert into t_param
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="paramId != null" >
        param_id,
      </if>
      <if test="paramName != null" >
        param_name,
      </if>
      <if test="paramCode != null" >
        param_code,
      </if>
      <if test="paramValue != null" >
        param_value,
      </if>
      <if test="paramTypeCode != null" >
        param_type_code,
      </if>
      <if test="paramTypeName != null" >
        param_type_name,
      </if>
      <if test="paramDesc != null" >
        param_desc,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="createOrgId != null" >
        create_org_id,
      </if>
      <if test="createUserId != null" >
        create_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="paramId != null" >
        #{paramId,jdbcType=VARCHAR},
      </if>
      <if test="paramName != null" >
        #{paramName,jdbcType=VARCHAR},
      </if>
      <if test="paramCode != null" >
        #{paramCode,jdbcType=VARCHAR},
      </if>
      <if test="paramValue != null" >
        #{paramValue,jdbcType=VARCHAR},
      </if>
      <if test="paramTypeCode != null" >
        #{paramTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="paramTypeName != null" >
        #{paramTypeName,jdbcType=VARCHAR},
      </if>
      <if test="paramDesc != null" >
        #{paramDesc,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOrgId != null" >
        #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null" >
        #{createUserId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.snbc.bbpf.system.db.common.entity.Param" >
    update t_param
    <set >
      <if test="paramName != null" >
        param_name = #{paramName,jdbcType=VARCHAR},
      </if>
      <if test="paramCode != null" >
        param_code = #{paramCode,jdbcType=VARCHAR},
      </if>
      <if test="paramValue != null" >
        param_value = #{paramValue,jdbcType=VARCHAR},
      </if>
      <if test="paramTypeCode != null" >
        param_type_code = #{paramTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="paramTypeName != null" >
        param_type_name = #{paramTypeName,jdbcType=VARCHAR},
      </if>
      <if test="paramDesc != null" >
        param_desc = #{paramDesc,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOrgId != null" >
        create_org_id = #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null" >
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
    </set>
    where param_id = #{paramId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.snbc.bbpf.system.db.common.entity.Param" >
    update t_param
    set param_name = #{paramName,jdbcType=VARCHAR},
      param_code = #{paramCode,jdbcType=VARCHAR},
      param_value = #{paramValue,jdbcType=VARCHAR},
      param_type_code = #{paramTypeCode,jdbcType=VARCHAR},
      param_type_name = #{paramTypeName,jdbcType=VARCHAR},
      param_desc = #{paramDesc,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where param_id = #{paramId,jdbcType=VARCHAR}
  </update>
  <select id="queryParamByMap" resultMap="BaseResultMap" parameterType="map">
  	 select
      param_id, param_name, param_code, param_value, param_type_code,
     (select value_name from t_dict_value where type_code='param_type' and value_code=param_type_name)
      param_type_name,param_desc, create_time, update_time,create_org_id,create_user_id
    from t_param
    where 1=1
    <if test="paramName != null and paramName !=''">
      AND param_name like concat('%',#{paramName,jdbcType=VARCHAR},'%')
    </if>
    <if test="paramTypeName != null and paramTypeName !=''">
      AND param_type_name = #{paramTypeName,jdbcType=VARCHAR}
    </if>
    order by param_type_name desc,update_time desc
  </select>
  <select id="queryParamCode" resultType="string" parameterType="java.lang.String">
      SELECT distinct param_type_name
      FROM t_param
  </select>
  <select id="queryParamByCode" resultMap="BaseResultMap" parameterType="java.lang.String">
    select
        <include refid="Base_Column_List"/>
    from t_param
    where param_code = #{paramCode,jdbcType=VARCHAR}
  </select>
</mapper>