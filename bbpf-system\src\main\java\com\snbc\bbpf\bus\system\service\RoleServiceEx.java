/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service;

import com.snbc.bbpf.system.db.common.vo.RoleUserListVo;
import com.snbc.bbpf.system.db.common.vo.RoleUserPage;

import java.util.List;

/**
 * @ClassName: RoleServiceEx
 * @Description: 角色业务处理 类过多，将两个查询类移出来
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/5/18
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
public interface RoleServiceEx {
    RoleUserListVo userListPage(String roleId, Integer pageNum, Integer pageSize,
                                String queryParam, String status);
    /***
      * @Description:
      * @Author:         wangsong
      * @param :         userId
      * @CreateDate:     2021/5/19 16:18
      * @UpdateDate:     2021/5/19 16:18
      * @return :        List<Map>
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    List<RoleUserPage> getUserByOrg(String orgId);

    List<String> queryRoleIdsByUserId(String userId);

}
