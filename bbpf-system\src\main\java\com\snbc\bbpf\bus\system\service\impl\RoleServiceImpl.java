/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.google.gson.Gson;
import com.snbc.bbpf.bus.system.constans.CommonConstant;
import com.snbc.bbpf.bus.system.constans.Constant;
import com.snbc.bbpf.bus.system.constans.NumberConstant;
import com.snbc.bbpf.bus.system.enums.BusTemplateCodeEnum;
import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.service.RoleService;
import com.snbc.bbpf.bus.system.utils.BossDES3Util;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.bus.system.utils.SendMsgUtil;
import com.snbc.bbpf.system.db.common.entity.Message;
import com.snbc.bbpf.system.db.common.entity.PermissionRole;
import com.snbc.bbpf.system.db.common.entity.Role;
import com.snbc.bbpf.system.db.common.entity.RoleOrg;
import com.snbc.bbpf.system.db.common.entity.User;
import com.snbc.bbpf.system.db.common.entity.UserRole;
import com.snbc.bbpf.system.db.common.mapper.PermissionRoleMapper;
import com.snbc.bbpf.system.db.common.mapper.RoleMapper;
import com.snbc.bbpf.system.db.common.mapper.RoleOrgMapper;
import com.snbc.bbpf.system.db.common.mapper.RolePermissionScopeMapper;
import com.snbc.bbpf.system.db.common.mapper.UserMapper;
import com.snbc.bbpf.system.db.common.mapper.UserRoleMapper;
import com.snbc.bbpf.system.db.common.model.RoleRelationUser;
import com.snbc.bbpf.system.db.common.model.SysRole;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Pattern;

/**
 * @ClassName: RoleServiceImpl
 * @Description: 角色业务层
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/5/18
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Service
@Transactional(rollbackFor = RuntimeException.class)
@Slf4j
public class RoleServiceImpl implements RoleService {

    //自建角色
    private static final Integer SELFBUILTROLE = 1;
    @Autowired
    private RoleMapper roleMapper;
    @Autowired
    private PermissionRoleMapper permissionRoleMapper;
    @Autowired
    private RolePermissionScopeMapper rolePermissionScopeMapper;
    @Autowired
    private UserRoleMapper userRoleMapper;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private RoleOrgMapper roleOrgMapper;

    /***
     * @Description: 获取角色列表
     * @Author: wangsong
     * @CreateDate: 2021/5/19 16:56
     * @UpdateDate: 2021/5/19 16:56
     * @return :        java.util.List<java.util.Map>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     * @param userId
     */
    @Override
    public List<Map<String,String>> getRoleList(String userId) {
        //查询该用户所属的角色列表
        List<String> roleIdList = userRoleMapper.selectRoleByUserId(userId);
        // 包含超级管理员：返回所有角色
        List<Map<String,String>> map = null;
        if (!CollectionUtils.isEmpty(roleIdList) && roleIdList.contains(String.valueOf(NumberConstant.F_NO_ONE))) {
            map = roleMapper.getAllRoleList(userId);
        } else {
            map = roleMapper.getOwnRoleList();
        }
        return map;
    }

    /***
     * @Description: 新增角色
     * @Author: wangsong
     * @param :         sysRole
     * @param :         userId
     * @CreateDate: 2021/6/3 9:49
     * @UpdateDate: 2021/6/3 9:49
     * @return :        void
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public void addRole(SysRole sysRole, String userId) {
        ErrorMessage.ROLE_NAME_ONLY_EXIST.assertEquals(roleMapper.selectOnlyRoleName(sysRole.getRoleName(), ""),NumberConstant.NO_ZERO);
        //转换为数据库role
        Role role = Role.builder().roleDesc(sysRole.getRoleDesc()).roleId(UUID.randomUUID().toString())
                .roleName(sysRole.getRoleName())
                //8位随机数
                .roleCode(RandomStringUtils.random(CommonConstant.EIGHT, true, true))
                // 2：系统角色、1：自建角色
                .createTime(LocalDateTime.now()).createUserId(userId).roleType(NumberConstant.NO_ONE).build();
        roleMapper.insertRole(role);
        //转换为权限、角色数据库对应实体类
        extracted(role.getRoleId(), sysRole);
    }

    /**
     * @param : userIds
     * @param : roleIds
     * @param : status
     * @return :        void
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     * @Description: 角色关联、解绑用户
     * @Author: wangsong
     * @CreateDate: 2021/5/20 11:13
     * @UpdateDate: 2021/5/20 11:13
     */
    @Override
    public void roleRelationUser(RoleRelationUser roleRelationUser) {
        List<UserRole> userRoleList = convArrayToList(roleRelationUser);
        String templateCode = "";
        //角色关联用户
        if (CommonConstant.ONE.toString().equals(roleRelationUser.getStatus())) {
            //用户关联角色  有就更新
            userRoleMapper.insertRelation(coveryUserRole(userRoleList));
            templateCode = BusTemplateCodeEnum.ROLE_BIND_USER.getName();
        }
        //角色解绑用户
        if (CommonConstant.ZERO.toString().equals(roleRelationUser.getStatus())) {
            userRoleMapper.deleteRelation(userRoleList);
            templateCode = BusTemplateCodeEnum.ROLE_UNBIND_USER.getName();
        }
        //删除 用户和角色的redis关系 20230630 wjc
        for (String userId : roleRelationUser.getUserIds()) {
            String key = Constant.USERID_ROLE_ID_MAPPING+userId;
            if(Boolean.TRUE.equals(redisTemplate.hasKey(key))){
                redisTemplate.delete(key);
            }
        }
        //下发系统消息通知
        try {
            RoleServiceImpl roleService = SpringUtil.getBean(RoleServiceImpl.class);
            roleService.sendRoleChangeMessage(roleRelationUser.getUserIds(), roleRelationUser.getRoleIds(),
                    templateCode,CurrentUser.getUserId());
        } catch (Exception e) {
            log.error("Description Failed to deliver the message about role binding to a user", e);
        }
    }

    @Async
    public void sendRoleChangeMessage(String[] userIds,String[] roleIds,String templateCode,String senderId){
        //为每个用户发送角色绑定系统消息
        for (String userId : userIds) {
            String[] userIdArr = {userId};
            //查询用户名称
            User user = userMapper.selectByPrimaryKey(userId);
            //查询绑定的的角色名称
            String roleNames = roleMapper.selectRoleNames(Arrays.asList(roleIds));
            String phone = BossDES3Util.decrypt(user.getPhone());
            String[] receivePhoneArr = {phone};
            //构建消息
            Message message1=Message.builder().receiveNos(userIdArr).receiverPhone(receivePhoneArr)
                    .templateCode(templateCode)
                    .templateParamJsonArr(convertTemplateParamJsonArr(user.getUserName(), roleNames))
                    .msgTitle(BusTemplateCodeEnum.getTitleByCode(templateCode)).senderId(senderId).build();
            //发送系统消息
            SendMsgUtil.sendSysMessage(message1);
            //发送短信消息
            SendMsgUtil.sendShortMessage(message1);
        }
    }
    /**
     * 转换模板参数
     * @param userName
     * @param roleName
     * @return
     */
    private static String[] convertTemplateParamJsonArr(String userName,String roleName){
        Map<String,String> templateParamMap=new HashMap<>();
        templateParamMap.put("userName", userName);
        templateParamMap.put("roleName", roleName);
        return new String[]{new Gson().toJson(templateParamMap)};
    }
    /***
      * @Description:    角色已经关联的话使用数据库数据，未关联代表是新增数据
      * @Author:         wangsong
      * @param :         userRoleList
      * @CreateDate:     2021/6/24 19:15
      * @UpdateDate:     2021/6/24 19:15
      * @return :        java.util.List<com.snbc.bbpf.system.db.common.entity.UserRole>
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    private List<UserRole> coveryUserRole(List<UserRole> userRoleList){
        //查询已经关联的用户角色
        List<UserRole> alreadyUserRoles = userRoleMapper.selectAlreadyUserRole(userRoleList);
        if (null == alreadyUserRoles || alreadyUserRoles.isEmpty()){
            return userRoleList;
        }
        //前端传递数据与数据库做对比，userid roleid相同则删除前端数据，使用数据库存储数据
        //只有使用数据库数据才能实现 用户有这个角色时修改 无此角色进行新增
        //最终需要关联的用户角色
        List<UserRole> finallyUserRoleList = new ArrayList<>();
        List<UserRole> removeRoleList = new ArrayList<>();
        for (UserRole userRole : userRoleList) {
            for (UserRole alreadyUserRole : alreadyUserRoles) {
                if (userRole.getRoleId().equals(alreadyUserRole.getRoleId())
                        && userRole.getUserId().equals(alreadyUserRole.getUserId())) {
                    //已有的用户角色关系使用数据库数据
                    finallyUserRoleList.add(alreadyUserRole);
                    removeRoleList.add(userRole);
                }
            }
        }
        //删除数据库中已与用户绑定的角色关系
        userRoleList.removeAll(removeRoleList);
        //新增的数据添加到最终保存数据中
        finallyUserRoleList.addAll(userRoleList);
        return finallyUserRoleList;
    }

    /**
     * @description: 删除角色信息
     * @param roleId 角色ID
     * @return: CommonResp<List<DictValue>>
     * @author: LiangJB
     * @time: 2021/6/7 16:13
     */
    @Override
    public void delRole(String roleId) {
        Integer roleType = roleMapper.selectRoleDetail(roleId).getRoleType();
        if (roleType.equals(NumberConstant.NO_ZERO) || roleType.equals(NumberConstant.NO_TWO)){
            throw new BusinessException(ErrorMessage.SYS_ROLE_CANNOT_DEL.getMessage(), ErrorMessage.SYS_ROLE_CANNOT_DEL.getCode(), null);
        }
        int userCountByRole = userRoleMapper.selectUserCountByRole(roleId);
        // 大于0 抛异常/绑定则抛出业务异常
        ErrorMessage.ROLE_BOUND_USER_DEL.assertEquals(userCountByRole > 0, false);
        //删除角色
        roleMapper.deleteByPrimaryKey(roleId);
        //删除角色-组织机构关联表
        roleOrgMapper.deleteByRoleId(roleId);
    }

    /***
      * @Description:    重置系统角色
      * @Author:         wangsong
      * @param :         roleId
      * @CreateDate:     2022/1/18 16:53
      * @UpdateDate:     2022/1/18 16:53
      * @return :        void
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public void resetRole(String roleId) {
        Integer roleType = roleMapper.selectRoleDetail(roleId).getRoleType();
        if (roleType.equals(SELFBUILTROLE)){
            throw new BusinessException(ErrorMessage.ROLE_CANNOT_RESET.getMessage(),ErrorMessage.ROLE_CANNOT_RESET.getCode());
        }
        String restSql = roleMapper.getRestSql(roleId);
        roleMapper.resetRole(restSql,roleId);
    }

    @Override
    public List<Map<String, String>> getRoleListBasedOrg(List<String> orgIds) {
        return roleMapper.getRolesByOrgId(orgIds);
    }


    /**
     * @param : userIds
     * @param : roleIds
     * @return :        java.util.List<com.snbc.bbpf.system.db.common.entity.UserRole>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     * @Description: 转换为用户角色关联实体类
     * @Author: wangsong
     * @CreateDate: 2021/5/20 11:26
     * @UpdateDate: 2021/5/20 11:26
     */
    private static List<UserRole> convArrayToList(RoleRelationUser roleRelationUser) {
        List<UserRole> userRoleList = new ArrayList<>();
        Arrays.asList(roleRelationUser.getUserIds()).forEach(userId ->
                Arrays.asList(roleRelationUser.getRoleIds()).forEach(roleId ->
                        userRoleList.add(UserRole.builder().id(UUID.randomUUID().toString())
                                .roleId(roleId)
                                .userId(userId)
                                .build())
                )
        );
        return userRoleList;
    }


    /***
     * @Description: 修改角色
     * @Author: wangsong
     * @param :         sysRole
     * @CreateDate: 2021/5/19 16:57
     * @UpdateDate: 2021/5/19 16:57
     * @return :        void
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public void updateRole(SysRole updateRoleVo) {
        ErrorMessage.ROLE_NAME_ONLY_EXIST.assertEquals(roleMapper.selectOnlyRoleName(updateRoleVo.getRoleName(),
                updateRoleVo.getRoleId()),NumberConstant.NO_ZERO);
        //转换为数据库role
        Role role = Role.builder().roleDesc(updateRoleVo.getRoleDesc())
                .roleName(updateRoleVo.getRoleName())
                .roleId(updateRoleVo.getRoleId())
                .updateTime(LocalDateTime.now())
                .build();
        // 角色等于超管，抛异常
        ErrorMessage.SUPER_ROLE_CANNOT_UPDATE.assertEquals(String.valueOf(NumberConstant.F_NO_ONE).equals(role.getRoleId()),false);
        //修改角色
        roleMapper.updateRole(role);
        //删除角色权限
        permissionRoleMapper.deletePermissionByRoleId(updateRoleVo.getRoleId());
        //删除数据权限
        rolePermissionScopeMapper.deleteSocpeByRoleId(updateRoleVo.getRoleId());
        // 20230704 wjc add, 删除组织机构
        roleOrgMapper.deleteByRoleId(updateRoleVo.getRoleId());
        SysRole sysRole = new SysRole("",updateRoleVo.getRoleName(), updateRoleVo.getRoleDesc(),
                updateRoleVo.getPermissionIds(), updateRoleVo.getBelongOrgIds());
        extracted(role.getRoleId(), sysRole);
        //删除该角色的资源权限
        redisTemplate.opsForHash().delete(Constant.ROLE_RESOURCE_PERMISSION,role.getRoleId());
    }

    /**
     * 操作：角色-权限，角色-数据权限
     * @param roleId
     * @param sysRole
     */
    private void extracted(String roleId, SysRole sysRole) {
        //转换为权限、角色数据库对应实体类
        List<PermissionRole> permissionRoleList = convPermissionRole(sysRole, roleId);
        //重新将角色权限关联
        if (!permissionRoleList.isEmpty()) {
            permissionRoleMapper.insertPermissionRole(permissionRoleList);
        }
        //重新关联 角色-组织机构
        List<String> orgIdList = Arrays.asList(sysRole.getBelongOrgIds().split(Pattern.quote(CommonConstant.JWT_FILTER_CHAR)));
        List<RoleOrg> result = Lists.newArrayList();
        orgIdList.forEach(orgId -> {
            RoleOrg roleOrg = RoleOrg.builder().roleId(roleId).orgId(orgId).id(UUID.randomUUID().toString()).build();
            result.add(roleOrg);
        });
        roleOrgMapper.insert(result);
    }

    /***
     * @Description: 转换为权限、角色关联实体类
     * @Author: wangsong
     * @param :         sysRole
     * @CreateDate: 2021/5/19 17:48
     * @UpdateDate: 2021/5/19 17:48
     * @return :        java.util.List<com.snbc.bbpf.system.db.common.entity.PermissionRole>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    private static List<PermissionRole> convPermissionRole(SysRole sysRole, String roleId) {
        //获取菜单权限id
        List<PermissionRole> permissionRoleList = new ArrayList<>();
        Arrays.asList(sysRole.getPermissionIds()).forEach(permissionId ->
                permissionRoleList.add(PermissionRole.builder().id(UUID.randomUUID().toString())
                        .permissionId(permissionId)
                        .roleId(roleId)
                        .build())
        );
        return permissionRoleList;
    }
}
