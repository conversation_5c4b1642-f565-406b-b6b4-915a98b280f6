/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.mapper;

import com.snbc.bbpf.component.dataauth.annotation.IgnoreAuth;
import com.snbc.bbpf.system.db.common.entity.PermissionScope;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName: PermissionDataMapper
 * @Description: 数据权限持久层
 * @module: bbpf-bus-system
 * @Author: zhouzheng
 * @date: 2021/5/19
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Mapper
public interface PermissionScopeMapper {
    int insertSelective(PermissionScope permissionScope);

    List<PermissionScope> selectbyList(PermissionScope permissionScope);

    @IgnoreAuth
    List<PermissionScope> getDataScopeByUserId(@Param("routingUrl") String routingUrl,@Param("userId") String userId);

    @IgnoreAuth
    PermissionScope selectByPrimaryKey(String scopeId);

    @IgnoreAuth
    PermissionScope selectByScopeCode(String scopeCode);

    int updateByPrimaryKeySelective(PermissionScope permissionScope);

    void deleteByScopeIds(String[] scopeIds);
}
