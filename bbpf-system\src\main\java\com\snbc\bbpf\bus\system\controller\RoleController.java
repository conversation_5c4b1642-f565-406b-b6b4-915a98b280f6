/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.bus.system.service.OrgService;
import com.snbc.bbpf.bus.system.service.PermissionService;
import com.snbc.bbpf.bus.system.service.RoleService;
import com.snbc.bbpf.bus.system.service.RoleServiceEx;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.bus.system.utils.ResultUtil;
import com.snbc.bbpf.buslog.annotations.Buslog;
import com.snbc.bbpf.buslog.annotations.OperationType;
import com.snbc.bbpf.system.db.common.model.RoleRelationUser;
import com.snbc.bbpf.system.db.common.model.SysRole;
import com.snbc.bbpf.system.db.common.vo.OrgVo;
import com.snbc.bbpf.system.db.common.vo.RolePermissionVo;
import com.snbc.bbpf.system.db.common.vo.RoleUserListVo;
import com.snbc.bbpf.system.db.common.vo.RoleUserPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: RoleController
 * @Description: 角色控制层
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/5/18
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@RestController
@RequestMapping("/console/role")
public class RoleController {

    @Autowired
    private RoleService roleService;
    @Autowired
    private RoleServiceEx roleServiceEx;
    @Autowired
    private OrgService orgService;
    @Autowired
    private PermissionService permissionService;

    /***
     * @Description: 获取角色列表
     * @Author: wangsong
     * @CreateDate: 2021/5/19 16:27
     * @UpdateDate: 2021/5/19 16:27
     * @return :        com.snbc.bbpf.component.config.CommonResp
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @GetMapping("/getRoleList")
    public CommonResp<List<Map<String,String>>> getRoleListPage() {
        List<Map<String,String>> roleList = roleService.getRoleList(CurrentUser.getUserId());
        return CommonResp.<List<Map<String,String>>>builder().head(ResultUtil.success()).body(roleList).build();
    }

    /**
     * 根据用户id获取角色
     * @param userId
     * @return
     */
    @GetMapping(value = "/authority/getRoleIdsByUserId")
    public CommonResp<List<String>> getRoleIdsByUserId(@RequestParam("userId") String userId) {
        CommonResp<List<String>> commonResp = new CommonResp<>();
        List<String> roleIds = roleServiceEx.queryRoleIdsByUserId(userId);
        commonResp.setHead(ResultUtil.success());
        commonResp.setBody(roleIds);
        return commonResp;
    }
    /**
     * @param : sysRole
     * @return :        com.snbc.bbpf.component.config.CommonResp
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     * @Description: 修改角色、权限、数据权限
     *权限命名唯一，新增业务规则
     * update by LJB
     * @Author: wangsong
     * @CreateDate: 2021/5/19 18:51
     * @UpdateDate: 2021/5/19 18:51
     */
    @PutMapping("/updateRole")
    @Buslog(oprationType = OperationType.MODIFY,opration = "modifyRole", target = "roleManage",type = SysRole.class )
    public CommonResp<Object> updateRole(@RequestBody @Validated SysRole sysRole) {
        roleService.updateRole(sysRole);
        return CommonResp.builder().head(ResultUtil.success()).build();
    }

    /***
     * @Description: 新增角色
     * @Author: wangsong
     *权限命名唯一，新增业务规则
     * update by LJB
     * @param :         sysRole
     * @CreateDate: 2021/5/20 11:11
     * @UpdateDate: 2021/5/20 11:11
     * @return :        com.snbc.bbpf.component.config.CommonResp
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @PostMapping("/addRole")
    @Buslog(target = "roleManage",opration = "addRole", zh = "创建角色［${roleName}］",en="Create role [${roleName}]")
    public CommonResp<Object> addRole(@Validated @RequestBody SysRole sysRole) {
        String userId = CurrentUser.getUserId();
        roleService.addRole(sysRole, userId);
        return CommonResp.builder().head(ResultUtil.success()).build();
    }

    /**
     * @param : roleRelationUser
     * @return :        com.snbc.bbpf.component.config.CommonResp
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     * @Description: 关联、解绑角色
     * @Author: wangsong
     * @CreateDate: 2021/5/20 13:25
     * @UpdateDate: 2021/5/20 13:25
     */
    @PutMapping("/roleRelationUser")
    @Buslog(target = "roleManage",opration = "roleAssocUser", zh = "在【${roleName}】角色中${status}用户【${userNames}】"
            ,type = RoleRelationUser.class ,en="${status} user [${userNameList}] to the role [${roleName}]")
    public CommonResp<Object> roleRelationUser(@Validated @RequestBody RoleRelationUser roleRelationUser) {
        roleService.roleRelationUser(roleRelationUser);
        return CommonResp.builder().head(ResultUtil.success()).build();
    }

    /**
     * @param : roleId
     * @return :        com.snbc.bbpf.component.config.CommonResp
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     * @Description: 删除用户
     * @Author: wangsong
     * @CreateDate: 2021/5/20 14:29
     * @UpdateDate: 2021/5/20 14:29
     */
    @Buslog(target = "roleManage",opration = "delRole", zh = "删除角色【${roleName}】",en="Delete role [${roleName}]")
    @DeleteMapping(value = "/delRole",produces = "application/json;charset=UTF-8")
    public CommonResp<Object> delRole(@RequestBody String roleId) throws JsonProcessingException {
        //获取json中roleId的值
        ObjectMapper objectMapper = new ObjectMapper();
        roleId = objectMapper.readTree(roleId).get("roleId").asText();
        roleService.delRole(roleId);
        return CommonResp.builder().head(ResultUtil.success()).build();
    }

    /**
     * @param : roleId
     * @param : pageNum
     * @param : pageSize
     * @param : queryParam
     * @param : status
     * @return :        com.snbc.bbpf.component.config.CommonResp
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     * @Description: 角色管理用户列表
     * @Author: wangsong
     * @CreateDate: 2021/5/20 14:45
     * @UpdateDate: 2021/5/20 14:45
     */
    @GetMapping("/userListPage")
    public CommonResp<RoleUserListVo> userListPage(@RequestParam(required = false) String roleId
            , @RequestParam Integer pageNum
            , @RequestParam Integer pageSize
            , @RequestParam(required = false) String queryParam
            , @RequestParam(required = false) String status) {
        RoleUserListVo roleUserListVo = roleServiceEx.userListPage(roleId, pageNum, pageSize, queryParam, status);
        return CommonResp.<RoleUserListVo>builder().head(ResultUtil.success()).body(roleUserListVo).build();
    }

    /**
      * @Description:    获取角色添加用户时的用户列表
      * @Author:         wangsong
      * @param :         orgId
      * @CreateDate:     2021/5/20 16:45
      * @UpdateDate:     2021/5/20 16:45
      * @return :        com.snbc.bbpf.component.config.CommonResp
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @GetMapping("/getUserByOrg")
    public CommonResp<List<RoleUserPage>> getUserByOrg(@RequestParam String orgId){
        List<RoleUserPage> userByOrg = roleServiceEx.getUserByOrg(orgId);
        return CommonResp.<List<RoleUserPage>>builder().head(ResultUtil.success()).body(userByOrg).build();
    }


    /**
     * @Description:    加载组织机构树
     * @Author:         wangsong
     * @param :         orgLevel
     * @param :         orgId
     * @CreateDate:     2021/5/21 11:19
     * @UpdateDate:     2021/5/21 11:19
     * @return :        com.snbc.bbpf.component.config.CommonResp
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @GetMapping("/getOrgTree")
    public CommonResp<List<OrgVo>> getOrgTree(){
        List<OrgVo> resultList = orgService.orgTree(CurrentUser.getUserId());
        return CommonResp.<List<OrgVo>>builder().head(ResultUtil.success()).body(resultList).build();
    }

    /***
      * @Description:    获取角色信息及用户所有权限及角色权限
      * @Author:         wangsong
      * @CreateDate:     2021/6/1 17:28
      * @UpdateDate:     2024/9/24 15:32
      * @return :        com.snbc.bbpf.component.config.CommonResp
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @GetMapping("/getroleinfo")
    public CommonResp getAllPermission(@RequestParam(required = false) String sysType,
                                       @RequestParam(required = false) String roleId) {
        String userId = CurrentUser.getUserId();
        RolePermissionVo rolePermissionVo = permissionService.getRoleInfo(sysType, userId, roleId);
        return CommonResp.builder().head(ResultUtil.success()).body(rolePermissionVo).build();
    }

    @PutMapping("/resetRole")
    @Buslog(target = "roleManage",opration = "resetRole", zh = "重置角色【${roleName}】已被还原",en="Built-in role [${roleName}] has been restored")
    public CommonResp<Object> resetRole(@RequestBody String roleId) throws JsonProcessingException {
        //获取json中roleId的值
        ObjectMapper objectMapper = new ObjectMapper();
        roleId = objectMapper.readTree(roleId).get("roleId").asText();
        roleService.resetRole(roleId);
        return CommonResp.builder().head(ResultUtil.success()).build();
    }
}
