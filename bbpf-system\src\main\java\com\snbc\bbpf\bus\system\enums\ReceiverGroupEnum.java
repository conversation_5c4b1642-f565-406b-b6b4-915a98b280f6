/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.enums;

/**
 * @ClassName: MessageTypeEnum
 * 消息分组
 * @module: si-bbpf-system
 * @Author: wjc1
 * @date: 2021/5/20 13:32
 */
public enum ReceiverGroupEnum {
    BOSS("boss", "bbpfboss");

    private String status;
    private String statusName;

    ReceiverGroupEnum(String status, String statusName) {
        this.status = status;
        this.statusName = statusName;
    }
    public String getStatus() {
        return status;
    }
    public String getStatusName() {
        return statusName;
    }
}
