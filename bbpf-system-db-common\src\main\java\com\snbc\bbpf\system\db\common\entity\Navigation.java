/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 顶部导航功能对象 t_navigation
 *
 * <AUTHOR>
 * @date 2022-12-27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Navigation {

    /** 主键id */
    private String navigationId;
    /** 导航名称 */
    private String navigationName;
    /** 导航编码 */
    private String navigationCode;
    /** 导航图标 */
    private String navigationIcon;
    /** 导航顺序 */
    private Integer navigationOrder;
    /** 创建时间 */
    private LocalDateTime createTime;
    /** 创建人 */
    private String createUserId;


}
