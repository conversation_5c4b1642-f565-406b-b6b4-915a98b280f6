<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snbc.bbpf.system.db.common.mapper.UserMapper">
    <resultMap id="BaseResultMap" type="com.snbc.bbpf.system.db.common.entity.User">
        <id column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="job_number" jdbcType="VARCHAR" property="jobNumber"/>
        <result column="user_pwd" jdbcType="VARCHAR" property="userPwd"/>
        <result column="has_lock" jdbcType="INTEGER" property="hasLock"/>
        <result column="has_online" jdbcType="INTEGER" property="hasOnline"/>
        <result column="login_time" jdbcType="TIMESTAMP" property="loginTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="user_desc" jdbcType="VARCHAR" property="userDesc"/>
        <result column="avatar" jdbcType="VARCHAR" property="avatar"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="user_status" jdbcType="INTEGER" property="userStatus"/>
        <result column="has_resign" jdbcType="INTEGER" property="hasResign"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="create_org_id" jdbcType="VARCHAR" property="createOrgId"/>
        <result column="userStatusName" jdbcType="VARCHAR" property="userStatusName"/>
        <result column="is_ldap" jdbcType="INTEGER" property="isLdap"/>
        <result column="user_dn" jdbcType="VARCHAR" property="userDn"/>
        <result column="update_pwd_time" jdbcType="TIMESTAMP" property="updatePwdTime"/>
        <result column="before_lock_status" jdbcType="INTEGER" property="beforeLockStatus"/>
        <result column="login_lock_time" jdbcType="TIMESTAMP" property="loginLockTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    user_id, user_name, job_number, user_pwd, has_lock, has_online, login_time, create_time,
    update_time, phone, user_desc, avatar, email, user_status, has_resign, create_user_id,is_ldap,user_dn,
    create_org_id,tdv.value_NAME userStatusName, before_lock_status,login_lock_time
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from t_user tu
        left join (SELECT value_NAME,value_code from t_dict_value where type_code='userWorkStatus') tdv
        on tu.user_status=tdv.value_code
        where user_id = #{userId,jdbcType=VARCHAR}
    </select>
    <update id="updatePwd">
        UPDATE t_user
        SET user_pwd = #{newPwd,jdbcType=VARCHAR},
            update_pwd_time = now()
        WHERE
            user_id = #{userId,jdbcType=VARCHAR}
    </update>
    <select id="selectUserRole4DataRule" resultType="java.util.Map">
        SELECT
            t1.role_id AS roleId,
            t1.role_name AS roleName
        FROM
            t_role t1
        LEFT JOIN t_user_role u ON t1.role_id = u.role_id
        left join t_role_org tro on t1.role_id = tro.role_id
    </select>
    <select id="selectRoleByUserNoDataAuth" parameterType="java.lang.String" resultType="java.util.Map">
        SELECT
            t1.role_id AS roleId,
            t1.role_name AS roleName,
            t1.role_code as roleCode
        FROM
            t_role t1
        LEFT JOIN t_user_role t2 ON t1.role_id = t2.role_id
        WHERE
            t2.user_id = #{userId,jdbcType=VARCHAR}
    </select>

    <select id="selectUserOrg4DataRule" resultType="java.util.Map">
        SELECT
            distinct (t1.org_id) AS orgId,
            t1.org_name AS orgName
        FROM t_org t1
        LEFT JOIN t_user_org u ON t1.org_id = u.org_id
        LEFT JOIN t_org o ON u.org_id = o.org_id
    </select>
    <select id="selectOrgByUserNoDataAuth" parameterType="java.lang.String" resultType="java.util.Map">
        SELECT
            t1.org_id AS orgId,
            t1.org_name AS orgName,
            IFNULL(t1.org_code, '') AS orgCode
        FROM
        t_org t1
        LEFT JOIN t_user_org u ON t1.org_id = u.org_id
        WHERE
        <!--20210727 wjc1 增加 t1.org_id!='0' 不显示根组织机构 -->
        u.user_id = #{userId,jdbcType=VARCHAR} and t1.org_id!='0'
    </select>
    <select id="selectOrgIdListByUserId" parameterType="java.lang.String" resultType="String">
        SELECT
        t1.org_id AS orgId
        FROM
        t_org t1
        LEFT JOIN t_user_org t2 ON t1.org_id = t2.org_id
        WHERE
        <!--20210727 wjc1 增加 t1.org_id!='0' 不显示根组织机构 -->
        t2.user_id = #{userId,jdbcType=VARCHAR} and t1.org_id!='0'
    </select>
    <select id="selectByJobNumber" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_user tu
        left join (SELECT value_NAME,value_code from t_dict_value where type_code='userWorkStatus') tdv
        on tu.user_status=tdv.value_code where job_number=#{jobNumber,jdbcType=VARCHAR}
    </select>
    <select id="selectByPhone" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_user tu
        left join (SELECT value_NAME,value_code from t_dict_value where type_code='userWorkStatus') tdv
        on tu.user_status=tdv.value_code where phone=#{phone,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from t_user  where user_id = #{userId,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="com.snbc.bbpf.system.db.common.entity.User">
    insert into t_user (user_id, user_name, job_number,
      user_pwd, has_lock, has_online,
      login_time, create_time, update_time,
      phone, user_desc, avatar,
      email, user_status, has_resign,
      create_user_id, create_org_id,is_ldap)
    values (#{userId,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, #{jobNumber,jdbcType=VARCHAR},
      #{userPwd,jdbcType=VARCHAR}, #{hasLock,jdbcType=INTEGER}, #{hasOnline,jdbcType=INTEGER},
      #{loginTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
      #{phone,jdbcType=VARCHAR}, #{userDesc,jdbcType=VARCHAR}, #{avatar,jdbcType=VARCHAR},
      #{email,jdbcType=VARCHAR}, #{userStatus,jdbcType=INTEGER}, #{hasResign,jdbcType=INTEGER},
      #{createUserId,jdbcType=VARCHAR}, #{createOrgId,jdbcType=VARCHAR},#{isLdap,jdbcType=INTEGER})
  </insert>
    <insert id="insertSelective" parameterType="com.snbc.bbpf.system.db.common.entity.User">
        insert into t_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                user_id,
            </if>
            <if test="userName != null">
                user_name,
            </if>
            <if test="jobNumber != null">
                job_number,
            </if>
            <if test="userPwd != null">
                user_pwd,
            </if>
            <if test="hasLock != null">
                has_lock,
            </if>
            <if test="hasOnline != null">
                has_online,
            </if>
            <if test="loginTime != null">
                login_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="userDesc != null">
                user_desc,
            </if>
            <if test="avatar != null">
                avatar,
            </if>
            <if test="email != null">
                email,
            </if>
            <if test="userStatus != null">
                user_status,
            </if>
            <if test="hasResign != null">
                has_resign,
            </if>
            <if test="createUserId != null">
                create_user_id,
            </if>
            <if test="createOrgId != null">
                create_org_id,
            </if>
            <if test="isLdap != null">
                is_ldap,
            </if>
            <if test="userDn != null">
                user_dn,
            </if>
            <if test="updatePwdTime != null">
                update_pwd_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="userName != null">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="jobNumber != null">
                #{jobNumber,jdbcType=VARCHAR},
            </if>
            <if test="userPwd != null">
                #{userPwd,jdbcType=VARCHAR},
            </if>
            <if test="hasLock != null">
                #{hasLock,jdbcType=INTEGER},
            </if>
            <if test="hasOnline != null">
                #{hasOnline,jdbcType=INTEGER},
            </if>
            <if test="loginTime != null">
                #{loginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="userDesc != null">
                #{userDesc,jdbcType=VARCHAR},
            </if>
            <if test="avatar != null">
                #{avatar,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="userStatus != null">
                #{userStatus,jdbcType=INTEGER},
            </if>
            <if test="hasResign != null">
                #{hasResign,jdbcType=INTEGER},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=VARCHAR},
            </if>
            <if test="createOrgId != null">
                #{createOrgId,jdbcType=VARCHAR},
            </if>
            <if test="isLdap != null">
                #{isLdap,jdbcType=INTEGER},
            </if>
            <if test="userDn != null">
                #{userDn,jdbcType=VARCHAR},
            </if>
            <if test="updatePwdTime != null">
                #{updatePwdTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.snbc.bbpf.system.db.common.entity.User">
        update t_user
        <set>
            <if test="userName != null">
                user_name = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="jobNumber != null">
                job_number = #{jobNumber,jdbcType=VARCHAR},
            </if>
            <if test="userPwd != null">
                user_pwd = #{userPwd,jdbcType=VARCHAR},
            </if>
            <if test="hasLock != null">
                has_lock = #{hasLock,jdbcType=INTEGER},
            </if>
            <if test="hasOnline != null">
                has_online = #{hasOnline,jdbcType=INTEGER},
            </if>
            <if test="loginTime != null">
                login_time = #{loginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="userDesc != null">
                user_desc = #{userDesc,jdbcType=VARCHAR},
            </if>
            <if test="avatar != null">
                avatar = #{avatar,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="userStatus != null">
                user_status = #{userStatus,jdbcType=INTEGER},
            </if>
            <if test="hasResign != null">
                has_resign = #{hasResign,jdbcType=INTEGER},
            </if>
            <if test="createUserId != null">
                create_user_id = #{createUserId,jdbcType=VARCHAR},
            </if>
            <if test="createOrgId != null">
                create_org_id = #{createOrgId,jdbcType=VARCHAR},
            </if>
            <if test="isLdap != null">
                is_ldap = #{isLdap,jdbcType=INTEGER},
            </if>
            <if test="userDn != null">
                user_dn = #{userDn,jdbcType=VARCHAR},
            </if>
            <if test="beforeLockStatus != null">
                before_lock_status = #{beforeLockStatus,jdbcType=INTEGER},
            </if>
            <if test="loginLockTime != null">
                login_lock_time = #{loginLockTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where user_id = #{userId,jdbcType=VARCHAR}
    </update>
    <select id="queryUserListPage" resultType="com.snbc.bbpf.system.db.common.vo.OrgUserPageVo">
        select
            t1.user_id as userId, t1.user_name as userName, t1.job_number as jobNumber,t1.phone, t1.email, t1.has_lock as hasLock
        from t_user t1
        LEFT JOIN t_user_org u ON t1.user_id = u.user_id
        LEFT JOIN t_org t3 ON u.org_id = t3.org_id
        <where>
            <if test="orgPathList != null and orgPathList.size() > 0">
                <foreach collection="orgPathList" index="index" item="item" open="(" separator="or" close=")">
                    t3.org_path like concat (#{item},'%')
                </foreach>
            </if>
            <if test="queryParam!='' and queryParam!=null">
                and (t1.user_name like CONCAT('%',#{queryParam,jdbcType=VARCHAR},'%') or  t1.phone=#{phoneParam,jdbcType=VARCHAR})
            </if>
        </where>
        GROUP BY
            t1.user_id
        order by t1.update_time desc,t1.user_id desc
    </select>
    <select id="queryUserListPageByOrgId" resultType="com.snbc.bbpf.system.db.common.vo.OrgUserPageVo">
        select
            t1.user_id as userId, t1.user_name as userName, t1.job_number as jobNumber,
            t1.phone, t1.email, t1.has_lock as hasLock
        from t_user t1
        LEFT JOIN t_user_org u ON t1.user_id = u.user_id
        LEFT JOIN t_org o ON u.org_id = o.org_id
        <where>
            <if test="orgIdList != null and orgIdList.size() > 0">
                and o.org_id in
                <foreach collection="orgIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="queryParam!='' and queryParam!=null">
                and (t1.user_name like CONCAT('%',#{queryParam,jdbcType=VARCHAR},'%') or
                t1.phone=#{phoneParam,jdbcType=VARCHAR})
            </if>
        </where>
        GROUP BY
            t1.user_id,t1.update_time
        order by t1.update_time desc,t1.user_id desc
    </select>
    <select id="selectUserByOrgId" resultMap="BaseResultMap">
        select
        t1.user_id, t1.user_name, t1.phone
        from t_user t1
        left join t_user_org t2 on t1.user_id=t2.user_id
        where t2.org_id in
        <foreach collection="orgIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="hasLock!=null">
            and t1.has_lock = #{hasLock,jdbcType=INTEGER}
        </if>
        GROUP BY t1.user_id
        order by t1.update_time desc,t1.user_id desc
    </select>

    <select id="selectUserIdAndName" resultType="com.snbc.bbpf.system.db.common.vo.TenantClerkVo">
        select
        t1.user_id as userId, t1.user_name as userName ,t1.phone
        from t_user t1
        left join t_user_org t2 on t1.user_id=t2.user_id
        where t2.org_id in
        <foreach collection="orgIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="hasLock!=null">
            and t1.has_lock = #{hasLock,jdbcType=INTEGER}
        </if>
        GROUP BY t1.user_id
        order by t1.update_time desc
    </select>
    <select id="queryExportUserListByOrgPath" resultType="com.snbc.bbpf.system.db.common.dto.UserExportDtoNew">
        SELECT DISTINCT
        t1.user_id AS userId,
        t1.user_name AS userName,
        t1.job_number AS jobNumber,
        t1.phone,
        t1.email,
        t1.has_lock AS hasLock,
        group_CONCAT(t3.org_name) AS orgNames,
        GROUP_CONCAT(
        CASE
        WHEN t3.org_desc IS NULL THEN
        "无"
        WHEN t3.org_desc = "" THEN
        "无"
        ELSE
        t3.org_desc
        END) AS orgRemarks,
        GROUP_CONCAT(
        CASE
        WHEN supervisor IS NULL THEN
        "否"
        WHEN supervisor = "" THEN
        "否"
        WHEN supervisor = t1.user_id THEN
        "是"
        ELSE
        "否"
        END
        ) AS isManagers
        FROM
        t_user t1
        <if test="orgPathList!=null and orgPathList.size()>0">
        left join t_user_org t2 on t1.user_id=t2.user_id
        LEFT JOIN t_org t3 on t2.org_id = t3.org_id
        </if>
        where 1=1
        <if test="orgPathList!=null and orgPathList.size()>0">
            and
            <foreach collection="orgPathList" index="index" item="item" open="(" separator="or" close=")">
                 t3.org_path like  concat(#{item},'%')
            </foreach>
        </if>
        GROUP BY t1.user_id
        order by t1.update_time desc
    </select>
    <select id="queryExportUserListByOrgPathByOrgId" resultType="com.snbc.bbpf.system.db.common.dto.UserExportDtoNew">
        SELECT
        t1.user_id AS userId,
        t1.user_name AS userName,
        t1.job_number AS jobNumber,
        t1.phone,
        t1.email,
        t1.has_lock AS hasLock,
        group_CONCAT(o.org_name) AS orgNames,
        GROUP_CONCAT(
        CASE
        WHEN o.org_desc IS NULL THEN
        "无"
        WHEN o.org_desc = "" THEN
        "无"
        ELSE
        o.org_desc
        END) AS orgRemarks,
        GROUP_CONCAT(
        CASE
        WHEN supervisor IS NULL THEN
        "否"
        WHEN supervisor = "" THEN
        "否"
        WHEN supervisor = t1.user_id THEN
        "是"
        ELSE
        "否"
        END
        ) AS isManagers
        FROM
        t_user t1
        <if test="orgIdList!=null and orgIdList.size()>0">
        left join t_user_org t2 on t1.user_id=t2.user_id
        LEFT JOIN t_org o on t2.org_id = o.org_id
        </if>
        <where>
            <if test="orgIdList != null and orgIdList.size() > 0">
                and o.org_id in
                <foreach collection="orgIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY t1.user_id
        order by t1.update_time desc
    </select>

    <select id="selectUserListByRoleId" resultType="com.snbc.bbpf.system.db.common.vo.RoleUserPage">
        SELECT
            t1.user_id AS userId,
            t1.user_name AS userName,
            t1.phone,
            t1.has_lock AS isLock
        FROM
        t_user t1
        LEFT JOIN t_user_role u ON t1.user_id = u.user_id
        LEFT JOIN t_user_org tuo ON t1.user_id = tuo.user_id
        LEFT JOIN t_org o ON tuo.org_id = o.org_id
        <where>
            <if test="roleId != null and roleId !=''">
                and u.role_id = #{roleId,jdbcType=VARCHAR}
            </if>
            <if test="queryParam !='' and queryParam != null">
                and (t1.user_name like concat('%',#{queryParam,jdbcType=VARCHAR},'%')  or t1.phone = #{phoneEncrypt,jdbcType=VARCHAR})
            </if>
        </where>
        GROUP BY
            t1.user_id,
            t1.user_name,
            t1.phone,
            t1.has_lock
        order by t1.user_name
    </select>
    <select id="selectUserListByAdmin" resultType="com.snbc.bbpf.system.db.common.vo.RoleUserPage">
        SELECT
            t1.user_id AS userId,
            t1.user_name AS userName,
            t1.phone,
            t1.has_lock AS isLock
        FROM
        t_user t1
        LEFT JOIN t_user_role t2 ON t1.user_id = t2.user_id
        <where>
            <if test="roleId != null and roleId !=''">
                and t2.role_id = #{roleId,jdbcType=VARCHAR}
            </if>
            <if test="queryParam !='' and queryParam != null">
                and (t1.user_name like concat('%',#{queryParam,jdbcType=VARCHAR},'%')  or t1.phone = #{phoneEncrypt,jdbcType=VARCHAR})
            </if>
        </where>
        GROUP BY
            t1.user_id,
            t1.user_name,
            t1.phone,
            t1.has_lock
        order by t1.user_name
    </select>
    <select id="selectUnboundRoleUserList" resultType="com.snbc.bbpf.system.db.common.vo.RoleUserPage">
        SELECT
            t1.user_id AS userId,
            t1.user_name AS userName,
            t1.phone,
            t1.has_lock AS isLock
        FROM
        t_user t1
        LEFT JOIN t_user_org u on t1.user_id = u.user_id
        LEFT JOIN t_org o on u.org_id = o.org_id
        where not exists ( select 1 from t_user_role where t1.user_id = t_user_role.user_id )
        <if test="queryParam !='' and queryParam != null">
            and ( t1.user_name like concat('%',#{queryParam,jdbcType=VARCHAR},'%')  or t1.phone = #{phoneEncrypt,jdbcType=VARCHAR} )
        </if>
        GROUP BY t1.user_id,t1.user_name
        order by t1.user_name
    </select>
    <select id="selectUnboundRoleUserListAdmin" resultType="com.snbc.bbpf.system.db.common.vo.RoleUserPage">
        SELECT
        t1.user_id AS userId,
        t1.user_name AS userName,
        t1.phone,
        t1.has_lock AS isLock
        FROM  t_user t1
        where not exists ( select 1 from t_user_role where t1.user_id = t_user_role.user_id )
        <if test="queryParam !='' and queryParam != null">
            and ( t1.user_name like concat('%',#{queryParam,jdbcType=VARCHAR},'%')  or t1.phone = #{phoneEncrypt,jdbcType=VARCHAR} )
        </if>
        GROUP BY t1.user_id,t1.user_name
        order by t1.user_name
    </select>
    <select id="getUserByOrgPath" parameterType="java.util.List" resultType="com.snbc.bbpf.system.db.common.vo.RoleUserPage">
        SELECT DISTINCT
        t1.user_id AS userId,
        t1.user_name AS userName,
        t1.phone,
        t1.job_number AS jobNumber,
        t1.avatar
        FROM
        t_user t1
        LEFT JOIN t_user_org t2 ON t1.user_id = t2.user_id
        LEFT JOIN t_org t3 on t2.org_id = t3.org_id
        where 1=1
        <if test="orgPathList != null and orgPathList.size() > 0">
        and
        <foreach collection="orgPathList" item="orgPath" index="index" open="(" close=")" separator="or">
            t3.org_path like CONCAT(#{orgPath},'%')
        </foreach>
        </if>
        order by t1.user_name
    </select>
    <select id="getUserByOrgId" parameterType="java.util.List" resultType="com.snbc.bbpf.system.db.common.vo.RoleUserPage">
        SELECT DISTINCT
        t1.user_id AS userId,
        t1.user_name AS userName,
        t1.phone,
        t1.job_number AS jobNumber,
        t1.avatar
        FROM
        t_user t1
        LEFT JOIN t_user_org t2 ON t1.user_id = t2.user_id
        LEFT JOIN t_org o on t2.org_id = o.org_id
        <where>
            <if test="orgIdList != null and orgIdList.size() > 0">
                and o.org_id in
                <foreach collection="orgIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by t1.user_name
    </select>
    <select id="getUserListByOrg" parameterType="java.util.List" resultType="java.lang.String">
        SELECT DISTINCT
        t1.user_id AS userId
        FROM
        t_user t1
        LEFT JOIN t_user_org t2 ON t1.user_id = t2.user_id
        where t2.org_id in
        <foreach collection="list" item="orgId" index="index" open="(" close=")" separator=",">
            #{orgId}
        </foreach>
    </select>
    <select id="getUserListByOrgAndSub" parameterType="list" resultType="map">
        select distinct u.user_id as userId
        from t_user u
        left join t_user_org tuo on u.user_id = tuo.user_id
        left join t_org t on tuo.org_id = t.org_id
        <where>
            <foreach collection="orgPathList" item="item" index="index" open="(" separator="or" close=")">
                t.org_path LIKE CONCAT(#{item},'%')
            </foreach>
        </where>
    </select>
    <select id="queryExportUserListByUserId" resultType="com.snbc.bbpf.system.db.common.dto.UserExportDtoNew">
        SELECT DISTINCT
        t1.user_id AS userId,
        t1.user_name AS userName,
        t1.job_number AS jobNumber,
        t1.phone,
        t1.email,
        t1.has_lock AS hasLock,
        group_CONCAT(t3.org_name) AS orgNames,
        GROUP_CONCAT(
        CASE
        WHEN t3.org_desc IS NULL THEN
        "无"
        WHEN t3.org_desc = "" THEN
        "无"
        ELSE
        t3.org_desc
        END) AS orgRemarks,
        GROUP_CONCAT(
        CASE
        WHEN supervisor IS NULL THEN
        "否"
        WHEN supervisor = "" THEN
        "否"
        WHEN supervisor = t1.user_id THEN
        "是"
        ELSE
        "否"
        END
        ) AS isManagers
        FROM
        t_user t1
        LEFT JOIN t_user_org t2 ON t1.user_id = t2.user_id
        LEFT JOIN t_org t3 ON t2.org_id = t3.org_id
        where 1=1
        <if test="userIds!=null and userIds.size()>0">
            and  t1.user_id in
            <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY t1.user_id
        ORDER BY t1.update_time desc
    </select>

    <select id="checkPhoneIsBind" resultType="java.lang.Integer">
        select count(0) from t_user where phone = #{phone, jdbcType=VARCHAR}
    </select>

    <select id="noticeUserList" resultType="com.snbc.bbpf.system.db.common.vo.NoticeUserVo">
        SELECT t1.user_id as userId,t1.user_name as userName,t1.phone,t1.avatar
        FROM t_user t1
        <where>
            <if test="userIds != null and userIds.size()!=0">
                AND t1.user_id IN
                <foreach collection="userIds" index="index" item="userId" open="(" separator="," close=")">
                    #{userId,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </select>
    <select id="noticeUserListConsole" resultType="com.snbc.bbpf.system.db.common.vo.NoticeUserVo">
        SELECT distinct (t1.user_id) as userId,t1.user_name as userName,t1.phone,t1.avatar
        from t_user t1
        left join t_user_org u on t1.user_id=u.user_id
        LEFT JOIN t_org t3 on u.org_id = t3.org_id
        <where>
            <if test="orgPathList != null and orgPathList.size() > 0">
                and
                <foreach collection="orgPathList" index="index" item="item" open="(" separator="or" close=")">
                    t3.org_path like concat (#{item},'%')
                </foreach>
            </if>
            <if test="queryParam!='' and queryParam!=null">
                and (t1.user_name like CONCAT('%',#{queryParam,jdbcType=VARCHAR},'%') or t1.phone=#{queryParam,jdbcType=VARCHAR})
            </if>
        </where>
    </select>
    <select id="noticeUserListConsoleByRoleId" resultType="com.snbc.bbpf.system.db.common.vo.NoticeUserVo">
        SELECT distinct (t1.user_id) as userId,t1.user_name as userName,t1.phone,t1.avatar
        from t_user t1
        LEFT JOIN t_user_org u ON t1.user_id = u.user_id
        LEFT JOIN t_org o ON u.org_id = o.org_id
        <where>
            <if test="orgIdList != null and orgIdList.size() > 0">
                and o.org_id in
                <foreach collection="orgIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="queryParam!='' and queryParam!=null">
                and (t1.user_name like CONCAT('%',#{queryParam,jdbcType=VARCHAR},'%') or t1.phone=#{queryParam,jdbcType=VARCHAR})
            </if>
        </where>
    </select>

    <update id="loginUnLock">
        <!--如果有before_lock_status=1，则解锁，否则不解锁-->
        UPDATE t_user
        SET has_lock =
        CASE
        WHEN before_lock_status = 1 THEN 1
        ELSE has_lock
        END
        , before_lock_status = null,login_lock_time = null
        WHERE user_id = #{userId};
    </update>
</mapper>
