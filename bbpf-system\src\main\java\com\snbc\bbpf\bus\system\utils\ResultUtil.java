/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.utils;


import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.resp.CallBaseResponse;

import java.text.MessageFormat;

/***
 * @Description:    私有化工具类
 * @Author:         wangsong
 * @CreateDate:     2021/5/17 16:07
 * @UpdateDate:     2021/5/17 16:07
 * @return :        null
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
public final class ResultUtil {

    private ResultUtil() {
        throw new IllegalStateException("ResultUtil class");
    }

    /***
      * @Description:    成功返回信息
      * @Author:         wangsong
      * @CreateDate:     2021/5/17 16:08
      * @UpdateDate:     2021/5/17 16:08
      * @return :        com.snbc.bbpf.component.config.CallResponse
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    public static CallBaseResponse success() {
        CallBaseResponse result = new CallBaseResponse();
        result.setCode(ErrorMessage.SUCCESS.getCode());
        result.setMessage(ErrorMessage.SUCCESS.getMessage());
        return result;
    }

    /***
      * @Description:    错位返回信息
      * @Author:         wangsong
      * @param :         code
      * @param :         msg
      * @CreateDate:     2021/5/17 16:08
      * @UpdateDate:     2021/5/17 16:08
      * @return :        com.snbc.bbpf.component.config.CallResponse
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    public static CallBaseResponse error(String code, String msg) {
        CallBaseResponse result = new CallBaseResponse();
        result.setCode(code);
        result.setMessage(msg);
        return result;
    }

    /**
     * 错误
     * @param msg 错误信息
     * @return CallResponse
     */
    public static CallBaseResponse error(String msg) {
        return error(ErrorMessage.FAILED.getCode(), msg);
    }

    /**
     * @description: 错误
     * @param errorMessage 错误信息
     * @return: com.snbc.bbpf.component.config.CallBaseResponse
     * @author: liuyi
     * @time: 2021/5/25 16:06
     */
    public static CallBaseResponse error(ErrorMessage errorMessage){
        return error(errorMessage.getCode(),errorMessage.getMessage());
    }

    /**
     * @description: 错误
     * @param errorMessage 错误信息
     * @param value 占位服值
     * @return: com.snbc.bbpf.component.config.CallBaseResponse
     * @author: liuyi
     * @time: 2021/5/25 16:07
     */
    public static CallBaseResponse error(ErrorMessage errorMessage,String value){
        return error(errorMessage.getCode(), MessageFormat.format(errorMessage.getMessage(), value));
    }
}
