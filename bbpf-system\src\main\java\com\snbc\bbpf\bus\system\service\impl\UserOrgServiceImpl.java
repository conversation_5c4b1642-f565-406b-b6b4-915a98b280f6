/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service.impl;

import com.snbc.bbpf.bus.system.constans.CommonConstant;
import com.snbc.bbpf.bus.system.service.UserOrgService;
import com.snbc.bbpf.bus.system.utils.CheckOrgPermission;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.system.db.common.entity.UserOrg;
import com.snbc.bbpf.system.db.common.mapper.UserOrgMapper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;


/**
 * @ClassName: UserRoleServiceImpl
 * 用户组织机构接口实现类
 * @module: si-bbpf-system
 * @Author: wjc1
 * @date: 2021/5/19 18:45
 */
@Service
public class UserOrgServiceImpl implements UserOrgService {
    @Autowired
    private UserOrgMapper userOrgMapper;
    @Autowired
    private CheckOrgPermission checkOrgPermission;
    @Override
    public int insert(UserOrg userOrg) {
        return userOrgMapper.insert(userOrg);
    }

    /**
     * @description: 查询字典值,复杂度过高，进行调整
     * 1，获取用户信息
     * 2，当用户删除的数量剩最后一个则赋值该用户组织机构为0
     * 3，其它平街用户ID
     * @return: CommonResp<List<DictValue>>
     * @author:JIANFEI
     * @updateuser:Liangjb
     * @time: 2021/6/7 16:13
     */
    @Override
    public void removeUser2Org(String userIds, String orgId) {
        //增加判断，只能调整到本部门及下属部门
        checkOrgPermission.checkOrg(CurrentUser.getUserId(), Collections.singletonList(orgId));
        //读取用户角色ID
        List<String> userIdList = Arrays.asList(userIds.split(CommonConstant.JWT_FILTER_CHAR));
        userOrgMapper.deleteUserRoleByUserIdsAndOrg(userIdList,orgId);
        //修改用户的组织部门，将修改的用户组织部门ID设置为默认0
        List<UserOrg> updateUserId=new ArrayList<>();
        userIdList.forEach(userId->{
            List<UserOrg> ugList= userOrgMapper.queryOrgListVoByUserId(userId);
            if(ugList.isEmpty()){
                UserOrg userOrg=new UserOrg();
                userOrg.setId(UUID.randomUUID().toString());
                userOrg.setOrgId("0");
                userOrg.setUserId(userId);
                updateUserId.add(userOrg);
            }
        });
        if(!CollectionUtils.isEmpty(updateUserId)){
            //批量插入
            userOrgMapper.insertOrgListByUserIdList(updateUserId);
        }
    }
}
