/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.entity;

import com.snbc.bbpf.buslog.annotations.DiffKey;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * @ClassName: DictType
 * 字典类型实体类
 * @module: si-bbpf-system
 * @Author: wjc1 增加的注释
 * @date:   2021/5/25 16:56
 */
@Data
public class DictType {
    private String id;

    @NotBlank(message = "字典类型编码不可为空")
    @Size(max = 50, message = "字典类型编码长度必须在1到50之间")
    private String typeCode;

    @NotBlank(message = "字段类型名称不可为空")
    @Size(max = 100, message = "字典类型名称长度必须在1到100之间")
    @DiffKey(name = "字典类型名称",enName = "Type Name")
    private String typeName;

    public DictType(String id, String typeCode, String typeName) {
        this.id = id;
        this.typeCode = typeCode;
        this.typeName = typeName;
    }

    public DictType() {
    }
}
