package com.snbc.bbpf.bus.system.strategy;

import com.snbc.bbpf.system.db.common.entity.User;
import com.snbc.bbpf.system.db.common.entity.login.LoginUser;

/**
 * @ClassName: LoginStrategy
 * @Description: 登录策略
 * @module: SI-bbpf-system
 * @Author: wangsong
 * @date: 2023/5/17
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
public interface LoginStrategy {
    boolean verify(LoginUser loginUser, User user) throws Exception;
}
