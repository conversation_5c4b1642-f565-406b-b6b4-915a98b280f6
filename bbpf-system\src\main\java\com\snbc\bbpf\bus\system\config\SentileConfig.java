/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.config;

import com.alibaba.csp.sentinel.slots.block.RuleConstant;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeRule;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeRuleManager;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRule;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

@Configuration
public class SentileConfig {
    /**
     * 配置资源名称
     */
    @Value ("${bbpf.sentinel.resource:userLogin}")
    private String resource;
    /**
     * 配置哨兵QPS规则每秒可以访问多少次
     */
    @Value ("${bbpf.sentinel.flowrule.count:2}")
    private double fcount;

    /**
     * 配置哨兵熔断限流规则多少毫S开始算延迟
     */
    @Value ("${bbpf.sentinel.degraderule.rtcount:300}")
    private double rtcount;
    /**
     * 配置哨兵熔断限流规则降级的时间单位，单位S
     */
    @Value ("${bbpf.sentinel.degraderule.timewindow:20}")
    private int timewindow;
    /**
     * 配置哨兵熔断限流规则犯规次数
     */
    @Value ("${bbpf.sentinel.degraderule.minrequestamount:5}")
    private int minrequestamount;
    /**
     * 配置哨兵熔断限流规则统计时长单位MS，在这个时间单位内触发超时的次数触发
     */
    @Value ("${bbpf.sentinel.degraderule.statintervalms:2000}")
    private int statintervalms;
    /**
     * 配置哨兵熔断限流规则比例阈值,0-1,就是满足这个系数乘以数量就触发熔断
     */
    @Value ("${bbpf.sentinel.degraderule.slowratiothreshold:0.8}")
    private double slowratiothreshold;
    @PostConstruct
    private void initRules() throws Exception {
        //限流策略
        FlowRule frule = new FlowRule();
        frule.setGrade(RuleConstant.FLOW_GRADE_QPS);
        frule.setResource(resource);
        frule.setCount(fcount);
        List <FlowRule> frules = new ArrayList <>();
        frules.add(frule);
        // 将控制规则载入到 Sentinel
        FlowRuleManager.loadRules(frules);

        DegradeRule drule = new DegradeRule();
        drule.setResource(resource);
        drule.setGrade(RuleConstant.DEGRADE_GRADE_RT);
        //降级模式, RT（平均响应时间）、异常比例(DEGRADE_GRADE_EXCEPTION_RATIO)/异常数量
        //1s内处理5个请求
        drule.setCount(rtcount);
        //降级的时间单位, 单位为s
        drule.setTimeWindow(timewindow);
        drule.setMinRequestAmount(minrequestamount);
        //阈值 5个请求平均响应时间超过300ms  触发降级
        drule.setStatIntervalMs(statintervalms);
        drule.setSlowRatioThreshold(slowratiothreshold);
        List <DegradeRule> drules = new ArrayList <>();
        drules.add(drule);
        DegradeRuleManager.loadRules(drules);
    }
}
