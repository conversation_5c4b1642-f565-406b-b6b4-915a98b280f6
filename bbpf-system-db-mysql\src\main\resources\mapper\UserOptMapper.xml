<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snbc.bbpf.system.db.common.mapper.UserOptMapper">
    <select id="selectOnlyPhone"  resultType="java.lang.Integer">
        select count(user_id) cid from t_user
        where phone=#{phone,jdbcType=VARCHAR}
        <if test="userId != null" >
            and user_id!=#{userId,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectOnlyEmail"  resultType="java.lang.Integer">
        select count(user_id) cid from t_user
        where email=#{email,jdbcType=VARCHAR}
        <if test="userId != null" >
            and user_id!=#{userId,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectOnlyPhoneByJobNumber"  resultType="java.lang.Integer">
        select count(user_id) cid from t_user
        where phone=#{phone,jdbcType=VARCHAR}
        <if test="jobNumber != null" >
            and job_number!=#{jobNumber,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectOnlyUserId"  resultType="java.lang.Integer">
        select count(user_id) cid from t_user
        where user_id!=#{userId,jdbcType=VARCHAR}
    </select>
    <select id="selectOnlyUserName"  resultType="java.lang.Integer">
        select count(user_id) cid from t_user
        where user_name=#{userName,jdbcType=VARCHAR}
        <if test="userId != null" >
            and user_id!=#{userId,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectOnlyJobNumber"  resultType="java.lang.Integer">
        select count(user_id) cid from t_user
        where job_number=#{jobNumber,jdbcType=VARCHAR}
        <if test="userId != null" >
            and user_id!=#{userId,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectUserIdByJobNumber"  resultType="java.lang.String">
        select user_id from t_user
        where job_number=#{jobNumber,jdbcType=VARCHAR} LIMIT 1
    </select>

    <insert id="addUserRoles" parameterType="list">
        insert into t_user_role (id,user_id, role_id)
            VALUES
        <foreach collection="listUserRole" item="test" separator=",">
            (#{test.id},            #{test.userId},            #{test.roleId})
        </foreach>
    </insert>
    <insert id="addUserOrgs" parameterType="list">
        insert into t_user_org (id,user_id, org_id)
        VALUES
        <foreach collection="listUserOrg" item="test" separator=",">
            (#{test.id},            #{test.userId},            #{test.orgId})
        </foreach>
    </insert>
</mapper>