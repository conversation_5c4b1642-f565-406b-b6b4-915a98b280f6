/*
 * 版权所有 2009-2023山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.system.db.common.vo;

import com.alibaba.fastjson.JSONArray;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 视图vo
 *
 * @ClassName: ViewVo
 * @module: SI-bbpf-system
 * @Author: wangsong
 * @date: 2023/9/13
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ViewVo {
    private String id;
    private String name;
    private Integer viewType = 0;
    private String userId;
    private String tenantCode;
    private Integer moduleType;
    private JSONArray groups;
}
