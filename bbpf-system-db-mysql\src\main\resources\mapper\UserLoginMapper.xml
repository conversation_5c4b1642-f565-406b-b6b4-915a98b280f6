<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snbc.bbpf.system.db.common.mapper.UserLoginMapper">
    <resultMap id="BaseResultMap" type="com.snbc.bbpf.system.db.common.entity.User">
        <id column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="job_number" jdbcType="VARCHAR" property="jobNumber"/>
        <result column="user_pwd" jdbcType="VARCHAR" property="userPwd"/>
        <result column="has_lock" jdbcType="INTEGER" property="hasLock"/>
        <result column="has_online" jdbcType="INTEGER" property="hasOnline"/>
        <result column="login_time" jdbcType="TIMESTAMP" property="loginTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="user_desc" jdbcType="VARCHAR" property="userDesc"/>
        <result column="avatar" jdbcType="VARCHAR" property="avatar"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="user_status" jdbcType="INTEGER" property="userStatus"/>
        <result column="has_resign" jdbcType="INTEGER" property="hasResign"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="create_org_id" jdbcType="VARCHAR" property="createOrgId"/>
        <result column="is_ldap" jdbcType="INTEGER" property="isLdap"/>
        <result column="user_dn" jdbcType="VARCHAR" property="userDn"/>
        <result column="update_pwd_time" jdbcType="TIMESTAMP" property="updatePwdTime"/>
        <result column="before_lock_status" jdbcType="INTEGER" property="beforeLockStatus"/>
        <result column="login_lock_time" jdbcType="TIMESTAMP" property="loginLockTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    user_id, user_name, job_number, user_pwd, has_lock, has_online, login_time, create_time,
    update_time, phone, user_desc, avatar, email, user_status, has_resign, create_user_id,
    create_org_id,is_ldap,user_dn,update_pwd_time,before_lock_status,login_lock_time
  </sql>
    <select id="selectUserByUserName" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List"/>
        from t_user
        where  job_number = #{userName,jdbcType=VARCHAR}
        limit 1
    </select>

    <select id="selectUserOrgsByUserId" resultType="com.snbc.bbpf.system.db.common.entity.login.UserSimpleOrg" >
        SELECT
            t1.org_id AS orgId,
            t1.org_name AS orgName
        FROM
            t_org t1
                LEFT JOIN t_user_org t2 ON t1.org_id = t2.org_id
        where  t2.user_id = #{userId,jdbcType=VARCHAR}
    </select>

    <select id="selectAllUser" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List"/>
        from t_user
        where   has_lock=1
    </select>

    <update id="updateAvatar">
        UPDATE t_user
        SET avatar = #{newAvatar,jdbcType=VARCHAR}
        WHERE
            user_id = #{userId,jdbcType=VARCHAR}
    </update>
    <select id="selectUserRoleIdsByUserId"  resultType="java.lang.String">
       SELECT
            t1.role_id AS roleId
        FROM
            t_role t1
            LEFT JOIN t_user_role t2 ON t1.role_id = t2.role_id
        WHERE
            t2.user_id = #{userId,jdbcType=VARCHAR}
    </select>


    <select id="selectUserRolesByUserId"  resultType="com.snbc.bbpf.system.db.common.entity.Permission">
        select DISTINCT
        tp.parent_id parentId,
        tp.permission_id permissionId,
        tp.permission_type permissionType,
        tp.permission_name permissionName,
        tp.permission_code permissionCode,
        tp.parent_name parentName,
        tp.permission_desc permissionDesc,
        tp.order_by orderBy,
        tp.permission_image permissionImage,
        tp.create_time createTime,
        tp.update_time updateTime,
        tp.permission_level permissionLevel,
        tp.sys_type sysType,
        tp.has_enable hasEnable,
        tp.routing_url routingUrl,
        tp.create_user_id createUserId,
        tp.create_org_id createOrgId,
        tp.permission_path permissionPath from t_permission_role tpr
        left join t_permission tp on tp.permission_id=tpr.permission_id
        LEFT JOIN t_user_role tur on tur.role_id=tpr.role_id
        where tp.has_enable=1 and tp.permission_type = #{permissionType,jdbcType=VARCHAR}
        <if test="sysType != null and sysType != ''">
            and tp.sys_type=#{sysType,jdbcType=VARCHAR}
        </if>
        <choose>
            <when test="roleIds != null and roleIds.size() > 0">
                and tpr.role_id in
                <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
                    #{roleId,jdbcType=VARCHAR}
                </foreach>
            </when>
            <otherwise>
                and tur.user_id= #{userId,jdbcType=VARCHAR}
            </otherwise>
        </choose>
        order by tp.parent_id,tp.order_by
    </select>

    <select id="selectAllRolesByType"  resultType="com.snbc.bbpf.system.db.common.entity.Permission">
        select tp.parent_id parentId, tp.permission_id permissionId,
        tp.permission_type permissionType,    tp.permission_name permissionName,    tp.permission_code permissionCode,
        tp.parent_name parentName,    tp.permission_desc permissionDesc,    tp.order_by orderBy,    tp.permission_image permissionImage,    tp.create_time createTime,
        tp.update_time updateTime,    tp.permission_level permissionLevel,    tp.sys_type sysType,    tp.has_enable hasEnable,
        tp.routing_url routingUrl,    tp.create_user_id createUserId,    tp.create_org_id createOrgId,
        tp.permission_path permissionPath from  t_permission tp
        where tp.has_enable=1 and tp.permission_type = #{permissionType,jdbcType=VARCHAR}
        <if test="sysType != null and sysType != ''">
            and tp.sys_type=#{sysType,jdbcType=VARCHAR}
        </if>
        order by tp.permission_level,tp.parent_id,tp.order_by
    </select>
    <select id="selectUserByPhone" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_user where phone = #{userPhone}
    </select>

    <!-- 获取用户绑定状态 用户ID-->
    <select id="selectUserBindInfo" resultType="java.lang.String" >
        select user_bindstate  from t_user
        where  user_id =#{userId,jdbcType=VARCHAR}
    </select>
    <!-- 获取用户绑定状态 用户ID-->
    <select id="selectUserBindByid" resultType="java.lang.String" >
        select user_bindstate  from t_user
        where  user_id =#{userId,jdbcType=VARCHAR}
    </select>
    <!-- 修改用户绑定状态 用户ID-->
    <update id="updateUserBindById">
        UPDATE t_user
        SET
        <if test="userBindstate != null and userBindstate !=''">
            user_bindstate = #{userBindstate,jdbcType=VARCHAR}
        </if>
        where user_id= #{userId,jdbcType=VARCHAR}
    </update>
    <!-- 获取用户头像 用户ID-->
    <select id="selectUserImageByid" resultType="java.lang.String" >
        select avatar  from t_user
        where  user_id =#{userId,jdbcType=VARCHAR}
    </select>
    <!-- 修改用户头像 用户ID-->
    <update id="updateUserImageById">
        UPDATE t_user
        SET
        <if test="userImage != null and userImage !=''">
            avatar = #{userImage}
        </if>
        where user_id= #{userId,jdbcType=VARCHAR}
    </update>
    <!-- 获取用户手机号 用户ID-->
    <select id="selectUserPhonebyUserId" resultType="java.lang.String" >
        select phone  from t_user
        where  user_id =#{userId,jdbcType=VARCHAR}
    </select>
    <!--查询用户头像、手机、userID状态-->
    <select id="selectUserMsgbyUserId" resultType="com.snbc.bbpf.system.db.common.dto.UserBindMsgDto" >
        select user_name userName, phone userPhone
        from t_user
        where  user_id = #{userId,jdbcType=VARCHAR} and has_lock=1;
    </select>
    <!--查询用户信息 通过、userID-->
    <select id="selectUserByUserId" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List"/>
        from t_user
        where  user_id = #{userId,jdbcType=VARCHAR}
        limit 1
    </select>
</mapper>
