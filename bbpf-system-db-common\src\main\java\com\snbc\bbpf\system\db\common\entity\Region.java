/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.entity;

import com.snbc.bbpf.buslog.annotations.DiffKey;
import com.snbc.bbpf.buslog.annotations.UnionDisplayKey;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
/**
 * @ClassName: Region
 * @Description: 地域类
 * @module: si-bbpf-system
 * @Author: liangjb
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Data
public class Region {
    /**
     * 编号
     */
    @NotBlank(message = "地域编号不可以为空")
    @Size(max = 20,message = "地域编号长度必须在1 到20之间")
    @DiffKey(name = "地域编号",enName = "Region Code")
    private String regionCode;
    /**
     * 名称
     */
    @NotBlank(message = "地域名称不可以为空")
    @Size(max = 20,message = "地域名称长度必须在1 到20之间")
    @DiffKey(name = "地域名称",enName = "Region Name")
    @UnionDisplayKey
    private String regionName;
    /**
     * 父级编码
     */
    @Size(min = 1,max = 20,message = "父级编码长度必须在1到20之间")
    private String parentCode;
    /**
     * 描述
     */
    @DiffKey(name = "描述",enName = "Region Desc")
    private String regionDesc;
    /**
     * ID
     */
    private String id;
    /**
     * 序号
     */
    private Integer sequence;


    public Region(String id, String regionName, String regionCode, String parentCode, String regionDesc, Integer sequence) {
        this.id = id;
        this.regionName = regionName;
        this.regionCode = regionCode;
        this.parentCode = parentCode;
        this.regionDesc = regionDesc;
        this.sequence = sequence;
    }

    public Region() {
    }
}
