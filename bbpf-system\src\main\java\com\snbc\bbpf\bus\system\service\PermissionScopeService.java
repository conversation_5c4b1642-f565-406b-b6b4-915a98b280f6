/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service;

import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.system.db.common.entity.PermissionScope;

import java.util.List;

/**
 * @ClassName: PermissionScopeService
 * @Description: 数据权限业务处理
 * @module: si-bbpf-system
 * @Author: zhouzheng
 * @date: 2021/5/19
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
public interface PermissionScopeService {

    /**
      * @Description:    新增数据权限接口
      * @Author:         zhouzheng
      * @param:  [permissionScope]
      * @CreateDate:     2021/5/19 17:26
      * @UpdateDate:     2021/5/19 17:26
      * @return: void
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    void addDataScope(PermissionScope permissionScope) throws BusinessException;

    /**
      * @Description:    数据权限更新接口
      * @Author:         zhouzheng
      * @param:  [permissionScope]
      * @CreateDate:     2021/5/20 11:29
      * @UpdateDate:     2021/5/20 11:29
      * @return: void
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    void updateDataScope(PermissionScope permissionScope) throws BusinessException;

    /**
     * @Description:    查询数据权限详情接口
     * @Author:         zhouzheng
     * @param:  [permissionScope]
     * @CreateDate:     2021/5/19 17:26
     * @UpdateDate:     2021/5/19 17:26
     * @return: com.snbc.bbpf.system.db.common.entity.PermissionScope
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    PermissionScope getDataScopeDetail(String scopeId) throws BusinessException;
    /**
      * @Description:    数据权限批量删除接口
      * @Author:         zhouzheng
      * @param:  [scopeIds]
      * @CreateDate:     2021/5/20 11:29
      * @UpdateDate:     2021/5/20 11:29
      * @return: void
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    void delDataScope(String[] scopeIds) throws BusinessException;
    /**
      * @Description:    数据权限列表查询接口
      * @Author:         zhouzheng
      * @param:  [permissionId]
      * @CreateDate:     2021/5/20 11:29
      * @UpdateDate:     2021/5/20 11:29
      * @return: java.util.List<com.snbc.bbpf.system.db.common.entity.PermissionScope>
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    List<PermissionScope> getDataScopeList(PermissionScope permissionScope) throws BusinessException;
}
