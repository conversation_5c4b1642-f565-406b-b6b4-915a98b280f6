package com.snbc.bbpf.bus.system.queue;

import com.alibaba.fastjson.JSONObject;
import com.snbc.bbpf.system.db.common.entity.Log;
import com.snbc.bbpf.system.db.common.mapper.LogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * @ClassName: CommonLogListener
 * @Description: 处理日志
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2022/6/7
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Slf4j
@Component
public class CommonLogListener {
    //队列名称
    private static final String QUEUE = "bossmanager.common.buslog";

    /**
     * 写入日志mapper
     */
    @Autowired
    private LogMapper logMapper;

    /**
     * 消息处理类
     *
     * @param message
     */
    @RabbitListener(queues = QUEUE)
    public void listenActivationTimeMQ(Message message) {
        log.info("The message listening to the bossmanager log MQ queue is:{}", new String(message.getBody(), StandardCharsets.UTF_8));
        try {
            logMapper.insert(JSONObject.parseObject(message.getBody(), Log.class));
        } catch (Exception ex) {
            log.error("Asynchronous log writing is abnormal", ex);
        }
    }
}
