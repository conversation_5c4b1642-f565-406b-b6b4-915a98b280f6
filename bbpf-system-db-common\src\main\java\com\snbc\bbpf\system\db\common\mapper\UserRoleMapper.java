/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.mapper;

import com.snbc.bbpf.system.db.common.entity.UserRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName: UserRoleMapper
 * 用户角色mapper
 * @module: si-bbpf-system
 * @Author: wjc1
 * @date: 2021/5/19 18:19
 */
@Mapper
public interface UserRoleMapper {
    void insertRelation(List<UserRole> userRoleList);

    void deleteRelation(List<UserRole> userRoleList);

    int selectUserCountByRole(String roleId);

    int insert(UserRole userRole);
    /**
     * 根据用户id删除用户组织机构
     *
     * @param userId 用户id
     */
    void deleteByUserId(String userId);

    List<UserRole> selectAlreadyUserRole(List<UserRole> userRoleList);

    List<UserRole> queryRoleListByRoleId(@Param("roleId") String roleId);

    List<String> selectRoleByUserId(String userId);
}
