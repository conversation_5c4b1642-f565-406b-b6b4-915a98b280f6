/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName: RolePermissionVo
 * @Description: RolePermissionVo
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/5/21
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RolePermissionVo extends RoleDetail {

    /**
     * 权限信息
     */
    private List<PermissionInfo> permissionInfo;
}
