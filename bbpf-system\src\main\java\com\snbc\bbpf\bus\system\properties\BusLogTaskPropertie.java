package com.snbc.bbpf.bus.system.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @ClassName: BusLogTaskPropertie
 * @Description: 处理buslog线程配置
 * @module: SI-bbpf-tenant-config
 * @Author: wangsong
 * @date: 2022/11/7
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@ConfigurationProperties(prefix = "bbpf.buslog.threadpool")
public class BusLogTaskPropertie {
        private int pool = 2; // 消息线程池
        private int workers = 1; // 同时几个线程处理访问记录
        private int delay = 1; // 间隔多少秒执行一次死信,单位秒

        public int getPool() {
            return pool;
        }

        public void setPool(int pool) {
            this.pool = pool;
        }

        public int getWorkers() {
            return workers;
        }

        public void setWorkers(int workers) {
            this.workers = workers;
        }

        public int getDelay() {
            return delay;
        }

        public void setDelay(int delay) {
            this.delay = delay;
        }

}
