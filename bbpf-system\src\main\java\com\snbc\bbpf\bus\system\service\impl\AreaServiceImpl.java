package com.snbc.bbpf.bus.system.service.impl;

import com.snbc.bbpf.bus.system.service.AreaService;
import com.snbc.bbpf.system.db.common.dto.AreaDto;
import com.snbc.bbpf.system.db.common.mapper.AreaMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: AreaServiceImpl
 * @Description: area实现类
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/12/30
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Service
public class AreaServiceImpl implements AreaService {
    @Autowired
    private AreaMapper areaMapper;
    @Override
    public List<AreaDto> getArea(String areaCode) {
        return areaMapper.getArea(areaCode);
    }

    @Override
    public List<Map<String, String>> getAllArea() {
        return areaMapper.getAllArea();
    }
}
