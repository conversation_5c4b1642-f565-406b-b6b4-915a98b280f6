/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.snbc.bbpf.bus.system.mapstruct.ClassConvertorMapper;
import com.snbc.bbpf.bus.system.service.IWeakPasswordService;
import com.snbc.bbpf.component.security.service.WeakPwdDictCheckService;
import com.snbc.bbpf.system.db.common.dto.WeakPasswordDto;
import com.snbc.bbpf.system.db.common.entity.WeakPassword;
import com.snbc.bbpf.system.db.common.mapper.WeakPasswordMapper;
import com.snbc.bbpf.system.db.common.vo.WeakPasswordVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 弱密码管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-01-16
 */
@Service
public class WeakPasswordServiceImpl implements IWeakPasswordService, WeakPwdDictCheckService {
    @Autowired
    private WeakPasswordMapper weakPasswordMapper;

    /**
     * 查询弱密码管理
     * 
     * @param id 弱密码管理主键
     * @return 弱密码管理
     */
    @Override
    public WeakPasswordVo selectWeakPasswordById(String id) {
        WeakPassword weakPassword = weakPasswordMapper.selectWeakPasswordById(id);
        return ClassConvertorMapper.INSTANCE.covertWeakPasswordVo(weakPassword);
    }

    /**
     * 查询弱密码管理列表
     * 
     * @param weakPasswordDto 弱密码管理
     * @param pageSize 分页大小
     * @param pageNum 分页数
     * @return 弱密码管理
     */
    @Override
    public PageInfo<WeakPasswordVo> selectWeakPasswordList(WeakPasswordDto weakPasswordDto, int pageSize, int pageNum) {
        WeakPassword weakPassword=ClassConvertorMapper.INSTANCE.covertWeakPassword(weakPasswordDto);
        PageMethod.startPage(pageNum, pageSize);
        List<WeakPassword> list= weakPasswordMapper.selectWeakPasswordList(weakPassword);
        PageInfo<WeakPassword> page=new PageInfo<>(list);
        PageInfo<WeakPasswordVo> pageInfo=new PageInfo<>(ClassConvertorMapper.INSTANCE.coverWeakPasswordVoList(list));
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        pageInfo.setPages(page.getPages());
        pageInfo.setTotal(page.getTotal());
        pageInfo.calcByNavigatePages(page.getNavigatePages());
        return pageInfo;
    }
    /**
     * 查询弱密码管理列表
     *
     * @param weakPasswordDto 弱密码管理
     * @return 弱密码管理
     */
    @Override
    public List<WeakPasswordVo> exportWeakPasswordList(WeakPasswordDto weakPasswordDto) {
        WeakPassword weakPassword=ClassConvertorMapper.INSTANCE.covertWeakPassword(weakPasswordDto);
        List<WeakPassword> list= weakPasswordMapper.selectWeakPasswordList(weakPassword);
        return ClassConvertorMapper.INSTANCE.coverWeakPasswordVoList(list);
    }
    /**
     * 新增弱密码管理
     * 
     * @param weakPasswordDto 弱密码管理
     * @return 受影响结果数
     */
    @Override
    public int insertWeakPassword(WeakPasswordDto weakPasswordDto) {
        WeakPassword weakPassword=ClassConvertorMapper.INSTANCE.covertWeakPassword(weakPasswordDto);
        weakPassword.setId(UUID.randomUUID().toString());
        weakPassword.setCreateTime(LocalDateTime.now());
        return weakPasswordMapper.insertWeakPassword(weakPassword);
    }

    /**
     * 修改弱密码管理
     * 
     * @param weakPasswordDto 弱密码管理
     * @return 受影响结果数
     */
    @Override
    public int updateWeakPassword(WeakPasswordDto weakPasswordDto) {
        WeakPassword weakPassword=ClassConvertorMapper.INSTANCE.covertWeakPassword(weakPasswordDto);
        weakPassword.setUpdateTime(LocalDateTime.now());
        return weakPasswordMapper.updateWeakPassword(weakPassword);
    }

    /**
     * 批量删除弱密码管理
     * 
     * @param ids 需要删除的弱密码管理主键
     * @return 受影响结果数
     */
    @Override
    public int deleteWeakPasswordByIds(String[] ids) {

        return weakPasswordMapper.deleteWeakPasswordByIds(ids);
    }

    /**
     * 删除弱密码管理信息
     * 
     * @param id 弱密码管理主键
     * @return 受影响结果数
     */
    @Override
    public int deleteWeakPasswordById(String id) {

        return weakPasswordMapper.deleteWeakPasswordById(id);
    }

    @Override
    public boolean checkWeakPwdExists(String pwd, Object... objects) {
        if(!StringUtils.hasText(pwd) ){
            return false;
        }
        WeakPassword weakPassword = new WeakPassword();
        weakPassword.setPwd(pwd);
        List<WeakPassword> weakPasswordSelective = weakPasswordMapper.selectWeakPasswordList(weakPassword);
        return  !CollectionUtils.isEmpty(weakPasswordSelective);
    }
}
