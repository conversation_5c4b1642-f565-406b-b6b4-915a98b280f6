/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.mapper;


import com.snbc.bbpf.system.db.common.dto.LogDto;
import com.snbc.bbpf.system.db.common.dto.RetentionInfoDto;
import com.snbc.bbpf.system.db.common.entity.Log;
import com.snbc.bbpf.system.db.common.vo.ExportLogVo;
import com.snbc.bbpf.system.db.common.vo.LogQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName: BusLogMapper
 * BusLogMapper
 * @module: si-bbpf-system
 * @Author: wjc1 增加的注释
 * @date:   2021/5/25 16:56
 */
@Mapper
public interface LogMapper {

    int deleteByPrimaryKey(String logId);

    int insert(Log log);

    int insertSelective(Log log);
    //组合获取日志进行优化，先根据条件获取日志ID，然后根据日志ID获取日志列表
    List<LogDto> selectByExample(@Param("logIdList") List<String> logIdList, String international);
    List<String> selelctBusLogIds(LogQuery logQuery);

    List<ExportLogVo> selectExportLogs(LogQuery logQuery);

    Log selectByPrimaryKey(String logId);

    int updateByPrimaryKeySelective(Log log);

    int updateByPrimaryKey(Log log);

    int clearLog(String yesterday);
    Object clearLogByOpenGauss(String yesterday);

    RetentionInfoDto retentionDays();

    String selectClearPartition(@Param("tableName") String tableName, @Param("partitionDate") String partitionDate);

    Object performingPartitionCleanup(@Param("tableName") String tableName, @Param("partition") String partition,
                                    @Param("extableName") String extableName, @Param("yesterday") String yesterday);
}
