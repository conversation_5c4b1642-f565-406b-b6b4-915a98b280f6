package com.snbc.bbpf.system.db.common.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: OrgUserPageVo
 * @Description: 组织机构用户分页列表vo
 * @module: bbpf-bus-system
 * @Author: wangsong
 * @date: 2021/9/16
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrgUserPageVo {

    private String userId;

    private String userName;

    private String jobNumber;

    private String phone;

    private String email;

    private Integer hasLock;

    private String orgNames;

    private String roleNames;

}
