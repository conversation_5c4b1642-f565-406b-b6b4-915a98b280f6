package com.snbc.bbpf.grafana.proxy.config;

import com.snbc.bbpf.grafana.proxy.handler.GrafanaWebSocketProxyHandler;
import com.snbc.bbpf.grafana.proxy.interceptor.WebSocketHandshakeInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * WebSocket配置类
 * 
 * 配置WebSocket代理处理器，支持Grafana Live WebSocket连接的代理转发
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2024-12-13
 */
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

	@Autowired
	private GrafanaWebSocketProxyHandler webSocketProxyHandler;

	@Autowired
	private WebSocketHandshakeInterceptor handshakeInterceptor;

	@Override
	public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
		// 注册WebSocket处理器，支持Grafana Live API
		registry.addHandler(webSocketProxyHandler, "/api/live/ws")
				.addInterceptors(handshakeInterceptor) // 添加握手拦截器
				.setAllowedOrigins("*") // 允许跨域，生产环境应该限制具体域名
				.withSockJS(); // 启用SockJS支持，提供WebSocket降级方案

		// 也可以注册不带SockJS的原生WebSocket支持
		registry.addHandler(webSocketProxyHandler, "/api/live/ws/native")
				.addInterceptors(handshakeInterceptor) // 添加握手拦截器
				.setAllowedOrigins("*");
	}
}