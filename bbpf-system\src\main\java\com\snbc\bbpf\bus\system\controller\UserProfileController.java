/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.controller;

import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.bus.system.service.IUserProfileService;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.bus.system.utils.ResultUtil;
import com.snbc.bbpf.system.db.common.dto.UserProfileDto;
import com.snbc.bbpf.system.db.common.vo.ViewVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 用户配置Controller
 *
 * <AUTHOR>
 * @date 2022-12-05
 */
@RestController
@RequestMapping("/console/user")
public class UserProfileController {
    /**
     * 用户
     */
    @Autowired
    private IUserProfileService userProfileService;

    /**
     * 获取用户配置详细信息
     */
    @GetMapping(value = "/getProfile")
    public CommonResp getInfo(@RequestParam("profileCode") String profileCode) {
        return CommonResp.builder()
                .head(ResultUtil.success())
                .body(userProfileService.getUserProfileByProfileCode(profileCode)).build();
    }

    /**
     * 保存用户配置
     */
    @PostMapping(value = "/updateProfile")
    public CommonResp add(@Validated @RequestBody UserProfileDto userProfileDto) {
        userProfileDto.setUserId(CurrentUser.getUserId());
        userProfileService.saveUserProfile(userProfileDto);
        return CommonResp.builder()
                .head(ResultUtil.success())
                .build();
    }

    /**
     * 保存高级查询视图
     * @param 	viewVo
     * @return com.snbc.bbpf.component.config.CommonResp
     * @throws //
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/9/13
     */
    @PostMapping("/saveorupdateview")
    public CommonResp saveOrUpdateView(@RequestBody ViewVo viewVo) {
        String tenantId = CurrentUser.getTenantId();
        if (StringUtils.isNotBlank(tenantId)) {
            viewVo.setTenantCode(tenantId);
        }
        userProfileService.saveOrUpdateView(viewVo);
        return CommonResp.builder().head(ResultUtil.success()).build();
    }

    /**
     * 删除视图
     * @param 	id
     * @return com.snbc.bbpf.component.config.CommonResp
     * @throws //
     * @since 1.0.0
     * <AUTHOR>
     * @date 2023/9/13
     */
    @DeleteMapping("/delprofile")
    public CommonResp deleteProfile(@RequestParam String id){
        userProfileService.deleteUserProfileByprofileId(id);
        return CommonResp.builder().head(ResultUtil.success()).build();
    }

    @GetMapping("/getmyview")
    public CommonResp getMyView(@RequestParam(value = "moduleType", required = false) Integer moduleType,
                                @RequestParam(value = "tenantCode", required = false) String tenantCode){
        String tenantId = CurrentUser.getTenantId();
        if (StringUtils.isBlank(tenantId)){
            tenantId = tenantCode;
        }
        List<Map<String, Object>> myView = userProfileService.getMyView(CurrentUser.getUserId(), moduleType, tenantId);
        return CommonResp.builder().head(ResultUtil.success()).body(myView).build();
    }
}
