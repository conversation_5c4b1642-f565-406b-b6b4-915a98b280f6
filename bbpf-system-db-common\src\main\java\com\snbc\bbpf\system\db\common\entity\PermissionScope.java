/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.entity;

import com.snbc.bbpf.buslog.annotations.DiffKey;
import com.snbc.bbpf.buslog.annotations.UnionDisplayKey;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @ClassName: DataScope
 * @Description: 数据权限实体类
 * @module: si-bbpf-system
 * @Author: zhouzheng
 * @date: 2021/5/19
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PermissionScope {
    //数据权限编号
    private String scopeId;
    //关联的功能权限编号
    private String permissionId;
    //关联的功能权限名称
    @UnionDisplayKey
    private String permissionName;
    //关联的功能权限码
    private String permissionCode;
    //数据权限名称
    @DiffKey(name = "数据权限名称",enName = "Scope Name")
    @UnionDisplayKey
    private String scopeName;
    //数据权限code
    @DiffKey(name = "数据权限编码",enName = "Scope Code")
    private String scopeCode;
    //可见字段
    @DiffKey(name = "可见字段",enName = "Scope Column")
    private String scopeColumn;
    //数据权限类型0：全部可见 1：本人可见 2：所在机构可见 3：所在机构及以下可见
    private Integer scopeType;
    //数据权限筛选字段
    @DiffKey(name = "数据权限筛选字段",enName = "Scope Field")
    private String scopeField;
    //数据权限类名
    @DiffKey(name = "数据权限类名",enName = "Scope Class")
    private String scopeClass;
    //备注
    @DiffKey(name = "备注",enName = "remarks")
    private String remarks;
    //创建时间
    private LocalDateTime createTime;
    //更新时间
    private LocalDateTime updateTime;
    //创建人
    private String createUserId;
    //数据权限类名
    @DiffKey(name = "数据权限扩展字段",enName = "Scope Ext")
    private String scopeExt;
}
