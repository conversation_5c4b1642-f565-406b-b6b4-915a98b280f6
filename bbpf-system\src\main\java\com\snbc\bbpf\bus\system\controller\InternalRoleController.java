/*
 * 版权所有 2009-2024山东新北洋信息技术股份有限公司 保留所有权利。
 */
package com.snbc.bbpf.bus.system.controller;

import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.bus.system.service.RoleService;
import com.snbc.bbpf.bus.system.utils.ResultUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 服务间调用角色信息
 *
 * @ClassName: InternalRoleController
 * @module: SI-bbpf-system
 * @Author: wangsong
 * @date: 2024/9/4
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@RestController
@RequestMapping("/service/v1/role")
public class InternalRoleController {
    @Autowired
    private RoleService roleService;
    /**
     * 提供服务信息化系统根据组织机构同步角色
     * @param 	orgIds
     * @return com.snbc.bbpf.component.config.CommonResp
     * @throws //
     * @since 1.0.0
     * <AUTHOR>
     * @date 2024/9/4
     */
    @GetMapping("/roleListBasedOrg")
    public CommonResp getRoleListBasedOrg(@RequestParam List<String> orgIds) {
        if (orgIds.isEmpty() || null == orgIds) {
            return CommonResp.builder().head(ResultUtil.success()).build();
        }        List<Map<String, String>> roleList = roleService.getRoleListBasedOrg(orgIds);
        return CommonResp.builder().head(ResultUtil.success()).body(roleList).build();
    }
}
