/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.mapper;

import com.snbc.bbpf.component.dataauth.annotation.IgnoreAuth;
import com.snbc.bbpf.system.db.common.dto.UserBindMsgDto;
import com.snbc.bbpf.system.db.common.entity.Permission;
import com.snbc.bbpf.system.db.common.entity.User;
import com.snbc.bbpf.system.db.common.entity.login.UserSimpleOrg;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName: UserLoginMapper
 * @Description: 数据库交互主
 * @module: si-bbpf-system
 * @Author: liangjb
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Mapper
public interface UserLoginMapper {
    //获取用户基本资料
    User selectUserByUserName(@Param("userName") String userName);
    //获取所有用户
    List<User> selectAllUser();
    //获取用户的角色ID
    List<String> selectUserRoleIdsByUserId(@Param("userId")String  userId);
    //获取用户的组织机构
    List<UserSimpleOrg> selectUserOrgsByUserId(@Param("userId")String  userId);
    //获取用户的头像
    void updateAvatar(@Param("newAvatar") String newPwd,@Param("userId")String userId);
    //获取用户的权限信息
    @IgnoreAuth
    List<Permission> selectUserRolesByUserId(@Param("userId") String userId,
                                             @Param("sysType") String sysType,
                                             @Param("permissionType") String permissionType,
                                             @Param("roleIds") List<String> roleIds);
    //获取所有的权限信息
    @IgnoreAuth
    List<Permission> selectAllRolesByType(@Param("sysType")String  sysType,
                                           @Param("permissionType")String  permissionType);

    User selectUserByPhone(String userPhone);
    /**
     * 获取用户绑详情--三方綁定新增
     * @param userId
     * @return
     */
    String selectUserBindByid(@Param ( "userId" ) String userId );
    /**
     * 更新用户绑定详情
     * @param userId
     * @param userBindstate
     * @return
     */
    Integer updateUserBindById(@Param ( "userId" ) String userId ,
                               @Param ( "userBindstate" ) String userBindstate);
    /**
     * 获取用户头像
     * @param userId
     * @return
     */
    String selectUserImageByid(@Param ( "userId" ) String userId );
    /**
     * 更新用户头像
     * @param userId
     * @param userBindstate
     * @return
     */
    Integer updateUserImageById(@Param ( "userId" ) String userId ,
                                @Param ( "userImage" ) String userBindstate);

    /**
     * 获取内部平台绑定用户信息(昵称和)
     * @param userId
     * @return
     */
    UserBindMsgDto selectUserMsgbyUserId(@Param ( "userId" ) String userId );
    //获取用户基本资料通过userId
    User selectUserByUserId(@Param("userId") String userId);

}
