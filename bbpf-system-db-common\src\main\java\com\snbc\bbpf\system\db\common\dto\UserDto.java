/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.system.db.common.dto;

import com.snbc.bbpf.buslog.annotations.DiffKey;
import com.snbc.bbpf.buslog.annotations.UnionDisplayKey;
import com.snbc.bbpf.commons.strings.security.annotation.Desensitized;
import com.snbc.bbpf.commons.strings.security.annotation.SensitiveType;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * @ClassName: UserRequest
 * 用户请求实体类
 * @module: si-bbpf-system
 * @Author: wjc1
 * @date: 2021/5/19 14:07
 */
@Data
public class UserDto {
    private String userId;
    @NotBlank(message = "用户名不能为空")
    @Length(max=20, message="用户名最大长度为20位")
    @UnionDisplayKey
    @DiffKey(name = "用戶名",enName = "User name")
    private String userName;


    @NotBlank(message = "用户工号不能为空")
    @Length(max=20, message="用户工号最大长度为20位")
    @DiffKey(name = "工号",enName = "Job Number")
    private String jobNumber;

    /**
     * 禁用启用 0禁用,1.启用
     * */
    private Integer hasLock;

    @Desensitized(type = SensitiveType.MOBILE_PHONE)
    @NotBlank(message = "用户手机号码不能为空")
    @Length(max=16, message="手机号长度最多为16位")
    @DiffKey(name = "手机号",enName = "phone")
    @UnionDisplayKey
    private String phone;

    private String userDesc;
    /**
     * 用户头像
     * */
    private String avatar;

    @Length(max=64, message="邮箱最大长度为64位")
    @DiffKey(name = "邮箱",enName = "email")
    private String email;
    /**
     * 用户状态 0请假中 1生病中 2出差中 3
     * 会议中 4外出中 5忙碌中 6调休中
     * */
    private Integer userStatus;
    /**
     * 在职离职 1：在职0：离职
     * */
    private Integer hasResign;
    /**
     * 所属组织机构id
     * */
    @NotBlank(message = "所属组织机构不能为空")
    private String belongOrgIds;
    /**
     * 所属角色id
     * */
    private String belongRoleIds;
    /**
     * 创建人id
     * */
    private String createUserId;

    private String createOrgId;
    /**
     *是否ldap
     */
    private Integer isLdap;
}
