/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.controller;

import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.bus.system.resp.CommonResp;
import com.snbc.bbpf.bus.system.service.LogService;
import com.snbc.bbpf.bus.system.utils.ResultUtil;
import com.snbc.bbpf.system.db.common.vo.BusLogVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * @ClassName: InternalDictTypeController
 * @Description: 提供内部接口调用
 * @module: bbpf-bus-system
 * @Author: liuyi
 * @date: 2021/5/31
 * copyright 2020 barm Inc. All rights reserver
 */

@Slf4j
@RestController
@RequestMapping("/service/v1/log")
public class InternalBusLogController {
    @Autowired
    private LogService logService;
    /***
      * @Description:    外部服务插入buslog
      * @Author:         jiafei
      * @param :         busLogVo
      * @CreateDate:     2021/6/18 15:57
      * @UpdateDate:     2021/6/18 15:57
      * @return :        com.snbc.bbpf.component.config.CommonResp<java.lang.Object>
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @PostMapping(value = "/addLogsByFeign", consumes = {"application/json"})
    public CommonResp<Object> addLogsByFeign(@RequestBody BusLogVo busLogVo) throws BusinessException {
        logService.addLogsByFeign(busLogVo);
        return CommonResp.builder().head(ResultUtil.success()).build();
    }
}
