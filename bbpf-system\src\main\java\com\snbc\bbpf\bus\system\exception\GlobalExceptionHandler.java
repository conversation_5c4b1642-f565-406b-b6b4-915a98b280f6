/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.exception;

import com.snbc.bbpf.bus.system.resp.CallBaseResponse;
import com.snbc.bbpf.bus.system.resp.CommonResp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.text.MessageFormat;
import java.util.Objects;

/**
 * @ClassName:      GlobalExceptionHandler.java
 * @Description:    controller层异常拦截处理
 * @module:
 * @Author:         wangsong
 * @date:           2021/5/17 15:12
 * copyright 2020 SNBC. All rights reserver
 */

@ControllerAdvice
@ResponseBody
/**
  * @Description:    拦截controller异常
  * @Author:         wangsong
  * @param:           * @param null :
  * @CreateDate:     2020/8/24 11:54
  * @UpdateUser:     wangsong
  * @UpdateDate:     2020/8/24 11:54
  * @return:          * @return : null
 */
public class GlobalExceptionHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(GlobalExceptionHandler.class);
    private CallBaseResponse callResponse = new CallBaseResponse();
    private CommonResp commonResp = new CommonResp();

    /***
      * @Description:    业务异常
      * @Author:         wangsong
      * @param :         businessException
      * @CreateDate:     2020/8/24 11:02
      * @UpdateUser:     wangsong
      * @UpdateDate:     2020/8/24 11:02
      * @return :        com.snbc.vems.product.exception.ProductCommonResp
     */
    @ExceptionHandler(BusinessException.class)
    public CommonResp bussessionException(BusinessException businessException){
        LOGGER.error(businessException.getMessage(), businessException.getData());
        callResponse.setCode(businessException.getCode());
        callResponse.setMessage(businessException.getMessage());
        commonResp.setHead(callResponse);
        return commonResp;
    }

    /***
      * @Description:    运行时异常
      * @Author:         wangsong
      * @param :         ex
      * @CreateDate:     2021/5/17 16:11
      * @UpdateDate:     2021/5/17 16:11
      * @return :        com.snbc.bbpf.component.config.CommonResp
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @ExceptionHandler({RuntimeException.class})
    public CommonResp runtimeExceptionHandler(Exception ex) {
        LOGGER.error(ex.getMessage(), ex);
        callResponse.setCode(ErrorMessage.FAILED.getCode());
        callResponse.setMessage(ErrorMessage.FAILED.getMessage());
        commonResp.setHead(callResponse);
        return commonResp;
    }

    /***
      * @Description:    其他异常
      * @Author:         wangsong
      * @param :         ex
      * @CreateDate:     2021/5/17 16:11
      * @UpdateDate:     2021/5/17 16:11
      * @return :        com.snbc.bbpf.component.config.CommonResp
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @ExceptionHandler({Exception.class})
    public CommonResp exception(Exception ex) {
        LOGGER.error("发生异常:{}",ex.getMessage(), ex);
        callResponse.setCode(ErrorMessage.FAILED.getCode());
        callResponse.setMessage(ErrorMessage.FAILED.getMessage());
        commonResp.setHead(callResponse);
        return commonResp;
    }


    /***
      * @Description:    必填参数异常
      * @Author:         wangsong
      * @param :         ex
      * @CreateDate:     2021/5/17 16:11
      * @UpdateDate:     2021/5/17 16:11
      * @return :        com.snbc.bbpf.component.config.CommonResp
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @ExceptionHandler({MissingServletRequestParameterException.class})
    public CommonResp exception(MissingServletRequestParameterException ex) {
        callResponse.setCode(ErrorMessage.PARAM_NOTNULL.getCode());
        callResponse.setMessage(MessageFormat.format(ErrorMessage.PARAM_NOTNULL.getMessage(),ex.getParameterName()));
        commonResp.setHead(callResponse);
        return commonResp;
    }
    /***
      * @Description:    拦截表单参数校验
      * @Author:         wangsong
      * @param :         e
      * @CreateDate:     2021/5/17 16:11
      * @UpdateDate:     2021/5/17 16:11
      * @return :        com.snbc.bbpf.component.config.CommonResp
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler({BindException.class})
    public CommonResp bindException(BindException e) {
        BindingResult bindingResult = e.getBindingResult();
        callResponse.setCode(ErrorMessage.PARAM_NOTNULL.getCode());
        callResponse.setMessage( MessageFormat.format(ErrorMessage.PARAM_NOTNULL.getMessage(),
                Objects.requireNonNull(bindingResult.getFieldError()).getField()));
        commonResp.setHead(callResponse);
        return commonResp;
    }
    /***
     * @Description:    方法405异常拦截
     * @Author:         wjc1
     * @param :         e
     * @CreateDate:     2021/5/17 16:11
     * @UpdateDate:     2021/5/17 16:11
     * @return :        com.snbc.bbpf.component.config.CommonResp
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    @ExceptionHandler({HttpRequestMethodNotSupportedException.class})
    public CommonResp httpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e) {
        callResponse.setCode(String.valueOf(HttpStatus.METHOD_NOT_ALLOWED));
        callResponse.setMessage(e.getLocalizedMessage());
        commonResp.setHead(callResponse);
        return commonResp;
    }
    /***
     * @Description:    方法415异常拦截
     * @Author:         wjc1
     * @param :         e
     * @CreateDate:     2021/5/17 16:11
     * @UpdateDate:     2021/5/17 16:11
     * @return :        com.snbc.bbpf.component.config.CommonResp
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @ResponseStatus(HttpStatus.UNSUPPORTED_MEDIA_TYPE)
    @ExceptionHandler({HttpMediaTypeNotSupportedException.class})
    public CommonResp httpRequestMethodNotSupportedException(HttpMediaTypeNotSupportedException e) {
        callResponse.setCode(String.valueOf(HttpStatus.UNSUPPORTED_MEDIA_TYPE));
        callResponse.setMessage(e.getLocalizedMessage());
        commonResp.setHead(callResponse);
        return commonResp;
    }
    /***
      * @Description:    拦截JSON参数校验
      * @Author:         wangsong
      * @param :         e
      * @CreateDate:     2021/5/17 16:11
      * @UpdateDate:     2021/5/17 16:11
      * @return :        com.snbc.bbpf.component.config.CommonResp
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public CommonResp bindException(MethodArgumentNotValidException e) {
        BindingResult bindingResult = e.getBindingResult();
        callResponse.setCode(ErrorMessage.PARAM_NOTNULL.getCode());
        // 目前每次返回第一个错误， 也可以把所有错误返回。wjc1 2021-05-21 add
        callResponse.setMessage(MessageFormat.format(bindingResult.getAllErrors().get(0).getDefaultMessage(),
                Objects.requireNonNull(bindingResult.getFieldError()).getField()));
        commonResp.setHead(callResponse);
        return commonResp;
    }
}
