/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * @ClassName: UserImportService
 * @Description: 用户类过复杂将用户导入和新增业务处理
 * @module: si-bbpf-system
 * @Author: wangsong
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
public interface UserImportNewService {

    void importUserNew(MultipartFile file, HttpServletResponse response) throws Exception;

    void exportUserNew(String orgId);

    void exportPartUserNew(String orgId, String userIds);
}
