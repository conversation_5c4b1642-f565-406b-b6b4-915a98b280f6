<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snbc.bbpf.system.db.common.mapper.UserOrgMapper" >
  <resultMap id="BaseResultMap" type="com.snbc.bbpf.system.db.common.entity.UserOrg" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <id column="user_id" property="userId" jdbcType="VARCHAR" />
    <id column="org_id" property="orgId" jdbcType="VARCHAR" />
  </resultMap>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from t_user_org where id = #{id,jdbcType=VARCHAR}
  </delete>

  <insert id="insert" parameterType="com.snbc.bbpf.system.db.common.entity.UserOrg" >
    insert into t_user_org (id,user_id, org_id)
    values (#{id,jdbcType=VARCHAR},#{userId,jdbcType=VARCHAR}, #{orgId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertOrUpdate" parameterType="com.snbc.bbpf.system.db.common.entity.UserOrg" >
    INSERT INTO  t_user_org (id,user_id, org_id)
    values (#{id,jdbcType=VARCHAR},#{userId,jdbcType=VARCHAR}, #{orgId,jdbcType=VARCHAR})
  </insert>
  <delete id="deleteByUserId" parameterType="java.lang.String">
		DELETE FROM t_user_org WHERE user_id = #{userId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByOrgId" parameterType="java.lang.String">
		DELETE FROM t_user_org	WHERE org_id = #{orgId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByOrgIdUserId" parameterType="java.lang.String">
    update t_org set supervisor='',supervisor_phone=''    where org_id = #{orgId,jdbcType=VARCHAR} and supervisor = #{userId,jdbcType=VARCHAR};
  </delete>
  <select id="selectUserCountByOrgId" resultType="java.lang.Integer">
      select count(0) from t_user_org where org_id=#{orgId,jdbcType=VARCHAR}
  </select>

  <select id="queryOrgListByOrgId" resultMap="BaseResultMap" parameterType="java.lang.String">
		SELECT
		user_id,org_id
		FROM t_user_org
		WHERE org_id = #{orgId,jdbcType=VARCHAR}
	</select>
  <select id="queryOrgListByUserId" resultType="java.lang.String" parameterType="java.lang.String">
		SELECT
            string_agg(org_id,',')
		FROM t_user_org
		WHERE user_id = #{userId,jdbcType=VARCHAR}
	</select>


    <select id="queryOrgListVoByUserId" resultMap="BaseResultMap" parameterType="java.lang.String">
		SELECT
		id,user_id,org_id
		FROM t_user_org
		WHERE user_id = #{userId,jdbcType=VARCHAR}
	</select>

    <update id="updateByPrimaryKeySelective" parameterType="com.snbc.bbpf.system.db.common.entity.UserOrg" >
        update t_user_org
        <set >
            <if test="userId != null" >
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="orgId != null" >
                org_id = #{orgId,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="selectOrgListByUserIdList" resultMap="BaseResultMap" parameterType="list">
        SELECT
            id,user_id,org_id
        FROM t_user_org
        where user_id in
        <foreach collection="userIdList" item="test" index="no" open="("
                 separator="," close=")">
            #{test}
        </foreach>
    </select>
    <insert id="insertOrgListByUserIdList" parameterType="list" >
        insert into t_user_org (id,user_id, org_id)
        values
        <foreach collection="userIdList" item="user"  separator="," >
            (#{user.id},#{user.userId,jdbcType=VARCHAR}, #{user.orgId})
        </foreach>
    </insert>
    <delete id="deleteUserRoleByUserIdsAndOrg" parameterType="list" >
        delete from t_user_org   where org_id in(
          select org_id from t_org where org_path like
        CONCAT(
        (
        SELECT
        org_path FROM t_org WHERE org_id = #{orgId,jdbcType=VARCHAR}
        ),
        '%')
        )
        and user_id in
        <foreach collection="userIdList" item="test" index="no" open="("
                 separator="," close=")">
            #{test}
        </foreach>
        ;
        update  t_org  set supervisor=NULL,supervisor_phone=null  where org_id in ( select org_id from (
        select org_id from t_org where org_path like
        CONCAT(
        (
        SELECT
        org_path FROM t_org WHERE org_id = #{orgId,jdbcType=VARCHAR}
        ),
        '%')
        ) as o)
        and supervisor in
        <foreach collection="userIdList" item="test" index="no" open="("
                 separator="," close=")">
            #{test}
        </foreach>
    </delete>
    <delete id="deleteOrgByOrgIdList" parameterType="list" >
        delete from t_user_org
        where id in
        <foreach collection="orgIdList" item="test" index="no" open="("
                 separator="," close=")">
            #{test}
        </foreach>
    </delete>
</mapper>
