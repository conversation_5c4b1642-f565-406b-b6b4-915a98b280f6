package com.snbc.bbpf.grafana.proxy.dto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户权限数据传输对象
 * 
 * 用于封装从BBPF权限API获取的用户权限信息，包括：
 * - 可访问的仪表盘ID列表
 * - 可访问的数据源权限
 * - 操作权限（查看、编辑、导出等）
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2024-12-13
 */
public class UserPermissionDto {

	/**
	 * 用户ID
	 */
	private String userId;

	/**
	 * 用户名
	 */
	private String username;

	/**
	 * 可访问的仪表盘ID列表
	 */
	private List<String> accessibleDashboardIds;

	/**
	 * 可访问的文件夹ID列表
	 */
	private List<String> accessibleFolderIds;

	/**
	 * 可访问的数据源ID列表
	 */
	private List<String> accessibleDataSourceIds;

	/**
	 * 操作权限集合
	 */
	private Set<String> permissions;

	/**
	 * 数据权限过滤条件（可选）
	 */
	private String dataFilter;

	/**
	 * 权限有效期（时间戳）
	 */
	private Long expirationTime;

	/**
	 * 原始权限数据（Map格式）
	 * 用于存储从BBPF API返回的原始权限数据，不同服务可能返回不同格式的数据
	 */
	private Map<String, Object> rawPermissionData;

	/**
	 * 默认构造函数
	 */
	public UserPermissionDto() {
	}

	/**
	 * 构造函数
	 * 
	 * @param userId   用户ID
	 * @param username 用户名
	 */
	public UserPermissionDto(String userId, String username) {
		this.userId = userId;
		this.username = username;
	}

	// Getter and Setter methods

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public List<String> getAccessibleDashboardIds() {
		return accessibleDashboardIds;
	}

	public void setAccessibleDashboardIds(List<String> accessibleDashboardIds) {
		this.accessibleDashboardIds = accessibleDashboardIds;
	}

	public List<String> getAccessibleFolderIds() {
		return accessibleFolderIds;
	}

	public void setAccessibleFolderIds(List<String> accessibleFolderIds) {
		this.accessibleFolderIds = accessibleFolderIds;
	}

	public List<String> getAccessibleDataSourceIds() {
		return accessibleDataSourceIds;
	}

	public void setAccessibleDataSourceIds(List<String> accessibleDataSourceIds) {
		this.accessibleDataSourceIds = accessibleDataSourceIds;
	}

	public Set<String> getPermissions() {
		return permissions;
	}

	public void setPermissions(Set<String> permissions) {
		this.permissions = permissions;
	}

	public String getDataFilter() {
		return dataFilter;
	}

	public void setDataFilter(String dataFilter) {
		this.dataFilter = dataFilter;
	}

	public Long getExpirationTime() {
		return expirationTime;
	}

	public void setExpirationTime(Long expirationTime) {
		this.expirationTime = expirationTime;
	}

	public Map<String, Object> getRawPermissionData() {
		return rawPermissionData;
	}

	public void setRawPermissionData(Map<String, Object> rawPermissionData) {
		this.rawPermissionData = rawPermissionData;
	}

	/**
	 * 检查用户是否有访问指定仪表盘的权限
	 * 
	 * @param dashboardId 仪表盘ID
	 * @return boolean true表示有权限，false表示无权限
	 */
	public boolean canAccessDashboard(String dashboardId) {
		return accessibleDashboardIds != null && accessibleDashboardIds.contains(dashboardId);
	}

	/**
	 * 检查用户是否有访问指定文件夹的权限
	 * 
	 * @param folderId 文件夹ID
	 * @return boolean true表示有权限，false表示无权限
	 */
	public boolean canAccessFolder(String folderId) {
		return accessibleFolderIds != null && accessibleFolderIds.contains(folderId);
	}

	/**
	 * 检查用户是否有访问指定数据源的权限
	 * 
	 * @param dataSourceId 数据源ID
	 * @return boolean true表示有权限，false表示无权限
	 */
	public boolean canAccessDataSource(String dataSourceId) {
		return accessibleDataSourceIds != null && accessibleDataSourceIds.contains(dataSourceId);
	}

	/**
	 * 检查用户是否有指定的操作权限
	 * 
	 * @param permission 权限名称
	 * @return boolean true表示有权限，false表示无权限
	 */
	public boolean hasPermission(String permission) {
		return permissions != null && permissions.contains(permission);
	}

	/**
	 * 检查原始权限数据中是否包含指定的权限
	 * 
	 * @param key   权限键
	 * @param value 权限值
	 * @return boolean true表示有权限，false表示无权限
	 */
	public boolean hasRawPermission(String key, Object value) {
		if (rawPermissionData == null) {
			return false;
		}
		Object permissionValue = rawPermissionData.get(key);
		return permissionValue != null && permissionValue.equals(value);
	}

	/**
	 * 从原始权限数据中获取指定键的值
	 * 
	 * @param key 权限键
	 * @return Object 权限值，如果不存在则返回null
	 */
	public Object getRawPermissionValue(String key) {
		return rawPermissionData != null ? rawPermissionData.get(key) : null;
	}

	/**
	 * 检查权限是否已过期
	 * 
	 * @return boolean true表示已过期，false表示未过期
	 */
	public boolean isExpired() {
		return expirationTime != null && System.currentTimeMillis() > expirationTime;
	}

	@Override
	public String toString() {
		return "UserPermissionDto{" +
				"userId='" + userId + '\'' +
				", username='" + username + '\'' +
				", accessibleDashboardIds=" + accessibleDashboardIds +
				", accessibleFolderIds=" + accessibleFolderIds +
				", accessibleDataSourceIds=" + accessibleDataSourceIds +
				", permissions=" + permissions +
				", dataFilter='" + dataFilter + '\'' +
				", expirationTime=" + expirationTime +
				", rawPermissionData=" + rawPermissionData +
				'}';
	}
}