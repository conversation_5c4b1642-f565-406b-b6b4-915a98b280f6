/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service.impl;

import com.alibaba.fastjson.JSON;
import com.snbc.bbpf.bus.system.constans.CommonConstant;
import com.snbc.bbpf.bus.system.constans.Constant;
import com.snbc.bbpf.bus.system.constans.NumberConstant;
import com.snbc.bbpf.bus.system.constans.OrgConstant;
import com.snbc.bbpf.bus.system.exception.BusinessException;
import com.snbc.bbpf.bus.system.exception.ErrorMessage;
import com.snbc.bbpf.bus.system.mapstruct.ClassConvertorMapper;
import com.snbc.bbpf.bus.system.service.OrgService;
import com.snbc.bbpf.bus.system.utils.BossDES3Util;
import com.snbc.bbpf.bus.system.utils.CheckOrgPermission;
import com.snbc.bbpf.bus.system.utils.ChineseCharacterUtil;
import com.snbc.bbpf.bus.system.utils.CurrentUser;
import com.snbc.bbpf.system.db.common.entity.Org;
import com.snbc.bbpf.system.db.common.entity.User;
import com.snbc.bbpf.system.db.common.mapper.OrgMapper;
import com.snbc.bbpf.system.db.common.mapper.UserMapper;
import com.snbc.bbpf.system.db.common.mapper.UserOrgMapper;
import com.snbc.bbpf.system.db.common.mapper.UserRoleMapper;
import com.snbc.bbpf.system.db.common.vo.NoticeOrgVo;
import com.snbc.bbpf.system.db.common.vo.OrgSort;
import com.snbc.bbpf.system.db.common.vo.OrgVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * @ClassName: OrgServiceImpl
 * @Description: 组织机构业务处理
 * @module: si-bbpf-system
 * @Author: jiafei
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
@Service
@Slf4j
public class OrgServiceImpl implements OrgService {
    public static final String ORG_SORT_INNER = "inner";
    public static final String ORG_SORT_PREV = "prev";
    public static final String SLASH = "/";
    @Autowired
    private OrgMapper orgMapper;
    @Autowired
    private UserOrgMapper userOrgMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private UserRoleMapper userRoleMapper;
    @Autowired
    private CheckOrgPermission checkOrgPermission;
    @Autowired
    private RedisTemplate redisTemplate;
    /***
     * @Description: 方法描述
     * @Author: jiafei
     * @param :         orgId
     * @param :         orgLevel
     * @CreateDate: 2021/5/21 13:29
     * @UpdateDate: 2021/5/21 13:29
     * @return :        java.util.List<com.snbc.bbpf.system.db.common.vo.OrgVo>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public List<OrgVo> getOrgTree(String orgId, String orgLevel) {
        List<OrgVo> orgResList = new ArrayList<>();
        try {
            int levelTemp = 0;
            Org orgReq = new Org();
            orgReq.setOrgId(StringUtils.isBlank(orgId) ? OrgConstant.ORG_PARNENT_ID : orgId);
            orgReq.setOrgStatus(OrgConstant.ORG_STATUS_ENABLE);
            List<Org> orgList = orgMapper.getOrgs(orgReq);

            if (!CollectionUtils.isEmpty(orgList)) {
                //循环遍历该节点下子节点的组织机构
                orgResList = loopSearch(orgList, levelTemp, Integer.parseInt(orgLevel));
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorMessage.QUERY_ORG_TREE_FAIL.getMessage(), ErrorMessage.QUERY_ORG_TREE_FAIL.getCode(), e);
        }
        return orgResList;
    }

    /***
      * @Description:    获取组织机构树
      * @Author:         wangsong
      * @param :         orgId
      * @param :         orgLevel
      * @CreateDate:     2021/9/17 13:49
      * @UpdateDate:     2021/9/17 13:49
      * @return :        java.util.List<com.snbc.bbpf.system.db.common.vo.OrgVo>
      * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public List<OrgVo> orgTree(String userId){
        //查询该用户所属的角色列表
        List<String> roleIdList = userRoleMapper.selectRoleByUserId(userId);
        Org orgReq = new Org();
        // 查询所有
        orgReq.setOrgId(String.valueOf(NumberConstant.NO_ZERO));
        orgReq.setOrgStatus(OrgConstant.ORG_STATUS_ENABLE);
        List<OrgVo> allOrgTree = orgMapper.getAllOrgTree(orgReq);
        if (!CollectionUtils.isEmpty(roleIdList) && roleIdList.contains(String.valueOf(NumberConstant.F_NO_ONE))) {
            // 包含超级管理员：返回所有组织机构
            // 构建组织机构树
            return createTree(allOrgTree);
        }
        // 查询所有符合要求的数据:所属组织机构及数据权限配置的数据
        // 查询userId所属的组织机构
        List<OrgVo> orgTree = checkOrgPermission.checkOrg(userId, null);
        // 过滤数据
        long startTime = System.currentTimeMillis();
        List<OrgVo> list = removeNodesExceptSpecified(allOrgTree,orgTree);
        log.debug("role of user is not super,find org tree,spend time = {} ms", System.currentTimeMillis()-startTime);
        return createTree(list);
    }

    /**
     * 通过List构建树，并返回
     * @param parentOrgList
     */
    private static List<OrgVo> createTree(List<OrgVo> parentOrgList) {
        parentOrgList.forEach(parentOrg -> {
            //手机号解密
            decrypt3DES(parentOrg);
            List<OrgVo> childOrgVoList = new ArrayList<>();
            parentOrgList.forEach(childOrg -> {
                if (parentOrg.getOrgId().equals(childOrg.getParentId())){
                    childOrgVoList.add(childOrg);
                }
            });
            parentOrg.setChildren(childOrgVoList);
        });
        return parentOrgList.stream().filter(orgVo -> orgVo.getOrgId().equals(OrgConstant.ORG_PARNENT_ID)).collect(Collectors.toList());
    }

    /**
     *
     * @param allOrg  所有的组织机构
     * @param owenOrgList 拥有的组织机构
     * @return
     */
    private static List<OrgVo> removeNodesExceptSpecified(List<OrgVo> allOrg, List<OrgVo> owenOrgList) {
        // 遍历整棵树，找到指定的List<Org> orgTree中的所有节点
        List<OrgVo> specifiedList = Lists.newArrayList();
        long start = System.currentTimeMillis();
        for (OrgVo org : owenOrgList) {
            //上级路径，但不包含当前节点的orgId
            String path = org.getOrgPath().replace(CommonConstant.PERMISSION_FILTER_CHAR +org.getOrgId(),"");
            for (OrgVo org1 : allOrg) {
                // 节点本身:
                if(org.getOrgId().equals(org1.getOrgId())){
                    org.setDisabled(false);
                    // 一定要add 的是org1；因为对象属性全，org对象只有2个属性
                    specifiedList.add(org1);
                }
                //节点上级
                String[] arr = path.split(CommonConstant.PERMISSION_FILTER_CHAR);
                if (Arrays.asList(arr).contains(org1.getOrgId())) {
                    org1.setDisabled(true);
                    specifiedList.add(org1);
                }
            }
        }
        long end = System.currentTimeMillis();
        log.debug("removeNodesExceptSpecified one step send time={} ", end-start);
        specifiedList.forEach(orgVo -> {
            if(owenOrgList.stream().anyMatch(org -> orgVo.getOrgId().equals(org.getOrgId()))){
                orgVo.setDisabled(false);
            }
        });
        log.debug("removeNodesExceptSpecified send time={} ", System.currentTimeMillis()-end);
        //去重
        return specifiedList.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public List<NoticeOrgVo> getNoticeOrgName(List<String> orgIdList) {
        return orgMapper.getNoticeOrgName(orgIdList);
    }

    @Override
    public List<OrgVo> getOrgListById(String orgId, String orgLevel) throws BusinessException {
        List<OrgVo> orgResList = new ArrayList<>();
        try {
            int levelTemp = 0;
            Org orgReq = new Org();
            orgReq.setOrgId(StringUtils.isBlank(orgId) ? OrgConstant.ORG_PARNENT_ID : orgId);
            orgReq.setOrgStatus(OrgConstant.ORG_STATUS_ENABLE);
            List<Org> orgList = orgMapper.getOrgs(orgReq);
            if (!CollectionUtils.isEmpty(orgList)) {
                //循环遍历该节点下子节点的组织机构
                orgResList = loopSearch2List(orgList, levelTemp, Integer.parseInt(orgLevel));
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorMessage.QUERY_ORG_TREE_FAIL.getMessage(), ErrorMessage.QUERY_ORG_TREE_FAIL.getCode(), e);
        }
        return orgResList;
    }

    @Override
    public OrgVo getOrg(String orgId) throws BusinessException {
        OrgVo orgVo = null;
        try {
            Org org = orgMapper.selectByPrimaryKey(orgId);
            if (null != org) {
                orgVo = ClassConvertorMapper.INSTANCE.covertOrgVo(org);
                if (StringUtils.isNotBlank(orgVo.getParentId())) {
                    Org orgPar = orgMapper.selectByPrimaryKey(orgVo.getParentId());
                    orgVo.setParentName(orgPar == null ? "" : orgPar.getOrgName());
                }
                if (StringUtils.isNotBlank(orgVo.getSupervisor())) {
                    User user = userMapper.selectByPrimaryKey(orgVo.getSupervisor());
                    orgVo.setSupervisorName(user == null ? "" : user.getUserName());
                }
                // 手机号先解密
                decrypt3DES(orgVo);
            }

        } catch (Exception e) {
            throw new BusinessException(ErrorMessage.QUERY_ORG_FAIL.getMessage(), ErrorMessage.QUERY_ORG_FAIL.getCode(), e);
        }
        return orgVo;
    }

    /***
     * @Description: 递归遍历组织机构及子组织机构
     * @Author: jiafei
     * @param :         orgList
     * @param :         levelTemp
     * @param :         orgLevel
     * @CreateDate: 2021/5/19 13:35
     * @UpdateDate: 2021/5/19 13:35
     * @return :        java.util.List<com.snbc.bbpf.system.db.common.vo.OrgVo>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    private List<OrgVo> loopSearch(List<Org> orgList, int levelTemp, int orgLevel) {
        List<OrgVo> res = new ArrayList<>();
        for (Org org : orgList) {
            OrgVo orgVo = ClassConvertorMapper.INSTANCE.covertOrgVo(org);
            if (StringUtils.isNotBlank(orgVo.getSupervisor())) {
                //查询主管对象
                User user = userMapper.selectByPrimaryKey(orgVo.getSupervisor());
                orgVo.setSupervisorName(user == null ? "" : user.getUserName());
            }
            // 手机号解密
            decrypt3DES(orgVo);
            //0代表全部遍历
            if (0 == orgLevel || levelTemp < orgLevel) {
                //获取子节点
                List<Org> orgChildList = getListByParentId(org.getOrgId());
                if (!CollectionUtils.isEmpty(orgChildList)) {
                    //子节点数据放入children字段中
                    orgVo.setChildren(loopSearch(orgChildList, levelTemp + 1, orgLevel));
                }
            }
            res.add(orgVo);
        }
        return res;
    }
    /**
     * 手机号解密
     * @param orgRes
     */
    private static void decrypt3DES(OrgVo orgRes){
        orgRes.setSupervisorPhone(BossDES3Util.decrypt(orgRes.getSupervisorPhone()));
    }

    private List<OrgVo> loopSearch2List(List<Org> orgList, int levelTemp, int orgLevel) {
        List<OrgVo> res = new ArrayList<>();
        for (Org org : orgList) {
            //vo转换
            OrgVo orgRes = ClassConvertorMapper.INSTANCE.covertOrgVo(org);
            //主管及手机号解密
            if (StringUtils.isNotBlank(orgRes.getSupervisor())) {
                User user = userMapper.selectByPrimaryKey(orgRes.getSupervisor());
                orgRes.setSupervisorName(user == null ? "" : user.getUserName());
            }
            decrypt3DES(orgRes);
            //0代表全部遍历
            if (0 == orgLevel || levelTemp < orgLevel) {
                List<Org> orgChildList = getListByParentId(org.getOrgId());
                if (!CollectionUtils.isEmpty(orgChildList)) {
                    //子节点数据放入list中
                    res.addAll(loopSearch2List(orgChildList, levelTemp + 1, orgLevel));
                }
            }
            res.add(orgRes);
        }
        return res;
    }

    /***
     * @Description: 查询子组织机构
     * @Author: jiafei
     * @param :         orgId
     * @CreateDate: 2021/5/19 13:37
     * @UpdateDate: 2021/5/19 13:37
     * @return :        java.util.List<com.snbc.bbpf.system.db.common.entity.Org>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    private List<Org> getListByParentId(String orgId) {
        Org orgReq = new Org();
        orgReq.setOrgStatus(OrgConstant.ORG_STATUS_ENABLE);
        orgReq.setParentId(orgId);
        return orgMapper.getOrgs(orgReq);
    }

    /***
     * @Description: 新增组织机构
     * 增加校验下部门主管是否真实 by liangjb
     * @Author: jiafei
     * @param :         orgVo
     * @param createUserId
     * @CreateDate: 2021/5/19 13:37
     * @UpdateDate: 2021/5/19 13:37
     * @return :        void
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public String addOrg(OrgVo orgVo, String createUserId) throws BusinessException {
        try {
            //针对用户ID进行获取手机号
            if (StringUtils.isNotBlank(orgVo.getSupervisor())) {
                User user = userMapper.selectByPrimaryKey(orgVo.getSupervisor());
                ErrorMessage.USER_NOT_EXIST.assertNotNull(user);
                orgVo.setSupervisorPhone(user.getPhone());
                orgVo.setSupervisorName(user.getUserName());
            }
            //wjc 20230621 当时登录人，校验选中的组织机构是否是本人数据权限配置的组织机构
            checkOrgPermission.checkOrg(CurrentUser.getUserId(), Collections.singletonList(orgVo.getParentId()));

            Org parentOrg = this.queryOrg(orgVo.getParentId());
            //校验名称在该父级下是否存在
            checkOrgNameExist(orgVo.getParentId(), orgVo.getOrgName(), "");
            //查询父机构最大序列
            int maxOrderBy = this.getMaxOrderByParentId(orgVo.getParentId());
            //组织机构名称首字母作为机构编号
            String orgCode = ChineseCharacterUtil.getUpperCase(orgVo.getOrgName(), false);
            //生成orgCode
            String orgId = UUID.randomUUID().toString();
            orgVo.setOrgId(orgId);
            //查询生成的code是否有重复
            orgVo.setOrgCode(getOrgCode(orgCode));
            orgVo.setCreateTime(LocalDateTime.now());
            orgVo.setSequence(orgVo.getSequence() != null ? orgVo.getSequence() : (maxOrderBy + 1));
            orgVo.setOrgStatus(OrgConstant.ORG_STATUS_ENABLE);
            orgVo.setOrgPath(parentOrg.getOrgPath() + SLASH + orgId);
            //当前登录人ID orgId
            orgVo.setCreateUserId(CurrentUser.getUserId());
            //是否父ID为根节点
            if (OrgConstant.ORG_PARNENT_ID.equalsIgnoreCase(orgVo.getParentId())) {
                orgVo.setOrgLevel(1);
            } else {
                orgVo.setOrgLevel(parentOrg.getOrgLevel() + 1);
            }
            log.info("addOrg orgVo={}", JSON.toJSONString(orgVo));
            orgMapper.insert(ClassConvertorMapper.INSTANCE.covertOrg(orgVo));
            //删除缓存
            redisTemplate.delete(Constant.USER_DATA_AUTH_ORG_AND_SUB_ORG);
            redisTemplate.delete(Constant.USER_DATA_AUTH_ORG);
            // 删除 ：组织机构数据权限
            redisTemplate.delete(Constant.ORG_DATA_AUTH_ORG_AND_SUB_ORG);
            redisTemplate.delete(Constant.ORG_DATA_AUTH_ORG_ORG);
            redisTemplate.delete(Constant.ORG_DATA_AUTH_ORG_AND_CREATE_ORG);
        } catch (BusinessException e) {
            log.debug(e.toString());
            throw new BusinessException(e.getMessage(), e.getCode(), e);
        } catch (Exception e) {
            log.debug(e.toString());
            throw new BusinessException(ErrorMessage.ADD_ORG_FAIL.getMessage(), ErrorMessage.ADD_ORG_FAIL.getCode(), e);
        }
        return orgVo.getOrgId();
    }

    /***
     * @Description: 编辑组织机构
     * @Author: jiafei
     * @param :         orgVo
     * @param userName
     * @param currentUserId
     * @CreateDate: 2021/5/19 13:39
     * @UpdateDate: 2021/5/19 13:39
     * @return :        void
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public void updateOrg(OrgVo orgVo, String userName, String currentUserId) throws BusinessException {
        try {
            //针对用户ID进行获取手机号
            if (StringUtils.isNotBlank(orgVo.getSupervisor())) {
                orgVo.setSupervisorName(userName);
                String encryptPhone = BossDES3Util.encrypt(orgVo.getSupervisorPhone());
                orgVo.setSupervisorPhone(encryptPhone);
            }
            //wjc 20230621 当时登录人，校验选中的组织机构是否是本人数据权限配置的组织机构
            checkOrgPermission.checkOrg(currentUserId, Collections.singletonList(orgVo.getOrgId()));
            //校验名称在该父级下是否存在
            checkOrgNameExist(orgVo.getParentId(), orgVo.getOrgName(), orgVo.getOrgId());
            orgVo.setUpdateTime(LocalDateTime.now());
            log.info("updateOrg orgVo={}", JSON.toJSONString(orgVo));
            orgMapper.updateByPrimaryKeySelective(ClassConvertorMapper.INSTANCE.covertOrg(orgVo));
        } catch (BusinessException e) {
            throw new BusinessException(e.getMessage(), e.getCode(), e);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(ErrorMessage.EDIT_ORG_FAIL.getMessage(), ErrorMessage.EDIT_ORG_FAIL.getCode(), e);
        }
    }

    /***
     * @Description: 删除组织机构及子组织机构
     * @Author: jiafei
     * @param :         orgId
     * @CreateDate: 2021/5/19 13:45
     * @UpdateDate: 2021/5/19 13:45
     * @return :        java.lang.String
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public void delOrg(String orgId) throws BusinessException {
        //wjc 20230621 当时登录人，校验选中的组织机构是否是本人数据权限配置的组织机构
        checkOrgPermission.checkOrg(CurrentUser.getUserId(), Collections.singletonList(orgId));
        try {
            Org org = orgMapper.selectByPrimaryKey(orgId);
            if (null != org) {
                String existUserOrgName = "";
                if (userOrgMapper.selectUserCountByOrgId(org.getOrgId()) > 0) {
                    existUserOrgName = org.getOrgName();
                } else {
                    //查询子组织机构是否存在用户
                    existUserOrgName = loopSearchOrgExistUser(org.getOrgId());
                }
                //1、判断 本身及子组织机构无用户
                if(StringUtils.isNotBlank(existUserOrgName)){
                    throw new BusinessException(
                            MessageFormat.format(ErrorMessage.ORG_HAS_USER_FAIL.getMessage(), existUserOrgName),
                            ErrorMessage.ORG_HAS_USER_FAIL.getCode());
                }
                // wjc;20230706 add true(不相等) 说明组织机构下存在角色，抛异常
                // 查询本身及下级组织机构
                List<String> subOrgIdList = orgMapper.selectAllOrgIdByOrgPath(Collections.singletonList(org.getOrgPath()));
                subOrgIdList.forEach(subOrg->ErrorMessage.ORG_HAS_ROLE_FAIL.assertEquals(checkOrgPermission.selectRoleByOrgId(subOrg),false));
                //循环删除该组织机构及以下机构
                loopDelOrg(orgId);
            }
        } catch (BusinessException e) {
            throw new BusinessException(e.getMessage(), e.getCode(), e);
        } catch (Exception e) {
            throw new BusinessException(ErrorMessage.DEL_ORG_FAIL.getMessage(), ErrorMessage.DEL_ORG_FAIL.getCode(), e);
        }
    }

    private void loopDelOrg(String orgId) {
        orgMapper.deleteByPrimaryKey(orgId);
        List<Org> orgList = orgMapper.queryOrgListByParentId(orgId);
        if (!CollectionUtils.isEmpty(orgList)) {
            for (Org temp : orgList) {
                loopDelOrg(temp.getOrgId());
            }
        }
    }

    private String loopSearchOrgExistUser(String parentId) {
        List<Org> orgList = orgMapper.queryOrgListByParentId(parentId);
        String existUserOrgName = "";
        if (!CollectionUtils.isEmpty(orgList)) {
            existUserOrgName = checkUserCount(orgList);
            if (StringUtils.isBlank(existUserOrgName)) {
                for (Org temp : orgList) {
                    String result = loopSearchOrgExistUser(temp.getOrgId());
                    if (StringUtils.isNotBlank(result)) {
                        return result;
                    }
                }
            } else {
                return existUserOrgName;
            }
        }
        return existUserOrgName;
    }

    private String checkUserCount(List<Org> orgList) {
        String existUserOrgName = "";
        for (Org temp : orgList) {
            if (userOrgMapper.selectUserCountByOrgId(temp.getOrgId()) > 0) {
                existUserOrgName = temp.getOrgName();
                break;
            }
        }
        return existUserOrgName;
    }

    /**
     * 组织机构编码唯一,生成规则：按组织机构首字母拼接，如果重复追加数字;
     *
     * @return
     */
    private String getOrgCode(String code) {
        int seq = 0;
        Org orgReq = new Org();
        orgReq.setOrgCode(code);
        List<Org> orgList = orgMapper.getOrgs(orgReq);
        if (!CollectionUtils.isEmpty(orgList)) {
            code = getOrgCode(code + String.valueOf(seq + 1));
        }
        return code;
    }

    /***
     * @Description: 组织机构拖拽排序
     * @Author: jiafei
     * @param :         orgSort
     * @CreateDate: 2021/5/19 13:46
     * @UpdateDate: 2021/5/19 13:46
     * @return :        java.util.List<com.snbc.bbpf.system.db.common.vo.OrgVo>
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    @Override
    public List<OrgVo> orgSort(OrgSort orgSort) throws BusinessException {
        List<Org> orgSortList = null;
        Org org = this.queryOrg(orgSort.getThisNodeId());
        try {
            //校验名称在该父级下是否存在 inner拖拽父节点应为目标节点
            if (orgSort.getSortType().equals(ORG_SORT_INNER)) {
                checkOrgNameExist(orgSort.getTargetNodeId(), org.getOrgName(), org.getOrgId());
            } else {
                checkOrgNameExist(orgSort.getParentId(), org.getOrgName(), org.getOrgId());
            }
            if (orgSort.getSortType().equals(ORG_SORT_INNER)) {
                int maxOrderByParentId = this.getMaxOrderByParentId(orgSort.getTargetNodeId());
                //目标节点
                Org parentOrg = this.queryOrg(orgSort.getTargetNodeId());
                if (OrgConstant.ORG_PARNENT_ID.equalsIgnoreCase(parentOrg.getParentId())) {
                    org.setOrgLevel(1);
                }
                String oldPath = org.getOrgPath();
                org.setSequence(maxOrderByParentId + 1);
                org.setOrgPath(parentOrg.getOrgPath()+ SLASH + org.getOrgId());
                org.setParentId(orgSort.getTargetNodeId());
                this.updateOrganize(org);
                //修改组织机构path
                orgMapper.updateOrgPathAndLevel(oldPath,
                        parentOrg.getOrgPath() + SLASH + org.getOrgId());
                orgSortList = this.queryOrgListByParentId(orgSort.getTargetNodeId());
            } else {
                // 根据父节点查询所有子节点
                List<Org> orgList = this.queryOrgListByParentId(orgSort.getParentId());
                // 对子节点重新排序
                orgSortList = sort(orgList, orgSort.getSortType(), orgSort.getThisNodeId(), orgSort.getTargetNodeId(), orgSort.getParentId());
                this.updateOrgList(orgSortList);
                orgSortList = this.queryOrgListByParentId(orgSort.getParentId());
            }
            //删除缓存
            redisTemplate.delete(Constant.USER_DATA_AUTH_ORG_AND_SUB_ORG);
            redisTemplate.delete(Constant.USER_DATA_AUTH_ORG);
            // 删除 ：组织机构数据权限
            redisTemplate.delete(Constant.ORG_DATA_AUTH_ORG_AND_SUB_ORG);
            redisTemplate.delete(Constant.ORG_DATA_AUTH_ORG_ORG);
            redisTemplate.delete(Constant.ORG_DATA_AUTH_ORG_AND_CREATE_ORG);
        } catch (BusinessException e) {
            throw new BusinessException(e.getMessage(), e.getCode(), e);
        } catch (Exception e) {
            throw new BusinessException(ErrorMessage.ORG_SORT_FAIL.getMessage(), ErrorMessage.ORG_SORT_FAIL.getCode(), e);
        }
        return ClassConvertorMapper.INSTANCE.covertOrgVoList(orgSortList);
    }

    /**
     * 修改组织机构
     *
     * @param orgList
     */
    public void updateOrgList(List<Org> orgList) {
        orgMapper.updateOrgList(orgList);
    }

    /**
     * 获得父机构最大排序
     *
     * @param parentId
     * @return
     * @throws Exception
     */
    public int getMaxOrderByParentId(String parentId) throws BusinessException {
        return orgMapper.queryMaxOrderByParentId(parentId);
    }

    /**
     * 根据ID查询组织机构
     *
     * @param orgId
     * @return
     * @throws Exception
     */
    public Org queryOrg(String orgId) throws BusinessException {
        return orgMapper.selectByPrimaryKey(orgId);
    }

    public int updateOrganize(Org org) throws BusinessException {
        org.setUpdateTime(LocalDateTime.now());
        return orgMapper.updateByPrimaryKeySelective(org);
    }

    public List<Org> queryOrgListByParentId(String parentOrgId) throws BusinessException {
        return orgMapper.queryOrgListByParentId(parentOrgId);
    }

    /***
     * @Description: parentId下的资组织机构名称是否重复
     * @Author: jiafei
     * @param :         parentId
     * @param :         orgName
     * @param :         orgId
     * @CreateDate: 2021/6/28 9:46
     * @UpdateDate: 2021/6/28 9:46
     * @return :        boolean
     * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
     */
    private void checkOrgNameExist(String parentId, String orgName, String orgId) throws BusinessException {
        List<Org> orgList = this.queryOrgListByParentId(parentId);
        for (Org org : orgList) {
            if ((null != orgId && !org.getOrgId().equalsIgnoreCase(orgId)) && org.getOrgName().equalsIgnoreCase(orgName)) {
                throw new BusinessException(ErrorMessage.ORG_NAME_EXIST.getMessage(), ErrorMessage.ORG_NAME_EXIST.getCode());
            }
        }
    }

    /**
     * 排序
     *
     * @param orgList
     * @param sortType
     * @param thisNodeId
     * @param targetNodeId
     * @param parentId
     * @return
     */
    private List<Org> sort(List<Org> orgList, String sortType, String thisNodeId, String targetNodeId, String parentId) {
        int targetNodeSubscript = 0;
        Org orgSort = new Org();
        orgSort.setOrgId(thisNodeId);
        orgSort.setParentId(parentId);
        //获取本节点下标值，并删除此节点
        int flag = -1;
        for (int i = 0; i < orgList.size(); i++) {
            if (orgList.get(i).getOrgId().equals(thisNodeId)) {
                orgSort = orgList.get(i);
                flag = i;
            }
            //获取目标节点下标值 如果是prev（向上插入）节点值=i，list下标从0开始
            if (orgList.get(i).getOrgId().equals(targetNodeId)) {
                if (sortType.equals(ORG_SORT_PREV)) {
                    targetNodeSubscript = i;
                } else {
                    targetNodeSubscript = i + 1;
                }
            }
        }
        if (flag > -1) {
            orgList.remove(flag);
            if (flag < targetNodeSubscript) {
                targetNodeSubscript = targetNodeSubscript - 1;
            }
        }
        //查询本节点org
        Org thisNodeOrg = this.queryOrg(thisNodeId);
        //目标父节点org
        Org targetParentNodeOrg = this.queryOrg(parentId);
        //替换所有orgpath和orglevel
        orgMapper.updateOrgPathAndLevel(thisNodeOrg.getOrgPath(),
                targetParentNodeOrg.getOrgPath() + SLASH + thisNodeOrg.getOrgId());
        //将目标节点list中的第一个数据的orgpath替换为本节点的orgpath
        String afterOrgPath = orgList.get(NumberConstant.NO_ZERO).getOrgPath()
                .replace(orgList.get(NumberConstant.NO_ZERO).getOrgId(),thisNodeId);
        orgSort.setOrgPath(afterOrgPath);
        //向list中插入节点
        orgList.add(targetNodeSubscript, orgSort);
        //next目标节点值等于list的长度直接add
        for (int i = 0; i < orgList.size(); i++) {
            Org org = orgList.get(i);
            org.setSequence(i);
        }
        return orgList;
    }
}
