# Grafana分析功能控制说明

## 功能概述

本功能允许通过配置来禁用Grafana界面中的分析相关功能，包括Explore（探索）和Inspect（检查）功能，以限制用户对数据的深度分析能力。

## 实现原理

通过在代理返回的HTML页面中注入CSS样式和JavaScript代码来实现：

1. **CSS样式隐藏**：隐藏相关的按钮、链接和菜单项
2. **JavaScript事件拦截**：拦截点击事件，阻止用户访问分析功能
3. **DOM监听**：动态监听页面变化，隐藏新添加的分析元素

## 配置说明

### 配置项

在`application.properties`或`application-local.properties`中添加以下配置：

```properties
# Grafana分析功能控制配置
# 是否禁用Grafana分析功能（Explore和Inspect）
# true: 禁用分析功能，false: 启用分析功能
bbpf.grafana.proxy.disable-analysis-features=true
```

### 配置值说明

- `true`：禁用分析功能（默认值）
- `false`：启用分析功能，用户可以正常使用Explore和Inspect功能

## 代码修改说明

### 1. 配置类修改

在`GrafanaProxyConfig.java`中添加了新的配置属性：

```java
/**
 * 是否禁用Grafana分析功能（Explore和Inspect）
 */
private boolean disableAnalysisFeatures = true;
```

### 2. 服务实现修改

在`GrafanaProxyServiceImpl.java`的`generatePermissionScript`方法中：

- 重构了代码结构，使用StringBuilder提高性能
- 添加了配置检查逻辑，只有在`config.isDisableAnalysisFeatures()`为true时才注入禁用代码
- 保持了原有的CSS样式和JavaScript功能

### 3. 配置文件修改

在以下配置文件中添加了新的配置项：
- `application-local.properties`
- `application-docker.properties`

## 禁用的功能

当启用此功能时，以下Grafana功能将被禁用：

1. **Explore功能**：
   - 导航栏中的Explore链接
   - 所有包含"explore"的按钮和链接
   - 相关的菜单项

2. **Inspect功能**：
   - 面板菜单中的Inspect选项
   - 所有包含"inspect"的按钮和链接
   - 数据检查相关功能

## 技术实现细节

### CSS选择器

```css
/* 隐藏Explore按钮 */
[data-testid="nav-item-explore"],
a[href*="/explore"],
[aria-label*="Explore"],
.navbar-nav a[href*="explore"] {
  display: none !important;
}

/* 隐藏Inspect按钮和菜单 */
[data-testid*="inspect"],
[aria-label*="Inspect"],
.dropdown-item[href*="inspect"],
.panel-menu-item[data-testid*="inspect"] {
  display: none !important;
}
```

### JavaScript事件拦截

```javascript
document.addEventListener('click', function(e) {
  var target = e.target;
  var href = target.href || target.closest('a')?.href || '';
  var testId = target.getAttribute('data-testid') || '';
  var ariaLabel = target.getAttribute('aria-label') || '';
  
  if (href.includes('/explore') || 
      href.includes('inspect') ||
      testId.includes('explore') ||
      testId.includes('inspect') ||
      ariaLabel.includes('Explore') ||
      ariaLabel.includes('Inspect')) {
    e.preventDefault();
    e.stopPropagation();
    console.log('分析功能已被禁用');
    return false;
  }
}, true);
```

### DOM变化监听

```javascript
var observer = new MutationObserver(function(mutations) {
  mutations.forEach(function(mutation) {
    mutation.addedNodes.forEach(function(node) {
      if (node.nodeType === 1) {
        var elements = node.querySelectorAll ? 
          node.querySelectorAll('[data-testid*="explore"], [data-testid*="inspect"], [aria-label*="Explore"], [aria-label*="Inspect"], a[href*="/explore"], a[href*="inspect"]') : [];
        elements.forEach(function(el) {
          el.style.display = 'none';
        });
      }
    });
  });
});

observer.observe(document.body, {
  childList: true,
  subtree: true
});
```

## 使用建议

1. **生产环境**：建议设置为`true`，限制用户的数据分析能力
2. **开发环境**：可以设置为`false`，方便开发人员调试和测试
3. **测试环境**：根据测试需求灵活配置

## 注意事项

1. 此功能仅在前端层面禁用相关功能，不影响Grafana后端API
2. 技术熟练的用户仍可能通过直接访问API或修改前端代码来绕过限制
3. 如需更严格的权限控制，建议结合后端API权限验证
4. 配置修改后需要重启应用才能生效

## 扩展性

如需禁用其他Grafana功能，可以在`generatePermissionScript`方法中添加相应的CSS选择器和JavaScript逻辑。建议为每个功能添加独立的配置项，以提供更细粒度的控制。