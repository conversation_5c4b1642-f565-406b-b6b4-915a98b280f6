/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.service;



/**
 * @ClassName: ThirdLoginBind
 * @Description: 用户三方绑定基础接口,
 * 用户绑定CODE、解绑、由具体utils实现
 * 更改
 * @module: si-bbpf-umis
 * @Author: liangjb
 * @date: 2021/5/15
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
public interface ThirdLoginBind {

    /**
     * 解除用户绑定
     * @param userId 对应的唯一ID
     * @return
     * @throws Exception
     */
    String delUserThirdBind(String userId) throws Exception;


    /**
     * 获得用户登录Code
     * @param bindId 该State 唯一的编码替换state
     * @return
     * @throws Exception
     */
    String getUserLoginQRcode(String bindId) throws Exception ;
    /**
     * 获得用户登录Code
     * @param code 用户授权码
     * @param state 回调标识
     * @return
     * @throws Exception
     */
    String dealCallback(String code, String state) throws Exception;
}
