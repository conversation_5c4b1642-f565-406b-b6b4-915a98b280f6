<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snbc.bbpf.system.db.common.mapper.OrgMapper">
  <resultMap id="BaseResultMap" type="com.snbc.bbpf.system.db.common.entity.Org">
    <id column="org_id" property="orgId" jdbcType="VARCHAR"/>
    <result column="org_name" property="orgName" jdbcType="VARCHAR"/>
    <result column="org_code" property="orgCode" jdbcType="VARCHAR"/>
    <result column="parent_id" property="parentId" jdbcType="VARCHAR"/>
    <result column="sequence" property="sequence" jdbcType="INTEGER"/>
    <result column="org_level" property="orgLevel" jdbcType="INTEGER"/>
    <result column="org_path" property="orgPath" jdbcType="VARCHAR"/>
    <result column="org_status" property="orgStatus" jdbcType="INTEGER"/>
    <result column="org_desc" property="orgDesc" jdbcType="VARCHAR"/>
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    <result column="supervisor" property="supervisor" jdbcType="VARCHAR"/>
    <result column="supervisor_phone" property="supervisorPhone" jdbcType="VARCHAR"/>
    <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
    <result column="create_org_id" property="createOrgId" jdbcType="VARCHAR"/>
  </resultMap>

  <sql id="Base_Column_List">
    org_id, org_name, org_code, parent_id, `sequence`, org_level, org_path, org_status,
    org_desc, create_time, update_time,supervisor,supervisor_phone,create_user_id,create_org_id
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_org
    where org_id = #{orgId,jdbcType=VARCHAR}
  </select>
  <select id="getOrgByName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_org
    where org_name = #{orgName,jdbcType=VARCHAR} and parent_id=#{parentId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey">
    delete
    from t_org
    where org_id = #{orgId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" useGeneratedKeys="true" keyProperty="orgId"
          parameterType="com.snbc.bbpf.system.db.common.entity.Org">
    insert into t_org (org_id, org_name, org_code,
                       parent_id, sequence, org_level,
                       org_path, org_status,
                       org_desc, create_time, update_time, supervisor, supervisor_phone, create_user_id,
                       create_org_id)
    values (#{orgId,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR}, #{orgCode,jdbcType=VARCHAR},
            #{parentId,jdbcType=VARCHAR}, #{sequence,jdbcType=INTEGER}, #{orgLevel,jdbcType=INTEGER},
            #{orgPath,jdbcType=VARCHAR}, #{orgStatus,jdbcType=INTEGER},
            #{orgDesc,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
            #{supervisor,jdbcType=VARCHAR}, #{supervisorPhone,jdbcType=VARCHAR}, #{createUserId,jdbcType=VARCHAR},
            #{createOrgId,jdbcType=VARCHAR})
  </insert>

  <select id="getOrgs" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_org where 1=1
    <if test="orgId != null and orgId !=''">
      AND org_id = #{orgId,jdbcType=VARCHAR}
    </if>
    <if test="orgCode != null and orgCode !=''">
      AND org_code = #{orgCode,jdbcType=VARCHAR}
    </if>
    <if test="parentId != null and parentId !=''">
      AND parent_id = #{parentId,jdbcType=VARCHAR}
    </if>
    <if test="orgStatus != null">
      AND org_status = #{orgStatus,jdbcType=INTEGER}
    </if>
    order by `sequence`
  </select>

  <select id="queryMaxOrderByParentId" resultType="java.lang.Integer" parameterType="java.lang.String">
    SELECT IFNULL(max(sequence), 0)
    FROM t_org
    where parent_id = #{parentId,jdbcType=VARCHAR}
  </select>
  <select id="queryOrgListByParentId" resultMap="BaseResultMap" parameterType="java.lang.String">
    select
    <include refid="Base_Column_List"/>
    from t_org
    <where>
      <if test="parentOrgId == '0'.toString()">
        AND parent_id = #{parentOrgId,jdbcType=VARCHAR} OR parent_id IS NULL
      </if>
      <if test="parentOrgId != null and parentOrgId !=''">
        AND parent_id = #{parentOrgId,jdbcType=VARCHAR}
      </if>
    </where>
    ORDER BY `sequence`
  </select>

  <update id="updateByPrimaryKeySelective" parameterType="com.snbc.bbpf.system.db.common.entity.Org">
    update t_org
    <set>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="orgCode != null">
        org_code = #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="sequence != null">
        sequence = #{sequence,jdbcType=INTEGER},
      </if>
      <if test="orgLevel != null">
        org_level = #{orgLevel,jdbcType=INTEGER},
      </if>
      <if test="orgPath != null">
        org_path = #{orgPath,jdbcType=VARCHAR},
      </if>
      <if test="orgStatus != null">
        org_status = #{orgStatus,jdbcType=INTEGER},
      </if>
      <if test="orgDesc != null">
        org_desc = #{orgDesc,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="supervisor != null">
        supervisor = #{supervisor,jdbcType=VARCHAR},
      </if>
      <if test="supervisorPhone != null">
        supervisor_phone = #{supervisorPhone,jdbcType=VARCHAR},
      </if>
    </set>
    where org_id = #{orgId,jdbcType=VARCHAR}
  </update>

  <update id="updateOrgList" parameterType="list">
    update t_org
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="sequence =case" suffix="end,">
        <foreach collection="list" item="item">
          <if test="item.sequence!=null">
            when org_id=#{item.orgId} then #{item.sequence}
          </if>
        </foreach>
      </trim>
      <trim prefix="parent_id =case" suffix="end,">
        <foreach collection="list" item="item">
          when org_id=#{item.orgId} then #{item.parentId}
        </foreach>
      </trim>
      <trim prefix="org_path =case" suffix="end,">
        <foreach collection="list" item="item">
          when org_id=#{item.orgId} then #{item.orgPath}
        </foreach>
      </trim>
      <trim prefix="update_time =case" suffix="end,">
        <foreach collection="list" item="item">
          when org_id=#{item.orgId} then now()
        </foreach>
      </trim>
    </trim>
    <where>
      <foreach collection="list" separator="or" item="item" index="index">
        org_id = #{item.orgId}
      </foreach>
    </where>
  </update>
  <update id="updateOrgPathAndLevel">
    <!--修改orgpath-->
    UPDATE t_org
    SET org_path = REPLACE (org_path,#{oldPath},#{newPath})
    where org_path like concat(#{oldPath},'%');
    <!--修改orglevel-->
    UPDATE t_org
    SET org_level = length(org_path) - length(REPLACE(org_path, '/', '')) - 1;
  </update>

  <select id="getOrgsByUserId" parameterType="java.lang.String"
          resultType="com.snbc.bbpf.system.db.common.entity.Org">
    select t1.org_name as orgName, t1.org_id as orgId
    FROM t_org t1
           LEFT JOIN t_user_org t2 ON t1.org_id = t2.org_id
    WHERE t2.user_id = #{userId,jdbcType=VARCHAR}
  </select>

  <select id="getOrgIdPath4DataRule" resultType="com.snbc.bbpf.system.db.common.entity.Org">
    select distinct (t1.org_path) as orgPath, t1.org_id as orgId
    FROM t_org t1
    LEFT JOIN t_user_org u ON t1.org_id = u.org_id
    LEFT JOIN t_org o ON u.org_id = o.org_id
  </select>
  <!--wjc1-->
  <select id="selectIdsByOrgNames" resultType="java.lang.String">
    SELECT
    GROUP_CONCAT(org_id)
    FROM t_org
    WHERE org_name in
    <foreach collection="belongOrgNames" index="index" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <select id="selectOrgIdsByParentId" resultType="java.lang.String" parameterType="java.lang.String">
    SELECT org_id AS orgId
    FROM t_org
    WHERE org_path LIKE CONCAT
      (
            (SELECT org_path
             FROM t_org
             WHERE org_id = #{orgId,jdbcType=VARCHAR}),
            '%'
      )
  </select>

  <select id="selectAllOrgIdByOrgPath" resultType="java.lang.String">
    SELECT distinct org_id FROM t_org
    where 1=1 and
    <foreach collection="orgPathList" item="orgPath" index="index" open="(" separator="or" close=")">
      org_path LIKE CONCAT(#{orgPath,jdbcType=VARCHAR},'%')
    </foreach>
  </select>
  <update id="updateBySupervisor" parameterType="java.lang.String">
    update t_org
    set supervisor='',
        supervisor_phone=''
    where supervisor = #{supervisorId,jdbcType=VARCHAR}
  </update>
  <select id="getAllEnableOrgItems" resultType="com.snbc.bbpf.system.db.common.dto.OrgItemDto">
    SELECT org_id orgId, org_name orgName, org_path orgIdPath
    from t_org
    where org_status = 1
    order by org_level asc
  </select>

  <select id="selectOrgNamesByUserId" resultType="com.snbc.bbpf.system.db.common.dto.OrgNamesDto">
    SELECT
    t2.user_id as userId,
    GROUP_CONCAT(org_name) as orgNames
    FROM
    t_org t1
    LEFT JOIN t_user_org t2 ON t1.org_id = t2.org_id and t2.org_id != "0"
    WHERE
    t2.user_id IN
    <foreach collection="userIdList" item="userId" index="index" open="(" separator="," close=")">
      #{userId}
    </foreach>
    GROUP BY userId
  </select>
  <select id="selectSupervisorByUserId" resultType="com.snbc.bbpf.system.db.common.vo.OrgSupervisorVo">
    SELECT
    t2.user_id as userId,
    GROUP_CONCAT(t1.org_id) as orgIds
    FROM
    t_org t1
    LEFT JOIN t_user_org t2 ON t1.org_id = t2.org_id and t2.org_id != "0"
    WHERE
    t2.user_id IN
    <foreach collection="userIdList" item="userId" index="index" open="(" separator="," close=")">
      #{userId}
    </foreach>
    GROUP BY userId
  </select>
  <select id="getAllOrgTree" resultType="com.snbc.bbpf.system.db.common.vo.OrgVo">
    SELECT t1.org_id           as orgId,
           t1.org_name         as orgName,
           t1.`sequence`,
           t1.org_code         as orgCode,
           t1.org_status       as orgStatus,
           t1.org_desc         as orgDesc,
           t1.org_level        as orgLevel,
           t1.parent_id        as parentId,
           t1.supervisor,
           t1.supervisor_phone as supervisorPhone,
           t2.user_name        AS supervisorName
    FROM t_org t1
           LEFT JOIN t_user t2 ON t1.supervisor = t2.user_id
    where t1.org_path like CONCAT((select org_path from t_org where org_id = #{orgId,jdbcType=VARCHAR}), '%')
      and t1.org_status = #{orgStatus,jdbcType=INTEGER}
    order by `sequence`
  </select>
  <select id="getOrgIdByCreateUserId" resultType="string">
    select org_id
    from t_org
    where create_user_id = #{userId,jdbcType=VARCHAR} and org_status= 1;
  </select>
  <select id="getNoticeOrgName" resultType="com.snbc.bbpf.system.db.common.vo.NoticeOrgVo">
    SELECT distinct
    org_id AS orgId,
    org_name as orgName
    FROM t_org
    <where>
      <if test="orgIdList.size() != 0">
        and org_id IN
        <foreach collection="orgIdList" item="orgId" index="index" open="(" separator="," close=")">
          #{orgId}
        </foreach>
      </if>
    </where>
  </select>
</mapper>
