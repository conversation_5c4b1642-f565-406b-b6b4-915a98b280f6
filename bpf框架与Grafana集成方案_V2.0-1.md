# BPF与Grafana集成预研方案

## 核心架构

我们在BPF系统和Grafana之间部署了一个**Grafana代理服务（bbpf-grafana-proxy）**。所有来自BBPF用户的Grafana访问请求都通过此代理服务，实现统一认证、权限控制和SQL拦截。

```mermaid
graph TD
    subgraph BBPF系统
        A[用户登录BBPF] --> B(获取BBPF JWT Token)
        C[BBPF前端/应用] --> D{请求访问Grafana资源}
    end

    subgraph Grafana代理服务
        D --> E[JWT认证过滤器]
        E --> F{调用BBPF后台验证Token}
        F -- Token有效 --> G[获取用户权限信息]
        G --> H[SQL拦截器]
        H --> I{检查API路径是否需要拦截}
        I -- 需要拦截 --> J[解析SQL查询]
        J --> K[根据权限修改SQL]
        K --> L[转发修改后的请求]
        I -- 无需拦截 --> L
        F -- Token无效 --> N[拒绝访问]
    end

    subgraph BBPF后端服务
        O[BBPF认证服务]
        P[BBPF权限API]
        Q[Redis缓存]
        F --> O
        G --> P
        P --> Q
    end

    subgraph Grafana
        M[Grafana实例]
        L --> M
        M --> R[返回查询结果]
        S[运维人员/图表设计者] -->|直接登录| M
    end

    R --> L
    L --> D
    N --> D
```

## 一、业务需求实现方案

### 1.1 统一认证与单点登录 (SSO)

**实现方式：**

1. **Token生成**：用户在BBPF系统登录后，获得标准JWT Token
2. **Token传递**：前端访问Grafana时，在请求头中携带JWT Token
3. **Token校验**：代理服务通过 `JwtAuthenticationFilter`拦截请求，调用BBPF后台认证服务验证Token
4. **用户映射**：验证通过后，代理服务通过 `X-WEBAUTH-USER`头将用户信息传递给Grafana

**技术实现：**

```java
// JWT认证过滤器核心逻辑
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {
  
    @Override
    protected void doFilterInternal(HttpServletRequest request, 
                                  HttpServletResponse response, 
                                  FilterChain filterChain) {
        String token = extractTokenFromRequest(request);
        if (token != null) {
            // 调用BBPF后台服务验证Token
            UserInfo userInfo = bbpfAuthService.validateToken(token);
            if (userInfo != null) {
                // 设置认证信息到SecurityContext
                setAuthentication(userInfo);
                // 将token存储到请求属性中供后续使用
                request.setAttribute(TOKEN_ATTRIBUTE, token);
            }
        }
        filterChain.doFilter(request, response);
    }
}
```

**单点登录(SSO)流程序列图：**

```mermaid
sequenceDiagram
    participant U as 用户
    participant BF as BBPF前端
    participant GP as Grafana代理服务
    participant BA as BBPF认证服务
    participant R as Redis缓存
    participant G as Grafana实例

    U->>BF: 1. 登录BBPF系统
    BF->>BA: 2. 用户认证请求
    BA-->>BF: 3. 返回JWT Token
    BF-->>U: 4. 登录成功，获得Token
  
    Note over U,G: 用户访问Grafana资源
    U->>GP: 5. 访问Grafana(携带JWT Token)
    GP->>GP: 6. JwtAuthenticationFilter拦截
    GP->>BA: 7. 调用BBPF后台验证Token
    BA-->>GP: 8. Token验证结果
  
    alt Token有效
        GP->>R: 9. 查询用户权限缓存
        alt 缓存命中
            R-->>GP: 10. 返回缓存的权限信息
        else 缓存未命中
            GP->>BA: 11. 调用权限API获取权限
            BA-->>GP: 12. 返回用户权限信息
            GP->>R: 13. 缓存权限信息
        end
        GP->>G: 14. 转发请求(设置X-WEBAUTH-USER头)
        G-->>GP: 15. 返回Grafana响应
        GP-->>U: 16. 返回最终响应
    else Token无效
        GP-->>U: 17. 返回401未授权
    end
```

### 1.2 基于权限控制的仪表盘访问

**实现方式：**

1. **权限定义**：在BBPF系统中定义用户/角色与Grafana资源的访问权限
2. **权限API**：BBPF提供权限查询API，支持仪表盘、数据源、文件夹级别的权限控制
3. **访问控制**：代理服务在转发请求前进行权限校验
4. **缓存优化**：使用Redis缓存权限信息，提高响应性能

**权限控制流程：**

```java
@Service
public class PermissionServiceImpl implements PermissionService {
  
    @Override
    public UserPermissionDto getUserPermissions(String userId) {
        // 1. 先从Redis缓存获取
        UserPermissionDto cached = getCachedPermission(userId);
        if (cached != null && !cached.isExpired()) {
            return cached;
        }
  
        // 2. 调用BBPF权限API获取最新权限
        UserPermissionDto permission = fetchPermissionFromBbpfApi(userId);
  
        // 3. 缓存权限信息
        if (permission != null) {
            cachePermission(userId, permission);
        }
  
        return permission;
    }
}
```

**权限缓存机制序列图：**

```mermaid
sequenceDiagram
    participant U as 用户
    participant GP as Grafana代理服务
    participant PS as 权限服务
    participant R as Redis缓存
    participant BA as BBPF权限API

    Note over U,BA: 首次访问 - 缓存未命中
    U->>GP: 1. 首次访问Grafana资源
    GP->>PS: 2. 获取用户权限
    PS->>R: 3. 查询权限缓存
    R-->>PS: 4. 缓存未命中
    PS->>BA: 5. 调用BBPF权限API
    BA-->>PS: 6. 返回权限数据
    PS->>R: 7. 缓存权限信息(TTL=30分钟)
    PS-->>GP: 8. 返回权限信息
    GP-->>U: 9. 处理请求并返回

    Note over U,BA: 后续访问 - 缓存命中
    U->>GP: 10. 后续访问Grafana资源
    GP->>PS: 11. 获取用户权限
    PS->>R: 12. 查询权限缓存
    R-->>PS: 13. 缓存命中，返回权限
    PS-->>GP: 14. 返回权限信息
    GP-->>U: 15. 快速处理请求并返回

    Note over U,BA: 权限变更 - 缓存失效
    BA->>R: 16. 权限变更时主动清除缓存
    U->>GP: 17. 下次访问触发缓存更新
    GP->>PS: 18. 获取用户权限
    PS->>R: 19. 查询权限缓存
    R-->>PS: 20. 缓存已失效
    PS->>BA: 21. 重新获取最新权限
    BA-->>PS: 22. 返回最新权限数据
    PS->>R: 23. 更新缓存
    PS-->>GP: 24. 返回最新权限
    GP-->>U: 25. 基于最新权限处理请求
```

### 1.3 数据级权限控制 (SQL拦截机制)

**实现方式：**

1. **请求拦截**：拦截发往Grafana的数据查询API请求
2. **SQL解析**：解析请求体中的SQL查询语句
3. **权限注入**：根据用户权限和表级配置，自动为SQL添加WHERE条件
4. **多租户支持**：支持不同租户使用不同Schema的数据隔离

**SQL拦截器核心逻辑：**

```java
@Service
public class SqlInterceptorService {
  
    public String interceptAndModifyRequest(String requestBody, String userId, String apiPath) {
        // 1. 获取用户权限信息
        UserPermissionDto userPermission = permissionService.getUserPermissions(userId);
  
        // 2. 解析请求体中的SQL
        JsonNode rootNode = objectMapper.readTree(requestBody);
        JsonNode queriesNode = rootNode.get("queries");
  
        // 3. 遍历所有查询，修改SQL
        for (JsonNode queryNode : queriesNode) {
            String originalSql = queryNode.get("rawSql").asText();
  
            // 4. 首先应用租户Schema隔离
            String sqlWithSchema = applyTenantSchemaIsolation(originalSql, userPermission);
  
            // 5. 构建其他权限过滤条件
            String permissionFilter = buildPermissionFilter(userPermission, sqlWithSchema);
  
            // 6. 修改SQL添加权限过滤
            String modifiedSql = modifySqlWithPermissionFilter(sqlWithSchema, permissionFilter);
  
            // 7. 更新请求体
            ((ObjectNode) queryNode).put("rawSql", modifiedSql);
        }
  
        return objectMapper.writeValueAsString(rootNode);
    }
  
    /**
     * 应用租户Schema隔离
     * 将SQL中的表名前加上租户Schema前缀
     */
    private String applyTenantSchemaIsolation(String sql, UserPermissionDto userPermission) {
        String tenantId = userPermission.getTenantId();
        if (tenantId == null || tenantId.isEmpty()) {
            return sql; // 无租户信息，返回原SQL
        }
      
        // 获取租户对应的Schema名称
        String schemaName = getSchemaForTenant(tenantId);
      
        // 为SQL中的所有表名添加Schema前缀
        return addSchemaToAllTables(sql, schemaName);
    }
  
    /**
     * 为SQL中的所有表名添加Schema前缀
     */
    private String addSchemaToAllTables(String sql, String schemaName) {
        // 匹配 FROM、JOIN、UPDATE、INTO 后面的表名
        String pattern = "\\b(FROM|JOIN|UPDATE|INTO)\\s+([a-zA-Z_][a-zA-Z0-9_]*)\\b";
      
        return sql.replaceAll(pattern, "$1 " + schemaName + ".$2");
    }
  
    /**
     * 获取租户对应的Schema名称
     */
    private String getSchemaForTenant(String tenantId) {
        // 从配置中获取租户对应的Schema
        String schema = config.getTenantSchemaMapping().get(tenantId);
        if (schema != null) {
            return schema;
        }
      
        // 使用默认前缀：租户ID就是Schema名称
        return tenantId;
    }
}
```

**表级权限映射配置：**

```yaml
bbpf:
  sql:
    interceptor:
      enabled: true
      verbose-logging: false
      deny-on-permission-failure: true
    
      # 租户Schema隔离配置
      tenant:
        schema-isolation-enabled: true
        schema-mapping:
          tenant_a: "tenant_a"
          tenant_b: "tenant_b"
          tenant_c: "tenant_c"
        default-schema-prefix: "tenant_"
  
      # 全局权限字段映射（移除租户字段，因为使用Schema隔离）
      user-fields:
        - "user_id"
        - "created_by"
        - "owner_id"
      permission-field-mapping:
        orgId: "org_id"
        deptId: "dept_id"
  
      # 表级权限映射配置（移除tenant-field配置）
      table-permission-mapping:
        # 系统日志表
        sys_log:
          user-fields:
            - "user_id"
            - "operator_id"
          org-field: "org_id"
          dept-field: "dept_id"
  
        # 业务数据表
        business_data:
          user-fields:
            - "create_user_id"
          org-field: "create_org_id"
  
        # 支持前缀匹配
        "report_*":
          user-fields:
            - "report_user_id"
          org-field: "org_id"
```

**多租户Schema支持：**

```java
private String modifySqlWithTenantSchema(String sql, UserPermissionDto userPermission) {
    String tenantId = userPermission.getTenantId();
    if (tenantId == null || tenantId.isEmpty()) {
        return sql; // 无租户信息，返回原SQL
    }
  
    // 提取SQL中的表名并添加Schema前缀
    String modifiedSql = addSchemaToTables(sql, tenantId);
  
    // 添加其他权限过滤条件
    String permissionFilter = buildPermissionFilter(userPermission, modifiedSql);
    if (!permissionFilter.isEmpty()) {
        modifiedSql = addWhereCondition(modifiedSql, permissionFilter);
    }
  
    return modifiedSql;
}

private String addSchemaToTables(String sql, String tenantId) {
    // 使用正则表达式匹配表名并添加Schema前缀
    // 匹配 FROM table_name 或 JOIN table_name 等模式
    String pattern = "\\b(FROM|JOIN|UPDATE|INTO)\\s+([a-zA-Z_][a-zA-Z0-9_]*)\\b";
  
    return sql.replaceAll(pattern, "$1 " + tenantId + ".$2");
}

private String buildPermissionFilter(UserPermissionDto userPermission, String sql) {
    StringBuilder filter = new StringBuilder();
  
    // 提取SQL中的表名（已包含Schema前缀）
    Set<String> tableNames = extractTableNames(sql);
  
    for (String tableName : tableNames) {
        // 移除Schema前缀获取原表名
        String originalTableName = removeSchemaPrefix(tableName);
      
        // 获取表级权限配置
        TablePermissionMapping tableConfig = config.getTablePermissionMappingForTable(originalTableName);
  
        if (tableConfig != null) {
            // 使用表级配置
            addTableSpecificFilter(filter, userPermission, tableConfig);
        } else {
            // 使用全局配置
            addGlobalFilter(filter, userPermission);
        }
    }
  
    return filter.toString();
}

private String removeSchemaPrefix(String tableName) {
    int dotIndex = tableName.indexOf('.');
    return dotIndex > 0 ? tableName.substring(dotIndex + 1) : tableName;
}
```

**数据权限控制(SQL拦截)流程序列图：**

```mermaid
sequenceDiagram
    participant U as 用户
    participant GP as Grafana代理服务
    participant SI as SQL拦截器
    participant PS as 权限服务
    participant R as Redis缓存
    participant G as Grafana实例
    participant DB as 数据库

    U->>GP: 1. 发起数据查询请求
    GP->>GP: 2. 检查API路径是否需要拦截
  
    alt 需要SQL拦截
        GP->>SI: 3. 调用SQL拦截器
        SI->>PS: 4. 获取用户权限信息
        PS->>R: 5. 查询权限缓存
        R-->>PS: 6. 返回权限信息
        PS-->>SI: 7. 返回用户权限
      
        SI->>SI: 8. 解析请求体中的SQL
        SI->>SI: 9. 提取SQL中的表名
        SI->>SI: 10. 根据表级权限配置构建过滤条件
      
        Note over SI: 权限过滤条件构建
        Note over SI: - 用户字段过滤: user_id = 'xxx'
        Note over SI: - 组织字段过滤: org_id = 'xxx'
        Note over SI: - Schema隔离: 表名添加租户前缀
        Note over SI: - 部门字段过滤: dept_id IN ('xxx','yyy')
      
        SI->>SI: 11. 修改SQL添加WHERE条件
        SI-->>GP: 12. 返回修改后的请求体
        GP->>G: 13. 转发修改后的请求
    else 无需拦截
        GP->>G: 13. 直接转发原始请求
    end
  
    G->>DB: 14. 执行SQL查询
    DB-->>G: 15. 返回查询结果
    G-->>GP: 16. 返回Grafana响应
    GP-->>U: 17. 返回最终响应
```

**多租户数据隔离流程序列图：**

```mermaid
sequenceDiagram
    participant U1 as 租户A用户
    participant U2 as 租户B用户
    participant GP as Grafana代理服务
    participant SI as SQL拦截器
    participant DB as 数据库

    Note over U1,DB: 租户A用户访问
    U1->>GP: 1. 查询业务数据(tenant_id=tenant_a)
    GP->>SI: 2. SQL拦截处理
    SI->>SI: 3. 识别用户租户ID=tenant_a
    SI->>SI: 4. 修改SQL添加Schema前缀
    Note over SI: SELECT * FROM business_data<br/>变为<br/>SELECT * FROM tenant_a.business_data
    GP->>DB: 5. 执行带Schema的SQL
    DB-->>GP: 6. 返回租户A数据
    GP-->>U1: 7. 返回结果

    Note over U1,DB: 租户B用户访问
    U2->>GP: 8. 查询业务数据(tenant_id=tenant_b)
    GP->>SI: 9. SQL拦截处理
    SI->>SI: 10. 识别用户租户ID=tenant_b
    SI->>SI: 11. 修改SQL添加Schema前缀
    Note over SI: SELECT * FROM business_data<br/>变为<br/>SELECT * FROM tenant_b.business_data
    GP->>DB: 12. 执行带Schema的SQL
    DB-->>GP: 13. 返回租户B数据
    GP-->>U2: 14. 返回结果

    Note over U1,DB: 通过Schema隔离，租户A无法访问租户B数据
```

### 1.4 iframe集成支持

**实现方式：**

1. **跨域配置**：代理服务配置CORS和X-Frame-Options头部
2. **Token传递**：iframe URL中包含认证参数或通过postMessage传递
3. **无缝集成**：Grafana图表可直接嵌入BBPF业务页面

### 1.5 数据导出权限控制

**实现方式：**

1. **导出权限**：在BBPF权限系统中定义导出权限
2. **请求拦截**：拦截Grafana导出相关API请求
3. **权限验证**：验证用户是否具有对应资源的导出权限
4. **审计日志**：记录所有导出操作的审计日志

### 1.6 预设监控模板

**实现方式：**

1. **模板管理**：在BBPF系统中维护预设模板列表
2. **权限控制**：基于用户权限过滤可用模板
3. **快速部署**：支持一键创建基于模板的监控看板

### 1.7 移动端适配

移动端自己实现展示，通过调用grafana api接口获取数据，然后移动端自己实现展示
grafana api接口访问地址 http://localhost:3000/swagger

## 二、技术需求实现方案

### 2.1 安全防护措施

**认证安全：**

- **统一Token校验**：所有Token校验统一通过BBPF后台服务，确保一致性
- **签名验证**：API调用使用MD5签名防止篡改
- **Token安全**：JWT设置合理过期时间，支持Token刷新机制

**数据安全：**

- **SQL注入防护**：SQL拦截器内置SQL注入检测和过滤
- **权限最小化**：严格按照最小权限原则分配用户权限
- **数据隔离**：多租户数据通过Schema级隔离，租户ID即为Schema名称

**租户隔离方案说明：**

- **Schema级隔离**：租户ID直接作为数据库Schema名称，实现物理级别的数据隔离
- **SQL自动改写**：SQL拦截器自动为表名添加Schema前缀（如：`SELECT * FROM user` → `SELECT * FROM tenant_a.user`）
- **配置简化**：无需配置复杂的租户字段映射，租户ID即Schema名称
- **性能优化**：避免了WHERE条件过滤，直接通过Schema路由到对应租户数据
- **安全性增强**：Schema级隔离提供更强的数据安全保障，避免跨租户数据泄露

**通信安全：**

- **HTTPS强制**：所有服务间通信强制使用HTTPS
- **内网隔离**：Grafana实例部署在内网，仅代理服务可访问
- **请求限流**：实现API请求频率限制，防止恶意攻击

### 2.2 性能优化

**缓存策略：**

```yaml
# Redis缓存配置
bbpf:
  grafana:
    proxy:
      enable-permission-cache: true
      permission-cache-expiration-seconds: 1800
      service-health-cache-seconds: 30
```

**SQL优化：**

- **索引建议**：为权限过滤字段建立合适索引
- **查询优化**：SQL拦截器优化WHERE条件的添加位置
- **批量处理**：支持批量查询的权限过滤

**连接池优化：**

```yaml
# HTTP客户端连接池配置
http:
  client:
    max-connections: 200
    max-connections-per-route: 50
    connection-timeout: 5000
    socket-timeout: 30000
```

### 2.3 监控和运维

**健康检查：**

```java
@RestController
public class HealthController {
  
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> status = new HashMap<>();
        status.put("status", "UP");
        status.put("grafanaAvailable", grafanaApiClient.isHealthy());
        status.put("permissionServiceAvailable", permissionService.isServiceAvailable());
        status.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(status);
    }
}
```

**性能监控：**

- **请求统计**：记录代理请求的成功率、响应时间
- **权限缓存命中率**：监控缓存性能
- **SQL拦截统计**：统计SQL修改的频率和类型

## 三、核心组件详细设计

### 3.1 Grafana代理服务 (bbpf-grafana-proxy)

**技术栈：**

- **框架**：Spring Boot 2.x
- **安全**：Spring Security + JWT
- **缓存**：Redis
- **HTTP客户端**：Apache HttpClient
- **监控**：Spring Boot Actuator + Micrometer

**核心模块：**

1. **认证模块**

   - `JwtAuthenticationFilter`：JWT认证过滤器
   - `JwtUtil`：JWT工具类
   - `SecurityConfig`：Spring Security配置
2. **权限模块**

   - `PermissionService`：权限服务接口
   - `PermissionServiceImpl`：权限服务实现
   - `UserPermissionDto`：用户权限数据传输对象
3. **SQL拦截模块**

   - `SqlInterceptorService`：SQL拦截服务
   - `SqlInterceptorConfig`：SQL拦截配置
   - `TablePermissionMapping`：表级权限映射
4. **代理模块**

   - `GrafanaProxyController`：代理控制器
   - `GrafanaProxyService`：代理服务
   - `GrafanaApiClient`：Grafana API客户端
5. **WebSocket模块**

   - `GrafanaWebSocketProxyHandler`：WebSocket代理处理器
   - `WebSocketHandshakeInterceptor`：WebSocket握手拦截器

### 3.2 BBPF权限API增强

**API接口设计：**

```java
// 权限查询API
GET /api/v1/permission/user/{userId}
Authorization: Bearer {jwt-token}
Signature: {api-signature}

// 响应格式
{
  "head": {
    "code": "000000",
    "message": "success"
  },
  "body": {
    "userId": "user123",
    "userName": "张三",
    "orgId": "org001",
    "tenantId": "tenant001",
    "deptId": "dept001",
    "role": "manager",
    "dashboards": ["dashboard-1", "dashboard-2"],
    "dataSources": ["datasource-mysql", "datasource-prometheus"],
    "folders": ["folder-1", "folder-2"],
    "permissions": ["read", "write", "export"],
    "dataFilter": "org_id = 'org001' AND dept_id IN ('dept001', 'dept002')"
  }
}
```

### 3.3 多租户数据权限配置

**租户隔离策略：**

1. **Schema级隔离（推荐）**

```yaml
# 租户Schema配置
bbpf:
  sql:
    interceptor:
      tenant:
        # 启用Schema级租户隔离
        schema-isolation-enabled: true
        # 租户ID到Schema的映射
        schema-mapping:
          tenant_a: "tenant_a"
          tenant_b: "tenant_b"
          tenant_c: "tenant_c"
        # 默认Schema前缀（当映射中没有找到时使用）
        default-schema-prefix: "tenant_"
```

**Schema级隔离实现：**

```java
// 租户Schema隔离
private String applyTenantSchemaIsolation(String sql, UserPermissionDto userPermission) {
    String tenantId = userPermission.getTenantId();
    if (tenantId == null || tenantId.isEmpty()) {
        return sql; // 无租户信息，返回原SQL
    }
  
    // 获取租户对应的Schema名称
    String schemaName = getSchemaForTenant(tenantId);
  
    // 为SQL中的所有表名添加Schema前缀
    return addSchemaToAllTables(sql, schemaName);
}

private String getSchemaForTenant(String tenantId) {
    // 从配置中获取租户对应的Schema
    String schema = config.getTenantSchemaMapping().get(tenantId);
    if (schema != null) {
        return schema;
    }
  
    // 使用默认前缀
    return config.getDefaultSchemaPrefix() + tenantId;
}
```

2. **字段级隔离（备选方案）**

```java
// 租户字段过滤（仅在Schema隔离不可用时使用）
private void addTenantFilter(StringBuilder filter, UserPermissionDto userPermission) {
    String tenantId = userPermission.getTenantId();
    if (tenantId != null && !tenantId.isEmpty()) {
        // 注意：此方法仅在Schema隔离不可用时作为备选方案
        // 正常情况下应使用Schema级隔离
        filter.append(" AND tenant_id = '").append(tenantId).append("'");
    }
}
```

## 四、部署与配置

### 4.1 环境配置

**开发环境配置 (application-dev.yml)：**

```yaml
bbpf:
  grafana:
    proxy:
      grafana-base-url: http://localhost:3000
      bbpf-permission-api-url: http://localhost:8081/api/v1/permission
      jwt-secret: dev-jwt-secret-key
      enable-permission-cache: true
      permission-cache-expiration-seconds: 300

  sql:
    interceptor:
      enabled: true
      verbose-logging: true
      deny-on-permission-failure: false

spring:
  redis:
    host: localhost
    port: 6379
    database: 0
```

**生产环境配置 (application-prod.yml)：**

```yaml
bbpf:
  grafana:
    proxy:
      grafana-base-url: https://grafana.internal.company.com
      bbpf-permission-api-url: https://bbpf-api.company.com/api/v1/permission
      jwt-secret: ${BBPF_JWT_SECRET}
      enable-permission-cache: true
      permission-cache-expiration-seconds: 1800

  sql:
    interceptor:
      enabled: true
      verbose-logging: false
      deny-on-permission-failure: true

spring:
  redis:
    host: ${REDIS_HOST}
    port: ${REDIS_PORT}
    password: ${REDIS_PASSWORD}
```

### 4.2 Docker部署

**Dockerfile：**

```dockerfile
FROM openjdk:8-jre-alpine

VOLUME /tmp

COPY target/bbpf-grafana-proxy-2.0.0.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "/app.jar"]
```

**docker-compose.yml：**

```yaml
version: '3.8'

services:
  bbpf-grafana-proxy:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - GRAFANA_BASE_URL=http://grafana:3000
      - BBPF_PERMISSION_API_URL=http://bbpf-api:8081/api/v1/permission
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - redis
      - grafana

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_AUTH_PROXY_ENABLED=true
      - GF_AUTH_PROXY_HEADER_NAME=X-WEBAUTH-USER
      - GF_AUTH_PROXY_AUTO_SIGN_UP=true

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
```

## 五、实施步骤

### 阶段一：基础架构搭建 (3日)

1. **环境准备**：搭建开发、测试环境
2. **代理服务部署**：部署bbpf-grafana-proxy服务
3. **基础认证**：实现JWT认证和基础代理功能
4. **Grafana配置**：配置Grafana Auth Proxy模式

### 阶段二：权限集成开发 (5日)

1. **BBPF权限API开发**：开发或增强权限查询API
2. **权限服务集成**：集成权限查询和缓存机制
3. **基础权限控制**：实现仪表盘、数据源级别权限控制
4. **测试验证**：进行权限控制功能测试

### 阶段三：SQL拦截器开发 (1周)

1. **SQL拦截器开发**：实现SQL解析和修改功能
2. **表级权限配置**：实现基于表名的权限映射
3. **多租户支持**：实现租户级数据隔离
4. **性能优化**：优化SQL修改性能和缓存策略

### 阶段四：高级功能与优化 (2周)

1. **iframe集成**：实现前端iframe嵌入功能
2. **数据导出控制**：实现导出权限控制
3. **WebSocket支持**：实现实时数据推送代理
4. **监控和日志**：完善监控指标和日志记录

### 阶段五：测试与上线 (2周)

1. **集成测试**：进行端到端集成测试
2. **性能测试**：进行压力测试和性能调优
3. **安全测试**：进行安全漏洞扫描和渗透测试
4. **生产部署**：灰度发布和全面上线

## 六、风险评估与应对

### 6.1 技术风险

**风险1：SQL拦截器性能影响**

- **风险描述**：SQL解析和修改可能影响查询性能
- **应对措施**：
  - 优化SQL解析算法，使用高效的正则表达式
  - 实现SQL修改结果缓存
  - 提供SQL拦截器开关，支持紧急关闭

**风险2：多租户数据隔离失效**

- **风险描述**：权限配置错误可能导致数据泄露
- **应对措施**：
  - 实现权限配置的多级审核机制
  - 提供权限测试工具，验证数据隔离效果
  - 建立权限变更审计日志

### 6.2 业务风险

**风险1：BBPF权限API不稳定**

- **风险描述**：权限API故障影响Grafana访问
- **应对措施**：
  - 实现权限缓存机制，API故障时使用缓存数据
  - 提供降级模式，允许基础功能继续使用
  - 建立权限API监控和告警

**风险2：用户体验影响**

- **风险描述**：代理层增加可能影响响应速度
- **应对措施**：
  - 优化代理服务性能，减少延迟
  - 实现连接池和请求复用
  - 提供性能监控和告警

## 七、成功标准

### 7.1 功能标准

- ✅ 用户通过BBPF系统可无缝访问Grafana
- ✅ 权限控制精确到仪表盘、数据源、文件夹级别
- ✅ 数据级权限控制通过SQL拦截实现
- ✅ 支持多租户数据隔离
- ✅ iframe集成功能正常
- ✅ 数据导出权限控制有效

### 7.2 性能标准

- ✅ 代理服务响应时间 < 100ms (95%)
- ✅ 权限查询响应时间 < 50ms (95%)
- ✅ SQL拦截处理时间 < 20ms (95%)
- ✅ 系统可用性 > 99.9%
- ✅ 并发用户数支持 > 1000

### 7.3 安全标准

- ✅ 所有API调用通过JWT认证
- ✅ 数据权限隔离100%有效
- ✅ SQL注入防护100%有效
- ✅ 审计日志完整记录
- ✅ 通过安全渗透测试

## 八、总结

BBPF与Grafana集成方案主要实现：

1. **统一认证**：通过BBPF后台服务统一校验JWT Token，确保认证一致性
2. **SQL拦截**：采用SQL拦截器机制替代Grafana变量，实现更精确的数据权限控制
3. **多租户支持**：支持不同租户使用不同Schema，实现真正的数据隔离
4. **表级权限**：支持基于表名的精细化权限配置，适应复杂业务场景
5. **性能优化**：通过缓存、连接池等技术提升系统性能
6. **安全加固**：完善的权限验证、SQL注入防护和审计日志
