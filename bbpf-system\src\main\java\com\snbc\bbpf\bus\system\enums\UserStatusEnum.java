/**
 * Copyright© Shandong New Beiyang Information Technology Co., Ltd. . all rights reserved.
 */
package com.snbc.bbpf.bus.system.enums;

/**
 * @ClassName: UserStatusEnum
 * 用户状态：启动，禁用
 * @module: si-bbpf-system
 * @Author: wjc1
 * @date: 2021/5/20 13:32
 */
public enum UserStatusEnum {
    NO0(0, "请假中"),
    NO1(1, "生病中"),
    NO2(2, "出差中"),
    NO3(3, "会议中"),
    NO4(4, "外出中"),
    NO5(5, "忙碌中"),
    NO6(6, "调休中");

    private Integer status;
    private String statusName;

    UserStatusEnum(Integer status, String statusName) {
        this.status = status;
        this.statusName = statusName;
    }

    /**
     * @return java.lang.String
     * <AUTHOR>
     * @Description 通过状态获取名称
     * @Date 16:54 2021/5/20
     * @Param [status]
     **/
    public static String getStatusName(Integer status) {
        for (UserStatusEnum value : UserStatusEnum.values()) {
            if (status != null && status.equals(value.status)) {
                return value.statusName;
            }
        }
        return null;
    }
    public Integer getStatus() {
        return status;
    }

    public String getStatusName() {
        return statusName;
    }
}
