# BBPF Grafana Proxy 开发视图与设计文档

## 1. 系统概述

本文档提供BBPF Grafana Proxy系统的完整开发视图，包括系统架构、功能模块设计、时序图、流程图、类图和运行视图。系统主要实现以下核心功能：

- **Token验证模块**: JWT认证和用户身份验证
- **数据权限模块**: 基于用户权限的数据访问控制
- **SQL拦截模块**: 动态SQL查询拦截和权限过滤
- **iframe报表嵌入模块**: 安全的Grafana仪表盘嵌入
- **分享禁止模块**: 防止未授权的仪表盘分享

## 2. 开发视图（目录结构图）

bbpf-grafana-proxy/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/
│   │   │       └── snbc/
│   │   │           └── bbpf/
│   │   │               └── grafana/
│   │   │                   ├── GrafanaProxyApplication.java          # 应用启动类
│   │   │                   └── proxy/
│   │   │                       ├── client/                          # 客户端层
│   │   │                       │   └── GrafanaApiClient.java        # Grafana API客户端
│   │   │                       ├── config/                          # 配置层
│   │   │                       │   ├── GrafanaProxyConfig.java      # 代理配置
│   │   │                       │   ├── RedisConfig.java             # Redis配置
│   │   │                       │   ├── SecurityConfig.java          # 安全配置
│   │   │                       │   ├── SqlInterceptorConfig.java    # SQL拦截器配置
│   │   │                       │   └── WebSocketConfig.java         # WebSocket配置
│   │   │                       ├── controller/                      # 控制器层
│   │   │                       │   ├── GrafanaProxyController.java  # 主代理控制器
│   │   │                       │   └── StaticResourceController.java # 静态资源控制器
│   │   │                       ├── dto/                             # 数据传输对象
│   │   │                       │   └── UserPermissionDto.java       # 用户权限DTO
│   │   │                       ├── filter/                          # 过滤器层
│   │   │                       │   └── JwtAuthenticationFilter.java # JWT认证过滤器
│   │   │                       ├── handler/                         # 处理器层
│   │   │                       │   └── GrafanaWebSocketProxyHandler.java # WebSocket代理处理器
│   │   │                       ├── interceptor/                     # 拦截器层
│   │   │                       │   └── WebSocketHandshakeInterceptor.java # WebSocket握手拦截器
│   │   │                       ├── service/                         # 服务层
│   │   │                       │   ├── GrafanaAuthService.java      # Grafana认证服务
│   │   │                       │   ├── GrafanaProxyService.java     # 代理服务接口
│   │   │                       │   ├── GrafanaProxyServiceImpl.java # 代理服务实现
│   │   │                       │   ├── GrafanaVariableService.java  # Grafana变量服务
│   │   │                       │   ├── PermissionService.java       # 权限服务接口
│   │   │                       │   ├── PermissionServiceImpl.java   # 权限服务实现
│   │   │                       │   └── SqlInterceptorService.java   # SQL拦截服务
│   │   │                       └── utils/                           # 工具类层
│   │   │                           └── JwtUtil.java                 # JWT工具类
│   └── test/
├── README.md                                                      # 项目说明

## 3. 功能模块架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                        BBPF Grafana Proxy 系统                    │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────────┐ │
│  │   Token验证模块   │ │   数据权限模块    │ │    SQL拦截模块      │ │
│  │                │ │                │ │                    │ │
│  │ • JWT认证       │ │ • 权限验证      │ │ • 查询拦截          │ │
│  │ • 用户身份验证   │ │ • 权限缓存      │ │ • 动态权限过滤      │ │
│  │ • Token刷新     │ │ • BBPF API集成  │ │ • SQL安全检查      │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────────┘ │
│                                                                 │
│  ┌─────────────────┐ ┌─────────────────────────────────────────┐ │
│  │ iframe报表嵌入   │ │           分享禁止模块                    │ │
│  │     模块        │ │                                         │ │
│  │                │ │ • 分享链接拦截                           │ │
│  │ • 安全嵌入      │ │ • 权限验证                              │ │
│  │ • 动态变量注入   │ │ • 分享功能禁用                           │ │
│  │ • 响应内容修改   │ │ • 安全策略执行                           │ │
│  └─────────────────┘ └─────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 4. 系统架构层次图

```
┌─────────────────────────────────────────────────────────────────┐
│                        应用启动层                                  │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │            GrafanaProxyApplication                          │ │
│  │         @SpringBootApplication                             │ │
│  │         @EnableAsync                                       │ │
│  │         @EnableScheduling                                  │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                ↓
┌─────────────────────────────────────────────────────────────────┐
│                        控制器层                                    │
│  ┌─────────────────────────┐  ┌─────────────────────────────────┐ │
│  │  GrafanaProxyController │  │  StaticResourceController       │ │
│  │  - 仪表盘访问            │  │  - 静态资源处理                  │ │
│  │  - API请求处理          │  │  - 文件服务                      │ │
│  │  - 数据导出             │  │                                 │ │
│  │  - 通用代理             │  │                                 │ │
│  └─────────────────────────┘  └─────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                ↓
┌─────────────────────────────────────────────────────────────────┐
│                        过滤器层                                    │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │              JwtAuthenticationFilter                       │ │
│  │              - JWT Token验证                               │ │
│  │              - 用户身份认证                                  │ │
│  │              - 请求预处理                                   │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                ↓
┌─────────────────────────────────────────────────────────────────┐
│                        服务层                                      │
│  ┌─────────────────────┐  ┌─────────────────────────────────────┐ │
│  │  GrafanaProxyService│  │       PermissionService             │ │
│  │  - 请求代理          │  │       - 权限验证                     │ │
│  │  - 响应处理          │  │       - 权限缓存                     │ │
│  │  - URL构建          │  │       - BBPF API集成                │ │
│  │  - 内容修改          │  │                                     │ │
│  └─────────────────────┘  └─────────────────────────────────────┘ │
│  ┌─────────────────────┐  ┌─────────────────────────────────────┐ │
│  │  GrafanaAuthService │  │    SqlInterceptorService            │ │
│  │  - Grafana认证      │  │    - SQL拦截                        │ │
│  │  - 用户管理          │  │    - 数据权限过滤                    │ │
│  └─────────────────────┘  └─────────────────────────────────────┘ │
│  ┌─────────────────────┐                                          │
│  │ GrafanaVariableService                                         │ │
│  │ - 变量注入                                                      │ │
│  │ - 动态参数                                                      │ │
│  └─────────────────────┘                                          │
└─────────────────────────────────────────────────────────────────┘
                                ↓
┌─────────────────────────────────────────────────────────────────┐
│                        客户端层                                    │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │              GrafanaApiClient                               │ │
│  │              - HTTP客户端                                   │ │
│  │              - API调用                                      │ │
│  │              - 连接管理                                      │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                ↓
┌─────────────────────────────────────────────────────────────────┐
│                        配置层                                      │
│  ┌─────────────────┐ ┌─────────────┐ ┌─────────────────────────┐ │
│  │GrafanaProxyConfig│ │RedisConfig  │ │    SecurityConfig       │ │
│  │- 代理配置        │ │- Redis配置  │ │    - 安全配置           │ │
│  └─────────────────┘ └─────────────┘ └─────────────────────────┘ │
│  ┌─────────────────┐ ┌─────────────────────────────────────────┐ │
│  │SqlInterceptorConfig│ │        WebSocketConfig              │ │
│  │- SQL拦截配置     │ │        - WebSocket配置              │ │
│  └─────────────────┘ └─────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 5. 系统时序图

### 5.1 用户访问仪表盘完整时序图

```mermaid
sequenceDiagram
    participant User as 用户浏览器
    participant Proxy as Grafana代理
    participant JWT as JWT验证器
    participant Permission as 权限服务
    participant BBPF as BBPF系统
    participant Redis as Redis缓存
    participant SQL as SQL拦截器
    participant Grafana as Grafana服务
    
    User->>Proxy: 1. 访问仪表盘(/d/dashboard-id)
    Proxy->>JWT: 2. 提取并验证JWT Token
    JWT->>JWT: 3. 验证Token有效性
    alt Token无效
        JWT-->>Proxy: Token验证失败
        Proxy-->>User: 返回401未授权
    else Token有效
        JWT->>Proxy: 4. 返回用户ID和用户名
        Proxy->>Permission: 5. 检查用户权限
        Permission->>Redis: 6. 查询权限缓存
        alt 缓存命中
            Redis-->>Permission: 返回缓存权限
        else 缓存未命中
            Permission->>BBPF: 7. 调用BBPF权限API
            BBPF-->>Permission: 8. 返回用户权限数据
            Permission->>Redis: 9. 缓存权限数据
        end
        Permission->>Permission: 10. 验证仪表盘访问权限
        alt 无访问权限
            Permission-->>Proxy: 权限验证失败
            Proxy-->>User: 返回403禁止访问
        else 有访问权限
            Permission-->>Proxy: 11. 权限验证通过
            Proxy->>Grafana: 12. 代理请求到Grafana
            Grafana-->>Proxy: 13. 返回仪表盘HTML
            Proxy->>Proxy: 14. 注入动态变量和修改内容
            Proxy->>Proxy: 15. 禁用分享功能
            Proxy-->>User: 16. 返回修改后的HTML
        end
    end
```

### 5.2 API请求处理时序图

```mermaid
sequenceDiagram
    participant User as 用户浏览器
    participant Proxy as Grafana代理
    participant JWT as JWT验证器
    participant Permission as 权限服务
    participant SQL as SQL拦截器
    participant Grafana as Grafana服务
    
    User->>Proxy: 1. API请求(/api/datasources/proxy/...)
    Proxy->>JWT: 2. 验证JWT Token
    JWT-->>Proxy: 3. 返回用户信息
    Proxy->>Permission: 4. 检查数据源访问权限
    Permission-->>Proxy: 5. 权限验证结果
    alt 无权限
        Proxy-->>User: 返回403禁止访问
    else 有权限
        Proxy->>SQL: 6. 检查是否需要SQL拦截
        alt 需要拦截
            SQL->>SQL: 7. 解析SQL查询
            SQL->>SQL: 8. 添加数据权限过滤条件
            SQL-->>Proxy: 9. 返回修改后的查询
        end
        Proxy->>Grafana: 10. 转发请求到Grafana
        Grafana-->>Proxy: 11. 返回查询结果
        Proxy-->>User: 12. 返回结果给用户
    end
```

### 5.3 WebSocket连接时序图

```mermaid
sequenceDiagram
    participant User as 用户浏览器
    participant Proxy as WebSocket代理
    participant Interceptor as 握手拦截器
    participant JWT as JWT验证器
    participant Grafana as Grafana WebSocket
    
    User->>Proxy: 1. WebSocket连接请求
    Proxy->>Interceptor: 2. 握手拦截
    Interceptor->>JWT: 3. 验证JWT Token
    JWT-->>Interceptor: 4. 返回验证结果
    alt Token无效
        Interceptor-->>Proxy: 握手失败
        Proxy-->>User: 连接被拒绝
    else Token有效
        Interceptor-->>Proxy: 5. 握手成功
        Proxy->>Grafana: 6. 建立到Grafana的WebSocket连接
        Grafana-->>Proxy: 7. 连接建立成功
        Proxy-->>User: 8. WebSocket连接建立
        
        loop 消息转发
            User->>Proxy: 发送消息
            Proxy->>Grafana: 转发消息
            Grafana-->>Proxy: 返回响应
            Proxy-->>User: 转发响应
        end
    end
```

## 6. 功能模块详细设计

### 6.1 Token验证模块

#### 6.1.1 模块流程图

```mermaid
flowchart TD
    A[接收HTTP请求] --> B{请求头包含Token?}
    B -->|否| C[返回401未授权]
    B -->|是| D[提取JWT Token]
    D --> E{Token格式正确?}
    E -->|否| C
    E -->|是| F[验证Token签名]
    F --> G{签名有效?}
    G -->|否| C
    G -->|是| H[检查Token过期时间]
    H --> I{Token已过期?}
    I -->|是| C
    I -->|否| J[提取用户信息]
    J --> K[设置认证上下文]
    K --> L[继续处理请求]
```

#### 6.1.2 类设计图

```mermaid
classDiagram
    class JwtAuthenticationFilter {
        -Logger logger
        -BbpfAuthService bbpfAuthService
        -GrafanaProxyConfig config
        +doFilterInternal(request, response, chain)
        +getUserId(request) String
        +getUsername(request) String
        -extractTokenFromRequest(request) String
        -setAuthenticationInContext(userId, username)
    }
    
    class BbpfAuthService {
        -RestTemplate restTemplate
        -String bbpfBaseUrl
        -int connectTimeout
        -int readTimeout
        +validateToken(token) AuthResult
        +getUserInfo(token) UserInfo
        -callBbpfAuthApi(token) ResponseEntity
        -parseAuthResponse(response) AuthResult
    }
    
    class AuthResult {
        -boolean valid
        -String userId
        -String username
        -String errorMessage
        +isValid() boolean
        +getUserId() String
        +getUsername() String
    }
    
    class SecurityConfig {
        -GrafanaProxyConfig config
        -JwtAuthenticationFilter jwtFilter
        +configure(http) SecurityFilterChain
        +corsConfigurationSource() CorsConfigurationSource
        +passwordEncoder() PasswordEncoder
    }
    
    JwtAuthenticationFilter --> BbpfAuthService
    BbpfAuthService --> AuthResult
    SecurityConfig --> JwtAuthenticationFilter
```

#### 6.1.3 运行视图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Filter as JwtAuthenticationFilter
    participant BBPF as BPF服务
    participant Context as 认证上下文
    
    Client->>Filter: 1. 发送HTTP请求
    Filter->>Filter: 2. 检查请求头Token
    
    alt Token不存在
        Filter-->>Client: 返回401未授权
    else Token存在
        Filter->>Filter: 3. 提取JWT Token
        Filter->>BBPF: 4. 调用BPF服务Token校验接口
        BBPF->>BBPF: 5. 验证Token签名和有效性
        
        alt Token校验失败
            BBPF-->>Filter: 6. 返回校验失败结果
            Filter-->>Client: 7. 返回401未授权
        else Token校验成功
            BBPF-->>Filter: 6. 返回用户信息和权限
            Filter->>Context: 7. 设置认证上下文
            Context->>Context: 8. 保存用户ID和用户名
            Filter->>Filter: 9. 继续处理请求链
        end
    end
    
    Note over Filter,BBPF: Token校验流程:<br/>• 代理服务不进行本地Token解析<br/>• 通过BPF服务接口进行Token验证<br/>• BPF服务返回用户信息和权限数据<br/>• 代理服务设置认证上下文供后续使用


### 6.2 数据权限模块

#### 6.2.1 模块流程图

```mermaid
flowchart TD
    A[接收权限检查请求] --> B[获取用户ID]
    B --> C{Redis缓存中有权限?}
    C -->|是| D[从缓存获取权限]
    C -->|否| E[调用BBPF API获取权限]
    E --> F[解析权限数据]
    F --> G[缓存权限数据]
    G --> H[返回权限信息]
    D --> H
    H --> I{检查具体权限}
    I -->|仪表盘权限| J[验证仪表盘访问权限]
    I -->|数据源权限| K[验证数据源访问权限]
    I -->|文件夹权限| L[验证文件夹访问权限]
    J --> M[返回权限检查结果]
    K --> M
    L --> M
```

#### 6.2.2 类设计图

```mermaid
classDiagram
    class PermissionService {
        <<interface>>
        +getUserPermissions(userId) UserPermissionDto
        +canAccessDashboard(userId, dashboardId) boolean
        +canAccessFolder(userId, folderId) boolean
        +canAccessDataSource(userId, dataSourceId) boolean
        +hasPermission(userId, permission) boolean
        +canExportData(userId, dashboardId) boolean
        +clearUserPermissionCache(userId)
        +refreshUserPermissions(userId) UserPermissionDto
    }
    
    class PermissionServiceImpl {
        -Logger logger
        -GrafanaProxyConfig config
        -RedisTemplate redisTemplate
        -RestTemplate restTemplate
        +getUserPermissions(userId) UserPermissionDto
        +canAccessDashboard(userId, dashboardId) boolean
        +canAccessFolder(userId, folderId) boolean
        +canAccessDataSource(userId, dataSourceId) boolean
        +hasPermission(userId, permission) boolean
        +canExportData(userId, dashboardId) boolean
        +clearUserPermissionCache(userId)
        +refreshUserPermissions(userId) UserPermissionDto
        -fetchPermissionsFromBbpf(userId) UserPermissionDto
        -getCacheKey(userId) String
    }
    
    class UserPermissionDto {
        -String userId
        -String username
        -List~String~ accessibleDashboardIds
        -List~String~ accessibleFolderIds
        -List~String~ accessibleDataSourceIds
        -Set~String~ permissions
        -String dataFilter
        -Long expirationTime
        +canAccessDashboard(dashboardId) boolean
        +canAccessFolder(folderId) boolean
        +canAccessDataSource(dataSourceId) boolean
        +hasPermission(permission) boolean
        +isExpired() boolean
    }
    
    PermissionService <|.. PermissionServiceImpl
    PermissionServiceImpl --> UserPermissionDto
```

#### 6.2.3 运行视图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Service as PermissionService
    participant Redis as Redis缓存
    participant BBPF as BBPF API
    participant DTO as UserPermissionDto
    
    Client->>Service: 1. 发送权限检查请求
    Service->>Service: 2. 获取用户ID
    Service->>Redis: 3. 查询权限缓存
    
    alt 缓存命中
        Redis-->>Service: 4. 返回缓存权限数据
        Service->>DTO: 5. 构建权限对象
        DTO->>DTO: 6. 检查权限有效性
        DTO-->>Service: 7. 返回权限验证结果
        Service->>Service: 8. 执行权限验证逻辑
        Service-->>Client: 9. 返回权限决策(允许/拒绝)
    else 缓存未命中
        Redis-->>Service: 4. 缓存未命中
        Service->>BBPF: 5. 调用BBPF API获取权限
        BBPF->>BBPF: 6. 查询用户权限数据
        BBPF-->>Service: 7. 返回权限数据
        Service->>DTO: 8. 解析并构建权限对象
        Service->>Redis: 9. 更新权限缓存
        Redis-->>Service: 10. 缓存更新完成
        Service->>Service: 11. 执行权限验证逻辑
        Service-->>Client: 12. 返回权限决策(允许/拒绝)
    end
    
    loop 权限检查类型
        alt 仪表盘权限检查
            Service->>DTO: 验证仪表盘访问权限
        else 数据源权限检查
            Service->>DTO: 验证数据源访问权限
        else 文件夹权限检查
            Service->>DTO: 验证文件夹访问权限
        end
        DTO-->>Service: 返回具体权限检查结果
    end
    
    Note over Service,Redis: 关键组件:<br/>• PermissionService: 权限服务接口<br/>• PermissionServiceImpl: 权限服务实现<br/>• UserPermissionDto: 用户权限数据传输对象<br/>• Redis: 权限缓存存储<br/>• BBPF API: 权限数据源
    
    Note over Redis,BBPF: 缓存策略:<br/>• 权限数据缓存30分钟<br/>• 支持手动刷新和清除缓存<br/>• 缓存键格式: permission:userId
```

### 6.3 SQL拦截模块

#### 6.3.1 模块流程图

```mermaid
flowchart TD
    A[接收SQL查询请求] --> B{是否需要拦截?}
    B -->|否| C[直接转发请求]
    B -->|是| D[解析SQL语句]
    D --> E[提取表名和字段]
    E --> F[获取用户权限信息]
    F --> G[构建权限过滤条件]
    G --> H[修改SQL查询]
    H --> I[验证SQL安全性]
    I --> J{SQL安全?}
    J -->|否| K[拒绝请求]
    J -->|是| L[转发修改后的SQL]
    L --> M[返回查询结果]
    C --> M
```

#### 6.3.2 类设计图

```mermaid
classDiagram
    class SqlInterceptorService {
        -Logger logger
        -SqlInterceptorConfig config
        -PermissionService permissionService
        +interceptSqlQuery(sql, userId) String
        +addDataPermissionFilter(sql, userId, tableName) String
        +validateSqlSecurity(sql) boolean
        -parseTableNames(sql) List~String~
        -buildPermissionCondition(userId, tableName) String
        -isSelectQuery(sql) boolean
        -containsRestrictedOperations(sql) boolean
    }
    
    class SqlInterceptorConfig {
        -boolean enabled
        -List~String~ interceptPaths
        -List~String~ excludePaths
        -List~String~ checkFields
        -Map~String,String~ tablePermissionFields
        -List~String~ excludeTables
        -List~String~ restrictedOperations
        +isPathIntercepted(path) boolean
        +getPermissionField(tableName) String
        +isTableExcluded(tableName) boolean
    }
    
    class SqlParser {
        +parseSelect(sql) SelectStatement
        +extractTables(sql) List~String~
        +extractFields(sql) List~String~
        +addWhereCondition(sql, condition) String
        +validateSyntax(sql) boolean
    }
    
    SqlInterceptorService --> SqlInterceptorConfig
    SqlInterceptorService --> SqlParser
    SqlInterceptorService --> PermissionService
```

#### 6.3.3 运行视图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Interceptor as SqlInterceptorService
    participant Parser as SqlParser
    participant Permission as PermissionService
    participant Database as 数据库
    
    Client->>Interceptor: 1. 发送SQL请求
    Interceptor->>Interceptor: 2. 检查拦截路径
    
    alt 路径需要拦截
        Interceptor->>Parser: 3. 解析SQL语句
        Parser->>Parser: 4. 提取表名和字段
        Parser-->>Interceptor: 5. 返回解析结果
        
        Interceptor->>Permission: 6. 获取用户权限
        Permission-->>Interceptor: 7. 返回权限信息
        
        Interceptor->>Interceptor: 8. 构建权限条件
        Interceptor->>Parser: 9. 修改SQL查询
        Parser->>Parser: 10. 验证SQL安全性
        Parser-->>Interceptor: 11. 返回修改后SQL
        
        Interceptor->>Database: 12. 执行修改后SQL
        Database-->>Interceptor: 13. 返回查询结果
        Interceptor-->>Client: 14. 返回过滤后结果
    else 路径无需拦截
        Interceptor->>Database: 3. 直接转发SQL
        Database-->>Interceptor: 4. 返回查询结果
        Interceptor-->>Client: 5. 返回原始结果
    end
    
    Note over Interceptor,Parser: 拦截策略:<br/>• 仅拦截SELECT查询<br/>• 根据表名添加权限过滤条件<br/>• 支持多表关联查询处理<br/>• 禁止危险SQL操作
    
    Note over Permission,Database: 权限过滤:<br/>• 基于用户ID的数据过滤<br/>• 支持部门级权限控制<br/>• 动态生成WHERE条件<br/>• 保持原SQL语义不变
```

### 6.4 iframe报表嵌入模块

#### 6.4.1 模块流程图

```mermaid
flowchart TD
    A[接收仪表盘访问请求] --> B[验证用户权限]
    B --> C{有访问权限?}
    C -->|否| D[返回403禁止访问]
    C -->|是| E[获取Grafana仪表盘]
    E --> F[注入动态变量]
    F --> G[修改HTML内容]
    G --> H[禁用分享功能]
    H --> I[添加安全策略]
    I --> J[返回修改后HTML]
```

#### 6.4.2 类设计图

```mermaid
classDiagram
    class GrafanaProxyController {
        -GrafanaProxyService proxyService
        -PermissionService permissionService
        -GrafanaVariableService variableService
        +accessDashboard(dashboardId, request, response)
        +handleApiRequest(request, response)
        +proxyToGrafana(request, response)
        +exportPanel(dashboardId, panelId, from, to, width, height, request, response)
    }
    
    class GrafanaVariableService {
        -Logger logger
        -PermissionService permissionService
        +injectUserVariables(content, userId) String
        +replaceVariablePlaceholders(content, variables) String
        +getUserVariables(userId) Map~String,String~
        +generateUserPermissionVariables(userId) Map~String,String~
        -buildVariableScript(variables) String
    }
    
    class ContentModifier {
        +disableShareFunction(html) String
        +addSecurityPolicies(html) String
        +injectCustomCSS(html) String
        +modifyNavigation(html) String
        +addSecurityHeaders(response)
    }
    
    GrafanaProxyController --> GrafanaVariableService
    GrafanaProxyController --> ContentModifier
    GrafanaVariableService --> PermissionService
```

#### 6.4.3 运行视图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as GrafanaProxyController
    participant Permission as PermissionService
    participant Variable as GrafanaVariableService
    participant Modifier as ContentModifier
    participant Grafana as Grafana服务
    
    Client->>Controller: 1. 发送仪表盘访问请求
    Controller->>Permission: 2. 验证用户权限
    Permission->>Permission: 3. 检查访问权限
    
    alt 权限验证通过
        Permission-->>Controller: 4. 权限验证通过
        Controller->>Grafana: 5. 获取仪表盘内容
        Grafana-->>Controller: 6. 返回原始HTML
        
        Controller->>Variable: 7. 注入动态变量
        Variable->>Variable: 8. 生成用户权限变量
        Variable->>Variable: 9. 替换变量占位符
        Variable-->>Controller: 10. 返回注入变量后内容
        
        Controller->>Modifier: 11. 修改HTML内容
        Modifier->>Modifier: 12. 禁用分享功能
        Modifier->>Modifier: 13. 添加安全策略
        Modifier->>Modifier: 14. 修改导航菜单
        Modifier->>Modifier: 15. 注入自定义样式
        Modifier-->>Controller: 16. 返回修改后HTML
        
        Controller-->>Client: 17. 返回安全的嵌入页面
    else 权限验证失败
        Permission-->>Controller: 4. 权限验证失败
        Controller-->>Client: 5. 返回403禁止访问
    end
    
    Note over Variable,Modifier: 内容修改策略:<br/>• 注入用户权限变量($_bbpf_user_id等)<br/>• 禁用分享按钮和功能<br/>• 添加CSP安全策略<br/>• 修改导航菜单<br/>• 添加自定义样式
    
    Note over Controller,Grafana: 安全措施:<br/>• X-Frame-Options控制<br/>• Content-Security-Policy设置<br/>• 移除敏感功能链接<br/>• 动态权限变量注入
```

### 6.5 分享禁止模块

#### 6.5.1 模块流程图

```mermaid
flowchart TD
    A[检测分享相关请求] --> B{是分享请求?}
    B -->|否| C[正常处理请求]
    B -->|是| D[检查请求类型]
    D --> E{创建分享链接?}
    D --> F{访问分享链接?}
    D --> G{分享API调用?}
    E -->|是| H[拦截并拒绝]
    F -->|是| I[验证权限]
    G -->|是| J[检查权限]
    I --> K{有访问权限?}
    J --> L{有分享权限?}
    K -->|否| H
    K -->|是| M[允许访问]
    L -->|否| H
    L -->|是| N[允许API调用]
    H --> O[返回403禁止]
    C --> P[继续处理]
    M --> P
    N --> P
```

#### 6.5.2 类设计图

```mermaid
classDiagram
    class ShareControlService {
        -Logger logger
        -GrafanaProxyConfig config
        -PermissionService permissionService
        +isShareRequest(request) boolean
        +canUserShare(userId, dashboardId) boolean
        +interceptShareCreation(request, response) boolean
        +validateShareAccess(shareId, userId) boolean
        +disableShareFeatures(content) String
        -isShareCreationRequest(request) boolean
        -isShareAccessRequest(request) boolean
        -extractShareId(request) String
    }
    
    class SharePolicy {
        -boolean allowPublicShares
        -boolean allowOrgShares
        -List~String~ allowedShareTypes
        -int maxShareDuration
        +isShareTypeAllowed(type) boolean
        +isShareDurationValid(duration) boolean
        +getDefaultShareSettings() Map~String,Object~
    }
    
    class ContentSecurityModifier {
        +removeShareButtons(html) String
        +disableShareMenus(html) String
        +addShareBlockingScript(html) String
        +modifyShareDialogs(html) String
        -findShareElements(html) List~Element~
        -replaceShareElements(html, elements) String
    }
    
    ShareControlService --> SharePolicy
    ShareControlService --> ContentSecurityModifier
    ShareControlService --> PermissionService
```

#### 6.5.3 运行视图

```
┌─────────────────────────────────────────────────────────────────┐
│                        分享禁止模块运行视图                        │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  HTTP请求 ──→ ShareControlService ──→ SharePolicy              │
│      │              │                      │                    │
│      │              ▼                      ▼                    │
│      │      [检测分享请求]            [检查分享策略]             │
│      │              │                      │                    │
│      │              ▼                      ▼                    │
│      │      [分析请求类型]            [验证分享权限]             │
│      │              │                      │                    │
│      │              ▼                      ▼                    │
│      │      [权限验证] ←──────────── [返回策略结果]              │
│      │              │                                           │
│      │              ▼                                           │
│      │      [ContentSecurityModifier]                           │
│      │              │                                           │
│      │              ▼                                           │
│      │      [修改页面内容]                                      │
│      │              │                                           │
│      │              ▼                                           │
│      └──────── [返回处理结果]                                   │
│                     │                                           │
│                     ▼                                           │
│              [允许/拒绝请求]                                     │
│                                                                 │
├─────────────────────────────────────────────────────────────────┤
│ 分享控制策略:                                                    │
│ • 完全禁用公共分享                                              │
│ • 限制组织内分享                                                │
│ • 基于用户权限的分享控制                                         │
│ • 分享链接有效期限制                                            │
│                                                                 │
│ 内容修改:                                                        │
│ • 移除分享按钮                                                  │
│ • 禁用分享菜单                                                  │
│ • 阻止分享对话框                                                │
│ • 注入分享阻止脚本                                              │
│                                                                 │
│ 安全措施:                                                        │
│ • API级别的分享拦截                                             │
│ • 前端界面的分享禁用                                            │
│ • 分享链接访问控制                                              │
│ • 审计日志记录                                                  │
└─────────────────────────────────────────────────────────────────┘
```

## 7. 组件交互图

### 7.1 系统整体交互图

```mermaid
graph TB
    subgraph "用户层"
        U[用户浏览器]
    end
    
    subgraph "代理层"
        P[Grafana代理服务]
        F[JWT过滤器]
        W[WebSocket代理]
    end
    
    subgraph "服务层"
        PS[权限服务]
        VS[变量服务]
        SS[SQL拦截服务]
        SC[分享控制服务]
    end
    
    subgraph "缓存层"
        R[Redis缓存]
    end
    
    subgraph "外部系统"
        G[Grafana服务]
        B[BBPF系统]
    end
    
    U --> P
    U --> W
    P --> F
    F --> PS
    P --> VS
    P --> SS
    P --> SC
    PS --> R
    PS --> B
    VS --> PS
    P --> G
    W --> G
    
    style U fill:#e1f5fe
    style P fill:#f3e5f5
    style G fill:#e8f5e8
    style B fill:#fff3e0
    style R fill:#fce4ec
```

### 7.2 请求处理流程交互图

```mermaid
sequenceDiagram
    participant Browser as 浏览器
    participant Proxy as 代理服务
    participant Filter as JWT过滤器
    participant Permission as 权限服务
    participant Variable as 变量服务
    participant SQL as SQL拦截
    participant Share as 分享控制
    participant Grafana as Grafana
    
    Browser->>Proxy: HTTP请求
    Proxy->>Filter: 过滤器处理
    Filter->>Permission: 验证权限
    Permission-->>Filter: 权限结果
    Filter-->>Proxy: 认证结果
    
    alt 仪表盘请求
        Proxy->>Variable: 注入变量
        Variable-->>Proxy: 变量脚本
        Proxy->>Share: 禁用分享
        Share-->>Proxy: 修改内容
    else API请求
        Proxy->>SQL: SQL拦截
        SQL-->>Proxy: 修改查询
    end
    
    Proxy->>Grafana: 转发请求
    Grafana-->>Proxy: 返回响应
    Proxy-->>Browser: 最终响应
```

### 7.3 数据流图

```mermaid
flowchart LR
    subgraph "输入"
        I1[HTTP请求]
        I2[JWT Token]
        I3[用户ID]
    end
    
    subgraph "处理"
        P1[Token验证]
        P2[权限检查]
        P3[变量注入]
        P4[SQL拦截]
        P5[内容修改]
    end
    
    subgraph "存储"
        S1[Redis缓存]
        S2[权限数据]
        S3[配置数据]
    end
    
    subgraph "输出"
        O1[修改后HTML]
        O2[过滤后SQL]
        O3[权限结果]
    end
    
    I1 --> P1
    I2 --> P1
    I3 --> P2
    P1 --> P2
    P2 --> S1
    S1 --> P3
    P3 --> P4
    P4 --> P5
    S2 --> P2
    S3 --> P4
    P3 --> O1
    P4 --> O2
    P2 --> O3
```

## 8. 部署架构图

### 8.1 系统部署架构

```mermaid
graph TB
    subgraph "负载均衡层"
        LB[负载均衡器]
    end
    
    subgraph "应用服务层"
        APP1[Grafana代理实例1]
        APP2[Grafana代理实例2]
        APP3[Grafana代理实例N]
    end
    
    subgraph "缓存层"
        REDIS1[Redis主节点]
        REDIS2[Redis从节点]
    end
    
    subgraph "服务层"
        GRAFANA[Grafana集群]
        BBPF[BBPF系统]
    end
    
    subgraph "监控层"
        MONITOR[监控系统]
        LOG[日志系统]
    end
    
    LB --> APP1
    LB --> APP2
    LB --> APP3
    
    APP1 --> REDIS1
    APP2 --> REDIS1
    APP3 --> REDIS1
    
    REDIS1 --> REDIS2
    
    APP1 --> GRAFANA
    APP2 --> GRAFANA
    APP3 --> GRAFANA
    
    APP1 --> BBPF
    APP2 --> BBPF
    APP3 --> BBPF
    
    APP1 --> MONITOR
    APP2 --> MONITOR
    APP3 --> MONITOR
    
    APP1 --> LOG
    APP2 --> LOG
    APP3 --> LOG
```

### 8.2 网络架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                           网络架构                               │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  Internet ──→ 防火墙 ──→ 负载均衡 ──→ 代理服务集群              │
│      │            │           │              │                  │
│      ▼            ▼           ▼              ▼                  │
│  [外部用户]   [安全策略]  [流量分发]    [应用实例]              │
│      │            │           │              │                  │
│      ▼            ▼           ▼              ▼                  │
│  [HTTPS]      [WAF规则]   [健康检查]    [内部网络]              │
│                                              │                  │
│                                              ▼                  │
│                                        [服务发现]              │
│                                              │                  │
│                                              ▼                  │
│                                      [Grafana/BBPF]            │
│                                                                 │
├─────────────────────────────────────────────────────────────────┤
│ 网络安全:                                                        │
│ • HTTPS加密传输                                                 │
│ • WAF应用防火墙                                                 │
│ • VPN内网访问                                                   │
│ • 网络隔离策略                                                  │
└─────────────────────────────────────────────────────────────────┘
```

## 9. 运行视图时序图

### 9.1 系统启动时序图

```mermaid
sequenceDiagram
    participant Boot as SpringBoot启动器
    participant Config as 配置管理器
    participant Redis as Redis连接池
    participant HTTP as HTTP客户端
    participant Monitor as 监控组件
    participant Health as 健康检查
    
    Boot->>Config: 1. 加载配置文件
    Config->>Config: 2. 验证配置参数
    Config-->>Boot: 3. 配置加载完成
    
    Boot->>Redis: 4. 初始化Redis连接池
    Redis->>Redis: 5. 建立连接
    Redis->>Redis: 6. 测试连接可用性
    Redis-->>Boot: 7. 连接池就绪
    
    Boot->>HTTP: 8. 初始化HTTP客户端
    HTTP->>HTTP: 9. 配置连接池参数
    HTTP-->>Boot: 10. HTTP客户端就绪
    
    Boot->>Monitor: 11. 启动监控组件
    Monitor->>Monitor: 12. 注册监控指标
    Monitor-->>Boot: 13. 监控组件启动完成
    
    Boot->>Health: 14. 启动健康检查
    Health->>Redis: 15. 检查Redis连接
    Health->>HTTP: 16. 检查外部服务
    Health-->>Boot: 17. 健康检查就绪
    
    Boot->>Boot: 18. 应用启动完成
    Boot->>Monitor: 19. 发送启动成功事件
```

### 9.2 缓存管理运行时序图

```mermaid
sequenceDiagram
    participant App as 应用服务
    participant Cache as 缓存管理器
    participant Redis as Redis集群
    participant BBPF as BBPF系统
    participant Monitor as 监控系统
    
    App->>Cache: 1. 请求用户权限数据
    Cache->>Redis: 2. 查询缓存
    
    alt 缓存命中
        Redis-->>Cache: 3. 返回缓存数据
        Cache->>Cache: 4. 检查数据有效性
        Cache-->>App: 5. 返回权限数据
        Cache->>Monitor: 6. 记录缓存命中指标
    else 缓存未命中
        Redis-->>Cache: 3. 缓存未命中
        Cache->>BBPF: 4. 调用BBPF API
        BBPF-->>Cache: 5. 返回权限数据
        Cache->>Redis: 6. 更新缓存
        Cache-->>App: 7. 返回权限数据
        Cache->>Monitor: 8. 记录缓存未命中指标
    end
    
    loop 定期清理
        Cache->>Redis: 清理过期缓存
        Cache->>Monitor: 记录清理统计
    end
```

### 9.3 负载均衡运行时序图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant LB as 负载均衡器
    participant App1 as 代理实例1
    participant App2 as 代理实例2
    participant App3 as 代理实例3
    participant Health as 健康检查
    
    Client->>LB: 1. 发送请求
    LB->>Health: 2. 检查实例健康状态
    Health-->>LB: 3. 返回健康实例列表
    
    LB->>LB: 4. 选择目标实例(轮询/权重)
    
    alt 实例1健康
        LB->>App1: 5. 转发请求
        App1->>App1: 6. 处理请求
        App1-->>LB: 7. 返回响应
        LB-->>Client: 8. 返回响应
    else 实例1故障
        LB->>App2: 5. 转发到备用实例
        App2->>App2: 6. 处理请求
        App2-->>LB: 7. 返回响应
        LB-->>Client: 8. 返回响应
        LB->>Health: 9. 标记实例1故障
    end
    
    loop 健康检查
        Health->>App1: 健康检查请求
        Health->>App2: 健康检查请求
        Health->>App3: 健康检查请求
        App1-->>Health: 健康状态响应
        App2-->>Health: 健康状态响应
        App3-->>Health: 健康状态响应
    end
```

### 9.4 监控告警运行时序图

```mermaid
sequenceDiagram
    participant App as 应用服务
    participant Metrics as 指标收集器
    participant Monitor as 监控系统
    participant Alert as 告警系统
    participant Admin as 运维人员
    
    loop 指标收集
        App->>Metrics: 1. 发送性能指标
        Metrics->>Metrics: 2. 聚合指标数据
        Metrics->>Monitor: 3. 推送指标到监控系统
    end
    
    Monitor->>Monitor: 4. 分析指标趋势
    Monitor->>Monitor: 5. 检查告警规则
    
    alt 触发告警
        Monitor->>Alert: 6. 发送告警事件
        Alert->>Alert: 7. 评估告警级别
        
        alt 严重告警
            Alert->>Admin: 8. 发送短信/电话
            Alert->>Admin: 9. 发送邮件通知
        else 一般告警
            Alert->>Admin: 8. 发送邮件通知
        end
        
        Admin->>App: 10. 处理告警问题
        App->>Monitor: 11. 问题解决确认
        Monitor->>Alert: 12. 告警恢复通知
        Alert->>Admin: 13. 发送恢复通知
    end
```

### 9.5 故障恢复运行时序图

```mermaid
sequenceDiagram
    participant Monitor as 监控系统
    participant App as 故障实例
    participant LB as 负载均衡器
    participant Backup as 备用实例
    participant Admin as 运维人员
    participant Log as 日志系统
    
    Monitor->>App: 1. 健康检查请求
    App-->>Monitor: 2. 无响应/错误响应
    Monitor->>Monitor: 3. 确认实例故障
    
    Monitor->>LB: 4. 通知实例故障
    LB->>LB: 5. 从负载列表移除故障实例
    
    Monitor->>Backup: 6. 启动备用实例
    Backup->>Backup: 7. 初始化服务
    Backup-->>Monitor: 8. 实例就绪通知
    
    Monitor->>LB: 9. 添加备用实例到负载列表
    
    Monitor->>Admin: 10. 发送故障告警
    Monitor->>Log: 11. 记录故障日志
    
    Admin->>App: 12. 诊断故障原因
    Admin->>App: 13. 修复故障
    App->>App: 14. 重启服务
    
    App->>Monitor: 15. 健康检查响应正常
    Monitor->>LB: 16. 恢复实例到负载列表
    Monitor->>Admin: 17. 发送恢复通知
    Monitor->>Log: 18. 记录恢复日志
```

### 9.6 性能优化运行时序图

```mermaid
sequenceDiagram
    participant Monitor as 性能监控
    participant Analyzer as 性能分析器
    participant Cache as 缓存优化器
    participant Pool as 连接池管理器
    participant Config as 配置管理器
    participant Admin as 运维人员
    
    Monitor->>Analyzer: 1. 发送性能数据
    Analyzer->>Analyzer: 2. 分析性能瓶颈
    
    alt 缓存命中率低
        Analyzer->>Cache: 3. 触发缓存优化
        Cache->>Cache: 4. 调整缓存策略
        Cache->>Config: 5. 更新缓存配置
        Cache-->>Analyzer: 6. 优化完成通知
    else 连接池不足
        Analyzer->>Pool: 3. 触发连接池优化
        Pool->>Pool: 4. 动态调整池大小
        Pool->>Config: 5. 更新连接池配置
        Pool-->>Analyzer: 6. 优化完成通知
    end
    
    Analyzer->>Monitor: 7. 请求性能验证
    Monitor->>Monitor: 8. 收集优化后指标
    Monitor-->>Analyzer: 9. 返回性能改善数据
    
    Analyzer->>Admin: 10. 发送优化报告
    Admin->>Config: 11. 确认配置变更
```
┌─────────────────────────────────────────────────────────────────┐
│                           网络架构图                              │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  Internet ──→ [防火墙] ──→ [负载均衡] ──→ [代理集群]              │
│                   │              │              │               │
│                   ▼              ▼              ▼               │
│              [安全策略]    [健康检查]    [应用实例]              │
│                   │              │              │               │
│                   ▼              ▼              ▼               │
│              [访问控制]    [流量分发]    [服务发现]              │
│                                                  │               │
│                                                  ▼               │
│                                          [内部网络]              │
│                                                  │               │
│                                                  ▼               │
│                                    ┌─────────────────────┐       │
│                                    │   Redis集群         │       │
│                                    │   Grafana集群       │       │
│                                    │   BBPF系统          │       │
│                                    │   监控系统          │       │
│                                    └─────────────────────┘       │
│                                                                 │
├─────────────────────────────────────────────────────────────────┤
│ 网络配置:                                                        │
│ • 外网访问端口: 80/443                                          │
│ • 内网通信端口: 8080, 6379, 3000                               │
│ • 安全组配置: 仅允许必要端口                                     │
│ • SSL/TLS加密: 全链路加密                                       │
│                                                                 │
│ 高可用配置:                                                      │
│ • 多实例部署                                                    │
│ • 故障自动切换                                                  │
│ • 数据备份恢复                                                  │
│ • 监控告警机制                                                  │
└─────────────────────────────────────────────────────────────────┘
```

## 9. 核心类图

### 3.1 控制器层类图

```mermaid
classDiagram
    class GrafanaProxyController {
        -Logger logger
        -GrafanaProxyService grafanaProxyService
        -PermissionService permissionService
        -GrafanaVariableService grafanaVariableService
        +accessDashboard(String, HttpServletRequest, HttpServletResponse)
        +handleApiRequest(HttpServletRequest, HttpServletResponse)
        +handleDataExport(String, HttpServletRequest, HttpServletResponse)
        +proxyToGrafana(HttpServletRequest, HttpServletResponse)
        +exportPanel(String, int, long, long, int, int, HttpServletRequest, HttpServletResponse)
    }
  
    class StaticResourceController {
        +handleStaticResource(HttpServletRequest, HttpServletResponse)
        +serveFile(String, HttpServletResponse)
    }
  
    GrafanaProxyController --> GrafanaProxyService
    GrafanaProxyController --> PermissionService
    GrafanaProxyController --> GrafanaVariableService
```

### 3.2 服务层类图

```mermaid
classDiagram
    class GrafanaProxyService {
        <<interface>>
        +proxyRequest(HttpServletRequest, HttpServletResponse, String, String) boolean
        +hasAccessPermission(String, String, String) boolean
        +buildTargetUrl(String, String) String
        +handleDashboardAccess(HttpServletRequest, HttpServletResponse, String, String) boolean
        +handleApiRequest(HttpServletRequest, HttpServletResponse, String, String) boolean
        +handleDataExport(HttpServletRequest, HttpServletResponse, String, String) boolean
        +modifyRequestHeaders(HttpServletRequest, String, String) Map~String,String~
        +modifyResponseContent(String, String, String) String
        +isGrafanaAvailable() boolean
        +getProxyStatistics() Map~String,Object~
        +getPanelAsPng(String, String, int, long, long, int, int, HttpServletRequest) byte[]
    }
  
    class GrafanaProxyServiceImpl {
        -Logger logger
        -GrafanaProxyConfig config
        -PermissionService permissionService
        -GrafanaApiClient grafanaApiClient
        -SqlInterceptorService sqlInterceptorService
        -RedisTemplate redisTemplate
        +proxyRequest(HttpServletRequest, HttpServletResponse, String, String) boolean
        +hasAccessPermission(String, String, String) boolean
        +buildTargetUrl(String, String) String
        +handleDashboardAccess(HttpServletRequest, HttpServletResponse, String, String) boolean
        +handleApiRequest(HttpServletRequest, HttpServletResponse, String, String) boolean
        +handleDataExport(HttpServletRequest, HttpServletResponse, String, String) boolean
        +modifyRequestHeaders(HttpServletRequest, String, String) Map~String,String~
        +modifyResponseContent(String, String, String) String
        +isGrafanaAvailable() boolean
        +getProxyStatistics() Map~String,Object~
        +getPanelAsPng(String, String, int, long, long, int, int, HttpServletRequest) byte[]
        -createProxyRequest(HttpServletRequest, String, String) HttpUriRequest
        -extractDashboardIdFromPath(String) String
        -extractDataSourceIdFromPath(String) String
        -isWriteOperation(String, String) boolean
        -isExportOperation(String) boolean
    }
  
    class PermissionService {
        <<interface>>
        +getUserPermissions(String) UserPermissionDto
        +canAccessDashboard(String, String) boolean
        +canAccessFolder(String, String) boolean
        +canAccessDataSource(String, String) boolean
        +hasPermission(String, String) boolean
        +canExportData(String, String) boolean
        +clearUserPermissionCache(String)
        +clearAllPermissionCache()
        +refreshUserPermissions(String) UserPermissionDto
        +isServiceAvailable() boolean
    }
  
    class PermissionServiceImpl {
        -Logger logger
        -GrafanaProxyConfig config
        -RedisTemplate redisTemplate
        -RestTemplate restTemplate
        +getUserPermissions(String) UserPermissionDto
        +canAccessDashboard(String, String) boolean
        +canAccessFolder(String, String) boolean
        +canAccessDataSource(String, String) boolean
        +hasPermission(String, String) boolean
        +canExportData(String, String) boolean
        +clearUserPermissionCache(String)
        +clearAllPermissionCache()
        +refreshUserPermissions(String) UserPermissionDto
        +isServiceAvailable() boolean
        -fetchPermissionsFromBbpf(String) UserPermissionDto
        -getCacheKey(String) String
    }
  
    class GrafanaAuthService {
        -Logger logger
        -GrafanaProxyConfig config
        -GrafanaApiClient grafanaApiClient
        +authenticateUser(String, String) boolean
        +createUser(String, String) boolean
        +getUserInfo(String) Map~String,Object~
        +updateUserPermissions(String, List~String~) boolean
    }
  
    class SqlInterceptorService {
        -Logger logger
        -SqlInterceptorConfig config
        +interceptSqlQuery(String, String) String
        +addDataPermissionFilter(String, String, String) String
        +validateSqlSecurity(String) boolean
        -parseTableNames(String) List~String~
        -buildPermissionCondition(String, String) String
    }
  
    class GrafanaVariableService {
        -Logger logger
        -PermissionService permissionService
        +injectUserVariables(String, String) String
        +replaceVariablePlaceholders(String, Map~String,String~) String
        +getUserVariables(String) Map~String,String~
    }
  
    GrafanaProxyService <|.. GrafanaProxyServiceImpl
    PermissionService <|.. PermissionServiceImpl
    GrafanaProxyServiceImpl --> PermissionService
    GrafanaProxyServiceImpl --> GrafanaApiClient
    GrafanaProxyServiceImpl --> SqlInterceptorService
    PermissionServiceImpl --> GrafanaProxyConfig
    GrafanaAuthService --> GrafanaApiClient
    GrafanaVariableService --> PermissionService
```

### 3.3 配置层类图

```mermaid
classDiagram
    class GrafanaProxyConfig {
        -String grafanaBaseUrl
        -String proxyBaseUrl
        -String bbpfPermissionApiUrl
        -String bbpfApiSignatureKey
        -String jwtSecret
        -long jwtExpirationSeconds
        -long permissionCacheExpirationSeconds
        -boolean enablePermissionCache
        -int proxyTimeoutMs
        -int maxRetryCount
        -boolean enableVerboseLogging
        -String grafanaAdminUsername
        -String grafanaAdminPassword
        -String grafanaApiToken
        -boolean enableAuthProxy
        -String authProxyHeaderName
        -boolean autoSignUp
        -String allowedOrigins
        -boolean enableWebSocket
        -int webSocketTimeoutMs
        -int webSocketHeartbeatMs
        +getters()
        +setters()
    }
  
    class SecurityConfig {
        -GrafanaProxyConfig config
        -JwtAuthenticationFilter jwtAuthenticationFilter
        +configure(HttpSecurity) SecurityFilterChain
        +corsConfigurationSource() CorsConfigurationSource
        +passwordEncoder() PasswordEncoder
    }
  
    class RedisConfig {
        +redisConnectionFactory() LettuceConnectionFactory
        +redisTemplate() RedisTemplate~String,Object~
        +stringRedisTemplate() StringRedisTemplate
    }
  
    class WebSocketConfig {
        -GrafanaWebSocketProxyHandler webSocketHandler
        -WebSocketHandshakeInterceptor handshakeInterceptor
        +registerWebSocketHandlers(WebSocketHandlerRegistry)
    }
  
    class SqlInterceptorConfig {
        -boolean enabled
        -List~String~ interceptPaths
        -List~String~ excludePaths
        -List~String~ checkFields
        -Map~String,String~ tablePermissionFields
        -List~String~ excludeTables
        +getters()
        +setters()
    }
  
    SecurityConfig --> GrafanaProxyConfig
    SecurityConfig --> JwtAuthenticationFilter
    WebSocketConfig --> GrafanaWebSocketProxyHandler
    WebSocketConfig --> WebSocketHandshakeInterceptor
```

### 3.4 数据传输对象类图

```mermaid
classDiagram
    class UserPermissionDto {
        -String userId
        -String username
        -List~String~ accessibleDashboardIds
        -List~String~ accessibleFolderIds
        -List~String~ accessibleDataSourceIds
        -Set~String~ permissions
        -String dataFilter
        -Long expirationTime
        -Map~String,Object~ rawPermissionData
        +canAccessDashboard(String) boolean
        +canAccessFolder(String) boolean
        +canAccessDataSource(String) boolean
        +hasPermission(String) boolean
        +isExpired() boolean
        +getters()
        +setters()
    }
```

### 3.5 过滤器和工具类图

```mermaid
classDiagram
    class JwtAuthenticationFilter {
        -Logger logger
        -JwtUtil jwtUtil
        -GrafanaProxyConfig config
        +doFilterInternal(HttpServletRequest, HttpServletResponse, FilterChain)
        +getUserId(HttpServletRequest) String
        +getUsername(HttpServletRequest) String
        -extractTokenFromRequest(HttpServletRequest) String
        -setAuthenticationInContext(String, String)
    }
  
    class JwtUtil {
        -String secretKey
        -long expirationTime
        +generateToken(String, String) String
        +validateToken(String) boolean
        +getUserIdFromToken(String) String
        +getUsernameFromToken(String) String
        +getExpirationDateFromToken(String) Date
        -getClaimsFromToken(String) Claims
        -isTokenExpired(String) boolean
    }
  
    class GrafanaApiClient {
        -Logger logger
        -GrafanaProxyConfig config
        -CloseableHttpClient httpClient
        +executeRequest(HttpUriRequest) CloseableHttpResponse
        +get(String) String
        +post(String, String) String
        +put(String, String) String
        +delete(String) String
        +isHealthy() boolean
        -buildAuthHeaders() Map~String,String~
        -handleResponse(CloseableHttpResponse) String
    }
  
    JwtAuthenticationFilter --> JwtUtil
    JwtAuthenticationFilter --> GrafanaProxyConfig
```

### 3.6 WebSocket相关类图

```mermaid
classDiagram
    class GrafanaWebSocketProxyHandler {
        -Logger logger
        -GrafanaProxyConfig config
        -Map~String,WebSocketSession~ sessions
        +afterConnectionEstablished(WebSocketSession)
        +handleMessage(WebSocketSession, WebSocketMessage)
        +handleTransportError(WebSocketSession, Throwable)
        +afterConnectionClosed(WebSocketSession, CloseStatus)
        -forwardToGrafana(WebSocketSession, String)
        -closeSession(WebSocketSession, String)
    }
  
    class WebSocketHandshakeInterceptor {
        -Logger logger
        -JwtUtil jwtUtil
        +beforeHandshake(ServerHttpRequest, ServerHttpResponse, WebSocketHandler, Map)
        +afterHandshake(ServerHttpRequest, ServerHttpResponse, WebSocketHandler, Exception)
        -validateAuthentication(ServerHttpRequest) boolean
        -extractUserInfo(ServerHttpRequest) Map~String,String~
    }
  
    WebSocketHandshakeInterceptor --> JwtUtil
```

## 4. 组件依赖关系图

```mermaid
graph TD
    A[GrafanaProxyApplication] --> B[GrafanaProxyController]
    B --> C[GrafanaProxyService]
    B --> D[PermissionService]
    B --> E[GrafanaVariableService]
  
    C --> F[GrafanaApiClient]
    C --> G[SqlInterceptorService]
    C --> H[RedisTemplate]
  
    D --> I[GrafanaProxyConfig]
    D --> H
  
    E --> D
  
    J[JwtAuthenticationFilter] --> K[JwtUtil]
    J --> I
  
    L[SecurityConfig] --> J
    L --> I
  
    M[WebSocketConfig] --> N[GrafanaWebSocketProxyHandler]
    M --> O[WebSocketHandshakeInterceptor]
  
    O --> K
  
    P[RedisConfig] --> H
  
    Q[SqlInterceptorConfig] --> G
  
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#e8f5e8
    style E fill:#e8f5e8
    style F fill:#fff3e0
    style G fill:#fff3e0
    style H fill:#fff3e0
    style I fill:#fce4ec
    style J fill:#f1f8e9
    style K fill:#fff3e0
    style L fill:#fce4ec
    style M fill:#fce4ec
    style N fill:#fff3e0
    style O fill:#fff3e0
    style P fill:#fce4ec
    style Q fill:#fce4ec
```

## 5. 技术栈说明

### 5.1 核心框架

- **Spring Boot 2.x**: 应用框架
- **Spring Security**: 安全框架
- **Spring WebSocket**: WebSocket支持

### 5.2 数据存储

- **Redis**: 权限缓存、会话存储
- **Spring Data Redis**: Redis操作

### 5.3 HTTP客户端

- **Apache HttpClient**: HTTP请求处理
- **RestTemplate**: REST API调用

### 5.4 认证授权

- **JWT**: Token认证
- **JJWT**: JWT库

### 5.5 日志监控

- **SLF4J + Log4j2**: 日志框架
- **Spring Boot Actuator**: 监控端点
- **Micrometer**: 指标收集

### 5.6 配置管理

- **Spring Boot Configuration Properties**: 配置绑定
- **Profile**: 多环境配置

### 5.7 测试框架

- **JUnit 5**: 单元测试
- **Mockito**: Mock框架
- **Spring Boot Test**: 集成测试

## 6. 设计模式应用

### 6.1 代理模式 (Proxy Pattern)

- **GrafanaProxyService**: 作为Grafana服务的代理
- **GrafanaApiClient**: HTTP请求代理

### 6.2 策略模式 (Strategy Pattern)

- **PermissionService**: 不同权限验证策略
- **SqlInterceptorService**: 不同SQL拦截策略

### 6.3 过滤器模式 (Filter Pattern)

- **JwtAuthenticationFilter**: JWT认证过滤
- **WebSocketHandshakeInterceptor**: WebSocket握手拦截

### 6.4 模板方法模式 (Template Method Pattern)

- **GrafanaProxyServiceImpl**: 代理请求处理模板

### 6.5 单例模式 (Singleton Pattern)

- **Spring Bean**: 所有服务类都是单例

### 6.6 工厂模式 (Factory Pattern)

- **Configuration类**: Bean工厂
- **RedisConfig**: Redis连接工厂

### 6.7 观察者模式 (Observer Pattern)

- **WebSocket事件处理**: 连接状态变化通知

### 6.8 装饰器模式 (Decorator Pattern)

- **请求/响应修改**: 对原始请求响应进行装饰

## 10. 性能监控与优化

### 10.1 性能监控指标

```mermaid
graph LR
    subgraph "响应时间监控"
        RT1[API响应时间]
        RT2[页面加载时间]
        RT3[数据库查询时间]
    end
    
    subgraph "吞吐量监控"
        TP1[每秒请求数]
        TP2[并发用户数]
        TP3[数据传输量]
    end
    
    subgraph "资源使用监控"
        RU1[CPU使用率]
        RU2[内存使用率]
        RU3[网络带宽]
    end
    
    subgraph "错误率监控"
        ER1[HTTP错误率]
        ER2[认证失败率]
        ER3[权限拒绝率]
    end
```

### 10.2 性能优化策略

#### 10.2.1 缓存优化
```
┌─────────────────────────────────────────────────────────────────┐
│                           缓存策略                               │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  L1缓存(本地) ──→ L2缓存(Redis) ──→ L3缓存(数据库)              │
│      │                  │                    │                  │
│      ▼                  ▼                    ▼                  │
│  [JVM内存]         [Redis集群]         [BBPF系统]              │
│      │                  │                    │                  │
│      ▼                  ▼                    ▼                  │
│  [权限数据]         [用户会话]         [权限配置]              │
│  [配置信息]         [查询结果]         [系统配置]              │
│                                                                 │
├─────────────────────────────────────────────────────────────────┤
│ 缓存策略:                                                        │
│ • L1缓存: 5分钟过期，热点数据                                   │
│ • L2缓存: 30分钟过期，用户权限                                  │
│ • L3缓存: 永久缓存，系统配置                                    │
│                                                                 │
│ 缓存更新:                                                        │
│ • 权限变更时主动失效                                            │
│ • 配置更新时广播通知                                            │
│ • 定期健康检查和清理                                            │
└─────────────────────────────────────────────────────────────────┘
```

#### 10.2.2 连接池优化
- **HTTP连接池**: 最大连接数200，超时时间30秒
- **Redis连接池**: 最大连接数50，最小空闲连接10
- **数据库连接池**: 最大连接数20，连接验证查询

#### 10.2.3 异步处理
- **日志记录**: 异步写入，避免阻塞主线程
- **权限刷新**: 后台定时任务，预加载热点数据
- **监控数据**: 异步收集和上报

### 10.3 监控告警配置

```yaml
# 监控配置示例
monitoring:
  metrics:
    response-time:
      threshold: 2000ms
      alert-level: warning
    error-rate:
      threshold: 5%
      alert-level: critical
    cpu-usage:
      threshold: 80%
      alert-level: warning
  alerts:
    channels:
      - email
      - webhook
      - sms
```

## 11. 技术总结与最佳实践

### 11.1 架构设计原则

1. **单一职责原则**: 每个模块专注于特定功能
2. **开闭原则**: 对扩展开放，对修改封闭
3. **依赖倒置原则**: 依赖抽象而非具体实现
4. **接口隔离原则**: 使用细粒度的接口
5. **最少知识原则**: 减少模块间的耦合

### 11.2 安全最佳实践

- **JWT Token**: 使用强加密算法，设置合理过期时间
- **权限控制**: 最小权限原则，细粒度权限管理
- **SQL注入防护**: 参数化查询，SQL语法验证
- **XSS防护**: 内容安全策略，输出编码
- **CSRF防护**: Token验证，同源检查

### 11.3 性能最佳实践

- **缓存策略**: 多级缓存，合理的过期策略
- **连接复用**: 连接池管理，长连接优化
- **异步处理**: 非阻塞I/O，事件驱动架构
- **资源优化**: 压缩传输，静态资源CDN
- **监控告警**: 实时监控，主动告警

### 11.4 可维护性最佳实践

- **代码规范**: 统一的编码风格，完善的注释
- **单元测试**: 高覆盖率的测试用例
- **文档管理**: 及时更新的技术文档
- **版本控制**: 规范的Git工作流
- **持续集成**: 自动化构建和部署

### 11.5 扩展性设计

- **微服务架构**: 服务拆分，独立部署
- **配置外部化**: 动态配置，热更新
- **插件机制**: 可插拔的功能模块
- **API版本管理**: 向后兼容的API设计
- **水平扩展**: 无状态设计，负载均衡

## 12. 项目总结

这个开发视图和设计文档展示了BBPF Grafana Proxy项目的完整架构，包括：

### 12.1 核心功能模块
- **Token验证模块**: JWT认证和用户身份验证
- **数据权限模块**: 基于用户权限的数据访问控制
- **SQL拦截模块**: 动态SQL查询权限过滤
- **iframe报表嵌入模块**: 安全的仪表盘嵌入
- **分享禁止模块**: 分享功能的安全控制

### 12.2 技术特色
- **动态模板变量注入**: 自动注入用户权限变量
- **多级缓存策略**: 提升系统性能
- **WebSocket代理**: 实时数据传输支持
- **安全防护机制**: 全方位的安全保障
- **高可用架构**: 支持集群部署和负载均衡

### 12.3 设计亮点
- **模块化设计**: 清晰的职责分离
- **可扩展架构**: 支持功能插件化
- **性能优化**: 多维度的性能提升策略
- **安全加固**: 多层次的安全防护
- **运维友好**: 完善的监控和告警机制

这个项目为企业级Grafana集成提供了完整的解决方案，在保证数据安全的前提下，实现了灵活的权限控制和高性能的数据访问。
